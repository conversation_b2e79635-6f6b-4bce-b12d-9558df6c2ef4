---
type: "always_apply"
---

# Robots 项目开发规则

## 🔒 代码修改和审核流程

### 1. 临时方案审核机制
- **所有功能调整**（包括新增、修改、删除等操作）必须先在 `/temp` 目录中创建方案
- **错误分析和修正**方案也必须先放到 `/temp` 目录供审核
- **版本管理**：同一个方案只保留最新版本，覆盖旧版本文件
- **审核确认**：等待用户明确告知"把新方案同步到真实代码"后，才能修改真实代码
- **严格禁止**：不能私自修改真实代码，必须经过审核流程

### 2. 工具使用授权
- **MCP工具使用**：在交互沟通过程中，如需使用MCP工具，可自行决定并使用
- **无需明确要求**：不需要用户明确要求才使用MCP工具
- **主动判断**：根据对话需要主动选择合适的工具

## 📋 操作流程示例

### 功能调整流程：
1. 分析用户需求
2. 在 `/temp` 目录创建方案文件
3. 详细说明修改内容和影响
4. 等待用户审核确认
5. 收到"同步到真实代码"指令后执行

### 错误修正流程：
1. 分析错误原因
2. 在 `/temp` 目录创建修正方案
3. 提供详细的问题分析和解决步骤
4. 等待用户审核确认
5. 收到确认后应用到真实代码

## ⚠️ 重要提醒

- **安全第一**：保护用户的真实代码不被意外修改
- **透明度**：所有修改都要经过用户审核
- **效率平衡**：在安全和效率之间找到平衡点
- **工具灵活性**：合理使用各种工具提高工作效率

