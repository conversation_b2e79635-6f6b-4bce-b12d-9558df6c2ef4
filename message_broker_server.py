#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unix Socket 消息代理服务器
为 Robot Park 项目提供消息路由服务

启动方式：
python message_broker_server.py

Author: Robot Park Team
Date: 2024-01-01
"""

import asyncio
import signal
import sys
import os

# 设置必要的环境变量（解决配置文件读取问题）
os.environ['DB_WRITE_SECTION'] = 'hadb_w'
os.environ['DB_READ_SECTION'] = 'hadb_r'
os.environ['SYS_LOG_FILE'] = './log/log_message_broker.txt'

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.unix_broker import UnixSocketBroker


class MessageBrokerServer:
    """消息代理服务器"""
    
    def __init__(self, socket_path: str = "./sessions/robot_park.sock"):
        self.broker = UnixSocketBroker(socket_path)
        self.is_running = False
        
    async def start(self):
        """启动服务器"""
        try:
            print("🚀 启动 Unix Socket 消息代理服务器...")
            
            # 初始化服务器
            await self.broker.init_server()
            
            # 设置信号处理
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            self.is_running = True
            print(f"✅ 消息代理服务器已启动: {self.broker.socket_path}")
            print("📡 等待客户端连接...")
            
            # 启动服务器监听
            await self.broker.start_server()
            
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")
            sys.exit(1)
            
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🛑 收到停止信号 ({signum})，正在关闭服务器...")
        self.is_running = False
        self.broker.cleanup_server()
        sys.exit(0)
        
    async def stop(self):
        """停止服务器"""
        print("🛑 正在停止消息代理服务器...")
        self.is_running = False
        self.broker.cleanup_server()
        print("✅ 服务器已停止")


async def main():
    """主函数"""
    print("=" * 50)
    print("Unix Socket 消息代理服务器")
    print("Robot Park 项目")
    print("=" * 50)
    
    # 检查权限
    socket_path = "./sessions/robot_park.sock"
    temp_dir = os.path.dirname(socket_path)
    
    if not os.access(temp_dir, os.W_OK):
        print(f"❌ 没有写入权限: {temp_dir}")
        print("请确保当前用户有权限写入 ./sessions 目录")
        sys.exit(1)
    
    # 创建并启动服务器
    server = MessageBrokerServer(socket_path)
    
    try:
        await server.start()
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在关闭服务器...")
    except Exception as e:
        print(f"❌ 服务器运行异常: {e}")
    finally:
        await server.stop()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)
