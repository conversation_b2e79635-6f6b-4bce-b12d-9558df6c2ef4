# -*- coding: utf-8 -*-
"""
女孩管理模块 - 重构版
Girls类：复杂功能类，使用缓存，融合缓存清理机制
TGirls类已移动到 plugins.hazoo.types 模块中
"""
__author__ = 'Kobee.li'

from shared.classes2 import *
from typing import List, Optional
from .feature_translator import featureTranslator
from ..types import TGirls  # 从类型模块导入
import asyncio


class Girls:
    """女孩功能类 - 复杂功能实现"""
    
    def __init__(self):
        self._cache = {}
        self.__sql_str = """
            SELECT id, name, country, age, height, weight, boobs, baby, location, special,
                   special_us, special_ru, special_kz, phone, price_in, price_out, picture,
                   grade, assess_cnt, viewed, status, note
            FROM girls
        """
    
    def __getitem__(self, girl_id: int) -> Optional[TGirls]:
        """获取女孩对象，融合缓存清理机制"""
        try:
            # 检查缓存
            if girl_id in self._cache:
                return self._cache[girl_id]
            
            # 缓存清理机制（融合到此方法中）
            if len(self._cache) >= 500:
                self._cache.clear()
            
            # 从数据库查询
            rst = read_connector.run(self.__sql_str + "WHERE id = %s", [girl_id])
            if rst:
                girl = TGirls(*rst[0])
                
                # 缓存结果
                self._cache[girl_id] = girl
                
                return girl
            
            return None
            
        except Exception as e:
            sys_log.error_log(f"获取女孩失败: {girl_id}, {str(e)}")
            return None


    def new(self, girl_data: dict) -> Optional[TGirls]:
        """创建新女孩"""
        try:
            original_name = girl_data.get('name', '')

            # 先检查是否已存在同名记录（避免唯一索引冲突）
            existing_count_rst = read_connector.run(
                "SELECT COUNT(*) FROM girls WHERE name = %s",
                [original_name]
            )
            existing_count = existing_count_rst[0][0] if existing_count_rst else 0
            sys_log.debug_log(f"baby 的值 {girl_data['baby']}   girl_data.get('baby', 2)")

            # 插入新记录 - 按数据库表结构顺序
            sql_str = """
                INSERT INTO girls (
                    name, country, age, height, weight, boobs, baby, location, special,
                    phone, price_in, price_out, picture, grade, viewed, status, note
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            params_list = [
                original_name,
                girl_data.get('country', 1),
                girl_data.get('age', 0),
                girl_data.get('height', 0),
                girl_data.get('weight', 0),
                girl_data.get('boobs', ''),
                girl_data.get('baby', 2),
                girl_data.get('location', ''),
                girl_data.get('special', ''),
                girl_data.get('phone', ''),
                girl_data.get('price_in', ''),
                girl_data.get('price_out', ''),
                girl_data.get('picture', ''),
                girl_data.get('grade', 0),
                girl_data.get('viewed', 0),
                girl_data.get('status', 0),
                girl_data.get('note', '')
            ]

            exe_connector.run(sql_str, params_list)

            # 获取新插入的ID - 使用同一个连接器确保可靠性
            rst = exe_connector.run("SELECT LAST_INSERT_ID()")
            if rst:
                girl_id = rst[0][0]

                # 确保名字唯一性
                if existing_count > 0:
                    new_name = f"{original_name}_{girl_id}"
                    exe_connector.run("UPDATE girls SET name = %s WHERE id = %s", [new_name, girl_id])

                # 在确保名字唯一性之后，进行多语言翻译
                loop = asyncio.get_event_loop()
                loop.run_until_complete(self._translate_and_update_features(girl_id, girl_data['special']))

                return self[girl_id]
            else:
                sys_log.error_log(f"插入女孩失败，LAST_INSERT_ID返回: {rst}")
                return None

        except Exception as e:
            sys_log.error_log(f"创建女孩失败: {str(e)}")
            return None

    def find_by_name(self, name: str) -> Optional[TGirls]:
        """根据名称查找女孩"""
        try:
            rst = read_connector.run(self.__sql_str + "WHERE name = %s", [name])
            if rst:
                return TGirls(*rst[0])
            return None
        except Exception as e:
            sys_log.error_log(f"根据名称查找女孩失败: {name}, {str(e)}")
            return None

    def search_girls(self, filters: dict) -> List[TGirls]:
        """搜索女孩 - 支持多种筛选条件"""
        try:
            conditions = ["1=1"]
            params_list = []

            # 名字模糊匹配
            if 'name' in filters:
                conditions.append("name LIKE %s")
                params_list.append(f"%{filters['name']}%")

            # 状态筛选
            if 'status' in filters:
                if isinstance(filters['status'], list):
                    placeholders = ','.join(['%s'] * len(filters['status']))
                    conditions.append(f"status IN ({placeholders})")
                    params_list.extend(filters['status'])
                else:
                    conditions.append("status = %s")
                    params_list.append(filters['status'])

            # 地区筛选
            if 'location' in filters:
                conditions.append("location LIKE %s")
                params_list.append(f"%{filters['location']}%")

            # 年龄范围
            if 'age_range' in filters:
                min_age, max_age = filters['age_range']
                conditions.append("age BETWEEN %s AND %s")
                params_list.extend([min_age, max_age])

            # 最小年龄
            if 'min_age' in filters:
                conditions.append("age >= %s")
                params_list.append(filters['min_age'])

            # 最大年龄
            if 'max_age' in filters:
                conditions.append("age <= %s")
                params_list.append(filters['max_age'])

            # 身高范围
            if 'height_range' in filters:
                min_height, max_height = filters['height_range']
                conditions.append("height BETWEEN %s AND %s")
                params_list.extend([min_height, max_height])

            # 最小身高
            if 'min_height' in filters:
                conditions.append("height >= %s")
                params_list.append(filters['min_height'])

            # 最大身高
            if 'max_height' in filters:
                conditions.append("height <= %s")
                params_list.append(filters['max_height'])

            # 体重范围
            if 'weight_range' in filters:
                min_weight, max_weight = filters['weight_range']
                conditions.append("weight BETWEEN %s AND %s")
                params_list.extend([min_weight, max_weight])

            # 最小体重
            if 'min_weight' in filters:
                conditions.append("weight >= %s")
                params_list.append(filters['min_weight'])

            # 最大体重
            if 'max_weight' in filters:
                conditions.append("weight <= %s")
                params_list.append(filters['max_weight'])

            # 胸围模糊匹配
            if 'boobs' in filters:
                conditions.append("boobs LIKE %s")
                params_list.append(f"%{filters['boobs']}%")

            # 是否有孩子
            if 'baby' in filters:
                if isinstance(filters['baby'], list):
                    placeholders = ','.join(['%s'] * len(filters['baby']))
                    conditions.append(f"baby IN ({placeholders})")
                    params_list.extend(filters['baby'])
                else:
                    conditions.append("baby = %s")
                    params_list.append(filters['baby'])

            # 电话筛选
            if 'phone' in filters:
                conditions.append("phone LIKE %s")
                params_list.append(f"%{filters['phone']}%")

            # 国籍筛选
            if 'country' in filters:
                if isinstance(filters['country'], list):
                    placeholders = ','.join(['%s'] * len(filters['country']))
                    conditions.append(f"country IN ({placeholders})")
                    params_list.extend(filters['country'])
                else:
                    conditions.append("country = %s")
                    params_list.append(filters['country'])

            # 等级筛选
            if 'grade' in filters:
                if isinstance(filters['grade'], list):
                    placeholders = ','.join(['%s'] * len(filters['grade']))
                    conditions.append(f"grade IN ({placeholders})")
                    params_list.extend(filters['grade'])
                else:
                    conditions.append("grade = %s")
                    params_list.append(filters['grade'])

            # 等级范围
            if 'grade_range' in filters:
                min_grade, max_grade = filters['grade_range']
                conditions.append("grade BETWEEN %s AND %s")
                params_list.extend([min_grade, max_grade])

            # 备注模糊匹配
            if 'note' in filters:
                conditions.append("note LIKE %s")
                params_list.append(f"%{filters['note']}%")

            # 特色搜索
            if 'special' in filters:
                conditions.append("special LIKE %s")
                params_list.append(f"%{filters['special']}%")

            # 构建SQL
            sql_str = self.__sql_str + f"WHERE {' AND '.join(conditions)} ORDER BY status ASC, grade DESC, viewed ASC"

            # 添加限制
            if 'limit' in filters:
                sql_str += f" LIMIT {filters['limit']}"

            rst = read_connector.run(sql_str, params_list)

            girls_list = []
            for row in rst:
                girls_list.append(TGirls(*row))

            return girls_list

        except Exception as e:
            sys_log.error_log(f"搜索女孩失败: {str(e)}")
            return []

    async def _translate_and_update_features(self, girl_id: int, special_text: str):
        """翻译并更新女孩特点的多语言字段"""
        try:
            # 使用翻译器进行翻译
            translations = await featureTranslator.translates(special_text)

            # 更新数据库多语言字段
            update_sql = """
            UPDATE girls
            SET special_us = %s, special_ru = %s, special_kz = %s
            WHERE id = %s
            """
            exe_connector.run(update_sql, [
                translations.get('english', ''),
                translations.get('russian', ''),
                translations.get('kazakh', ''),
                girl_id
            ])

        except Exception as e:
            sys_log.error_log(f"翻译女孩 {girl_id} 特点失败: {e}")


# 创建全局实例
girls = Girls()
