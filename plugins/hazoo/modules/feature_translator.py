#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
plugins/hazoo/modules/feature_translator.py
最终版智能特点翻译器 - 继承基础翻译服务，提供简化的智能缓存和自动学习功能
"""

import asyncio
from typing import Dict, List
from shared.classes2 import Translator, sys_log, read_connector, exe_connector
# from plugins.hazoo.common import *


class FeatureTranslator(Translator):
    """最终版智能特点翻译器 - 继承基础翻译服务"""
    
    def __init__(self, client=None):
        """初始化特点翻译器"""
        super().__init__(client)
        
        # 缓存配置
        self._cache = {}  # 翻译缓存
        self._max_cache_size = 200  # 最大缓存记录数
        
        # 自动学习配置
        self._temp_mapping_threshold = 5  # 临时映射转固定映射的阈值
        self._short_text_length = 10  # 短文本长度阈值
        
        # 初始化时加载数据库映射
        self._load_database_mappings()
    
    def _load_database_mappings(self):
        """从数据库加载固定映射到缓存"""
        try:
            sql = "SELECT cn, us, ru, kz FROM trans_mapping"
            results = read_connector.run(sql)
            
            for row in results:
                cn, us, ru, kz = row
                self._cache[cn.strip()] = {
                    'en': us,
                    'ru': ru,
                    'kz': kz,
                    'type': 'fixed',  # 固定映射
                    'count': 0
                }
                
        except Exception as e:
            sys_log.error_log(f"加载数据库映射失败: {e}")
    
    def _is_short_text(self, text: str) -> bool:
        """判断是否为短文本（用于临时映射）"""
        return len(text.strip()) <= self._short_text_length
    
    def _manage_cache_size(self):
        """管理缓存大小，清理最少使用的临时映射"""
        if len(self._cache) <= self._max_cache_size:
            return
        
        # 分离固定映射和临时映射
        fixed_mappings = {k: v for k, v in self._cache.items() if v['type'] == 'fixed'}
        temp_mappings = {k: v for k, v in self._cache.items() if v['type'] == 'temp'}
        
        # 如果固定映射就超过限制，只保留固定映射
        if len(fixed_mappings) >= self._max_cache_size:
            self._cache = fixed_mappings
            sys_log.error_log(f"固定映射数量({len(fixed_mappings)})超过缓存限制，已清理所有临时映射，请减少固定映射或增加缓存大小")
            return
        
        # 按使用次数排序临时映射，保留使用次数高的
        sorted_temp = sorted(temp_mappings.items(), key=lambda x: x[1]['count'], reverse=True)
        keep_count = int((self._max_cache_size - len(fixed_mappings)) * 2 / 3)  # 保留2/3数量
        
        # 重建缓存
        self._cache = fixed_mappings
        for i, (key, value) in enumerate(sorted_temp):
            if i < keep_count:
                self._cache[key] = value
        
        sys_log.debug_log(f"缓存清理完成，保留固定映射 {len(fixed_mappings)} 条，临时映射 {min(keep_count, len(temp_mappings))} 条")
    
    async def _save_to_database(self, cn: str, translations: Dict[str, str]):
        """将临时映射保存到数据库"""
        try:
            sql = """
            INSERT INTO trans_mapping (cn, us, ru, kz) 
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            us = VALUES(us),
            ru = VALUES(ru),
            kz = VALUES(kz)
            """
            
            params = [
                cn,
                translations.get('en', ''),
                translations.get('ru', ''),
                translations.get('kz', '')
            ]
            
            exe_connector.run(sql, params)
            sys_log.debug_log(f"已保存翻译映射到数据库: {cn}")
            
        except Exception as e:
            sys_log.error_log(f"保存翻译映射到数据库失败: {e}")
    
    async def _translate_single_text(self, text: str) -> Dict[str, str]:
        """
        翻译单个文本
        
        Args:
            text: 中文文本
            
        Returns:
            翻译字典 {'en': '英文', 'ru': '俄语', 'kz': '哈语'}
        """
        if not text or not text.strip():
            return {'en': text, 'ru': text, 'kz': text}
        
        text = text.strip()
        
        # 1. 检查缓存
        if text in self._cache:
            cached_item = self._cache[text]
            
            # 更新使用次数（仅对临时映射）
            if cached_item['type'] == 'temp':
                cached_item['count'] += 1
                
                # 检查是否需要转为固定映射
                if cached_item['count'] > self._temp_mapping_threshold:
                    await self._save_to_database(text, cached_item)
                    cached_item['type'] = 'fixed'  # 转为固定映射
                    cached_item['count'] = 0  # 重置计数
                    sys_log.debug_log(f"临时映射已转为固定映射: {text}")
            
            return {
                'en': cached_item['en'],
                'ru': cached_item['ru'],
                'kz': cached_item['kz']
            }
        
        # 2. 使用父类翻译服务
        try:
            translations = await self.translate_to_multiple_languages(
                text, 
                ['en', 'ru', 'kz'], 
                method='simple'  # 使用simple方法
            )
            
            # 3. 决定是否缓存（优化：长文本不缓存）
            if self._is_short_text(text):
                # 短文本：添加到临时映射缓存
                self._cache[text] = {
                    'en': translations.get('en', text),
                    'ru': translations.get('ru', text),
                    'kz': translations.get('kz', text),
                    'type': 'temp',
                    'count': 1
                }
                sys_log.debug_log(f"短文本已添加到临时映射缓存: {text}")
            else:
                # 长文本：不缓存，不保存数据库，直接返回翻译结果
                sys_log.debug_log(f"长文本直接翻译，不缓存: {text}")
            
            return translations
            
        except Exception as e:
            sys_log.error_log(f"翻译单个文本失败: {text}, {e}")
            return {'en': text, 'ru': text, 'kz': text}
    
    def _split_features(self, features: str) -> List[str]:
        """分割特点字符串"""
        if not features:
            return []
        
        # 使用"/"分割，并清理空白
        parts = [part.strip() for part in features.split('/') if part.strip()]
        return parts
    
    async def translates(self, chinese_features: str) -> Dict[str, str]:
        """
        翻译女孩特点到多语言
        
        Args:
            chinese_features: 中文特点字符串，用"/"分隔
            
        Returns:
            {
                'english': '英文特点',
                'russian': '俄语特点', 
                'kazakh': '哈语特点'
            }
        """
        if not chinese_features or not chinese_features.strip():
            return {'english': '', 'russian': '', 'kazakh': ''}
        
        try:
            # 分割特点
            features = self._split_features(chinese_features)
            if not features:
                return {'english': '', 'russian': '', 'kazakh': ''}
            
            # 翻译每个特点
            translated_features = {
                'english': [],
                'russian': [],
                'kazakh': []
            }
            
            for feature in features:
                feature_translations = await self._translate_single_text(feature)
                translated_features['english'].append(feature_translations['en'])
                translated_features['russian'].append(feature_translations['ru'])
                translated_features['kazakh'].append(feature_translations['kz'])
            
            # 组合结果
            result = {
                'english': '/'.join(translated_features['english']),
                'russian': '/'.join(translated_features['russian']),
                'kazakh': '/'.join(translated_features['kazakh'])
            }
            
            # 优化：缓存管理移到这里执行
            self._manage_cache_size()
            
            sys_log.debug_log(f"特点翻译完成: {chinese_features} -> {result}")
            return result
            
        except Exception as e:
            sys_log.error_log(f"翻译女孩特点失败: {e}")
            return {
                'english': chinese_features,
                'russian': chinese_features,
                'kazakh': chinese_features
            }
    
    async def batch_translate(self, features_list: List[str]) -> List[Dict[str, str]]:
        """
        批量翻译特点列表
        
        Args:
            features_list: 中文特点字符串列表
            
        Returns:
            翻译结果列表
        """
        if not features_list:
            return []
        
        try:
            # 并行翻译
            tasks = []
            for features in features_list:
                task = self.translates(features)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            translations = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    sys_log.error_log(f"批量翻译第{i}项失败: {result}")
                    # 失败时使用原文
                    original = features_list[i]
                    translations.append({
                        'english': original,
                        'russian': original,
                        'kazakh': original
                    })
                else:
                    translations.append(result)
            
            return translations
            
        except Exception as e:
            sys_log.error_log(f"批量翻译特点失败: {e}")
            return []


featureTranslator = FeatureTranslator()

