# -*- coding: utf-8 -*-
"""
Hazoo模块统一导入文件 - 重构版
解决导入语句过多的问题，兼顾职责清晰和导入便利
消除循环导入问题
"""
__author__ = 'Kobee.li'

# 从类型模块导入类型定义
from ..types import TGirls

# 导入功能类（不再导入 girls_manage 避免循环）
from .girls import Girls
from .guests import Guests

# 统一导出，方便外部导入
__all__ = [
    'Girls',
    'TGirls',
    'Guests',
]

# 注意：girls_manage 相关功能需要直接导入，不再通过 __init__.py 导入
# 使用方式：
# from plugins.hazoo.modules.girls_manage import handle_girl_add_start
# 而不是：
# from plugins.hazoo.modules import handle_girl_add_start
