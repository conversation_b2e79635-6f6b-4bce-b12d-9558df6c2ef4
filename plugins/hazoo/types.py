# -*- coding: utf-8 -*-
"""
Hazoo 项目类型定义模块
集中管理所有数据类型，避免循环导入

这个模块包含：
1. TGirls 数据模型类
2. 其他数据类型定义

使用方式：
    from plugins.hazoo.types import TGirls

    # 创建女孩对象
    girl = TGirls(id=1, name="Alice", ...)

    # 使用属性
    print(girl.name)
    girl.age = 25  # 自动更新数据库
"""
__author__ = 'Kobee.li'

from dataclasses import dataclass
from shared.classes2 import exe_connector


@dataclass
class TGirls:
    """女孩数据模型类 - 按数据库字段顺序"""
    id: int
    name: str
    _country: int
    _age: int
    _height: int
    _weight: int
    _boobs: str
    _baby: int
    _location: str
    _special: str
    _special_us: str
    _special_ru: str
    _special_kz: str
    _phone: str
    _price_in: str
    _price_out: str
    _picture: str
    _grade: int
    _assess_cnt: int
    _viewed: int
    _status: int
    _note: str

    _sql_str = "UPDATE girls SET {field} = %s WHERE id = %s"

    @property
    def country(self):
        return self._country
    
    @country.setter
    def country(self, value):
        exe_connector.run(self._sql_str.format(field="country"), [value, self.id])
        self._country = value
        
    @property
    def age(self):
        return self._age
    
    @age.setter
    def age(self, value):
        exe_connector.run(self._sql_str.format(field="age"), [value, self.id])
        self._age = value
        
    @property
    def height(self):
        return self._height
    
    @height.setter
    def height(self, value):
        exe_connector.run(self._sql_str.format(field="height"), [value, self.id])
        self._height = value
        
    @property
    def weight(self):
        return self._weight
    
    @weight.setter
    def weight(self, value):
        exe_connector.run(self._sql_str.format(field="weight"), [value, self.id])
        self._weight = value
        
    @property
    def boobs(self):
        return self._boobs
    
    @boobs.setter
    def boobs(self, value):
        exe_connector.run(self._sql_str.format(field="boobs"), [value, self.id])
        self._boobs = value
        
    @property
    def baby(self):
        return self._baby
    
    @baby.setter
    def baby(self, value):
        exe_connector.run(self._sql_str.format(field="baby"), [value, self.id])
        self._baby = value
        
    @property
    def location(self):
        return self._location
    
    @location.setter
    def location(self, value):
        exe_connector.run(self._sql_str.format(field="location"), [value, self.id])
        self._location = value
        
    @property
    def special(self):
        return self._special
    
    @special.setter
    def special(self, value):
        exe_connector.run(self._sql_str.format(field="special"), [value, self.id])
        self._special = value
        
    @property
    def special_us(self):
        return self._special_us
    
    @special_us.setter
    def special_us(self, value):
        exe_connector.run(self._sql_str.format(field="special_us"), [value, self.id])
        self._special_us = value
        
    @property
    def special_ru(self):
        return self._special_ru
    
    @special_ru.setter
    def special_ru(self, value):
        exe_connector.run(self._sql_str.format(field="special_ru"), [value, self.id])
        self._special_ru = value
        
    @property
    def special_kz(self):
        return self._special_kz
    
    @special_kz.setter
    def special_kz(self, value):
        exe_connector.run(self._sql_str.format(field="special_kz"), [value, self.id])
        self._special_kz = value
        
    @property
    def phone(self):
        return self._phone
    
    @phone.setter
    def phone(self, value):
        exe_connector.run(self._sql_str.format(field="phone"), [value, self.id])
        self._phone = value
        
    @property
    def price_in(self):
        return self._price_in
    
    @price_in.setter
    def price_in(self, value):
        exe_connector.run(self._sql_str.format(field="price_in"), [value, self.id])
        self._price_in = value
        
    @property
    def price_out(self):
        return self._price_out
    
    @price_out.setter
    def price_out(self, value):
        exe_connector.run(self._sql_str.format(field="price_out"), [value, self.id])
        self._price_out = value
        
    @property
    def picture(self):
        return self._picture
    
    @picture.setter
    def picture(self, value):
        exe_connector.run(self._sql_str.format(field="picture"), [value, self.id])
        self._picture = value
        
    @property
    def grade(self):
        return self._grade
    
    @grade.setter
    def grade(self, value):
        exe_connector.run(self._sql_str.format(field="grade"), [value, self.id])
        self._grade = value
        
    @property
    def assess_cnt(self):
        return self._assess_cnt
    
    @assess_cnt.setter
    def assess_cnt(self, value):
        exe_connector.run(self._sql_str.format(field="assess_cnt"), [value, self.id])
        self._assess_cnt = value
        
    @property
    def viewed(self):
        return self._viewed
    
    @viewed.setter
    def viewed(self, value):
        exe_connector.run(self._sql_str.format(field="viewed"), [value, self.id])
        self._viewed = value
        
    @property
    def status(self):
        return self._status
    
    @status.setter
    def status(self, value):
        exe_connector.run(self._sql_str.format(field="status"), [value, self.id])
        self._status = value
        
    @property
    def note(self):
        return self._note
    
    @note.setter
    def note(self, value):
        exe_connector.run(self._sql_str.format(field="note"), [value, self.id])
        self._note = value
    
    def to_tuple(self) -> tuple:
        """把模型字段转成元组返回 - 按数据库表结构字段顺序"""
        return (
            self.id, self.name, self._country, self._age, self._height, self._weight,
            self._boobs, self._baby, self._location, self._special, self._special_us,
            self._special_ru, self._special_kz, self._phone, self._price_in, 
            self._price_out, self._picture, self._grade, self._assess_cnt, 
            self._viewed, self._status, self._note
        )


# 未来可以在这里添加其他类型定义
# 例如：TGuests, TOrders, TChannels 等
