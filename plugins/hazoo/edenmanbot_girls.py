# -*- coding: utf-8 -*-
"""
edenmanbot 女孩管理插件 - 重构版
通过单字符消息触发内联按钮，包含新增女孩功能
使用 filters.user() 进行权限控制，无需装饰器
解决循环导入问题
"""
__author__ = 'Kobee.li'

from pyrogram import Client, filters
from pyrogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

# 直接导入需要的函数，避免通过 modules.__init__ 导入
from plugins.hazoo.modules.girls_manage import (
    handle_girl_add_start,
    handle_text_input,
    handle_media_group_input,
    handle_selection_callback,
    get_keyboard
)

# 导入公共功能（明确导入，避免 * 导入）
from plugins.hazoo.common import (
    clients,
    params,
    e_warning,
    e_succ,
    e_flower,
    sys_log
)



# 回调查询处理器（使用 filters.user 权限控制）
@Client.on_callback_query(filters.regex(r"^girl_add$") & filters.user(params['hazoo.kefu']))
async def girl_add_callback(client: Client, callback_query: CallbackQuery):
    """开始新增女孩回调"""
    await handle_girl_add_start(callback_query)


@Client.on_callback_query(filters.regex(r"^girl_list$") & filters.user(params['hazoo.kefu']))
async def girl_list_callback(client: Client, callback_query: CallbackQuery):
    """查看女孩列表回调"""
    try:
        # 这里可以实现女孩列表功能
        await callback_query.answer("女孩列表功能待实现")
        
    except Exception as e:
        sys_log.error_log(f"处理女孩列表回调失败: {e}")
        await callback_query.answer(f"{e_warning} 处理失败")


@Client.on_callback_query(filters.regex(r"^girl_stats$") & filters.user(params['hazoo.kefu']))
async def girl_stats_callback(client: Client, callback_query: CallbackQuery):
    """女孩统计回调"""
    try:
        # 这里可以实现女孩统计功能
        await callback_query.answer("女孩统计功能待实现")
        
    except Exception as e:
        sys_log.error_log(f"处理女孩统计回调失败: {e}")
        await callback_query.answer(f"{e_warning} 处理失败")


@Client.on_callback_query(filters.regex(r"^main_menu$") & filters.user(params['hazoo.kefu']))
async def main_menu_callback(client: Client, callback_query: CallbackQuery):
    """返回主菜单回调 - 修复重复内容错误"""
    try:
        # 检查当前消息内容，避免重复编辑
        new_text = f"{e_flower} 欢迎进入<b>伊甸园</b>管理系统 {e_flower}"

        # 只有当内容不同时才编辑消息
        if callback_query.message.text.html != new_text:
            await callback_query.edit_message_text(new_text, 
                                                   reply_markup=get_keyboard_common('main_menu')
                                                   )
        else:
            # 如果内容相同，只回答callback_query
            await callback_query.answer("已在主菜单")

    except Exception as e:
        sys_log.error_log(f"处理主菜单回调失败: {e}")
        await callback_query.answer(f"{e_warning} 处理失败")


@Client.on_callback_query(filters.regex(r"^(field_|country_|baby_|girl_|confirm_)") & filters.user(params['hazoo.kefu']))
async def girl_selection_callback(client: Client, callback_query: CallbackQuery):
    """女孩管理选择回调"""
    await handle_selection_callback(callback_query)


# 文本消息处理器 - 处理女孩管理相关的文本输入（使用 filters.user 权限控制）
@Client.on_message(filters.text & filters.private & filters.reply & filters.user(params['hazoo.kefu']))
async def girl_text_input_handler(client: Client, message: Message):
    """女孩管理文本输入处理"""
    # 检查是否是女孩管理相关的输入
    if (message.reply_to_message and "回复本消息" in message.reply_to_message.text):
        await handle_text_input(client, message)


# 媒体组消息处理器 - 处理图片上传（使用 filters.media_group 和 filters.user 权限控制）
@Client.on_message(filters.media_group & filters.private & filters.reply & filters.user(params['hazoo.kefu']))
async def girl_media_input_handler(client: Client, message: Message):
    """女孩管理媒体输入处理"""
    # 检查是否是女孩管理相关的媒体输入
    if (message.reply_to_message and "回复本消息上传图片或视频" in message.reply_to_message.text):
        await handle_media_group_input(client, message)
