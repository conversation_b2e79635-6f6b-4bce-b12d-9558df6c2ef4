# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'

from plugins.hazoo.common import sys_log, std_to_user
from shared.unix_broker import unix_broker
from pyrogram import Client, filters

@Client.on_message(filters.private & filters.incoming & filters.regex('test'), group=3)
async def f_on_message2(client, message):
    try:
        # media = await medias.get_media(client, "11,13")
        # await client.send_media_group(1914064729, media)
        # sys_log.debug_log(f"ayanroom 发送信息\n{media}")
        user_id = std_to_user("0001A")
        await message.reply_text(f"你的用户ID是: {user_id}", quote=False)
    except Exception as e:
        sys_log.error_log("f_on_message2 收到信息异常")


# # test收到信息
# @Client.on_message(filters.private & filters.incoming, group=3)
# async def f_on_message(client, message):
#     try:
#         # 获取消息内容
#         message_text = message.text or message.caption or ""

#         # 如果有文本内容，转发给edenguestbot
#         if message_text:
#             sys_log.write_log(f"ayanroom收到私信: {message_text}")

#             # 通过Unix Socket发送消息给edenguestbot
#             await unix_broker.send_message(
#                 receiver="hazooguest",
#                 message_type="forward_message",
#                 content={
#                     "text": message_text,
#                     "from_user_id": message.from_user.id,
#                     "from_user_name": message.from_user.first_name or "Unknown"
#                 }
#             )

#             sys_log.write_log(f"消息已转发给edenguestbot: {message_text}")

#         # 保留原有的媒体保存功能
#         sys_log.debug_log(f"ayanroom 收到信息")
#         rst_check = await check_media_group(client, message)
#         if rst_check[0] != '1':
#             return
#         ids = await medias.save_media(client, message)
#         sys_log.debug_log(f"ayanroom 保存信息 \n{ids}")

#     except Exception as e:
#         sys_log.error_log("f_on_message 收到信息异常")


