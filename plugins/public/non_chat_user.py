# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from shared.classes2 import *
from pyrogram import Client, filters


# 更新消息的chats信息
@Client.on_message(group=-9)
async def all_message(client, message):
    try:
        myChats.update_message(message)
        username = clients.get_username(client)
        if message.outgoing:
            sys_log.debug_log(f"{username} 发送信息_无成员 {message.id}")
        else:
            sys_log.debug_log(f"{username} 收到信息_无成员 {message.id}")
    except Exception:
        sys_log.error_log("消息处理异常")


# 更新callback中的chats信息
@Client.on_callback_query(group=-9)
async def all_callback(_, callback):
    try:
        myChats.update_callback(callback)
    except Exception:
        sys_log.error_log("回调处理异常")


# 用户加入
@Client.on_message(filters.new_chat_members, group=-8)
async def new_chat_member(_, message):
    try:
        for member in message.new_chat_members:
            myChats.update_chat(member)
    except Exception:
        sys_log.error_log("新成员处理异常")


# 用户退出
@Client.on_message(filters.left_chat_member, group=-8)
async def left_chat_member(_, message):
    try:
        pass # 这里可以完全为空，或者只做必要的日志记录
    except Exception:
        sys_log.error_log("成员退出处理异常")


# chat_user命令
@Client.on_message(filters.me & filters.command("chat_user"), group=-8)
async def update_chat_member_disabled(_, message):
    try:
        await message.reply("此机器人已禁用chat_user功能")
    except Exception:
        sys_log.error_log("chat_user命令处理异常")
