# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from shared.classes2 import *
import traceback
import asyncio
from pyrogram import Client, filters


# 更新消息的chats信息
@Client.on_message(group=-9)
async def all_message(client, message):
    try:
        myChats.update_message(message)
        username = clients.get_username(client)
        if message.outgoing:
            sys_log.debug_log(f"{username} 发送信息 {message.id}")
        else:
            sys_log.debug_log(f"{username} 收到信息 {message.id}")
        # 监控群活跃成员
        if message.chat is not None and message.from_user is not None \
                and message.chat.type in (ChatType.GROUP, ChatType.SUPERGROUP):
            chatUser.active(message.chat.id, message.from_user.id)
    except Exception:
        sys_log.error_log("消息处理异常")


# 更新callback中的chats信息
@Client.on_callback_query(group=-9)
async def all_callback(client, callback):
    try:
        myChats.update_callback(callback)
    except Exception:
        sys_log.error_log("回调处理异常")


# 用户加入
@Client.on_message(filters.new_chat_members, group=-8)
async def new_chat_member(client, message):
    try:
        chat_id = message.chat.id
        for member in message.new_chat_members:
            myChats.update_chat(member)
            user_id = member.id
            chatUser.join(chat_id, user_id)
            if myChats[user_id].username == clients.get_username(client):
                await chatUser.update_chat_user(clients.get_username(client), chat_id, False)
    except Exception:
        sys_log.error_log("新成员处理异常")


# 用户退出
@Client.on_message(filters.left_chat_member, group=-8)
async def left_chat_member(_, message):
    try:
        chat_id = message.chat.id
        user_id = message.left_chat_member.id
        chatUser.leave(chat_id, user_id)
    except Exception:
        sys_log.error_log("成员退出处理异常")


# 更新群户关系
@Client.on_message(filters.me & filters.command("chat_user"), group=-8)
async def update_chat_member(client, message):
    try:
        await chatUser.poll_client([clients.get_username(client)])
    except Exception:
        sys_log.error_log("chat_user命令处理异常")


# 获取对方的peer
@Client.on_message(filters.me & filters.regex(r"^/peer -?\w+$"), group=-8)
async def get_peer(client, message):
    try:
        comm = message.text.split(' ')
        if re.match(r'^-?\d+$', comm[1], re.S) is not None:
            text = "通过id提取peer"
            try:
                peer = str(await client.resolve_peer(peer_id=int(comm[1])))
                rst = "成功：\n"
            except Exception:
                peer = traceback.format_exc()
                rst = "失败：\n"
        else:
            text = "通过username/number提取peer"
            try:
                peer = str(await client.resolve_peer(peer_id=comm[1]))
                rst = "成功：\n"
            except Exception:
                peer = traceback.format_exc()
                rst = "失败：\n"
        await message.reply(text+rst+peer)

    except Exception:
        sys_log.error_log("peer获取异常")


# 获取对方的user
@Client.on_message(filters.me & filters.regex(r"^/chat -?\w+$"), group=-8)
async def get_chat(client, message):
    try:
        comm = message.text.split(' ')
        chat_arr = []
        chat_db = chat_tg = err = ''
        if re.match(r'^-?\d+$', comm[1], re.S) is not None:
            text = "通过id提取chat{0:s}:\n"
            try:
                chat_arr = myChats[int(comm[1])].to_list()
                if comm[1][0] == '-':
                    chat_tg = str(await client.get_chat(int(comm[1])))
                else:
                    chat_tg = str(await client.get_users(int(comm[1])))
                rst = "成功"
            except Exception:
                err = traceback.format_exc()
                rst = "失败"
        else:
            text = "通过username提取chat{0:s}:\n"
            try:
                user = await client.get_chat(comm[1])
                if user.type in (ChatType.PRIVATE, ChatType.BOT):
                    user = await client.get_users(comm[1])
                chat_tg = str(user)
                chat_arr = myChats[user.id].to_list()
                rst = "成功"
            except Exception:
                err = traceback.format_exc()
                rst = "失败"
                sql_str = "select id,type,name,outname,title,username,first_name,last_name,status," \
                          "online_date from chats where username in ('{0:s}')".format(comm[1])
                rst2 = read_connector.run(sql_str)
                if len(rst2) != 0:
                    chat_arr = rst2[0]
        # 获取数据库中信息
        if len(chat_arr) > 0:
            chat_db = """id: {0[0]:d}
type: {0[1]:s}
name: {0[2]:s}
outname: {0[3]:s}
title: {0[4]:s}
username: {0[5]:s}
first_name: {0[6]:s}
last_name: {0[7]:s}
status: {1:s}
online_date: {0[9]:%Y-%m-%d %X}
\n""".format(chat_arr, str(explain('UserStatus', chat_arr[8])))
        await message.reply(text.format(rst)+chat_db+chat_tg+err)
    except Exception:
        sys_log.error_log("chat获取异常")


# 获取std_id
@Client.on_message(filters.outgoing & filters.command("id"), group=-8)
async def get_guest_id(client, message):
    try:
        user_id = message.chat.id
        await message.delete()
        new_id = get_users_id(user_id)
        await client.send_message('me', f"用户 {myChats[user_id].outname} 的ID是 <b><code>{new_id:05}</code></b>")
    except Exception as e:
        sys_log.error_log("获取ID异常")


# 获取emo
@Client.on_message(filters.me & filters.command("emo"), group=-8)
async def get_emo(client, message):
    try:
        print(message)
        text = ''
        for entity in message.entities:
            if entity.type == MessageEntityType.CUSTOM_EMOJI:
                text += f"Emo(位置 {entity.offset}, 长度 {entity.length}) ID:{entity.custom_emoji_id}\n"
        if text == '':
            text = f"输入的消息没有emo"
        await client.send_message('me', text)
    except Exception as e:
        sys_log.error_log("emo获取异常")
