# -*- coding: utf-8 -*-
"""
Hazoo项目任务处理器
放置位置: ./plugins/handlers/hazooadmin.py
"""

import asyncio
from shared.classes2 import *


# ==================== Telegram 相关处理器 ====================


async def test_monit(**params):
    try:
        client_name = params.get('client_name', '')
        obj_chat = params.get('obj_chat', 0)

        # 检查客户端是否存在
        sys_log.debug_log(clients._cache)
        if client_name not in clients.keys():
            sys_log.write_log(f"[{client_name}] 客户端不存在，跳过测试消息")
            return

        client = clients[client_name]

        # 检查客户端是否已连接
        if not hasattr(client, 'is_connected') or not client.is_connected:
            sys_log.write_log(f"[{client_name}] 客户端未连接，跳过测试消息")
            return

        sys_log.write_log(f"[{client_name}] 开始发消息给 {obj_chat}")

        await client.send_message(obj_chat, 'test')

        result = f"[{client_name}] 给 {obj_chat} 发送了测试消息"
        sys_log.write_log(result)

    except ConnectionError as e:
        sys_log.write_log(f"[{client_name}] 客户端未启动完成，跳过测试消息: {e}")
    except Exception as e:
        sys_log.error_log(f"发送测试消息失败")
        raise


