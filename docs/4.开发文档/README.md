# 开发文档

## 概述

本目录包含开发环境配置、编码规范、实现说明等开发相关文档。

## 文档分类

### 开发环境
- 环境搭建指南
- 依赖管理
- 开发工具配置
- 调试指南

### 编码规范
- 代码风格规范
- 命名规范
- 注释规范
- 代码审查标准

### 实现说明
- 核心功能实现
- 算法实现说明
- 第三方库使用
- 性能优化技巧

### 开发流程
- Git工作流程
- 分支管理策略
- 代码提交规范
- 发布流程

## 当前文档列表

> 暂无文档

## 核心开发原则

### 第一原则：禁止使用模拟数据
- **必须**使用真实的数据源和API接口
- **严禁**硬编码测试数据、占位符数据
- **必须**通过配置文件、数据库或外部数据源提供数据

### 第二原则：修复所有编译错误和警告
- **必须**确保零编译错误
- **必须**解决所有编译器警告
- **必须**使用代码检查工具确保质量
- **必须**确保在Python 3.11.9 + QML/Qt环境正常运行

### 第三原则：避免硬编码实现
- **必须**通过配置文件或环境变量管理参数
- **严禁**直接写入具体数值或字符串常量
- **必须**使用配置管理系统

### 第四原则：MECE原则
- **必须**确保功能模块相互独立
- **必须**确保功能覆盖完整
- **必须**保持逻辑清晰和结构完整

## 技术栈

- **后端**: Python 3.11.9
- **前端**: QML/Qt
- **开发模式**: TDD (测试驱动开发)
- **架构模式**: 领域驱动设计 + 依赖注入

## 文档模板

### 功能实现文档模板
```markdown
# [功能名称]实现说明

## 功能概述
## 技术方案
## 实现步骤
## 关键代码说明
## 测试方案
## 注意事项
```

## 更新指南

1. **新增功能时**，必须提供实现文档
2. **技术选型变更时**，需要更新相关文档
3. **每次更新后**，请更新本README.md文件的文档列表

---

**最后更新时间**：$(date +"%Y-%m-%d")
