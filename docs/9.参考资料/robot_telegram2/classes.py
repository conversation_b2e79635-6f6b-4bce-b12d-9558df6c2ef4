# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
import pymysql
from configparser import ConfigParser
from typing import Union
import pathlib
import re
import time
import shutil
import os
from pyrogram import Client
import logging
import traceback
import sys
import pandas as pd
import telnetlib


log = logging.getLogger('werkzeug')
logging.basicConfig(level=logging.ERROR,
                    # level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


# 定义全局方法
# 验证代理是否可用
def test_ip(ip: str, port: int):
    try:
        telnetlib.Telnet(ip, port, timeout=5)
        return True
    except (telnetlib.socket.timeout, ConnectionRefusedError):
        return False
    else:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 把ip地址从txt文档入库，并合并到IP池
def proxy_load():
    # 入库前清空目标表
    sql_str = "delete from ip_load"
    tg_connector.exe(sql_str)
    load_rst = tg_connector.txt_to_tab('./txt/ip.txt', 'ip_load')
    sql_str = "delete from ip_load where id not in (select min(id) from ip_load group by ip)"
    tg_connector.exe(sql_str)
    sql_str = "select count(1) from ip_load"
    rst = tg_connector.query(sql_str)
    if load_rst[2] == rst[0][0]:
        print("恭喜！数据导入已完成\n文件行数 {0[0]:d}\n入库前记录数 {0[1]:d}\n入库后记录数 {0[2]:d}\n记录数变化 {0[3]:d}\n"
              "记录中无重复项\n".format(load_rst))
    else:
        print("恭喜！数据导入已完成\n文件行数 {0[0]:d}\n入库前记录数 {0[1]:d}\n入库后记录数 {0[2]:d}\n记录数变化 {0[3]:d}\n"
              "删除重复项后记录数 {1:d}\n".format(load_rst, rst[0][0]))
    str_input = input("是否继续合并到ip池？(y/n) ")
    if str_input.lower() == 'y':
        sql_str = "select count(1) from ips"
        pre_cnt = tg_connector.query(sql_str)[0][0]
        # sql_str_2 = "insert into ips(ip, created_at) select ip,CURRENT_TIMESTAMP() from ip_load ON DUPLICATE " \
        #             "key update created_at=CURRENT_TIMESTAMP(),checked_at='2000-01-01 00:00:00',check_status='0'"
        # tg_connector.exe(sql_str_2)
        sql_str_2 = "delete from ips"
        tg_connector.exe(sql_str_2)
        sql_str_2 = "insert into ips(ip, created_at) select ip,CURRENT_TIMESTAMP() from ip_load"
        tg_connector.exe(sql_str_2)
        aft_cnt = tg_connector.query(sql_str)[0][0]
        print("恭喜！数据已合并到ip池\n入库前记录数 {0:d}\n入库后记录数 {1:d}\n记录数变化 {2:d}\n"
              .format(pre_cnt, aft_cnt, aft_cnt-pre_cnt))


# 代理IP池 检查ip状态 检查状态 0:未检查(默认)；1:有效；2:失效
def proxy_check(ip, port):
    try:
        if test_ip(ip, port):
            status = '1'
        else:
            status = '2'
        sql_str = "update ips set check_status='{1:s}',checked_at=CURRENT_TIMESTAMP() " \
                  "where ip='{0:s}'".format(ip, status)
        tg_connector.exe(sql_str)
        return status
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 循环检查ip状态
def proxy_cycle_check():
    try:
        sql_str = "select ip,port,check_status from ips where checked_at<date_add(current_timestamp(),interval - 24 hour)" \
                  " and check_status in ('0','1') order by created_at desc,checked_at"
        ck_list = tg_connector.query(sql_str)
        for row in ck_list:
            check_status = proxy_check(row[0], row[1])
            print("代理 {0[0]:s}: {0[2]:s} -> {1:s}".format(row, check_status))
            time.sleep(3)
        print("批量检查已完成 {0:d}".format(len(ck_list)))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 设定使用状态 使用状态 0:未使用(默认)；1:临时使用；2:长期使用；3：瞬间使用
def proxy_set_use_status(ip, use_status):
    try:
        if use_status in ('1', '2'):
            sql_str = "update ips set use_status='{1:s}', use_cnt=use_cnt+1, used_at=CURRENT_TIMESTAMP() where " \
                      "ip='{0:s}'".format(ip, use_status)
        elif use_status == '3':
            sql_str = "update ips set use_status='0', use_cnt=use_cnt+1, used_at=CURRENT_TIMESTAMP() where " \
                      "ip='{0:s}'".format(ip)
        else:
            sql_str = "update ips set use_status='{1:s}', used_at=CURRENT_TIMESTAMP() where " \
                      "ip='{0:s}'".format(ip, use_status)
        tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 修复使用状态
# 1、如果不在clients中的ip不属于长期使用
# 2、使用时长超过1天的不属于临时使用
def proxy_repair_use_status():
    try:
        sql_str = "update ips set use_status='0' where use_status='2' and ip not in (select proxy_addr from clients)"
        tg_connector.exe(sql_str)
        sql_str = "update ips set use_status='0' where use_status='1' and " \
                  "used_at<date_add(current_timestamp(),interval - 24 hour)"
        tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取一个可用IP 使用状态 0:未使用(默认)；1:临时使用；2:长期使用
def proxy_get(use_status='1'):
        try:
            sql_str = "select ip,port from ips where check_status in ('0','1') order by use_status,used_at,use_cnt," \
                      "checked_at desc limit 5"
            rst = tg_connector.query(sql_str)
            for row in rst:
                check_status = proxy_check(row[0], row[1])
                if check_status == '1':
                    proxy_set_use_status(row[0], use_status)
                    return row
                else:
                    proxy_set_use_status(row[0], '0')
                    time.sleep(2)
            return ()
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            

# 重写ConfigParser类，取消自动转换成小写的功能
class MyConfigParser(ConfigParser):
    def __init__(self, defaults=None):
        ConfigParser.__init__(self, defaults=defaults)

    def optionxform(self, optionstr):
        return optionstr


# 数据库操作类
class MySQLConn:
    def __init__(self, db_section):
        self.host = db_conf.read(db_section, 'host')
        self.port = db_conf.read(db_section, 'port', 'int')
        self.dataBase = db_conf.read(db_section, 'dbname')
        self.user = db_conf.read(db_section, 'user')
        self.pwd = db_conf.read(db_section, 'pwd')
        self.charset = db_conf.read(db_section, 'charset')
        self.conn = ''
        self.cursor = ''
        self.retry_time = time.time() - 70

# 打开数据库连接
    def open(self):
        try:
            self.conn = pymysql.connect(host=self.host,
                                        port=self.port,
                                        user=self.user,
                                        password=self.pwd,
                                        database=self.dataBase,
                                        charset=self.charset,
                                        autocommit=True)
            self.cursor = self.conn.cursor()
            return True
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 关闭数据库连接
    def close(self):
        try:
            self.cursor.close()
            self.conn.close()
            return True
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 执行查询语句，返回数据表
    def query(self, sql_str):
        try:
            self.cursor.execute(sql_str)
            return self.cursor.fetchall()
        except pymysql.err.OperationalError:
            if self.retry_conn():
                return self.query(sql_str)
            return []
        except Exception as e:
            sys_log.write_log(sql_str, 'a')
            sys_log.write_log(traceback.format_exc(), 'a')
            if self.retry_conn():
                return self.query(sql_str)
            return []

    # 执行操作语句，返回受影响记录数
    def exe(self, sql_str):
        try:
            self.cursor.execute(sql_str)
            self.conn.commit()
            return self.cursor.rowcount
        except pymysql.err.OperationalError:
            if self.retry_conn():
                self.conn.rollback()
                return self.exe(sql_str)
            return -1
        except Exception as e:
            sys_log.write_log(sql_str, 'a')
            sys_log.write_log(traceback.format_exc(), 'a')
            if self.retry_conn():
                self.conn.rollback()
                return self.exe(sql_str)
            return -1

    # 执行批量操作语句，返回受影响记录数
    def exe_many(self, sql_str, values):
        try:
            self.cursor.executemany(sql_str, values)
            self.conn.commit()
            return self.cursor.rowcount
        except Exception as e:
            self.conn.rollback()
            sys_log.write_log(sql_str, 'a')
            sys_log.write_log(traceback.format_exc(), 'a')
            if self.retry_conn():
                return self.exe_many(sql_str, values)
            return -1

    # 执行存储过程，返回数据表，或返回output参数 @_PRO_NAME_n
    def pro(self, pro_name, param):
        try:
            self.cursor.callproc(pro_name, param)
            return self.cursor.fetchall()
        except Exception as e:
            sys_log.write_log(param, 'a')
            sys_log.write_log(traceback.format_exc(), 'a')
            if self.retry_conn():
                return self.pro(pro_name, param)
            return []

    # 从txt文件中导入数据到数据表
    # 返回格式 [文件行数, 入库前记录数, 入库后记录数, 记录数变化]
    def txt_to_tab(self, file_path, table_name):
        try:
            # 入库前记录数
            sql_str = "select count(1) from {0:s}".format(table_name)
            pre_cnt = self.query(sql_str)[0][0]
            # 读取文本文件并将数据加载到DataFrame中
            df = pd.read_csv(file_path, header=None)
            # 文件行数
            file_cnt = len(df)
            # 将数据逐行插入到数据库表中
            for row in df.itertuples(index=False):
                values = ','.join(str(value) if isinstance(value, (int, float)) else f"'{str(value)}'" for value in row)
                self.cursor.execute(f"INSERT INTO {table_name} VALUES ({values})")
            # 入库后记录数
            aft_cnt = self.query(sql_str)[0][0]
            # 返回操作记录
            return file_cnt, pre_cnt, aft_cnt, aft_cnt-pre_cnt
        except Exception as e:
            if self.retry_conn():
                return self.import_to_db(file_path, table_name)
            return -1, -1, -1, -1

    # 当m秒内第一次异常，尝试n秒后重连数据库
    def retry_conn(self, m=60):
        try:
            if time.time() - self.retry_time > m:
                sys_log.write_log("尝试连接数据库...", 'a')
                self.retry_time = time.time()
                self.open()
                return True
            else:
                return False
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False


# 配置文件操作类
class ConfFile:
    def __init__(self, file_dir):
        self.file_dir = file_dir
        # 若配置文件不存在，则创建新
        pathlib.Path(self.file_dir).touch()
        self.config = MyConfigParser()
        self.config.read(self.file_dir)

    # 写配置文件
    def write(self, section, option, value):
        try:
            self.config.set(section, str(option), str(value))
            self.config.write(open(self.file_dir, 'w'))
        except Exception as e:
            if re.match('^No section:.*', str(e)) is not None:
                self.config.add_section(section)
                self.write(section, option, value)
                return
            sys_log.write_log(traceback.format_exc(), 'a')

    # 读配置文件
    def read(self, section, option, r_type='string'):
        try:
            if r_type == 'string':
                return self.config.get(section, str(option))
            elif r_type == 'int':
                return self.config.getint(section, str(option))
            elif r_type == 'float':
                return self.config.getfloat(section, str(option))
            elif r_type == 'boolean':
                return self.config.getboolean(section, str(option))
            else:
                return
        except Exception as e:
            if re.match('^No option .*? in section.*', str(e)) is not None:
                return
            sys_log.write_log(traceback.format_exc(), 'a')
            return

    # 检查option是否存在
    def has_opt(self, section, option):
        try:
            return str(option) in self.config.options(section)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 删除option操作
    def del_opt(self, section, option):
        try:
            self.config.remove_option(section, option)
            self.config.write(open(self.file_dir, 'w'))
            return True
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False


# 文件操作类
class FileOpt:
    def __init__(self, file_dir):
        self.file_dir = file_dir

    # 从文件中读出匹配上的记录
    def read(self, pat):
        try:
            file = open(self.file_dir, 'r')
            content = file.read()
            rst_list = re.compile(pat, re.M).findall(content)
            file.close()
            return rst_list
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
            return ''

    # 把内容写入文件
    def write(self, content, model):
        try:
            file = open(self.file_dir, model)
            file.write(content + '\n')
            file.flush()
            file.close()
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 更新文件内容
    def replace(self, old_str, new_str):
        try:
            file_bak = '{0:s}.bak'.format(self.file_dir)
            with open(self.file_dir, "r") as rf, open(file_bak, "w") as wf:
                for line in rf:
                    wf.write(line.replace(old_str, new_str))
            os.remove(self.file_dir)
            os.rename(file_bak, self.file_dir)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 为日志打上时间标签
    def write_log(self, content, model):
        try:
            tm = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
            self.write(tm + ' ' + content, model)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 保存最新的keep_num条记录
    def clean_log(self, keep_num):
        try:
            read_file = open(self.file_dir, 'r')
            contents = read_file.readlines()
            if len(contents) > keep_num:
                str_line = len(contents) - keep_num
                end_line = len(contents)
                bak_file = self.file_dir + 'bak'
                write_file = open(bak_file, 'a')
                for i in range(str_line, end_line):
                    write_file.write(contents[i])
                read_file.close()
                write_file.close()
                shutil.move(bak_file, self.file_dir)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


# 定义公共全局变量
DefaultStr = 'DefaultStr'
DefaultInt = 0
DefaultFloat = -0.00
DefaultBool = False
db_conf = ConfFile('./config/db_info.ini')
tg_connector = MySQLConn('tgdb')
tg_connector.open()
read_connector = MySQLConn('readdb')
read_connector.open()
sys_conf = ConfFile('./config/sys_config.ini')
# 公共参数池
pub_params = {'past_media_group_id': 1
              }

# 定义全局变量
main_file = sys.argv[0]
if main_file == 'main_other.py':  # 其中
    client_list = ['maymay2004', 'Aki090214']
    sys_log = FileOpt('./log/log_other.txt')
elif main_file == 'main_adv.py':  # 推广自动化
    client_list = ['autorunbot']
    myclients = {}
    sys_log = FileOpt('./log/log_adv.txt')
elif main_file == 'main_zoo.py':  # 动物园
    # client_list = ['myhappygirlsbot', 'happyzonebot', 'happyzoneadvbot',
    #                'daqiang0638', 'happy_167', 'happyzone_xl', 'happyzone_jt'
    #                'ad_zoo01', 'ad_zoo02', 'lashou01', 'lashou02', 'lashou03', 'lashou04'
    #                ]
    client_list = ['happyzoneadvbot']
    poll_list = ['40秒', '2分钟频率', '5分钟频率1', '5分钟频率2', '10分钟频率1', '10分钟频率2', '10分钟频率3', '20分钟频率1',
                 '20分钟频率2', '20分钟频率3', '20分钟频率4', '40分钟频率1', '40分钟频率2', '40分钟频率3', '40分钟频率4',
                 '40分钟频率5', '60分钟频率1', '60分钟频率2', '60分钟频率3', '60分钟频率4', '60分钟频率5',
                 '2小时频率1', '4小时频率1']
    sys_log = FileOpt('./log/log_zoo.txt')
elif main_file == 'main_zoo_in.py':  # 内部
    client_list = ['myhappygirlsbot', 'happy_167', 'myhappyzonebot']
    sys_log = FileOpt('./log/log_zoo_in.txt')
elif main_file == 'main_zoo_out.py':  # 外部
    client_list = ['happyzonebot']
    sys_log = FileOpt('./log/log_zoo_out.txt')
elif main_file == 'main_zoo_video.py':  # 视频
    client_list = ['daqiang2014', 'happyzoneadvbot']
    sys_log = FileOpt('./log/log_zoo_video.txt')
elif main_file == 'test_func.py':  # test 测试功能
    sys_log = FileOpt('./log/log_func.txt')
elif main_file == 'main_login.py':  # 注册
    client_list = []
    sys_log = FileOpt('./log/log_login.txt')
elif main_file == 'main_reg.py':  # 注册
    client_list = []
    sys_log = FileOpt('./log/log_reg.txt')
elif main_file == 'main_tomato.py':  # Tomato的income项目
    client_list = ['yusheng1999', 'Nowmimi_Bot']
    sys_log = FileOpt('./log/log_tomato.txt')
elif main_file == 'main_skc.py':  # skc社区项目
    client_list = ['skckefu', 'skcmanbot']
    sys_log = FileOpt('./log/log_skc.txt')
else:
    client_list = []
    sys_log = FileOpt('./log/log.txt')

clients = {}
chats_name = {}
chats_id = {}
chat_user = {}

if __name__ == "__main__":
    plus = sys_conf.read('plugins', 'Billy')
    if plus is None:
        print("None")
    else:
        print(plus.split('|')[1])

