# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
import asyncio
from pyrogram import Client
from classes import *

sys_log.write_log('注册客户端 开始...', 'a')
api_id = 22146217
api_hash = "244a2ce3944e2ad74b5d4f6704c5893f"
username='xiaoce520'
sys_conf = ConfFile('./config/sys_config.ini')
plus_read = sys_conf.read('plugins', username)
plus = plus_read.split('|')
plugins = dict(root="plugins", include=plus[0].split(','))
client = Client("sessions/"+username,
                api_id=api_id,
                api_hash=api_hash)
sys_log.write_log('注册客户端 结束。。。', 'a')

async def main():
    try:
        sys_log.write_log('The Test_func is begining...', 'a')
        await client.start()
        await client.send_message("me", "Hi!")
        me = await client.get_me()
        print(me)
        await client.stop()
        sys_log.write_log('The Test_func is end。。。', 'a')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')



if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())
