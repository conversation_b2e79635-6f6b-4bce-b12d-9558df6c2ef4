# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from init import *
# from main import *
from pyrogram import filters
from pyrogram.errors import ChatAdminRequired
from pyrogram.errors import FloodWait
from pyrogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton, User, Chat
from pyrogram.enums import ChatMembersFilter, ChatType, UserStatus, ChatMemberStatus, ParseMode, MessageMediaType
from pyrogram import raw
import asyncio
import json
import traceback
import datetime
from watermarker.marker import add_mark
from moviepy.editor import *
from moviepy.video.VideoClip import TextClip
from googletrans import Translator


# 取 chats_id 函数
def f_chats_id(chat_id):
    try:
        if chat_id not in chats_id.keys():
            sql_str = "select id,type,name,outname,title,username,first_name,last_name,status,online_date from chats " \
                      "where id in ({0:d})".format(chat_id)
            rst = tg_connector.query(sql_str)
            if len(rst) != 0:
                chats_name.update({rst[0][2]: rst[0]})
                chats_id.update({rst[0][0]: rst[0]})
        return chats_id.get(chat_id, [])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 取 chats_name 函数
def f_chats_name(chat_name):
    try:
        if chat_name not in chats_name.keys():
            sql_str = "select id,type,name,outname,title,username,first_name,last_name,status,online_date from chats " \
                      "where name in ('{0:s}')".format(chat_name)
            rst = tg_connector.query(sql_str)
            if len(rst) != 0:
                chats_name.update({rst[0][2]: rst[0]})
                chats_id.update({rst[0][0]: rst[0]})
        return chats_name.get(chat_name, [])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 取client所对应名字
def f_client_username(client):
    try:
        for clt in client_list:
            if clients[clt] == client:
                return clt
        return ''
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 取client所对应id
def f_client_id(client):
    try:
        for clt in client_list:
            if clients[clt] == client:
                str_sql = "select id from clients where username='{0:s}'".format(clt)
                rst = tg_connector.query(str_sql)
                return rst[0][0]
        return 0
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 批量模糊匹配
def fuzzy_finder(patterns=[], contents=[], flags=re.S):
    collects = []
    for pattern in patterns:
        for content in contents:
            match = re.match(pattern, content, flags)
            if match is not None:
                collects.append([pattern, content, match.groups])
    return collects


# 获取可用代理
def get_proxy():
    try:
        str_sql2 = "update proxys set status='{1:s}',used_at=now() where addr='{0:s}'"
        while 1:
            str_sql = "select addr,port,name,pwd from proxys where status<>'2' order by status,used_at limit 1"
            rst = tg_connector.query(str_sql)
            if len(rst) == 1:
                row = rst[0]
                if test_ip(row[0], int(row[1])):
                    str_sql = str_sql2.format(row[0], '1')
                    tg_connector.exe(str_sql)
                    return row
                else:
                    str_sql = str_sql2.format(row[0], '2')
                    tg_connector.exe(str_sql)
            else:
                return ()
    except:
        sys_log.write_log(traceback.format_exc(), 'a')


# 取随机数 order_by['updated_at','cnt','rand()']
def get_random(no, order_by='updated_at'):
    try:
        str_sql = "select * from randoms where id='{0:s}' order by {1:s} limit 1".format(no, order_by)
        rst = tg_connector.query(str_sql)
        str_sql = "update randoms set updated_at=CURRENT_TIMESTAMP(), cnt=cnt+1 where id='{0[0]:s}' and " \
                  "value='{0[1]:s}'".format(rst[0])
        tg_connector.exe(str_sql)
        if len(rst) > 0:
            return rst[0]
        else:
            return ()
    except:
        sys_log.write_log(traceback.format_exc(), 'a')


# 在屏幕和日志中都显示
def print_both(text: str):
    try:
        print(text)
        sys_log.write_log(text, 'a')
    except:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取客户链接
def link(param):
    if isinstance(param, int):
        my_chat = f_chats_id(param)
    else:
        my_chat = f_chats_name(param)
    if my_chat[5] != '':
        return 't.me/' + my_chat[5]
    else:
        return "tg://openmessage?user_id={0:d}".format(my_chat[0])


# user_list验证
def user_cert(user_list, user_id):
    try:
        if user_list.count('|') == 0:
            if user_list.upper() == 'ALL':
                return True
            else:
                for name in user_list.split(','):
                    if user_id == f_chats_name(name)[0]:
                        return True
        elif user_list.count('|') == 1:
            for name in user_list.split('|')[1].split(','):
                if user_id == f_chats_name(name)[0]:
                    return False
            return True
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 代理数据 客户数据 特殊处理
def comm_spec_deal(command):
    if command[0][0:4] in ('客户数据', '代理数据') and len(command[0]) > 4:
        new_command = [command[0][0:4], command[0][4:]]
        new_command.extend(command[1:])
        return new_command
    else:
        return command


# 字段转义
def explain(type, text='', point='to'):
    try:
        to_dic = {}
        # 设定转义内容
        if type == 'UserStatus':
            to_dic = {'1': UserStatus.ONLINE, '2': UserStatus.OFFLINE, '3': UserStatus.RECENTLY,
                      '4': UserStatus.LAST_WEEK, '5': UserStatus.LAST_MONTH, '6': UserStatus.LONG_AGO}
        elif type == 'ChatType':
            to_dic = {'user': ChatType.PRIVATE, 'bot': ChatType.BOT, 'group': ChatType.GROUP,
                      'supergroup': ChatType.SUPERGROUP, 'channel': ChatType.CHANNEL}
        elif type == 'ChatMemberStatus':
            to_dic = {'0': ChatMemberStatus.OWNER, '1': ChatMemberStatus.ADMINISTRATOR, '2': ChatMemberStatus.MEMBER,
                      '3': ChatMemberStatus.LEFT, '4': ChatMemberStatus.RESTRICTED, '5': ChatMemberStatus.BANNED}
        elif type == 'bool':
            to_dic = {'0': False, '1': True}
        elif type == 'MyclientStatus':
            to_dic = {'00': "待注册", '01': "待初始化", '02': "待养号", '10': "正常", '20': "限制", '30': "禁用", '40': "销号"}
        elif type == 'JIAMI':
            to_dic = {'0': 'O', '1': 'I', '2': 'Z', '3': 'E', '4': 'X', '5': 'S', '6': 'G', '7': 'J', '8': 'B', '9': 'P'}
        # 确定转义方向
        if point == 'to':
            return to_dic.get(text, None)
        elif point == 'from':
            from_dict = {v: k for k, v in to_dic.items()}
            return from_dict.get(text, None)
        else:
            return None
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# user_id 转 std_id
def user_to_std(user_id):
    try:
        user_id = str(user_id)
        str_sql = "select count(1),ifnull(max(case when user_id={0:s} then std_id else '' end),'') from user_std " \
                  "where short_id=right({0:s},4)".format(user_id)
        rst = tg_connector.query(str_sql)
        if rst[0][1] != '':
            std_id = rst[0][1]
        else:
            encode = {0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F', 6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K', 11: 'L',
                      12: 'M', 13: 'N', 14: 'O', 15: 'P', 16: 'Q', 17: 'R', 18: 'S', 19: 'T', 20: 'U', 21: 'V', 22: 'W',
                      23: 'X', 24: 'Y', 25: 'Z'}
            std_id = user_id[-4:] + encode[rst[0][0]]
            str_sql = "insert into user_std(user_id,short_id,std_id) values({0:s},right({0:s},4),'{1:s}')" \
                .format(user_id, std_id)
            tg_connector.exe(str_sql)
        return std_id
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# std_id 转 user_id
def std_to_user(std_id):
    try:
        str_sql = "select user_id from user_std where std_id='{0:s}'".format(std_id)
        rst = tg_connector.query(str_sql)
        if len(rst) == 0:
            return 0
        else:
            return rst[0][0]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 检查user_std中的两个id是否存在
def user_std_exist(id):
    try:
        if re.match('^\d{6,}$', str(id), re.S) is None:
            str_sql = "select count(1) from user_std where std_id='{0:s}'".format(str(id))
        else:
            str_sql = "select count(1) from user_std where user_id={0:s}".format(str(id))
        rst = tg_connector.query(str_sql)
        return rst[0][0] == 1
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 权限验证
def flt_command_detail(flt, _, query):
    try:
        if type(query) == Message:
            name = comm_spec_deal(query.matches[0].group(1).split(" "))[0]
            str_id = "','".join(flt.id_list)
            tpye = query.chat.type
            str_sql = "select chat_name,user_list from talks where id in ('{0:s}') and name = '{1:s}' and " \
                      "type = '{2:s}' and status = '1' limit 1".format(str_id, name, tpye)
            rst = tg_connector.query(str_sql)
            if len(rst) == 0:
                return False
            # chat_name验证
            user_id = query.from_user.id
            chat_id = query.chat.id
            if rst[0][0].count('|') == 0:
                if rst[0][0].upper() == 'ALL':
                    return user_cert(rst[0][1], user_id)
                else:
                    for chat_name in rst[0][0].split(','):
                        if chat_id == f_chats_name(chat_name)[0]:
                            return user_cert(rst[0][1], user_id)
            elif rst[0][0].count('|') == 1:
                for chat_name in rst[0][0].split('|')[1].split(','):
                    if chat_id == f_chats_name(chat_name)[0]:
                        return False
                return user_cert(rst[0][1], user_id)
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立权限验证筛选器
def flt_command(id_list):
    try:
        return filters.create(flt_command_detail, id_list=id_list)
    except Exception as e:
        sys_log.write_log('{0:s}'.format(traceback.format_exc()), 'a')
        return False


# 文本验证
def flt_text_detail(flt, query):
    try:
        if type(query) == Message and query.text is not None:
            if query.text in flt.text_list:
                return True
        else:
            return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立文本验证筛选器
def flt_text(text_list):
    try:
        return filters.create(flt_text_detail, text_list=text_list)
    except Exception as e:
        sys_log.write_log('{0:s}'.format(traceback.format_exc()), 'a')
        return False


# 回调查询权限验证
def flt_callback_detail(flt, _, query):
    try:
        if type(query) == CallbackQuery:
            str_id = "','".join(flt.id_list)
            tpye = query.message.chat.type
            name = [flt.data, query.data][flt.data == '']
            str_sql = "select chat_name,user_list from talks where id in ('{0:s}') and name = '{1:s}' and " \
                      "type = '{2:s}' and status = '1' limit 1".format(str_id, name, tpye)
            rst = tg_connector.query(str_sql)
            if len(rst) == 0:
                return False
            # chat_name验证
            user_id = query.from_user.id
            chat_id = query.message.chat.id
            if rst[0][0].count('|') == 0:
                if rst[0][0].upper() == 'ALL':
                    return user_cert(rst[0][1], user_id)
                else:
                    for chat_name in rst[0][0].split(','):
                        if chat_id == f_chats_name(chat_name)[0]:
                            return user_cert(rst[0][1], user_id)
            elif rst[0][0].count('|') == 1:
                for chat_name in rst[0][0].split('|')[1].split(','):
                    if chat_id == f_chats_name(chat_name)[0]:
                        return False
                return user_cert(rst[0][1], user_id)
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立回调查询权限验证筛选器
def flt_callback(id_list, data=''):
    try:
        return filters.create(flt_callback_detail, id_list=id_list, data=data)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False

# 建立Callback私发筛选器
def pv_callback(_, __, callback):
    return callback.message.chat.type == ChatType.PRIVATE

privateCallbackFilter = filters.create(pv_callback)


# 剔除特殊字符
def correct_char(sour_str):
    try:
        return ''.join(re.findall('[\u4e00-\u9fa5a-zA-Z0-9 \（\）\_\(\)\&\%\#\@\!\$\^\【\】\.\:\~\-\/\,\，\。\！]+', sour_str, re.S))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return 'Error'


# 提取inputPeer
# id: 可以是user_id或者username
# client： 通过client来解析，每个client解析不一样
# join_group： 目标群的username，如果不赋值，取不到就不取，
#              如果赋值auto，使用user_id取不到时，client加入库表中的目标群，重新解析
#              如果赋某个特点值，使用user_id取不到时，client加入特定的目标群，重新解析
async def get_input_peer(id: Union[int, str], client=None, join_group=''):
    try:
        try:
            if re.match('^-?\d+$', id, re.S) is not None:
                id = int(id)
            # 直接解析
            peer = await client.resolve_peer(peer_id=id)
            return True, peer
        except Exception as e:
            err = "直接解释错误： {0:s}".format(str(id))
            print(err)
            if isinstance(id, int) and f_chats_id(id)[5] != '':
                try:
                    # 通过id取username后解析
                    username = f_chats_id(id)[5]
                    peer = await client.resolve_peer(peer_id=username)
                    return True, peer
                except Exception as e:
                    err = "通过id取username后，解析错误: {0:s}-{1:s}".format(str(id), username)
                    print(err)
        # 如果赋值auto，使用user_id取不到时，client加入库表中的目标群，重新解析
        if join_group == 'auto' and isinstance(id, int):
            sql_str = "select b.username,a.chat_id,a.chat_name from (select * from chat_user where user_id={0:d}) a " \
                      "join chats b on a.chat_id=b.id where b.username<>'' order by a.joined_at desc limit 1".format(id)
            rst = tg_connector.query(sql_str)
            if len(rst) == 1:
                try:
                    await client.join_chat(rst[0][0])
                    try:
                        await asyncio.sleep(3)
                        peer = await client.resolve_peer(peer_id=id)
                        return True, peer
                    except Exception as e:
                        err = "加入库表中的群后，解析错误: {0:s}-({1[0]:s},{1[1]:d},{1[2]:s})".format(str(id), rst[0])
                        print(err)
                except Exception as e:
                    err = "加入库表中的群失败: {0:s}-({1[0]:s},{1[1]:d},{1[2]:s})".format(str(id), rst[0])
                    print(err)
        elif join_group != '':
            try:
                await client.join_chat(join_group)
                try:
                    await asyncio.sleep(3)
                    peer = await client.resolve_peer(peer_id=id)
                    return True, peer
                except Exception as e:
                    err = "加入目标群后，解析错误: {0:s}-{1:s}".format(str(id), join_group)
                    print(err)
            except Exception as e:
                err = "加入目标群失败: {0:s}-{1:s}".format(str(id), join_group)
                print(err)
        return False, err
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新单个会话
async def update_chat(client, current_chat):
    try:
        # 缓存中会话超过一定数量，则清空缓存
        if len(chats_name) > 5000:
            sys_log.write_log('重新初始化chats', 'a')
            init_chats()
        v_id = current_chat.id
        # 收集User类型的信息
        if type(current_chat) == User:
            v_title = ''
            # v_type
            if current_chat.is_bot is not None and current_chat.is_bot:
                v_type = "bot"
            else:
                v_type = "user"
            # v_status
            if current_chat.status is not None:
                v_status = explain('UserStatus', current_chat.status, 'from')
            else:
                v_status = '0'
            if current_chat.is_deleted is not None and current_chat.is_deleted:
                v_status = '7'
            # v_online_date
            if current_chat.last_online_date is not None:
                v_online_date = current_chat.last_online_date
            elif current_chat.next_offline_date is not None:
                v_online_date = current_chat.next_offline_date
            else:
                v_online_date = datetime.datetime.now()
        # 收集Chat类型的信息
        else:
            v_title = correct_char('' if current_chat.title is None else current_chat.title)
            v_type = explain('ChatType', current_chat.type, 'from')
            v_status = '0'
            v_online_date = datetime.datetime.strptime('2000-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
        # 收集共有的变量
        v_username = correct_char('' if current_chat.username is None else current_chat.username)
        v_first_name = correct_char('' if current_chat.first_name is None else current_chat.first_name)
        v_last_name = correct_char('' if current_chat.last_name is None else current_chat.last_name)
        v_outname = v_title + v_first_name + v_last_name
        # v_status
        if current_chat.is_restricted is not None and current_chat.is_restricted:
            v_status = '8'
        elif current_chat.is_scam is not None and current_chat.is_scam:
            v_status = '9'
        # 检查内存中id是否已存在
        if v_id in chats_id.keys():
            # 同步有更新的信息
            if (v_type, v_outname, v_title, v_username, v_first_name, v_last_name, v_status, v_online_date.strftime("%Y-%m-%d")) != \
                    (chats_id[v_id][1], chats_id[v_id][3], chats_id[v_id][4], chats_id[v_id][5], chats_id[v_id][6],
                     chats_id[v_id][7], chats_id[v_id][8], chats_id[v_id][9].strftime("%Y-%m-%d")):
                # chat不更新user信息
                if type(current_chat) == Chat and chats_id[v_id][1] == 'user':
                    return
                sql_str = "update chats set type='{1:s}', outname='{2:s}', title='{3:s}', username='{4:s}', " \
                          "first_name='{5:s}', last_name='{6:s}', status=if('{7:s}'<>'0','{7:s}',status), " \
                          "online_date=if(online_date<'{8:%Y-%m-%d %X}', '{8:%Y-%m-%d %X}', online_date) where id = {0:d}" \
                    .format(v_id, v_type, v_outname, v_title, v_username, v_first_name, v_last_name, v_status, v_online_date)
                tg_connector.exe(sql_str)
                init_chats([v_id], False)
        else:
            sql_str = "select count(1) from chats where id = {0:d}".format(v_id)
            rst = tg_connector.query(sql_str)
            # 更新未入库的信息
            if rst[0][0] == 0:
                sql_str = "select count(1) from chats where name = '{0:s}'".format(v_outname)
                rst2 = tg_connector.query(sql_str)
                v_name = v_outname if rst2[0][0] == 0 else v_outname + '_' + str(v_id)
                sql_str = "insert into chats(id,type,name,outname,title,username,first_name,last_name,status," \
                          "online_date) values({0:d},'{1:s}','{2:s}','{3:s}','{4:s}','{5:s}','{6:s}','{7:s}','{8:s}'," \
                          "'{9:%Y-%m-%d %X}') on duplicate key update type='{1:s}',outname='{3:s}',title='{4:s}'," \
                          "username='{5:s}',first_name='{6:s}',last_name='{7:s}',status=if('{8:s}'<>'0','{8:s}'," \
                          "status),online_date=if(online_date<'{9:%Y-%m-%d %X}', '{9:%Y-%m-%d %X}', online_date)"\
                    .format(v_id, v_type, v_name, v_outname, v_title, v_username, v_first_name, v_last_name, v_status, v_online_date)
                tg_connector.exe(sql_str)
            else:
                # 更新已入库的信息
                sql_str = "update chats set type='{1:s}', outname='{2:s}', title='{3:s}', username='{4:s}', " \
                          "first_name='{5:s}', last_name='{6:s}', status=if('{7:s}'<>'0','{7:s}',status), " \
                          "online_date=if(online_date<'{8:%Y-%m-%d %X}', '{8:%Y-%m-%d %X}', online_date) where id = {0:d}" \
                    .format(v_id, v_type, v_outname, v_title, v_username, v_first_name, v_last_name, v_status, v_online_date)
                tg_connector.exe(sql_str)
            init_chats([v_id], False)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新消息中的会话
async def update_chats(client, message):
    try:
        # 为了更新user的状态，User类型需要优先获取
        if message.from_user is not None:
            await update_chat(client, message.from_user)
        if message.forward_from is not None:
            await update_chat(client, message.forward_from)
        if message.new_chat_members is not None:
            for new_chat_member in message.new_chat_members:
                await update_chat(client, new_chat_member)
        if message.left_chat_member is not None:
            await update_chat(client, message.left_chat_member)
        if message.sender_chat is not None:
            await update_chat(client, message.sender_chat)
        if message.chat is not None:
            await update_chat(client, message.chat)
        if message.forward_from_chat is not None:
            await update_chat(client, message.forward_from_chat)
        if message.via_bot is not None:
            await update_chat(client, message.via_bot)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新callback中的会话
async def update_chats_callback(client, callback):
    try:
        # 为了更新user的状态，User类型需要优先获取
        if callback.from_user is not None:
            await update_chat(client, callback.from_user)
        if callback.message is not None:
            await update_chats(client, callback.message)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新记录 chat_user
# status: 0：creator；1：administrator；2：member；3：left；4：restricted；5：banned；8：unKnow；9：noRecord
# is_active： 只用于跟踪活跃用户聊天
def f_chat_user(chat_id, user_id, status, is_active=False):
    try:
        if len(chat_user) > 2000:
            sys_log.write_log('重新初始化chat_user', 'a')
            chat_user.clear()
        if is_active:
            sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,active_at,updated_at) " \
                      "values({0:d},'{1:s}',{2:d},'{3:s}','{4:s}',now(),now()) on duplicate key update " \
                      "chat_name='{1:s}',user_name='{3:s}'," \
                      "status=case when status>'1' then '{4:s}' else status end,active_at=now(),updated_at=now()" \
                .format(chat_id, f_chats_id(chat_id)[2], user_id, f_chats_id(user_id)[2], status)
        else:
            sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,updated_at) " \
                      "values({0:d},'{1:s}',{2:d},'{3:s}','{4:s}',now()) on duplicate key update " \
                      "chat_name='{1:s}',user_name='{3:s}'," \
                      "status=case when status>'1' then '{4:s}' else status end,updated_at=now()" \
                .format(chat_id, f_chats_id(chat_id)[2], user_id, f_chats_id(user_id)[2], status)
        tg_connector.exe(sql_str)
        sql_str = "select chat_id,user_id,status,updated_at from chat_user where chat_id={0:d} and user_id={1:d}"\
            .format(chat_id, user_id)
        rst = tg_connector.query(sql_str)
        key = str(rst[0][0]) + '|' + str(rst[0][1])
        chat_user.update({key: rst[0]})
        return rst[0]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新记录 chat_user_info
def f_chat_user_info(chat_id, real_all_cnt=0, all_cnt=0):
    try:
        sql_str = "select count(1) from chat_user where chat_id={0:d} and status not in ('3','5','8','9')".format(chat_id)
        rst = tg_connector.query(sql_str)
        if real_all_cnt == 0 and all_cnt == 0:
            sql_str = "insert into chat_user_info(chat_id,chat_name,record_cnt,real_all_cnt,all_cnt,updated_at) " \
                      "values({0:d},'{1:s}',{2:d},{2:d},{2:d},now()) on duplicate key update chat_name='{1:s}'," \
                      "record_cnt={2:d},real_all_cnt=real_all_cnt+{2:d}-record_cnt,all_cnt=all_cnt+{2:d}-record_cnt" \
                .format(chat_id, f_chats_id(chat_id)[2], rst[0][0])
        else:
            sql_str = "insert into chat_user_info(chat_id,chat_name,record_cnt,real_all_cnt,all_cnt,updated_at) " \
                      "values({0:d},'{1:s}',{2:d},{3:d},{4:d},now()) on duplicate key update chat_name='{1:s}'," \
                      "record_cnt={2:d},real_all_cnt={3:d},all_cnt={4:d},updated_at=now()" \
                .format(chat_id, f_chats_id(chat_id)[2], rst[0][0], real_all_cnt, all_cnt)
        tg_connector.exe(sql_str)
        return rst[0][0]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新chat_user的活跃成员 更新频率为6小时
# status: 0：creator；1：administrator；2：member；3：left；4：restricted；5：banned；8：unKnow；9：noRecord
def active_chat_user(chat_id, user_id):
    try:
        key = str(chat_id) + '|' + str(user_id)
        cache = chat_user.get(key, [0, 0, '8', datetime.datetime.strptime('2000-01-01 00:00:00','%Y-%m-%d %H:%M:%S')])
        record_time = cache[3].timestamp()
        if time.time() - record_time > 21600 or cache[2] > '2':
            f_chat_user(chat_id, user_id, '2', True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 完善chat_user
async def perfect_chat_user(client_name, chat_id, no_force=True):
    try:
        chat_name = f_chats_id(chat_id)[2]
        sys_log.write_log("检查是否需要更新群: {0:s}->{1:s}".format(client_name, chat_name), 'a')
        # 对于非管理员的Channel，不更新
        if f_chats_id(chat_id)[1] == "channel":
            me = await clients[client_name].get_chat_member(chat_id, "me")
            await asyncio.sleep(3)
            if me.status not in (ChatMemberStatus.OWNER, ChatMemberStatus.ADMINISTRATOR):
                sys_log.write_log("非管理员的频道不更新: {0:s}->{1:s}".format(client_name, chat_name), 'a')
                return
        # 判断是否需要更新
        sql_str = "select all_cnt,(case when record_cnt<real_all_cnt then 1 else 0 end), (case when time_to_sec" \
                  "(timediff(now(), updated_at))>(all_cnt/3000+2)*86400 then 1 else 0 end) from chat_user_info " \
                  "where chat_id = {0:d}".format(chat_id)
        rst = tg_connector.query(sql_str)
        if len(rst) != 0 and rst[0][2] == 0 and no_force:
            sys_log.write_log("更新周期内不更新: {0:s}->{1:s}".format(client_name, chat_name), 'a')
            return
        all_cnt = await clients[client_name].get_chat_members_count(chat_id)
        await asyncio.sleep(3)
        if all_cnt == 0:
            sys_log.write_log("目标群无成员不更新: {0:s}->{1:s}".format(client_name, chat_name), 'a')
            return
        if len(rst) != 0 and rst[0][0] == all_cnt and rst[0][1] == 0 and no_force:
            sys_log.write_log("检查成员数正确不更新: {0:s}->{1:s} 总人数：{2:d}"
                              .format(client_name, chat_name, all_cnt), 'a')
            return
        sys_log.write_log("开始更新群: {0:s}->{1:s} 官方总人数：{2:d}"
                          .format(client_name, chat_name, all_cnt), 'a')
        # 提取已记录成员信息
        sql_str = "select concat(user_id,'|',status) from chat_user where chat_id={0:d} and status < '9'".format(chat_id)
        rst = tg_connector.query(sql_str)
        record_user_status = set(chain.from_iterable(rst))
        new_user_status = set()
        real_all_cnt = 0
        if len(record_user_status) > 9999 or (f_chats_id(chat_id)[1] == 'channel' and len(record_user_status) > 199):
            # 已经超限的群，只取最近在线的成员
            async for member in clients[client_name].get_chat_members(chat_id, filter=ChatMembersFilter.RECENT):
                if member.user is not None:
                    user_id = member.user.id
                else:
                    continue
                real_all_cnt += 1
                status = explain('ChatMemberStatus', member.status, 'from')
                new_user_status.add(str(user_id) + '|' + status)
                if member.joined_date is not None:
                    joined_at = member.joined_date
                else:
                    joined_at = datetime.datetime.strptime('2000-01-01 00:00:00','%Y-%m-%d %H:%M:%S')
                # 检查群成员是否需要更新
                if str(user_id) + '|' + status not in record_user_status:
                    await update_chat(clients[client_name], member.user)
                    sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,joined_at,updated_at) " \
                              "values({0:d},'{1:s}',{2:d},'{3:s}','{4:s}','{5:%Y-%m-%d %X}',now()) on duplicate key update " \
                              "chat_name='{1:s}',user_name='{3:s}',status='{4:s}',joined_at='{5:%Y-%m-%d %X}',updated_at=now()" \
                        .format(chat_id, f_chats_id(chat_id)[2], user_id, f_chats_id(user_id)[2], status, joined_at)
                    tg_connector.exe(sql_str)
        else:
            # 未超限的群，取全部成员
            async for member in clients[client_name].get_chat_members(chat_id):
                if member.user is not None:
                    user_id = member.user.id
                else:
                    continue
                real_all_cnt += 1
                status = explain('ChatMemberStatus', member.status, 'from')
                new_user_status.add(str(user_id) + '|' + status)
                if member.joined_date is not None:
                    joined_at = member.joined_date
                else:
                    joined_at = datetime.datetime.strptime('2000-01-01 00:00:00','%Y-%m-%d %H:%M:%S')
                # 检查群成员是否需要更新
                if str(user_id) + '|' + status not in record_user_status:
                    await update_chat(clients[client_name], member.user)
                    sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,joined_at,updated_at) " \
                              "values({0:d},'{1:s}',{2:d},'{3:s}','{4:s}','{5:%Y-%m-%d %X}',now()) on duplicate key update " \
                              "chat_name='{1:s}',user_name='{3:s}',status='{4:s}',joined_at='{5:%Y-%m-%d %X}',updated_at=now()" \
                        .format(chat_id, f_chats_id(chat_id)[2], user_id, f_chats_id(user_id)[2], status, joined_at)
                    tg_connector.exe(sql_str)
        # 不在新记录中的成员 总人数1w以下，且非频道，则置为noRecord(9)，以上不变
        del_user_status = record_user_status - new_user_status
        if all_cnt < 10000 and f_chats_id(chat_id)[1] != 'channel' and len(del_user_status) > 0:
            del_record = []
            for record in del_user_status:
                del_record.append(record.split('|')[0])
            sql_str = "update chat_user set status='9' where chat_id={0:d} and user_id in ({1:s}) and status>'1'" \
                .format(chat_id, ",".join(del_record))
            tg_connector.exe(sql_str)
        # 更新chat_user_info信息
        record_cnt = f_chat_user_info(chat_id, len(new_user_status), all_cnt)
        sys_log.write_log("群信息更新完毕: {0:s}->{1:s} 数据表有效成员数：{4:d} 本轮轮询成员数：{3:d} 官方总人数：{2:d}"
                          .format(client_name, chat_name, all_cnt, len(new_user_status), record_cnt), 'a')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        sys_log.write_log(str(chat_id), 'a')


# 轮询各个client的群户信息
async def poll_client(ck_client=client_list):
    try:
        for client_name in ck_client:
            sys_log.write_log("开始更新用户 {0:s} 的所有群信息".format(client_name), 'a')
            async for dialog in clients[client_name].get_dialogs():
                if dialog.chat.type in (ChatType.GROUP, ChatType.SUPERGROUP, ChatType.CHANNEL):
                    await update_chat(clients[client_name], dialog.chat)
                    await perfect_chat_user(client_name, dialog.chat.id)
            sys_log.write_log("已更新用户 {0:s} 的所有群信息".format(client_name), 'a')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发监控信息
async def send_monit_msg(monit, check_ind, msg, parse_mode=ParseMode.HTML, disable_web_page_preview=None,
                       disable_notification=False, reply_to_message_id=None, schedule_date=None, reply_markup=None):
    try:
        sql_str = "select count(1) from monit_crol where ck_key='{0:s}'".format(check_ind)
        if tg_connector.query(sql_str)[0][0] == 0:
            # send_log.write_log("{0[0]:s}|{0[1]:s}:{0[2]:s}->{0[3]:s}\n{1:s}\n".format(monit, msg), 'a')
            for chat_name in monit[3].split(','):
                await clients[monit[2]].send_message(f_chats_name(chat_name)[0], msg,
                                                     parse_mode=parse_mode,
                                                     disable_web_page_preview=disable_web_page_preview,
                                                     disable_notification=disable_notification,
                                                     reply_to_message_id=reply_to_message_id,
                                                     schedule_date=schedule_date,
                                                     reply_markup=reply_markup)
            sql_str = "insert into monit_crol(ck_key,created_at) values('{0:s}', now())".format(check_ind)
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发会话信息
async def send_talk_msg(client, message, msg, parse_mode=ParseMode.HTML, disable_web_page_preview=None,
                       disable_notification=False, reply_to_message_id=None, schedule_date=None, reply_markup=None):
    try:
        if msg != '':
            user_id = message.from_user.id
            user_name = f_chats_id(user_id)[2]
            chat_id = message.chat.id
            chat_name = f_chats_id(chat_id)[2]
            # send_log.write_log("{0:s}~{1:s}:{2:s}\n{3:s}\n".format(user_name, chat_name, message.text, msg), 'a')
            return await client.send_message(message.chat.id, msg,
                                             parse_mode=parse_mode,
                                             disable_web_page_preview=disable_web_page_preview,
                                             disable_notification=disable_notification,
                                             reply_to_message_id=reply_to_message_id,
                                             schedule_date=schedule_date,
                                             reply_markup=reply_markup)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取 media_group 中的消息列表（含过滤后续message作用）
# ['1',  media_group列表] media_group的第一条消息，获取全部消息列表
# ['2',  原消息]           media_group的非第一条消息，返回原消息
# ['0',  原消息]           不是media_group消息，返回原消息
async def check_media_group(client, message):
    try:
        # 处理media_group的第一条记录
        if message.media_group_id is not None and message.media_group_id != pub_params['past_media_group_id']:
            pub_params['past_media_group_id'] = message.media_group_id
            rec_msg = await client.get_media_group(message.chat.id, message.id)
            return ['1', rec_msg]
        elif message.media_group_id is not None and message.media_group_id == pub_params['past_media_group_id']:
            return ['2', [message]]
        else:
            pub_params['past_media_group_id'] = 0
            return ['0', [message]]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给图片打水印
def add_pic_mark(in_file, out_dir, mark, color="#F11B11", size=18, opacity=0.4, angle=30, space=250):
    try:
        add_mark(file=in_file,
                 out=out_dir,
                 mark=mark,
                 color=color,
                 size=size,
                 opacity=opacity,
                 angle=angle,
                 space=space)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给视频打水印
def add_video_mark(in_file, out_file, mark, color="#FF7F50", fontsize=16, opacity=0.5, position=(0.382, 0.618)):
    try:
        video = VideoFileClip(in_file)
        logo = (TextClip(mark, color=color, fontsize=fontsize, font="/etc/fonts/truetype/msyh.ttc")
                .set_duration(video.duration)
                .margin(opacity=opacity)
                .set_position(position, True)
                )
        final = CompositeVideoClip([video, logo])
        final.write_videofile(out_file, fps=30, threads=1, codec="libx264")
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新通讯录
async def update_contact(username):
    try:
        # 提取该客户端所有的通讯录成员
        contact_list = await clients[username].get_contacts()
        # 先把目前所有成员设置为非好友
        sql_str = "update contacts set status='0',updated_at=now() where owner_username='{0:s}' and status='1'".format(username)
        tg_connector.exe(sql_str)
        # 逐个检查通讯录情况
        for user in contact_list:
            await update_chat(clients[username], user)
            sql_str = "insert into contacts(owner_username,id,name,outname,status,updated_at,created_at) " \
                      "values('{0:s}', {1[0]:d}, '{1[2]:s}', '{1[3]:s}','1',now(),now()) on duplicate key update " \
                      "name='{1[2]:s}', outname='{1[3]:s}', status='1', updated_at=now()"\
                .format(username, chats_id[user.id])
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 翻译工具
def trans(origin, dest='auto'):
    try:
        translator = Translator()
        if dest == 'auto':
            detected = translator.detect(origin)
            if detected.lang == 'zh-CN':
                dest = 'en'
            else:
                dest = 'zh-cn'
        return translator.translate(origin, dest=dest).text.replace("'", '')
    except Exception as e:
        return f"翻译错误：{e}"


if __name__ == "__main__":
    print(f_chats_id(-1001474111980))
    print(chats_id[-1001474111980])


