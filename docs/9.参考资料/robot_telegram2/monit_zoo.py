# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
# from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from func import *
from init import *
from plugins.zoo.showup import *
import re
import time
import datetime
import traceback
import os


main_adv = """
🌹🌹🌹  寂寞 👉 欢乐园  🌹🌹🌹
我们致力于打造全菲<b>最专业、最实惠、最高质量</b>的修车平台，这里有300+个美女任君挑选!
<b>下单联系</b>： @happy_167

选择我们的<b>理由</b>：
<b>全部贵妃</b>：贵妃一览无余 @happyzone168
<b>精选贵妃</b>：个人喜好筛选 @happyzonebot
<b>精彩视频</b>：海量精彩视频 @happyzone176
<b>快速通道</b>：可快速集中查看自己喜爱的贵妃类型
<b>空闲在线</b>：让您的期待永远不落空
"""


async def send_model(client, chat_name):
    try:
        clt_id = f_client_id(client)
        clt_name = f_chats_id(clt_id)[2]
        # 获取待发送信息
        sql_str = "select a.id,a.name,a.age,a.height,a.weight,a.boobs,a.baby,a.location,a.special,a.level,a.price_out," \
                  "a.picture,a.status,a.country,c.msg_list from zoo_info a join (select * from chat_message where " \
                  "chat_id={1:d} and kind='{2:s}') c on a.id=c.ind left join (select * from zoo_guest_rel where " \
                  "guest_name='{0:s}') b on a.id=b.girl_id where a.status in ('1','2') order by ifnull(b.updated_at," \
                  "'2016-08-17 00:00:00'),a.viewed,a.level desc,a.id limit 1".format(clt_name, cm[0].chat_id, cm[0].kind)
        rst = tg_connector.query(sql_str)
        content = await get_content_home(rst, True)
        for name in chat_name.split(','):
            # 转发主频道动物信息
            await client.copy_media_group(f_chats_name(name)[0],
                                          cm[0].chat_id,
                                          int(rst[0][14].split(',')[1]),
                                          captions=content,
                                          disable_notification=True)
            # 发送广告界面
            await client.send_message(f_chats_name(name)[0],
                                      adv(params['adv_count']),
                                      parse_mode=ParseMode.HTML,
                                      disable_web_page_preview=True,
                                      disable_notification=True,
                                      reply_markup=get_imp('group'))
            await asyncio.sleep(10)
        params['adv_count'] = (params['adv_count'] + 1) % params['adv_num']
        # 更新客户关系表
        sql_str = "insert into zoo_guest_rel(guest_id,guest_name,girl_id,girl_name,viewed,updated_at) " \
                  "values({1:d},'{2:s}',{0[0]:d},'{0[1]:s}',1,CURRENT_TIMESTAMP()) on duplicate key update " \
                  "girl_name='{0[1]:s}', viewed=viewed+1, updated_at=CURRENT_TIMESTAMP()" \
            .format(rst[0], f_chats_name(clt_name)[0], clt_name)
        tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


async def do_monit_zoo(monit):
    try:
        now_time = time.localtime(time.time())
        sys_log.write_log('{0[0]:s}|{0[1]:s}:{0[2]:s}->{0[3]:s}'.format(monit), 'a')
        # 测试监控
        if monit[0] == 'MTEST001':
            date_to_str = time.strftime('%Y-%m-%d %H:%M:%S', now_time)
            check_ind = date_to_str
            if check_ind != '':
                msg = '本次监控执行时间： {0:s}'.format(check_ind)
                await send_monit_msg(monit, check_ind, msg)
        # zoo群组动物播报
        elif monit[0] == 'ZOOOUT01':
            await send_model(clients[monit[2]], monit[3])
        # zoo系统架构播报
        elif monit[0] == 'ZOOOUT02':
            for name in monit[3].split(','):
                await clients[monit[2]].send_message(f_chats_name(name)[0],
                                                     main_adv,
                                                     parse_mode=ParseMode.HTML,
                                                     reply_markup=get_imp('group'),
                                                     disable_web_page_preview=True)
                await asyncio.sleep(20)
        # zoo公告栏播报
        elif monit[0] == 'ZOOOUT03':
            for name in monit[3].split(','):
                sql_str = "select msg_list from chat_message where chat_id={0:d} and kind='公告牌' and ind='0'"\
                    .format(cm[0].chat_id)
                rst = tg_connector.query(sql_str)
                await clients[monit[2]].forward_messages(f_chats_name(name)[0],
                                                         cm[0].chat_id,
                                                         int(rst[0][0].strip(',')),
                                                         disable_notification=True)
                await asyncio.sleep(20)
        # zoo主频道检查
        elif monit[0] == 'ZOOHOME01':
            # 矫正所有群信息
            for i in range(12):
                await cm[i].correct_msgs(clients['happy_167'])
            await cm_pinglun.correct_msgs(clients['happy_167'])
            await gm_pinglun.correct_msgs(clients['happy_167'], ["尊敬的客户.*"])
            # 总表->引导表 补充总表中遗留项
            sql_str = "select a.id from zoo_info a left join (select * from chat_message where chat_id={0:d} and " \
                      "kind='动物列表') b on a.id=b.ind where a.status in (1,2) and b.ind is null".format(cm[0].chat_id)
            rst = tg_connector.query(sql_str)
            for no in rst:
                sys_log.write_log("Zoo主频道检查|总表->引导表 补充总表中遗留项: {0:d}".format(no[0]), 'a')
                await update_home(str(no[0]))
            # 总表->引导表 删除引导表中多余项
            sql_str = "select a.ind from (select * from chat_message where chat_id={0:d} and kind='动物列表') a left " \
                      "join (select * from zoo_info b where status in ('1','2')) b on a.ind=b.id where b.id is null"\
                .format(cm[0].chat_id)
            rst = tg_connector.query(sql_str)
            for no in rst:
                sys_log.write_log("Zoo主频道检查|总表->引导表 删除引导表中多余项: {0:s}".format(no[0]), 'a')
                await update_home(no[0])
            if await show_pinglun_home():
                await asyncio.sleep(2)
            await show_board()
            auto_class()
            await update_sub_chl()
        # zoo英文主频道检查
        elif monit[0] == 'ZOOHOME01_EN':
            # 矫正所有群信息
            for i in [20, 21, 22]:
                await cm[i].correct_msgs(clients['happy_167'])
            # 总表->引导表 补充总表中遗留项
            sql_str = "select a.id from zoo_info a left join (select * from chat_message where chat_id={0:d} and " \
                      "kind='动物列表') b on a.id=b.ind where a.status in (1,2) and b.ind is null".format(cm[20].chat_id)
            rst = read_connector.query(sql_str)
            for no in rst:
                sys_log.write_log("Zoo英文主频道检查|总表->引导表 补充总表中遗留项: {0:d}".format(no[0]), 'a')
                await update_home_en(str(no[0]))
                # 当英文频道补全后删除停留时间
                await asyncio.sleep(60)
            # 总表->引导表 删除引导表中多余项
            sql_str = "select a.ind from (select * from chat_message where chat_id={0:d} and kind='动物列表') a left " \
                      "join (select * from zoo_info b where status in ('1','2')) b on a.ind=b.id where b.id is null"\
                .format(cm[20].chat_id)
            rst = read_connector.query(sql_str)
            for no in rst:
                sys_log.write_log("Zoo英文主频道检查|总表->引导表 删除引导表中多余项: {0:s}".format(no[0]), 'a')
                await update_home_en(no[0])
            await show_board_en()
            auto_class([21, 22])
            await update_sub_chl_en()
        # zoo主频道历史动物轮播
        elif monit[0] == 'ZOOHOME02':
            # 检查是否已经超过轮播时间
            # 上线需要改时间
            now = datetime.datetime.now()
            if now.hour < 2 or now.hour > 18 or (now.hour > 14 and now.weekday() in (5, 6)):
                freq = 36
            else:
                freq = 150
            sql_str = "select case when time_to_sec(timediff(now(), updated_at))/60>{2:d} then '1' else '0' end from " \
                      "chat_message where chat_id={0:d} and kind='{1:s}' order by updated_at desc limit 1"\
                .format(cm[0].chat_id, cm[0].kind, freq)
            rst = tg_connector.query(sql_str)
            if rst[0][0] == '0':
                return
            # 若要轮播，则展示最久的动物
            sql_str = "select * from chat_message where chat_id={0:d} and kind='{1:s}' order by updated_at limit 1"\
                .format(cm[0].chat_id, cm[0].kind)
            rst = tg_connector.query(sql_str)
            sys_log.write_log("zoo主频道历史动物轮播:{0[3]:s}->{0[5]:%Y-%m-%d %X}".format(rst[0]), 'a')
            # 更新主频道
            await update_home(rst[0][3])
            if await show_pinglun_home():
                await asyncio.sleep(2)
            await show_board()
            # 更新英文主频道
            await update_home_en(rst[0][3])
            await show_board_en()
            # 更新分类频道
            auto_class()
            await update_sub_chl()
            await update_sub_chl_en()
            # 更新讨论区名片
            await asyncio.sleep(2)
            await copy_to_disc(rst[0][3])
        # zoo大众点评轮播
        elif monit[0] == 'ZOODIANPING':
            wait_ind = await show_pinglun_dianping(1)
            if wait_ind:
                await asyncio.sleep(2)
            # 更新子频道的公告牌
            await copy_board(4)
        # zoo自动更新空闲频道
        elif monit[0] == 'ZOOAVA01':
            sql_str = "update zoo_note set content=(select GROUP_CONCAT(b.girl_id) from (select id,type,girl_id," \
                      "created_at from (select * from zoo_girlc_log where created_at>date_add(CURRENT_TIMESTAMP(), " \
                      "interval -3 hour) and girl_id<>0 and type in ('1','2','3','4') order by id desc limit 2000 ) a " \
                      "group by girl_id order by id) b where b.type in ('1','3')) where id='auto_available'"
            tg_connector.exe(sql_str)
            auto_class(['1', '21'])
            await update_sub_chl([1])
            await update_sub_chl_en([21])
        # 日常清理工作
        elif monit[0] == 'ZOOCLEAN01':
            # 清理表 zoo_girlc_log 每个动物只保留最近30条报空记录
            sql_str = "delete from zoo_girlc_log where id in (select id from (select row_number() over(partition by " \
                      "girl_id order by id desc) as row_num, m.* from zoo_girlc_log m where girl_id<>0) as n where row_num>30)"
            tg_connector.exe(sql_str)
            sql_str = "delete from zoo_girlc_log where id in (select id from (select row_number() over(partition by " \
                      "user_id order by id desc) as row_num, m.* from zoo_girlc_log m where user_id<>0) as n where row_num>30)"
            tg_connector.exe(sql_str)
            # 清理表 zoo_show_log 删除20天前的记录
            sql_str = "delete from zoo_show_log where created_at<date_add(CURRENT_TIMESTAMP(), interval -20 day)"
            tg_connector.exe(sql_str)

    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
