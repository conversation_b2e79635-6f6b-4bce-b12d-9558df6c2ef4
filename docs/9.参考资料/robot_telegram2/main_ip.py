# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from classes import *

items = """
1、把ip地址从txt文档入库，并合并到IP池
2、批量检查ip可用性
3、修复ip使用状态
4、获取可用ip
IP池功能选择："""

if __name__ == "__main__":
    while 1:
        str_input = input(items)
        if str_input == '1':
            proxy_load()
        elif str_input == '2':
            proxy_cycle_check()
        elif str_input == '3':
            proxy_repair_use_status()
        elif str_input == '4':
            ip_port = proxy_get('1')
            print(ip_port)
            proxy_set_use_status(ip_port[0], '0')



