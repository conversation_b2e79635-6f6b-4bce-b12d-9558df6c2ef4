import os
import openai
from classes import ConfFile

sys_conf = ConfFile('./config/sys_config.ini')
openai.api_key = sys_conf.read('chatGPT', 'API')

start_sequence = "\nA:"
restart_sequence = "\n\nQ: "

prompt = input(restart_sequence)
response = openai.Completion.create(
  model="text-davinci-003",
  prompt=prompt,
  temperature=0,
  max_tokens=100,
  top_p=1,
  frequency_penalty=0,
  presence_penalty=0
  # stop=["\n"]
)
print(response["choices"][0]["text"].strip())
