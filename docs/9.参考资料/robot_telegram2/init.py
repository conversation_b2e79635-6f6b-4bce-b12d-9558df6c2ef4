# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from classes import *
import telnetlib
from itertools import chain
import traceback


# 初始化客户端字典 clients
def init_clients(c_list=client_list, clear=True):
    try:
        if clear:
            clients.clear()
        client_str = "','".join(c_list)
        sql_str = "select username,is_bot,token,api_id,api_hash,proxy_addr,proxy_port,proxy_name,proxy_pwd " \
                  "from clients where status='1' and username in ('{0:s}')".format(client_str)
        rst = tg_connector.query(sql_str)
        proxy = {}
        for row in rst:
            # # 确认代理信息
            # if row[5] != '' and telnetlib.Telnet(row[5], row[6]):
            #     proxy = dict(proxy_type="socks5", addr=row[5], port=row[6])
            #     sys_log.write_log("使用代理登录tg: {0:s}->{1:s}:{2:d}".format(row[0], row[5], row[6]), 'a')
            # elif row[5] != '':
            #     sys_log.write_log("代理不可用，直接使用服务器ip: {0:s}->{1:s}:{2:d}".format(row[0], row[5], row[6]), 'a')
            # else:
            #     sys_log.write_log("没有设置代理，直接使用服务器ip: {0:s}".format(row[0]), 'a')

            # 确认代理信息
            if row[5] != '':
                if test_ip(row[5], row[6]):
                    # 若历史代理可用，则继续使用
                    proxy = dict(scheme="socks5", hostname=row[5], port=row[6])
                    sys_log.write_log("使用原代理登录tg: {0:s}->{1:s}:{2:d}".format(row[0], row[5], row[6]), 'a')
                else:
                    # 若历史代理不可用，则重新申请新代理
                    new_proxy = proxy_get('2')
                    if len(new_proxy) > 0:
                        sql_str = "update clients set proxy_addr='{1[0]:s}',proxy_port={1[1]:d} where username='{0[0]:s}'"\
                            .format(row, new_proxy)
                        tg_connector.exe(sql_str)
                        proxy = dict(scheme="socks5", hostname=new_proxy[0], port=new_proxy[1])
                        sys_log.write_log("使用新代理登录tg: {0:s}->{1:s}:{2:d}".format(row[0], new_proxy[0], new_proxy[1]), 'a')
                    else:
                        sql_str = "update clients set proxy_addr='',proxy_port=0 where username='{0[0]:s}'".format(row)
                        tg_connector.exe(sql_str)
                        sys_log.write_log("无可用代理，直接使用服务器ip: {0:s}".format(row[0]), 'a')
            else:
                sys_log.write_log("没有设置代理，直接使用服务器ip: {0:s}".format(row[0]), 'a')
            # 修复代理表中的使用状态
            proxy_repair_use_status()
            # 配置客户端构造函数
            plus_read = sys_conf.read('plugins', row[0])
            if plus_read is None:
                # 无更新客户端
                if row[1] == '1' and len(proxy) == 0:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', bot_token=row[2], no_updates=True)
                elif row[1] == '1' and len(proxy) > 0:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', proxy=proxy, bot_token=row[2], no_updates=True)
                elif row[1] == '0' and len(proxy) == 0:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', no_updates=True)
                else:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', proxy=proxy, no_updates=True)
            else:
                # 更新客户端
                plus = plus_read.split('|')
                if len(plus) == 1:
                    plugins = dict(root="plugins", include=plus[0].split(','))
                else:
                    plugins = dict(root="plugins", include=plus[0].split(','), exclude=plus[1].split(','))
                if row[1] == '1' and len(proxy) == 0:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', bot_token=row[2], plugins=plugins)
                elif row[1] == '1' and len(proxy) > 0:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', proxy=proxy, bot_token=row[2], plugins=plugins)
                elif row[1] == '0' and len(proxy) == 0:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', plugins=plugins)
                else:
                    clients[row[0]] = Client('sessions/' + row[0], row[3], row[4], app_version='AlinkPro',
                                             device_model='PC', system_version='Linux', proxy=proxy, plugins=plugins)
            proxy.clear()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 初始化会话字典 chats_id chats_name
def init_chats(c_list=[], clear=True):
    try:
        if clear:
            chats_name.clear()
            chats_id.clear()
        if len(c_list) == 0:
            c_str = "','".join(client_list)
            sql_str = "select id,type,name,outname,title,username,first_name,last_name,status,online_date from chats " \
                      "where id in (select id from clients where username in ('{0:s}'))".format(c_str)
        else:
            c_str = ",".join(map(str, c_list))
            sql_str = "select id,type,name,outname,title,username,first_name,last_name,status,online_date from chats " \
                      "where id in ({0:s})".format(c_str)
        rst = tg_connector.query(sql_str)
        chats_name.update(dict((col[2], col) for col in rst))
        chats_id.update(dict((col[0], col) for col in rst))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 统一初始化
def init():
    try:
        init_clients()
        init_chats()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


init()

if __name__ == "__main__":
    chats_id(-1001499822690)
    # chats_name("易行国际旅行社2群")


