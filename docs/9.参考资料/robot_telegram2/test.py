# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
import re
import asyncio
import time
from pyrogram import Client
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import sys
import random
import os
from itertools import permutations, combinations
from PIL import Image, ImageDraw, ImageFont
# from watermarker.marker import add_mark
# from moviepy.editor import *
# from moviepy.video.VideoClip import TextClip
from cryptography.fernet import Fernet


async def test2():
    # await asyncio.sleep(time)
    print('test')
    test4()
    key = Fernet.generate_key()
    print(key)
    cipher_suite = Fernet(key)
    password = "WO是密码"
    encrypted_password = cipher_suite.encrypt(password.encode('utf-8'))
    decrypted_password = cipher_suite.decrypt(encrypted_password.encode('utf-8'))
    print(encrypted_password)
    print(decrypted_password)



async def test1():
    start_time = time.time()
    tasks = [asyncio.create_task(test2(2)) for i in range(100000)]
    [await t for t in tasks]
    print(time.time() - start_time)


def test3():
    t1 = time.time() - 2
    str = "客户数据:abc1234"
    # print(str)
    # print(str[0:4])
    # print(len(str[0:4]))
    # print(str[4:])
    # print(str.split(":"))
    # print(time.time()-t1)
    print(time.time())
    print(round(time.time()*1000))
    time.sleep(0.001)
    print(time.time())
    print(round(time.time()*1000))


def test4():
    print(time.time())
    tm = time.localtime()
    print(tm)
    print(tm[3])
    if 6 <= tm[3] <= 10:
        note = '早上好'
    elif 11 <= tm[3] <= 13:
        note = '中午好'
    elif 14 <= tm[3] <= 17:
        note = '傍晚好'
    elif 18 <= tm[3] <= 23:
        note = '晚上好'
    else:
        note = '晚安'
    print(note)


def test5():
    monit = ['sd', '123', '234fd']
    str = "'23','sd','f3'"
    print(str)
    print("'" + "','".join(monit) + "'")


async def test6():
    api_id = 1150699
    api_hash = "11d593c2694c2df2d9a82774d827b5b7"
    await asyncio.sleep(1)
    # await Client("config/" + 'Manco', api_id, api_hash).send_message("me", "Greetings from **Pyrogram**!")
    client = Client("config/" + 'Manco', api_id, api_hash)
    client.start()
    await client.idle()
    await client.send_message(246479316, 'testing~~~~~')
    client.stop()


def test7():
    srss = "悟空—招嫖!—菲@#$%%$#^&*(律(宾*)黄嫖—（%^*华人社区）_马尼拉财富圈【杏鑫】杏4震撼上线"
    print(''.join(re.findall('[\u4e00-\u9fa5a-zA-Z0-9\（\）\_\(\)\&\%\#\@\!\$\^\*\【\】]+', srss, re.S)))


def test8():
    a = {1, 2, 3, 4}
    b = {3, 4, 5, 6}
    print(a-b)


def test9():
    str_list = ['qwer', 'iend']
    print("'" + "','".join(str_list) + "'")
    # print({str_list[0], str_list[1]})
    int_list = [123, 3443]
    print(",".join(map(str, int_list)))


def test10():
    # msg_text = '/woshi'
    # match = re.match('^/(.+)', msg_text, re.S)
    # print(msg_text)
    # print((match.group(0), match.group(1)))
    # if match is None:
    #     print("no match")
    # else:
    #     print((match.group(0), match.group(1)))
    #     print(match.groups())
    # content = "sdfsdgdAge:lsAge:dfoesdfg"
    # match = re.findall('Age:+?', content, re.S)
    # print(match)
    str = '\nR年龄: 18-24\nR胸围: 24-|34\n'
    match = re.findall('R(.*?): (.*?)\n', str, re.S)
    print(len(match))
    print(match)


def test11():
    # curr_date = time.strftime('%Y-%m-%d', time.localtime(time.time()))
    # date_1 = curr_date[0:8] + '01'
    # pre_date_1 = curr_date[0:8] + '01'
    # print((curr_date, date_1))
    # print(type(datetime.datetime.now()))
    str = '2012-11-19'
    # date_time = datetime.datetime.strptime(str, '%Y-%m-%d')
    # date_time = datetime.datetime.now()
    # date_1 = date_time.strftime('%Y-%m-') + '01'
    # datetime_1 = datetime.datetime.strptime(date_1, '%Y-%m-%d')
    # pre_date_1 = (datetime_1 + datetime.timedelta(days=-1)).strftime('%Y-%m-') + '01'
    # print((date_time, date_1, pre_date_1))

    print(datetime.datetime.now())
    end_time = (datetime.datetime.now() + datetime.timedelta(hours=2)).strftime('%m-%d %H:%M')
    print(end_time)


def test12():
    rst = [0, 1][True]
    print(rst)


def test13():
    command = ['客户数据234', '1234']
    new_command = [command[0][0:4], command[0][4:]]
    new_command.extend(command[1:])
    print(new_command)


def test14():
    print(sys.argv[0])
    print(sys.argv)


def test15():
    print(random.randint(0, 2))


def test16():
    print(os.listdir('./log'))


def test17():
    string = '23e|dkf|text_'
    regax = re.match('.*\|.*\|text_.*', string, re.S)
    if regax is None:
        print("no")
        print(regax)
    else:
        print("yes")
        print(regax)



def test18():
    if re.match(r'^([0-9]{1,3}\.){3}[0-9]{1,3}$', '23.234.092.34') is not None:
        print(True)
        print(293%10)
    else:
        print(False)

    string = "Chivas471 5-471 09062926942"
    match = re.match('^([a-zA-Z]+)(\d*) (\d+)-(\d+) \w*', string, re.S)
    if match is not None:
        in_name1 = match.group(1)
        in_name2 = match.group(2)
        group_id = match.group(3)
        girl_id = match.group(4)
        print((in_name1, in_name2, group_id, girl_id))


def test19(uchar):
    print(uchar)
    if u'\u4e00' <= uchar <= u'\u9fa5':
        print("yes")
    else:
        print("no")


def test20(ip):
    sql_username = "select a.username,ifnull(b.note,'正常'),case when b.risk_rank=1 then '严重' when b.risk_rank=2 then '一般' else '无' end from ( "
    for i in range(10):
        sql_username += "select distinct username from user_login_logs_{1:d} where client_ip = '{0:s}' union \n".format(ip, i)
    sql_username = sql_username.rstrip('union \n') + ") a left join bi.risk_users b on a.username = b.username where a.username not like 'test%'"
    print(sql_username)


def test21():
    row = '*******(未封禁)'
    [ip, note] = row.split('(')
    print(ip)
    print(note)


def test22():
    nums = [23, 4, 56, 8]
    print(max(nums))


def test23():
    ip_white = ['**************', '*************']
    user_str = "'" + "','".join(ip_white) + "'"
    sql_str = "select client_ip,count(distinct username) from ( "
    for i in range(10):
        sql_str += "select distinct username,client_ip from user_login_logs_{1:d} where client_ip in " \
                   "({0:s}) union ".format(user_str, i)
    sql_str = sql_str.rstrip("union ") + ") a group by client_ip having count(distinct username)>5"
    print(sql_str)


def test24():
    str = "012|013|014|015|016|023|024|025|026|028|029|034|035|037|038|047|056|057|058|059|067|124|128|129|134|136|138|139|145|146|148|149|156|158|159|167|168|169|236|238|239|245|246|247|248|249|256|259|267|268|269|278|279|345|346|347|348|349|356|358|359|367|368|378|389|456|457|458|459|467|479|567|569|578|579|589|678|679"
    for substr in str.split('|'):
        print(list(permutations(list(substr), 3)))
    # print(list(permutations(['a', 'b', 'c'], 3)))


def test25():
    str = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    rst = combinations(str, 2)
    print(list(rst))


def test26():
    print(3001 % 1000)


def test27():
    num = '6216608000013036795'
    print(valid(num))
    print(luhn.is_valid(num))


def test28():
    str = """�y information�
Name: must
Age: must
Height: must
Weight: must
Boobs: must
Location: makati/pasay/pasig/..
Baby: must
Special: special services/skin/..
Phone: not must
Wechat: not must
Telegram: not must
Facebook: not must
Price: your price ask
Pictures: 
-----
"""
    line = '-----'
    content = str.split(line)[0]
    print(content)
    match = re.match('.*(Pictures: .*)', content, re.S)
    print(match)
    print(match.group(1))


def test29():
    li = '|2|32|293 32|2'
    print(li.split('|'))
    print(set(li.split('|')))
    print(list(set(li.lstrip('|').split('|'))))
    print('-------')
    strd = 'ksd|'
    print(strd)
    print(len(strd.split('|')))
    print(strd.split('|')[1])


def test290():
    str = """lsldf {0:s}""".format('0000000')
    print(str)


def mean(type, point='to', text=''):
    to_dic = {}
    if type == 'sss':
        to_dic = {0: 'unknow', 1: 'very bad', 2: 'bad', 3: 'common', 4: 'good', 5: 'very good'}
    if point == 'to':
        return to_dic.get(text, '')
    elif point == 'from':
        from_dict = {v: k for k, v in to_dic.items()}
        return from_dict.get(text, '')
    else:
        return ''


def test30():
    print(mean('sss', 'to', 6))
    print(mean('sss', 'from', 'very  bad'))


def test31():
    a = 123
    b = 'sdksdfff23'
    print(str(a).isdigit())
    print(str(b).isdigit())


def test32():
    str1 = ['23', '3', '545', '3', '234', '23']
    content = '|'.join(list(set(str1)))
    print(content)
    print(','.join(str1))
    str2 = [123, 43, 523, 6345]
    str22 = ",".join(list(map(str, str2)))
    print(str22)
    print(list(map(int, str22.split(','))))


def test33():
    # joined_at = datetime.datetime.now()
    # sql_str = "dsfsdfsdfsd('{0:%Y-%m-%d %X}')".format(joined_at)
    # print(sql_str)

    for i in range(2, 5):
        print(i)
    #
    # a = [i for i in range(100)]
    # for i in range(random.randint(1, 5)):
    #     print(i)
    #     random.shuffle(a)
    #     print(a)

    # str = "lsdfsdfadsf"
    # print(str.split('a'))
    # print(12 % 5)
    # print(10 % 5)
    # list = [i for i in range(6)]
    # print(list)
    # for i in range(100, 125):
    #     print(i)
    # str2 = ',1,2,3,4,'
    # print(str2.split(',')[1])
    # abc = [False] * 12
    # print(abc)
    # print()
    # abc[3] = abc[3] or True
    # print(abc)


def test34():
    concent = 'kkkkk  Picture: 81|80|77|48   Pafdsfsdfldk'
    str = 'Picture: 81|80|77|48'
    after = re.sub(str, 'Picture: ' + '|'.join(['4','5','6']), concent, 1)
    after2 = concent.replace(str, 'Picture: ' + '|'.join(['4','5','6']))
    print(concent)
    print(str)
    print(after)
    print(after2)


def test35():
    sa = [1999226611]
    admin = [1999226611, 1914064729, 1788945841, 1957712323, 1960478848, 1976413572, 1972925245, -579336599]
    print(map(str, admin))
    print(",".join(list(map(str, sa))))
    print(",".join(list(map(str, admin))))
    print(",".join(admin))


def test36():
    num = -929828382873737
    print(isinstance(num, int))


import math
def test37():
    num = 20
    rst = num / 3
    print(rst)
    print(math.ceil(rst))
    print(math.floor(rst))


def test38():
    str = "1000 2000 3000"
    price_list = str.split(' ')
    if len(price_list) == 3:
        price = "3小时  1炮 {0[0]:s}\n6小时  2炮 {0[1]:s}\n一晚 无限炮 {0[2]:s}".format(price_list)
    else:
        price = "只提供按摩 " + str
    print(price)
    list = random.randint(1, 9)
    print(list)


def test39():
    str = "media_reactive|703|myhappygirlsbot"
    print(str.split('\|'))
    print(str.split('|'))
    print(left(str, 4))


def test40():
#     content = '''<b>灵魂拷问</b><b><emoji id="5267239001508554968">🤣</emoji></b><b><emoji id="5267239001508554968">🤣</emoji></b><b><emoji id="5267239001508554968">🤣</emoji></b><b>
#
# 如果未来有一天bc转型了</b><b><emoji id="5456441785595206330">😭</emoji></b><b>
#
# 百分80是做海外盘， 狗推会失业吗❓❓❓
#
# </b><a href="https://t.me/jinbeitiyu"><b>➖➖➖➖ ➖       ➖➖➖➖➖
# </b></a><b><emoji id="5213227721882082247">👄</emoji></b><emoji id="5213227721882082247">👄</emoji><a href="https://t.me/jwnjd608608"><b>中🇨🇳日🇯🇵韩🇰🇷越🇻🇳俄🇷🇺欧美各国佳丽、高端品质、至尊服务
#
# </b></a><emoji id="5433824784167214743">🅰️</emoji><emoji id="5431862469444249940">🔠</emoji><a href="https://t.me/kf6885"><b>AG真人总部直招代理
#
# </b></a><emoji id="5445267285713365026">💲</emoji><emoji id="5445267285713365026">💲</emoji><a href="https://app.superex.live/register?invitationCode=1KLEALYQU"><b>本群强推比特币持牌交易所🔥Superex.com🔥，匿名无身份认证，150X期货合约杠杆，邀请高返佣！
# '''
    # print(content)
    # print(re.sub('@\S*', '', content, 1))
    # print(re.sub('http\S*', '', content, 1))
    # print(re.sub('(?:http\S*|@\S*)', '', content))
    # print(re.sub('abc|def', '1234', content, 1))
    # print(content.replace('abc|def', '1234'))
    # print(content)
    # new_caption = re.sub(r'➖.*$', '', new_caption, flags=re.S)
    # print(re.sub(r'➖.*$', '', content, flags=re.S))
    # num = 1293840123
    # print(str(num)[-4:])
    print("This is my sister's job".replace("'", ''))


def test41():
    list1 = ['1', '42', '3']
    list2 = ['12', '3456', '4', '5']
    c = [x for x in list1 if x not in list2]
    d = [y for y in list2 if y not in list1]
    print(c)
    print(d)
    print(list(map(int, ['1', '2', '3'])))
    print("1,2,3,4".replace(',', "','"))
    print(datetime.date.today())
    print(datetime.date.today().day)
    print(datetime.date.today().day % 5)



def test42():
    string = "1,2,34,5,667,7,888,3"
    print(list(map(int, string.split(','))))


# 批量模糊匹配
def fuzzy_finder(patterns=[], contents=[], flags=re.S):
    collects = []
    for pattern in patterns:
        for content in contents:
            match = re.match(pattern, content, flags)
            if match is not None:
                collects.append([pattern, content, match.groups])
    return collects


def test43():
    num_list = [1, 2, 3, 4]
    print(','.join(map(str, num_list)))
    for i in range(4, 12):
        print(i)
    for i in range(5):
        print(i)
    for i in range(0):
        print(i)
    updateing = [False] * 12
    print(updateing)
    print(int(time.time()))
    print(time.time())
    print(datetime.datetime.now())
    print(datetime.datetime.now() + datetime.timedelta(seconds=3))


# 打水印
def test44():
    # im = Image.open("F:\20_社区_01_社区\图片\Alink.jpg").convert('RGBA')
    # txt = Image.new('RGBA', im.size, (0, 0, 0, 0))
    # fnt = ImageFont.truetype("c:/Windows/fonts/Tahoma.ttf", 20)
    # d = ImageDraw.Draw(txt)
    # d.text((txt.size[0]-80,txt.size[1]-30), "cnBlogs",font=fnt, fill=(255,255,255,255))
    # out = Image.alpha_composite(im, txt)
    # out.show()
    im = Image.open("/home/<USER>/media/photo/V2Lv7-dBUiDUAAgBAAMCAAN5AAceBA.jpg")
    text = "欢乐园 @happyzone168"
    # 指定要使用的字体和大小
    font = ImageFont.truetype('/usr/share/fonts/truetype/SIMLI.TTF', 30)
    layer = im.convert('RGBA')  # 将图像转为RGBA图像
    # 生成同等大小的图片
    text_overlay = Image.new('RGBA', layer.size, (255, 255, 255, 0))
    image_draw = ImageDraw.Draw(text_overlay) #画图
    # 获取文本大小
    text_size_x, text_size_y = image_draw.textsize(text, font=font)
    # 设置文本文字位置
    text_xy = (layer.size[0]*0.86 - text_size_x, layer.size[1]*0.638 - text_size_y)
    # 设置文本颜色和透明度和位置
    image_draw.text(text_xy, text, font=font, fill="black")
    # 将新生成的图片覆盖到需要加水印的图片上
    after = Image.alpha_composite(layer, text_overlay)
    after.show()
    after.save("/home/<USER>")


# 打水印
def test45():
    add_mark(file="/home/<USER>",
             out="/home/<USER>",
             mark="欢乐园 tg:happyzone168",
             color="#F11B11",
             size=30,
             opacity=0.5,
             angle=30,
             space=500)


def test46():
    video = VideoFileClip("/home/<USER>/media/video/-JVQACkwQAAhkRkVVv_M4csAxf9B4E.mp4")
    logo = (TextClip("欢乐园 tg: happyzone168", color="#FF7F50", fontsize=16, font="/usr/share/fonts/truetype/msyh.ttc")
            .set_duration(video.duration)
            # .resize(height=20)
            .margin(opacity=0.6)
            .set_position((0.382, 0.618), True)
            )
    final = CompositeVideoClip([video, logo])
    final.write_videofile("/home/<USER>/cccceee.mp4")


def test47():
    print(' '.join('123456'))
    kk = '123456'
    print(type(kk))
    if type(kk) == str:
        print("yes")
    else:
        print("no")


from functools import wraps

def log(func):
    @wraps(func)
    def wrapper(*args, **kw):
        print('call %s():'.format(func.__name__))
        return func(*args, **kw)
    return wrapper


def test48():
    # d1 = datetime.datetime.strptime('2012-03-05 17:41:20', '%Y-%m-%d %H:%M:%S')
    # d2 = datetime.datetime.strptime('2012-03-02 17:41:20', '%Y-%m-%d %H:%M:%S')
    # delta = d1 - d2
    # print(delta.days)
    # print(delta.seconds)
    # print(delta.total_seconds())
    now = datetime.datetime.now()
    print(now)
    print(type(now))
    print(now.hour)
    print(now.weekday())
    if now.hour < 2 or now.hour > 18:
        freq = 25
    else:
        freq = 150
    print(freq)


def test49():
    # number = 840773051744
    # short = str(int(str(number).lstrip('84')))
    # print(short)
    string = "12345"
    print(string[:-3])
    string = "我水滴 df ef"
    print(string.title())
    string = "abdcsdfef"
    strin2 = "abd"
    for i in string:
        print(i)
    print(min(len(strin2), len(string)))
    for i in range(0, min(len(strin2), len(string))):
        print(i)
    # print(string.index(10))


def test50():
    a = {'a':1, 'b':3}
    print(a)
    del a['c']
    print(a)


def test51():
    a = [5]
    mylist = [[1, 2, 3]]
    mylist.append(a)
    print(a)
    print(mylist)
    print("........")
    a = [3]
    print(a)
    print(mylist)
    print(">>>>>>>")
    mylist.append(a)
    print(a)
    print(mylist)


def test52():
    print("test52")
    kkkk = 816230498
    print(kkkk)
    llll = str(kkkk)[:4]
    print(llll)
    print(type(llll))


def test53():
    rst = fuzzy_finder(['.*delete.*', '.*drop.*', '.*alter.*', '.*add.*'], ['334ETEdELete(fdrop)'.lower()])
    print(rst)


def test54():
    substr = 'Iee'
    string = 'DIeEEfeF'
    for c in string.upper():
        print(c)
    if substr in string:
        print(True)
    else:
        print(False)


def test54():
    # 获取当前日期
    current_date = datetime.now().date()
    # 获取上一天的日期
    previous_day_date = current_date - timedelta(days=1)
    # 获取这个月月初的日期
    first_day_of_current_month = current_date.replace(day=1)
    # 获取上个月月初的日期
    first_day_of_last_month = first_day_of_current_month - relativedelta(months=1)
    # 获取上个月月末的日期
    last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
    print("当前日期:", current_date)
    print("上一天的日期:", previous_day_date)
    print("这个月月初的日期:", first_day_of_current_month)
    print("上个月月初的日期:", first_day_of_last_month)
    print("上个月月末的日期:", last_day_of_last_month)
    print([0]*9)


def test55():
    list_a = [[1, 2, 3, 4, 9], [5, 6, 7, 8, 10]]
    print(list_a)
    print(list_a[0])
    print(list_a[0][0:3])
    print(list_a[0][1:])
    print(list_a[0][:3])


def test56():
    num = 3402
    print(f"测试结果为：1234567890")
    print("测试结果为：{0}".format(num))
    print(f"测试结果为：{num:05}")
    print("测试结果为：{0:08}".format(num))
    print(f"测试结果为：{num:03}")


if __name__ == "__main__":
    test56()

