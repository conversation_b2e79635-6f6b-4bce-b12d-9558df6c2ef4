# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from func import *
from init import *
import re
import time
import datetime
import traceback
import os


async def do_monit_public(monit):
    try:
        sys_log.write_log('{0[0]:s}|{0[1]:s}:{0[2]:s}->{0[3]:s}'.format(monit), 'a')

        # 完善群成员关系
        if monit[0] == 'PUBLIC01':
            await poll_client([monit[2]])
        # 更新通讯录
        elif monit[0] == 'PUBLIC02':
            await update_contact(monit[2])

    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

