# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from init import *
from func import *
from classes2 import *
import asyncio
from pyrogram import idle


async def to_do(semaphore, no, country_id):
    async with semaphore:
        print_both("注册第 {0:d} 个账号 开始...".format(no))
        reg_client = RegClient()
        if RegClient.normal:
            await reg_client.register_tele(int(country_id), no)
        else:
            print_both("程序异常，后续不执行")
        print_both("注册第 {0:d} 个账号 结束。。。".format(no))


async def main():
    try:
        sys_log.write_log('The Reg is begining...', 'a')
        (_, country_id, cnt) = sys.argv
        print(country_id, cnt)
        task_list = []
        # 控制并发数量
        semaphore = asyncio.Semaphore(1)
        for no in range(1, int(cnt)+1):
            task = asyncio.create_task(to_do(semaphore, no, country_id))
            task_list.append(task)
        await asyncio.gather(*task_list)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())
