# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from monit import *
from init import *
from func import *
import asyncio
from pyrogram import idle


async def main():
    try:
        sys_log.write_log('The skc is begining...', 'a')
        await asyncio.gather(*(c.start() for c in clients.values()))
        await asyncio.sleep(3)
        monit = Monit()
        await asyncio.gather(monit.start())
        await idle()
        await asyncio.gather(*(c.stop() for c in clients.values()))
        tg_connector.close()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())


