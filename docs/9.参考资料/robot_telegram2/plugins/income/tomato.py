# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from classes2 import *
import traceback


# user转发视频给bot
# 测试主频道： -1001662014767
@Client.on_message(filters.chat([-1001300542637, -1001808048939, -1001789811811]))
async def forward_video(client, message):
    try:
        # 机器人id
        bot_id = 6305148654
        rst_check = await check_media_group(client, message)
        if rst_check[0] == '2':
            return
        elif rst_check[0] == '0':
            await client.forward_messages("Nowmimi_Bot", message.chat.id, message.id, disable_notification=True)
        elif rst_check[0] == '1':
            await client.copy_media_group("Nowmimi_Bot", message.chat.id, message.id, disable_notification=True)
        sys_log.write_log("从 {0[3]:s} 获取视频转发给 {1[3]:s}"
                          .format(f_chats_id(message.sender_chat.id), f_chats_id(bot_id)), 'a')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# bot接收并发布视频到频道
@Client.on_message(filters.chat('yusheng1999'))
async def rec_forward_video(client, message):
    try:
        # 目标频道
        obj_chat_id = -1001628805110
        # 剔除media_group中的非第一条消息
        rst_check = await check_media_group(client, message)
        if rst_check[0] == '2':
            return
        # 剔除消息中的链接
        if message.caption is not None:
            old_caption = message.caption
        elif message.text is not None:
            old_caption = message.text
        else:
            old_caption = ''
        new_caption = re.sub('(?:http\S*|@\S*)', '', old_caption)
        # 转发消息
        if rst_check[0] == '0':
            await client.copy_message(obj_chat_id, message.chat.id, message.id,
                                      caption=new_caption, disable_notification=True)
        elif rst_check[0] == '1':
            await client.copy_media_group(obj_chat_id, message.chat.id, message.id,
                                          captions=new_caption, disable_notification=True)
        sys_log.write_log("从 {0[3]:s} 获取视频转发给 {1[3]:s}"
                          .format(f_chats_id(message.chat.id), f_chats_id(obj_chat_id)), 'a')
        await asyncio.sleep(5)
        if rst_check[0] == '1':
            list_del_msg = []
            for msg in rst_check[1]:
                list_del_msg.append(msg.id)
            await client.delete_messages(message.chat.id, list_del_msg)
        else:
            await message.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


@Client.on_message(group=-3)
async def receive_msg(client, message):
    try:
        print(message.id)
        # await client.delete_messages(-1001628805110, 17481)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')



