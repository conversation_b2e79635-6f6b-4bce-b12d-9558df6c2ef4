# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from func import *
import traceback
import asyncio
from pyrogram import Client, filters


# 更新消息的chats信息
@Client.on_message(group=-9)
async def all_message(client, message):
    try:
        await update_chats(client, message)
        # 监控群活跃成员
        if message.chat is not None and message.from_user is not None \
                and message.chat.type in (ChatType.GROUP, ChatType.SUPERGROUP):
            active_chat_user(message.chat.id, message.from_user.id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新callback中的chats信息
@Client.on_callback_query(group=-9)
async def all_callback(client, callback):
    try:
        await update_chats_callback(client, callback)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 用户加入
@Client.on_message(filters.new_chat_members, group=-8)
async def new_chat_member(client, message):
    try:
        chat_id = message.chat.id
        for member in message.new_chat_members:
            await update_chat(client, member)
            user_id = member.id
            f_chat_user(chat_id, user_id, '2')
            f_chat_user_info(chat_id)
            if clients.get(f_chats_id(user_id)[2], None) == client:
                await perfect_chat_user(f_client_username(client), chat_id, False)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 用户退出
@Client.on_message(filters.left_chat_member, group=-8)
async def left_chat_member(_, message):
    try:
        chat_id = message.chat.id
        user_id = message.left_chat_member.id
        f_chat_user(chat_id, user_id, '3')
        f_chat_user_info(chat_id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新群户关系
@Client.on_message(filters.me & filters.command("chat_user"), group=-8)
async def update_chat_member(client, message):
    try:
        await poll_client([f_client_username(client)])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取对方的peer
@Client.on_message(filters.me & filters.regex("^/peer -?\w+$"), group=-8)
async def get_peer(client, message):
    try:
        comm = message.text.split(' ')
        if re.match('^-?\d+$', comm[1], re.S) is not None:
            text = "通过id提取peer"
            try:
                peer = str(await client.resolve_peer(peer_id=int(comm[1])))
                rst = "成功：\n"
            except Exception as e:
                peer = traceback.format_exc()
                rst = "失败：\n"
        else:
            text = "通过username/number提取peer"
            try:
                peer = str(await client.resolve_peer(peer_id=comm[1]))
                rst = "成功：\n"
            except Exception as e:
                peer = traceback.format_exc()
                rst = "失败：\n"
        await message.reply(text+rst+peer)

    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取对方的user
@Client.on_message(filters.me & filters.regex("^/chat -?\w+$"), group=-8)
async def get_chat(client, message):
    try:
        comm = message.text.split(' ')
        chat_arr = []
        chat_db = chat_tg = err = ''
        if re.match('^-?\d+$', comm[1], re.S) is not None:
            text = "通过id提取chat{0:s}:\n"
            try:
                chat_arr = f_chats_id(int(comm[1]))
                if comm[1][0] == '-':
                    chat_tg = str(await client.get_chat(int(comm[1])))
                else:
                    chat_tg = str(await client.get_users(int(comm[1])))
                rst = "成功"
            except Exception as e:
                err = traceback.format_exc()
                rst = "失败"
        else:
            text = "通过username提取chat{0:s}:\n"
            try:
                user = await client.get_chat(comm[1])
                if user.type in (ChatType.PRIVATE, ChatType.BOT):
                    user = await client.get_users(comm[1])
                chat_tg = str(user)
                chat_arr = f_chats_id(user.id)
                rst = "成功"
            except Exception as e:
                err = traceback.format_exc()
                rst = "失败"
                sql_str = "select id,type,name,outname,title,username,first_name,last_name,status," \
                          "online_date from chats where username in ('{0:s}')".format(comm[1])
                rst2 = tg_connector.query(sql_str)
                if len(rst2) != 0:
                    chat_arr = rst2[0]
        # 获取数据库中信息
        if len(chat_arr) > 0:
            chat_db = """id: {0[0]:d}
type: {0[1]:s}
name: {0[2]:s}
outname: {0[3]:s}
title: {0[4]:s}
username: {0[5]:s}
first_name: {0[6]:s}
last_name: {0[7]:s}
status: {1:s}
online_date: {0[9]:%Y-%m-%d %X}
\n""".format(chat_arr, str(explain('UserStatus', chat_arr[8])))
        await message.reply(text.format(rst)+chat_db+chat_tg+err)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取std_id
@Client.on_message(filters.outgoing & filters.command("id"), group=-8)
async def get_std_id(client, message):
    try:
        user_id = message.chat.id
        await message.delete()
        std_id = user_to_std(user_id)
        await client.send_message('me', "用户 {0[3]:s} 的标准ID是 <b><code>{1:s}</code></b>".format(f_chats_id(user_id), std_id))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
