# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common_girlc import *
from pyrogram import types
import datetime


cm_rep_save = ChatMessage(params['my_channel'], '举报公告牌')
cm_rep_chl = ChatMessage(params['c_report_id'], '举报公告牌')


# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        if index == 'girlc':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_cha + "Busy", callback_data='girlc_busy'),
                 InlineKeyboardButton(e_gou + "Available", callback_data='girlc_avail')],
                [InlineKeyboardButton(e_time2 + "Remind", callback_data='girlc_remind'),
                 InlineKeyboardButton(e_man3 + "Boss", url="t.me/happy_167")]
            ])
        elif index == 'girlc_remind':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_alert + "Yes, pls remind me", callback_data='girlc_remind_1')],
                [InlineKeyboardButton(e_noalert + "No, I don't need", callback_data='girlc_remind_2')]
            ])
        elif index == 'task':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_kong + "报空", callback_data='task_avail_' + param),
                 InlineKeyboardButton(e_mai + "调查", callback_data='task_research_' + param)],
                [InlineKeyboardButton(e_market + "引流", callback_data='task_promote_' + param),
                 InlineKeyboardButton(e_report2 + "举报", callback_data='task_report_' + param)]
            ])
        elif index == 'task_report':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_adv + "更新公告", callback_data='task_report_board_' + param),
                 InlineKeyboardButton(e_file + "增加名单", callback_data='task_report_add_' + param)]
            ])
        elif index == 'task_report_contact':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_man3 + "Report to Boss", url="t.me/happy_167")]
            ])
        elif index == 'task_avail_private':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_cycle + "刷新", callback_data='task_avail_refresh_' + param),
                 InlineKeyboardButton(e_bell2 + "活跃提醒", callback_data='task_avail_activate')]
            ])
        elif index == 'task_avail':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_cha + "Busy", callback_data='task_avail_busy_' + param),
                 InlineKeyboardButton(e_gou + "Available", callback_data='task_avail_avail_' + param)],
                [InlineKeyboardButton(e_report2 + "Report", url="t.me/OncallReport"),
                 InlineKeyboardButton(e_man3 + "Boss", url="t.me/happy_167")]
            ])
        elif index == 'task_research_private':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_cycle + "刷新", callback_data='task_research_refresh_' + param)]
            ])
        elif index == 'task_research':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_refuse + "Refuse", callback_data='task_research_refuse_' + param),
                 InlineKeyboardButton(e_ok + "Accept", callback_data='task_research_accept_' + param)],
                [InlineKeyboardButton(e_report2 + "Report", url="t.me/OncallReport"),
                 InlineKeyboardButton(e_man3 + "Boss", url="t.me/happy_167")]
            ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取反馈话术
async def get_feedback(user_id, comm=''):
    try:
        my_girlc = await explain_girlc(user_id)
        status = my_girlc[6][0]
        # 无效反馈话术
        if status == '0':
            content = "Pls talk to boss first <b>@happy_167</b>"
        elif status in ('1', '2'):
            content = "铁子，选妹子请联系 <b>@happy_167</b>"
        elif status == '5':
            content = "Beb, pls ask boss to show you up first <b>@happy_167</b>"
        elif status == '7':
            content = "Sorry, you can't use this bot. If in doubt, please contact <b>@happy_167</b>"
        else:
            # 有效反馈话术
            if my_girlc[4] == '':
                content = e_flower + "Dear beb" + e_flower + "\n"
            else:
                name_tmp = re.sub(r'[0-9]', '', my_girlc[4])
                content = e_flower + "Dear {0:s}".format(name_tmp) + e_flower + "\n"
            end_time = (datetime.datetime.now() + datetime.timedelta(hours=3)).strftime('%m-%d %H:%M')
            if 'avail' in comm:
                content += e_gou + "Good! I will keep recommend you until <b>{0:s}</b>\n".format(end_time)
            elif 'busy' in comm:
                content += e_cha + "Noted, I will not disturb you before <b>{0:s}</b>\n".format(end_time)
            elif 'accept' in comm:
                content += e_ok + "Good! You accept the research, I will recommend you to this guest"
            elif 'refuse' in comm:
                content += e_refuse + "Noted, You refuse the research, Never mind"
            elif comm == 'girlc_remind':
                content += "{1:s}We <b>only recommend</b> girl who is available for nearly <b>{0:s}</b> hours\n" \
                           "{2:s}Do you want bot remind you after that?".format(e_num[3], e_money, e_light)
            elif comm == 'girlc_remind_1':
                content += e_smile2 + "Okay, bot will remind you"
            elif comm == 'girlc_remind_2':
                content += e_weiqu + "Noted, bot will not disturb you, but pls remember update boss frequently"
            else:
                content += e_clap + "Welcome to join us for oncall!" + e_lip + "\n"
        return status, content
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 执行活跃成员任务[新户,私发,私发,历史,历史,历史,历史]
async def do_task_avail_activate():
    try:
        # 目标成员
        members = []
        user_ids = []
        notes=[]
        except_str = ''
        # 新户-最近40个有效动物 1个
        sql_str = "select id,user_id,created_at from ( select a.id,ifnull(b.user_id,0) as user_id,ifnull(b.created_at," \
                  "'2000-01-01 00:00:00') as created_at from (select * from zoo_info where status in ('1','2','3') " \
                  "and phone<>'-' order by id desc limit 40) a left join ( select row_number() over (partition by " \
                  "girl_id order by created_at desc) as row_num,m.* from zoo_girlc_log m where girl_id <>0) b on " \
                  "a.id=b.girl_id where (b.girl_id is null or b.row_num=1)) c order by created_at,id limit 1"
        rst = read_connector.query(sql_str)
        for row in rst:
            members.append(row[0])
            user_ids.append(row[1])
            notes.append('新户')
            except_str += str(row[0]) + ','
        # 私发-状态为私发的动物 2个
        sql_str = "select id,user_id,created_at from ( select a.id,ifnull(b.user_id,0) as user_id,ifnull(b.created_at," \
                  "'2000-01-01 00:00:00') as created_at from (select * from zoo_info where status='3' and phone<>'-' " \
                  "and id not in ({0:s})) a left join ( select row_number() over (partition by girl_id order by " \
                  "created_at desc) as row_num,m.* from zoo_girlc_log m where girl_id <>0) b on a.id=b.girl_id where " \
                  "(b.girl_id is null or b.row_num=1)) c order by created_at,id desc limit 2"\
            .format(except_str.strip(','))
        rst = read_connector.query(sql_str)
        for row in rst:
            members.append(row[0])
            user_ids.append(row[1])
            notes.append('私发')
            except_str += str(row[0]) + ','
        # 历史-最长时间没更新状态的动物 4个
        sql_str = "select id,user_id,created_at from ( select a.id,ifnull(b.user_id,0) as user_id,ifnull(b.created_at," \
                  "'2000-01-01 00:00:00') as created_at from (select * from zoo_info where status in ('1','2') and " \
                  "phone<>'-' and id not in (1,2,3)) a left join ( select row_number() over (partition by girl_id " \
                  "order by created_at desc) as row_num,m.* from zoo_girlc_log m where girl_id <>0) b on a.id=b.girl_id " \
                  "where (b.girl_id is null or b.row_num=1)) c order by created_at,id desc limit 4"\
            .format(except_str.strip(','))
        rst = read_connector.query(sql_str)
        for row in rst:
            members.append(row[0])
            user_ids.append(row[1])
            notes.append('历史')
        # 列出活跃提醒的列表
        content = '活跃提醒列表：'
        for i in range(7):
            content += '<code>' + str(members[i]) + '</code>(' + notes[i] + '),'
        await clients['myhappyzonebot'].send_message(params['user_hugo'],
                                                     content.rstrip(','),
                                                     parse_mode=ParseMode.HTML,
                                                     disable_notification=True)
        # 进行提醒操作
        content_new = [

        ]

        content = ["😜Beb, if you want get guests, you need to update me often.",
                   "🌹Beb, we only recommend girl who update me in 3 hours, 🔉pls tell me you are available when you are ready",
                   "🫶beb, you didn't update me for a long time already. pls update me often if you want guest🌷",
                   "beb, do you still want to work? 💸why no update for a long time?💥",
                   "💞Beb, where is your update? why you don't tell me that you are available now?",
                   "Beb, update me often na👏, I want to recommend you.💝",
                   "🤡beb, pls join my channel @happyzonegirl and update me often, so I can recommend you👍"
                   ]
        # for i in range(1):
        #     if user_ids == 0:





    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面 管理员
@Client.on_callback_query(privateCallbackFilter & filters.user(sa), group=-2)
async def callback_interface_admin(client, callback):
    try:
        user_id = callback.from_user.id
        # 节流策略
        throttle_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if 'girlc_' + str(user_id) in params.keys() and params['girlc_' + str(user_id)] == callback.data + throttle_time[:-1]:
            await callback.answer("☕慢点操作，老铁~", show_alert=False)
            return
        else:
            params['girlc_' + str(user_id)] = callback.data + throttle_time[:-1]
        # 任务-报空/调查
        if re.match('^task_(?:avail|research)_\d+$', callback.data, re.S) is not None:
            (_, comm, id_code) = callback.data.split('_')
            if comm == 'avail':
                pre_code = 'K'
                e_tmp = e_kong
            else:
                pre_code = 'D'
                e_tmp = e_mai
            id = int(id_code)
            sour_msg = await client.get_messages(params['user_hugo'], id)
            # 如果涉及到媒体，则优先转发媒体
            if sour_msg.media_group_id is not None:
                await client.copy_media_group(
                    params['c_girlc_id'],
                    params['user_hugo'],
                    id,
                    captions="",
                    disable_notification=True
                )
            elif sour_msg.photo is not None:
                await client.copy_message(
                    params['c_girlc_id'],
                    params['user_hugo'],
                    id,
                    caption="",
                    disable_notification=True
                )
            # 任务编号递增
            sql_str = "update params set value_int=value_int+1 where type='Zoo' and name='任务编号'"
            tg_connector.exe(sql_str)
            # 获取最新的任务ID
            sql_str = "select concat('{0:s}',value_int) from params where type='Zoo' and name='任务编号'".format(pre_code)
            rst = read_connector.query(sql_str)
            # 调整发送内容
            ahead_content = "{1:s} No.{0:s}\n{2:s} ".format(rst[0][0], e_task, e_tmp)
            after_content = "\n{0:s} Pls click button or PM boss".format(e_light)
            if sour_msg.text is not None:
                new_content = ahead_content + sour_msg.text + after_content
            elif sour_msg.caption is not None:
                new_content = ahead_content + sour_msg.caption + after_content
            else:
                new_content = ''
            # 调整发送格式
            if sour_msg.entities is not None:
                new_entities = []
                for e in sour_msg.entities:
                    e.offset += len(ahead_content) + 2
                    new_entities.append(e)
            elif sour_msg.caption_entities is not None:
                new_entities = []
                for e in sour_msg.caption_entities:
                    e.offset += len(ahead_content) + 2
                    new_entities.append(e)
            else:
                new_entities = None
            # 发送频道消息
            send_msg = await client.send_message(
                params['c_girlc_id'],
                new_content,
                ParseMode.HTML,
                new_entities,
                disable_web_page_preview=True,
                disable_notification=False,
                reply_markup=get_imp("task_{0:s}".format(comm), rst[0][0]))
            # 如果格式失真，则修复
            if new_entities is not None and (send_msg.entities is None or len(new_entities) != len(send_msg.entities)):
                await clients['happy_167'].edit_message_text(
                    params['c_girlc_id'],
                    send_msg.id,
                    new_content,
                    ParseMode.HTML,
                    new_entities,
                    disable_web_page_preview=True
                )
            # 置顶频道信息
            await client.unpin_all_chat_messages(params['c_girlc_id'])
            await send_msg.pin()
            # 调整机器人按钮
            content = "{0:s}编号: {1:s}\n".format(e_tmp, rst[0][0])
            content += "{0:s}时间: {1:s}\n".format(e_time3, throttle_time)
            content += "{0:s}进度: 已发送".format(e_he)
            await callback.message.edit(content,
                                        parse_mode=ParseMode.HTML,
                                        disable_web_page_preview=True,
                                        reply_markup=get_imp("task_{0:s}_private".format(comm), rst[0][0])
                                        )
        # 刷新
        elif re.match('^task_(?:avail|research)_refresh_(?:K|D)\d+$', callback.data, re.S) is not None:
            (_, opt, _, id_code) = callback.data.split('_')
            if opt == 'avail':
                content = "{0:s}编号: {1:s}\n".format(e_kong, id_code)
            else:
                content = "{0:s}编号: {1:s}\n".format(e_mai, id_code)
            content += "{0:s}时间: {1:s}\n".format(e_time3, throttle_time)
            sql_str = "select case when a.type in ('1','3') and ifnull(c.status,'0') in ('1','2','3') then '1' when " \
                      "a.type in ('1','3') then '2' when a.type in ('2','4') and ifnull(c.status,'0') in ('1','2','3') " \
                      "then '3' when a.type in ('2','4') then '4' when a.type ='5' and ifnull(c.status,'0') in " \
                      "('1','2','3') then '5' when a.type ='5' then '6' when a.type ='6' and ifnull(c.status,'0') in " \
                      "('1','2','3') then '7' when a.type ='6' then '8' else '9' end as ident,a.girl_id,a.user_id," \
                      "a.created_at,ifnull(b.outname,'未知'),ifnull(b.username,'') from ( select type,user_id,girl_id," \
                      "created_at, row_number() over(partition by case when girl_id<>0 then girl_id else user_id end " \
                      "order by created_at desc) as rn from zoo_girlc_log where note='{0:s}' ) a left join chats b on " \
                      "a.user_id=b.id left join zoo_info c on a.girl_id=c.id where a.rn=1 order by ident,a.created_at " \
                      "desc".format(id_code)
            rst = tg_connector.query(sql_str)
            show_dict = {'1': [], '2': [], '3': [], '4': [], '5': [], '6': [], '7': [], '8': [], '9': []}
            for row in rst:
                if row[1] == 0:
                    name = row[4]
                else:
                    name = str(row[1])
                if row[5] == '' and row[2] == 0:
                    item = name
                elif row[5] == '' and row[2] != 0:
                    item = "<a href='tg://user?id={0[2]:d}'>{1:s}</a>".format(row, name)
                else:
                    item = "<a href='tg://resolve?domain={0[5]:s}'>{1:s}</a>".format(row, name)
                show_dict[row[0]].append(item)
            acc_cnt = len(show_dict['1']) + len(show_dict['2']) + len(show_dict['3']) + len(show_dict['4']) + \
                      len(show_dict['5']) + len(show_dict['6']) + len(show_dict['7']) + len(show_dict['8']) + \
                      len(show_dict['9'])
            content += "{0:s}进度: 目前共 {1:d} 个回复\n".format(e_he, acc_cnt)
            if len(show_dict['1']) != 0:
                content += "{0:s}{1:s} 空闲-有效\n{2:s}\n".format(e_gou, e_circle, " . ".join(show_dict['1']))
            if len(show_dict['2']) != 0:
                content += "{0:s}{1:s} 空闲-无效\n{2:s}\n".format(e_gou, e_no, " . ".join(show_dict['2']))
            if len(show_dict['3']) != 0:
                content += "{0:s}{1:s} 忙碌-有效\n{2:s}\n".format(e_cha, e_circle, " . ".join(show_dict['3']))
            if len(show_dict['4']) != 0:
                content += "{0:s}{1:s} 忙碌-无效\n{2:s}\n".format(e_cha, e_no, " . ".join(show_dict['4']))
            if len(show_dict['5']) != 0:
                content += "{0:s}{1:s} 接受-有效\n{2:s}\n".format(e_ok, e_circle, " . ".join(show_dict['5']))
            if len(show_dict['6']) != 0:
                content += "{0:s}{1:s} 接受-无效\n{2:s}\n".format(e_ok, e_no, " . ".join(show_dict['6']))
            if len(show_dict['7']) != 0:
                content += "{0:s}{1:s} 拒绝-有效\n{2:s}\n".format(e_refuse, e_circle, " . ".join(show_dict['7']))
            if len(show_dict['8']) != 0:
                content += "{0:s}{1:s} 拒绝-无效\n{2:s}\n".format(e_refuse, e_no, " . ".join(show_dict['8']))
            if len(show_dict['9']) != 0:
                content += "{0:s} 未知\n{1:s}\n".format(e_unknow, " . ".join(show_dict['9']))
            await callback.message.edit(content,
                                        parse_mode=ParseMode.HTML,
                                        disable_web_page_preview=True,
                                        reply_markup=get_imp('task_avail_private', id_code)
                                        )
        # 激活
        if callback.data == 'task_avail_activate':
            await do_task_avail_activate()
        # 任务-引流
        elif callback.data == 'task_promote':
            await callback.answer("⏳开发中，敬请期待！", show_alert=False)
        #任务-举报
        elif re.match('^task_report_\d+$', callback.data, re.S) is not None:
            await callback.message.edit("请输入举报的内容",
                                        parse_mode=ParseMode.HTML,
                                        disable_web_page_preview=True,
                                        reply_markup=get_imp("task_report", callback.data.split('_')[2])
                                        )
        # 任务-举报-更新公告
        elif re.match('^task_report_board_\d+$', callback.data, re.S) is not None:
            # 提取源消息
            id = int(callback.data.split('_')[3])
            sour_msg = await client.get_messages(params['user_hugo'], id)
            # 检查：只有普通消息才能作为公告
            if sour_msg.text is None:
                await callback.message.edit("只有普通消息才能作为公告")
                return
            # 更新公告到举报群
            await cm_rep_chl.del_msg('0', clients['happy_167'])
            msg_report = await clients['happy_167'].send_message(cm_rep_chl.chat_id,
                                                                 sour_msg.text,
                                                                 parse_mode=ParseMode.HTML,
                                                                 entities=sour_msg.entities,
                                                                 disable_web_page_preview=True
                                                                 )
            cm_rep_chl.add_row('0', msg_report)
            await callback.message.edit("已更新公告！", parse_mode=ParseMode.HTML)
            # 更新公告到缓存群
            await asyncio.sleep(2)
            await cm_rep_save.del_msg('0', clients['happy_167'])
            msg_save = await clients['happy_167'].send_message(cm_rep_save.chat_id,
                                                               sour_msg.text,
                                                               parse_mode=ParseMode.HTML,
                                                               entities=sour_msg.entities,
                                                               disable_web_page_preview=True)
            cm_rep_save.add_row('0', msg_save)
        # 任务-举报-增加名单
        elif re.match('^task_report_add_\d+$', callback.data, re.S) is not None:
            id = int(callback.data.split('_')[3])
            sour_msg = await client.get_messages(params['user_hugo'], id)
            if sour_msg.media_group_id is None:
                await callback.message.edit("需要提供多张图片的消息")
                return
            rec_msg = await client.get_media_group(params['user_hugo'], id)
            # 下载并给媒体打水印
            id_list = []
            media_list = []
            for msg in rec_msg:
                row_media_online = await media.save(client, msg, 'both')
                row_media = await media.get(row_media_online[0])
                id_list.append(row_media[0])
                adds = 'media/' + row_media[1] + '/' + row_media[3] + '.' + row_media[4]
                adds_tmp = 'media/' + row_media[1] + '/' + row_media[3] + '_tmp.' + row_media[4]
                if row_media[1] == 'photo':
                    add_pic_mark(adds, 'media/' + row_media[1] + '/', "tg: OncallReport")
                else:
                    add_video_mark(adds, adds_tmp, "tg: OncallReport")
                    os.rename(adds_tmp, adds)
                # 重新整理待发送内容
                if row_media[1] == 'photo':
                    media_list.append(InputMediaPhoto(adds, caption=msg.caption, parse_mode=ParseMode.HTML))
                elif row_media[1] == 'video':
                    media_list.append(InputMediaVideo(adds, caption=msg.caption, parse_mode=ParseMode.HTML))
            # 往举报群组发送消息
            try:
                msg_sent = await clients['happy_167'].send_media_group(params['c_report_id'],
                                                                       media_list,
                                                                       disable_notification=False
                                                                       )
                await clients['happy_167'].edit_message_caption(params['c_report_id'],
                                                                msg_sent[0].id,
                                                                caption=msg_sent[0].caption,
                                                                parse_mode=ParseMode.HTML,
                                                                caption_entities=rec_msg[0].caption_entities)
                await callback.message.edit("已增加名单！", parse_mode=ParseMode.HTML)
                # 更新举报群的公告
                await asyncio.sleep(2)
                await cm_rep_chl.del_msg('0', clients['happy_167'])
                cm_rep_save_id = int(cm_rep_save.get_row('0')[0][4].strip(','))
                sour_msg = await client.get_messages(cm_rep_save.chat_id, cm_rep_save_id)
                msg_report = await clients['happy_167'].send_message(cm_rep_chl.chat_id,
                                                                     sour_msg.text,
                                                                     parse_mode=ParseMode.HTML,
                                                                     entities=sour_msg.entities,
                                                                     disable_web_page_preview=True
                                                                     )
                cm_rep_chl.add_row('0', msg_report)
            except FloodWait as e:
                await asyncio.sleep(e.value)
            # 删除本地中已上传的文档
            for id in id_list:
                await media.del_file(id)


    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 主界面 管理员
@Client.on_message(~filters.bot & filters.incoming & ~filters.reply & filters.private & filters.user(sa))
async def welcome_admin(client, message):
    try:
        rst_check = await check_media_group(client, message)
        if rst_check[0] == '2':
            return
        content = "请输入你需要的操作"
        await message.reply_text(content,
                                 quote=False,
                                 protect_content=False,
                                 reply_markup=get_imp('task', str(message.id))
                                 )
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面 普通成员 群组
@Client.on_callback_query(~privateCallbackFilter & ~filters.user(sa), group=-2)
async def callback_interface_member_channel(client, callback):
    try:
        user_id = callback.from_user.id
        # 节流策略
        throttle_time = (datetime.datetime.now() + datetime.timedelta(hours=3)).strftime('%m-%d %H:%M')[:-2]
        if 'girlc_' + str(user_id) in params.keys() and params['girlc_' + str(user_id)] == callback.data + throttle_time:
            await callback.answer("☕Have a break, pls don't operate too often", show_alert=False)
            return
        else:
            params['girlc_' + str(user_id)] = callback.data + throttle_time
        if re.match('^task_(?:avail|research)_(?:avail|busy|accept|refuse)_(?:K|D)\d+$', callback.data, re.S) is not None:
            (_, _, opt, id_code) = callback.data.split('_')
            # 登记操作类型
            if opt == 'avail':
                opt_code = '1'
            elif opt == 'busy':
                opt_code = '2'
            elif opt == 'accept':
                opt_code = '5'
            else:
                opt_code = '6'
            # 登记girlc日志
            await log_girlc(opt_code, user_id, note=id_code)
            # 登记用户活跃情况，并不确认是否与bot聊天
            await active_girlc(user_id, False)
            # 检查身份，并根据不同身份进行处理
            feedback = await get_feedback(user_id, opt)
            if feedback[0] not in ('3', '4', '6'):
                await callback.answer(feedback[1].replace('</b>', '').replace('<b>', ''), show_alert=True)
            else:
                girlc = get_girlc(user_id)
                if girlc[6][1] != '0':
                    await callback.answer(feedback[1].replace('</b>', '').replace('<b>', ''), show_alert=True)
                else:
                    await callback.answer(url="t.me/myhappyzonebot?start=" + opt)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面 普通成员 私人
@Client.on_callback_query(privateCallbackFilter & ~filters.user(sa), group=-2)
async def callback_interface_member_private(client, callback):
    try:
        user_id = callback.from_user.id
        # 节流策略
        throttle_time = (datetime.datetime.now() + datetime.timedelta(hours=3)).strftime('%m-%d %H:%M')[:-2]
        if 'girlc_' + str(user_id) in params.keys() and params['girlc_' + str(user_id)] == callback.data + throttle_time:
            await callback.answer("☕Have a break, pls don't operate too often", show_alert=False)
            return
        else:
            params['girlc_' + str(user_id)] = callback.data + throttle_time
        feedback = await get_feedback(user_id, callback.data)
        # 若成员无效，则退出
        if feedback[0] not in ('3', '4', '6'):
            await callback.message.reply_text(feedback[1], parse_mode=ParseMode.HTML)
        # 私聊机器人 空闲
        elif callback.data == 'girlc_avail':
            await log_girlc('1', user_id)
            await send_imp_member(client, feedback[1], user_id, callback.message)
        # 私聊机器人 忙碌
        elif callback.data == 'girlc_busy':
            await log_girlc('2', user_id)
            await send_imp_member(client, feedback[1], user_id, callback.message)
        # 私聊机器人 提醒
        elif callback.data == 'girlc_remind':
            await callback.message.edit_text(feedback[1],
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('girlc_remind')
                                             )
        # 私聊机器人 提醒 选择
        elif callback.data in ('girlc_remind_1', 'girlc_remind_2'):
            opt = callback.data.split('_')[2]
            set_girlc_stauts(user_id, '*'+opt)
            await send_imp_member(client, feedback[1], user_id, callback.message)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 主界面 普通成员
@Client.on_message(~filters.bot & filters.incoming & filters.text & ~filters.reply & ~filters.forwarded & filters.private & ~filters.user(sa))
async def welcome_member(client, message):
    try:
        user_id = message.from_user.id
        feedback = await get_feedback(user_id, message.text)
        if feedback[0] not in ('3', '4', '6'):
            await message.reply_text(feedback[1], parse_mode=ParseMode.HTML)
        else:
            await send_imp_member(client, feedback[1], user_id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发送主界面
async def send_imp_member(client, content, user_id, message=None, entities=None):
    try:
        # 记录活跃状态
        await active_girlc(user_id)
        # 反馈消息
        if message is None:
            await client.send_message(user_id,
                                      content,
                                      parse_mode=ParseMode.HTML,
                                      entities=entities,
                                      disable_web_page_preview=True,
                                      protect_content=True,
                                      reply_markup=get_imp('girlc')
                                      )
        else:
            await message.edit_text(content,
                                    parse_mode=ParseMode.HTML,
                                    entities=entities,
                                    disable_web_page_preview=True,
                                    reply_markup=get_imp('girlc')
                                    )
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')





# 测试
@Client.on_message(filters.command("test"), group=-5)
async def test(client, message):
    try:
        print(message)
        # await client.send_message(message.chat.id, msg)
        # async for dialog in clients['happy_167'].get_dialogs():
        #     if dialog.chat.type == ChatType.PRIVATE:
        #         match = re.match('^([a-zA-Z]+)(\d*) (\d+)-(\d+) (\w*)', dialog.chat.first_name, re.S)
        #         if match is not None:
        #             print(match.group(4) + ',' + match.group(5))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# # 测试
# @Client.on_edited_message(filters.chat("OncallReport"), group=-5)
# async def test1(client, message):
#     try:
#         print("OncallReport 收到新消息")
#         print(message)
#         # await client.send_message(message.chat.id, msg)
#         # async for dialog in clients['happy_167'].get_dialogs():
#         #     if dialog.chat.type == ChatType.PRIVATE:
#         #         match = re.match('^([a-zA-Z]+)(\d*) (\d+)-(\d+) (\w*)', dialog.chat.first_name, re.S)
#         #         if match is not None:
#         #             print(match.group(4) + ',' + match.group(5))
#     except Exception as e:
#         sys_log.write_log(traceback.format_exc(), 'a')


# # 测试
# @Client.on_message(filters.chat("OncallReport"), group=-5)
# async def test2(client, message):
#     try:
#         print(message)
#         # await client.send_message(message.chat.id, msg)
#         # async for dialog in clients['happy_167'].get_dialogs():
#         #     if dialog.chat.type == ChatType.PRIVATE:
#         #         match = re.match('^([a-zA-Z]+)(\d*) (\d+)-(\d+) (\w*)', dialog.chat.first_name, re.S)
#         #         if match is not None:
#         #             print(match.group(4) + ',' + match.group(5))
#     except Exception as e:
#         sys_log.write_log(traceback.format_exc(), 'a')
