# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common_girlc import *
from pyrogram.raw.functions.messages import *
from pyrogram import raw
import random
import datetime
import threading


# 7832B 8384C 合作
@Client.on_message(filters.command(["girl", "stop"]) & filters.chat([-4525530023, -4508167794]))
async def girl_7832B(client, message):
    try:
        text_list = message.text.split(" ")
        chat_id = message.chat.id
        if message.text == '/stop':
            if 'girl_' + str(chat_id) in params.keys():
                del params['girl_' + str(chat_id)]
                await message.delete()
                return
        # 删除消息
        await message.delete()
        # 如果本会话有同类任务，则不操作
        if 'girl_' + str(chat_id) in params.keys() and not (len(text_list) == 2 and text_list[1] not in ('1', '2', '3', '4')):
            await client.send_message(chat_id,
                                      "本会话中已有/girl命令正在执行!请稍后操作\n",
                                      parse_mode=ParseMode.HTML)
            return
        # 区分合作伙伴
        if chat_id == -4525530023:
            partner = 1
        elif chat_id == -4508167794:
            partner = 2
        else:
            partner = 0
        # 设定动物发送频率
        sleep_time = 6
        # 按时间区间展示
        if len(text_list) == 2 and text_list[1] in ('1', '2', '3', '4'):
            # 命令类型
            explain_area = {'1': '1小时内',
                            '2': '2小时内',
                            '3': '3小时内',
                            '4': '越南'}
            sql_str = "select content from zoo_note where id='board_available'"
            rst = tg_connector.query(sql_str)
            if rst[0][0] == '':
                fix_ava = '0'
            else:
                fix_ava = rst[0][0]
            sql_str = "select b.girl_id from ( select type,girl_id,created_at," \
                      "ceil(time_to_sec(timediff(now(), created_at))/3600) as time, row_number() over(partition by " \
                      "girl_id order by created_at desc) as rn  from ( select type,girl_id,created_at from " \
                      "zoo_girlc_log where created_at>date_add(CURRENT_TIMESTAMP(), interval -3 hour) and girl_id<>0 " \
                      "and type in ('1','2','3','4') union select '3',id,date_add(CURRENT_TIMESTAMP(), interval -200 " \
                      "minute) from zoo_info where id in ({1:s}) ) a ) b join zoo_info c  on b.girl_id=c.id left join (" \
                      "select girl_id,max(case when guest_user_id={0:d} then 1 else 0 end) as is_read,count(1) as cnt " \
                      "from zoo_show_log where created_at>date_add(CURRENT_TIMESTAMP(), interval -3 hour) group by " \
                      "girl_id) d on b.girl_id=d.girl_id where b.rn=1 and b.type in ('1','3') and c.status in " \
                      "('1','2','3') and ifnull(d.is_read,0)=0 and b.time={2:s} order by b.time,c.status desc," \
                      "ifnull(d.cnt,0),b.created_at desc".format(chat_id, fix_ava, text_list[1])
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                await client.send_message(
                    chat_id,
                    "目前<b>{0:s}</b>在岗的妹子都<b>已经全部展示</b>".format(explain_area[text_list[1]]),
                    parse_mode=ParseMode.HTML
                    )
            else:
                params['girl_' + str(chat_id)] = text_list[1]
                for row in rst:
                    if 'girl_' + str(chat_id) not in params.keys():
                        return
                    # 发送动物信息
                    sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture," \
                              "status,country from zoo_info where id={0:d}".format(row[0])
                    await send_girl(sql_str, client, chat_id, partner=partner)
                    # 登记日志文件
                    log_show(chat_id, row[0])
                    await asyncio.sleep(sleep_time)
                if 'girl_' + str(chat_id) in params.keys():
                    del params['girl_' + str(chat_id)]
                await client.send_message(
                    chat_id,
                    "以上是<b>{0:s}</b>在岗的妹子".format(explain_area[text_list[1]]),
                    parse_mode=ParseMode.HTML
                    )
        # 只展示1个动物
        elif len(text_list) == 2 and re.match('^\d{2,4}(,\d{2,4})+$', text_list[1], re.S) is None:
            # 检查输入的是编号还是名字
            str_input = await input_cert(text_list[1], 'search')
            if str_input[0] == 'no':
                sub_sql_str = "id={0:d}".format(str_input[1])
            elif str_input[0] == 'phone':
                sub_sql_str = "phone like '%{0:s}%'".format(str_input[1])
            else:
                sub_sql_str = "name='{0:s}'".format(str_input[1])
            # 查询具体动物信息
            sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture,status," \
                      "country from zoo_info where {0:s}".format(sub_sql_str)
            await send_girl(sql_str, client, chat_id, partner=partner)
            # 登记日志文件
            rst = tg_connector.query(sql_str)
            if len(rst) > 0:
                log_show(chat_id, rst[0][0])
        # 批量展示动物
        elif len(text_list) == 2 and re.match('^\d{2,4}(,\d{2,4})+$', text_list[1], re.S) is not None:
            params['girl_' + str(chat_id)] = text_list[1]
            no_list = text_list[1].split(',')
            for no in no_list:
                if 'girl_' + str(chat_id) not in params.keys():
                    break
                # 发送动物信息
                sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture," \
                          "status,country from zoo_info where id={0:s}".format(no)
                await send_girl(sql_str, client, chat_id, partner=partner)
                # 登记日志文件
                log_show(chat_id, int(no))
                await asyncio.sleep(sleep_time)
            if 'girl_' + str(chat_id) in params.keys():
                del params['girl_' + str(chat_id)]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        if 'girl_' + str(chat_id) in params.keys():
            del params['girl_' + str(chat_id)]

# 查看girl信息
@Client.on_message(filters.outgoing & filters.command("girl"))
async def girl(client, message):
    try:
        text_list = message.text.split(" ")
        chat_id = message.chat.id
        # 删除消息
        await message.delete()
        # 验证身份
        ident = await identity(chat_id)
        # 如果本会话有同类任务，则不操作
        if 'girl_' + str(chat_id) in params.keys() and not (len(text_list) == 2 and text_list[1] not in ('1', '2', '3', '4')):
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                "在会话<i>{0:s}</i>中已有/girl命令正在执行!\n如需要重新执行，则需要在会话中执行<b>/stop</b>命令".format(ident[1]),
                parse_mode=ParseMode.HTML)
            return
        # 设定动物发送频率
        sleep_time = 12
        # 若Hugo与客户不是双向好友，提醒对方加好友才能快速展示所有动物信息
        try:
            sql_str = "select count(1) from zoo_show_log where guest_user_id={0:d} and created_at>date_add(" \
                      "current_timestamp(),interval - 1 day)".format(chat_id)
            rst_ind = tg_connector.query(sql_str)
            user_tmp = await clients['happy_167'].get_users(chat_id)
            if user_tmp.is_mutual_contact:
                sleep_time = 6
            elif rst_ind[0][0] == 0:
                await client.send_message(
                    chat_id,
                    "兄弟，由于Telegram的限制，<b>您要先添加我为好友</b>，我才能快速地把<b>在岗妹子</b>发给您，感谢🙏\n\n"
                    "Bro, Coz the rule of Telegram official, <b>pls add me to your contact</b> if you want to "
                    "receive all the <b>available</b> girls soon🙏",
                    parse_mode=ParseMode.HTML
                    )
                await asyncio.sleep(sleep_time / 3)
        except Exception as e:
            sys_log.write_log("若Hugo与客户不是双向好友，提醒对方加好友才能快速展示所有动物信息", 'a')
            sys_log.write_log(traceback.format_exc(), 'a')
        # 不允许发girl信息给动物
        if ident[0] not in ('0', '1', '2'):
            await clients['myhappygirlsbot'].send_message(params['user_hugo'],
                                                          "<b>不允许</b>发 /girl 命令给女孩 <i>{0:s}</i>".format(ident[1]),
                                                          parse_mode=ParseMode.HTML)
        # 按任务编号展示
        elif len(text_list) == 2 and re.match('^(?:D|d|K|k)\d+$', text_list[1], re.S) is not None and chat_id != 1957712323:
            id_code = text_list[1].upper()
            sql_str = "select a.girl_id from ( select type,user_id,girl_id,created_at, row_number() over(partition by " \
                      "girl_id order by created_at desc) as rn from zoo_girlc_log where note='{1:s}' and girl_id in " \
                      "('1','2','3','4','5','6')) a join zoo_info c on a.girl_id=c.id left join (select distinct " \
                      "girl_id from zoo_show_log where guest_user_id={0:d} and note='{1:s}' and created_at>" \
                      "date_add(CURRENT_TIMESTAMP(), interval -3 hour)) b on a.girl_id=b.girl_id where a.rn=1 and " \
                      "a.type in ('1','3','5') and c.status in ('1','2','3') and b.girl_id is null order by " \
                      "a.created_at desc".format(chat_id, id_code)
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "目前任务<b>{1:s}</b>没有待展示的妹子给客户：<i>{0:s}</i>".format(f_chats_id(chat_id)[3], id_code),
                    parse_mode=ParseMode.HTML
                    )
            else:
                params['girl_' + str(chat_id)] = id_code
                for row in rst:
                    if 'girl_' + str(chat_id) not in params.keys():
                        break
                    # 发送动物信息
                    if '人' in ident[1]:
                        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out," \
                                  "picture,status,country,special_en from zoo_info where id={0:d}".format(row[0])
                    else:
                        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out," \
                                  "picture,status,country from zoo_info where id={0:d}".format(row[0])
                    await send_girl(sql_str, client, chat_id)
                    # 登记日志文件
                    log_show(chat_id, row[0], id_code)
                    await asyncio.sleep(sleep_time)
                if 'girl_' + str(chat_id) in params.keys():
                    del params['girl_' + str(chat_id)]
                content_list = [
                    "铁子，以上是符合你要求的妹子，有喜欢的吗？😉",
                    "兄弟，按照你的要求，我收集了以上妹子给你😊，喜欢谁告诉我编号就好👏",
                    "老铁，这些都是符合你要求的妹子🎉，看看喜欢谁？🚀",
                    "兄弟，目前收集到符合你要求的妹子是这些😁，你看看哪个是你的菜？👯‍♀️",
                    "铁子，这些是服务你要求的妹子💦，如何？🌊"
                    ]
                await client.send_message(
                    chat_id,
                    content_list[random.randint(0, len(content_list) - 1)],
                    parse_mode=ParseMode.HTML
                    )
        # 按时间区间展示
        elif len(text_list) == 2 and text_list[1] in ('1', '2', '3', '4') and chat_id != 1957712323:
            # 对于外国人，引流到英文频道
            try:
                if rst_ind[0][0] == 0 and '人' in ident[1]:
                    chat_list = await client.get_common_chats(chat_id)
                    join_ind = 0
                    for chat in chat_list:
                        if chat.id == cm[20].chat_id:
                            join_ind = 1
                            break
                    if join_ind == 0:
                        await client.send_message(
                            chat_id,
                            "🎊In order to provide you with <b>better service</b>, you can join our <b>English channel "
                            "@happyzone666</b>\n\n 👏Welcome to <i>introduce friends</i> to join",
                            parse_mode=ParseMode.HTML
                            )
                        await asyncio.sleep(sleep_time / 3)
            except Exception as e:
                sys_log.write_log(traceback.format_exc(), 'a')
            # 命令类型
            explain_area = {'1': '1小时内',
                            '2': '2小时内',
                            '3': '3小时内',
                            '4': '越南'}
            sql_str = "select content from zoo_note where id='board_available'"
            rst = tg_connector.query(sql_str)
            if rst[0][0] == '':
                fix_ava = '0'
            else:
                fix_ava = rst[0][0]
            sql_str = "select b.girl_id from ( select type,girl_id,created_at," \
                      "ceil(time_to_sec(timediff(now(), created_at))/3600) as time, row_number() over(partition by " \
                      "girl_id order by created_at desc) as rn  from ( select type,girl_id,created_at from " \
                      "zoo_girlc_log where created_at>date_add(CURRENT_TIMESTAMP(), interval -3 hour) and girl_id<>0 " \
                      "and type in ('1','2','3','4') union select '3',id,date_add(CURRENT_TIMESTAMP(), interval -200 " \
                      "minute) from zoo_info where id in ({1:s}) ) a ) b join zoo_info c  on b.girl_id=c.id left join (" \
                      "select girl_id,max(case when guest_user_id={0:d} then 1 else 0 end) as is_read,count(1) as cnt " \
                      "from zoo_show_log where created_at>date_add(CURRENT_TIMESTAMP(), interval -3 hour) group by " \
                      "girl_id) d on b.girl_id=d.girl_id where b.rn=1 and b.type in ('1','3') and c.status in " \
                      "('1','2','3') and ifnull(d.is_read,0)=0 and b.time={2:s} order by b.time,c.status desc," \
                      "ifnull(d.cnt,0),b.created_at desc".format(chat_id, fix_ava, text_list[1])
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "目前<b>{1:s}</b>在岗的妹子都<b>已经全部展示</b>给客户：<i>{0:s}</i>".format(
                        f_chats_id(chat_id)[3], explain_area[text_list[1]]),
                    parse_mode=ParseMode.HTML
                    )
            else:
                params['girl_' + str(chat_id)] = text_list[1]
                for row in rst:
                    if 'girl_' + str(chat_id) not in params.keys():
                        return
                    # 发送动物信息
                    if '人' in ident[1]:
                        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out," \
                                  "picture,status,country,special_en from zoo_info where id={0:d}".format(row[0])
                    else:
                        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out," \
                                  "picture,status,country from zoo_info where id={0:d}".format(row[0])
                    await send_girl(sql_str, client, chat_id)
                    # 登记日志文件
                    log_show(chat_id, row[0])
                    await asyncio.sleep(sleep_time)
                if 'girl_' + str(chat_id) in params.keys():
                    del params['girl_' + str(chat_id)]
                if '人' in ident[1]:
                    content_list = [
                        "Bro, these are the girls available in {0:s} hour. Do you like any of them?🫦".format(text_list[1]),
                        "How about these girls who are available in {0:s} hour?🍑".format(text_list[1]),
                        "Bro, all the girls available in {0:s} hour are here, so let me know who you like💦".format(text_list[1]),
                        "Bro, is there a girl you like here? These are the girls available in {0:s} hour😜".format(text_list[1]),
                        "Dude, take your time. These are the girls available in {0:s} hour😁".format(text_list[1]),
                        ]
                else:
                    content_list = [
                        "铁子，这是<b>{0:s}</b>在岗的妹子，有喜欢的吗？🫦".format(explain_area[text_list[1]]),
                        "这批<b>{0:s}</b>在岗的妹子，如何？🍑".format(explain_area[text_list[1]]),
                        "兄弟，<b>{0:s}</b>在岗的妹子都在这里，喜欢谁告诉我编号就行💦".format(explain_area[text_list[1]]),
                        "老铁，这里有你的菜不？这是<b>{0:s}</b>在岗的妹子😜".format(explain_area[text_list[1]]),
                        "兄弟，慢慢看，<b>{0:s}</b>在岗的妹子😁".format(explain_area[text_list[1]]),
                        ]
                await client.send_message(
                    chat_id,
                    content_list[random.randint(0, len(content_list) - 1)],
                    parse_mode=ParseMode.HTML
                )
        # 只展示1个动物
        elif len(text_list) == 2 and re.match('^\d{2,4}(,\d{2,4})+$', text_list[1], re.S) is None:
            # 检查输入的是编号还是名字
            str_input = await input_cert(text_list[1], 'search')
            if str_input[0] == 'no':
                sub_sql_str = "id={0:d}".format(str_input[1])
            elif str_input[0] == 'phone':
                sub_sql_str = "phone like '%{0:s}%'".format(str_input[1])
            else:
                sub_sql_str = "name='{0:s}'".format(str_input[1])
            # 查询具体动物信息
            if '人' in ident[1]:
                sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture," \
                          "status,country,special_en from zoo_info where {0:s}".format(sub_sql_str)
            else:
                sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture," \
                          "status,country from zoo_info where {0:s}".format(sub_sql_str)
            await send_girl(sql_str, client, chat_id)
            # 登记日志文件
            rst = tg_connector.query(sql_str)
            if len(rst) > 0:
                log_show(chat_id, rst[0][0])
        # 批量展示动物
        elif len(text_list) == 2 and re.match('^\d{2,4}(,\d{2,4})+$', text_list[1], re.S) is not None:
            params['girl_' + str(chat_id)] = text_list[1]
            no_list = text_list[1].split(',')
            for no in no_list:
                if 'girl_' + str(chat_id) not in params.keys():
                    break
                # 发送动物信息
                if '人' in ident[1]:
                    sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture," \
                              "status,country,special_en from zoo_info where id={0:s}".format(no)
                else:
                    sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture," \
                              "status,country from zoo_info where id={0:s}".format(no)
                await send_girl(sql_str, client, chat_id)
                # 登记日志文件
                log_show(chat_id, int(no))
                await asyncio.sleep(sleep_time)
            if 'girl_' + str(chat_id) in params.keys():
                del params['girl_' + str(chat_id)]
        # 按优先度展示近7个动物
        elif len(text_list) == 1 and chat_id != 1957712323:
            sql_str = "select content from zoo_note where id='board_available'"
            rst = tg_connector.query(sql_str)
            if rst[0][0] == '':
                fix_ava = '0'
            else:
                fix_ava = rst[0][0]
            sql_str = "select b.girl_id from ( select type,girl_id,created_at," \
                      "ceil(time_to_sec(timediff(now(), created_at))/3600) as time, row_number() over(partition by " \
                      "girl_id order by created_at desc) as rn  from ( select type,girl_id,created_at from " \
                      "zoo_girlc_log where created_at>date_add(CURRENT_TIMESTAMP(), interval -3 hour) and girl_id<>0 " \
                      "and type in ('1','2','3','4') union select '3',id,date_add(CURRENT_TIMESTAMP(), interval -200 " \
                      "minute) from zoo_info where id in ({1:s}) ) a ) b join zoo_info c  on b.girl_id=c.id left join (" \
                      "select girl_id,max(case when guest_user_id={0:d} then 1 else 0 end) as is_read,count(1) as cnt " \
                      "from zoo_show_log where created_at>date_add(CURRENT_TIMESTAMP(), interval -3 hour) group by " \
                      "girl_id) d on b.girl_id=d.girl_id where b.rn=1 and b.type in ('1','3') and c.status in " \
                      "('1','2','3') and ifnull(d.is_read,0)=0 order by b.time,c.status desc,ifnull(d.cnt,0)," \
                      "b.created_at desc".format(chat_id, fix_ava)
            rst = tg_connector.query(sql_str)
            rest_cnt = len(rst)
            if rest_cnt == 0:
                await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "目前在岗的妹子都<b>已经全部展示</b>给客户：<i>{0:s}</i>".format(f_chats_id(chat_id)[3]),
                    parse_mode=ParseMode.HTML
                )
            else:
                params['girl_' + str(chat_id)] = '/girl'
                show_cnt = 0
                for row in rst:
                    if 'girl_' + str(chat_id) not in params.keys():
                        return
                    # 发送动物信息
                    if '人' in ident[1]:
                        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out," \
                                  "picture,status,country,special_en from zoo_info where id={0:d}".format(row[0])
                    else:
                        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out," \
                                  "picture,status,country from zoo_info where id={0:d}".format(row[0])
                    await send_girl(sql_str, client, chat_id)
                    # 登记日志文件
                    log_show(chat_id, row[0])
                    await asyncio.sleep(sleep_time)
                    # 判断结束条件
                    rest_cnt -= 1
                    show_cnt += 1
                    if show_cnt == 7:
                        break
                if 'girl_' + str(chat_id) in params.keys():
                    del params['girl_' + str(chat_id)]
                if '人' in ident[1]:
                    content_list = ["These are the available girls who just update. You like them? 🌊🔥 There are {0:d} girls left to show".format(rest_cnt)
                                    ]
                    finish_list = ["Bro, just show the available girls, you choose if there is any like?",
                                   "Bro, do you like it?😉",
                                   "Take your time, Bro. Pick your number and tell me💦🍌",
                                   "Bro, how about these girls?🚘🚘",
                                   "These are the girls who just said they were available, and who will be lucky enough to get your love?🌊🔥",
                                   "Bro, which do you like?🤗"
                                   ]
                else:
                    content_list = ["铁子，你看看上面是否有你的菜？后面还有 {0:d} 个妹子待推荐🤩".format(rest_cnt),
                                    "铁子，喜欢吗？后面还有 {0:d} 个妹子😉".format(rest_cnt),
                                    "铁子，选好妹子后告诉我编号就好了💦🍌，后面还有 {0:d} 个妹子".format(rest_cnt),
                                    "铁子，如果上面没有喜欢的，后面还有 {0:d} 个妹子🚘🚘".format(rest_cnt),
                                    "这些都是刚报在岗的妹子，喜欢不？🌊🔥后面还有 {0:d} 个".format(rest_cnt)
                                    ]
                    finish_list = ["铁子，刚报空闲的妹子都已经发给你了，你看看是否有喜欢的？",
                                   "铁子，喜欢吗？😉",
                                   "铁子，慢慢看，选好告诉我编号💦🍌",
                                   "铁子，这批如何？🚘🚘",
                                   "这些都是刚报在岗的妹子，看谁有幸得到你的宠幸？🌊🔥",
                                   "铁子，看看喜欢哪个？🤗"
                                   ]
                if rest_cnt == 0:
                    content = finish_list[random.randint(0, len(finish_list) - 1)]
                else:
                    content = content_list[random.randint(0, len(content_list) - 1)]
                await client.send_message(
                    chat_id,
                    content,
                    parse_mode=ParseMode.HTML
                    )
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        if 'girl_' + str(chat_id) in params.keys():
            del params['girl_' + str(chat_id)]


# 查看guest信息
@Client.on_message(filters.outgoing & filters.command("guest"))
async def guest(client, message):
    try:
        text_list = message.text.split(" ")
        chat_id = message.chat.id
        await message.delete()
        user_id = params['user_hugo']
        # 新增客户
        if len(text_list) == 1:
            # 要求新客户必须是普通用户
            if f_chats_id(chat_id)[1] != 'user':
                await clients['myhappygirlsbot'].send_message(user_id,
                                                              "查询的客户必须是<b>普通用户</b>： <i>{0:s}</i>".format(f_chats_id(chat_id)[3]),
                                                              parse_mode=ParseMode.HTML)
            else:
                # 展示客户信息
                await show_guest([chat_id], user_id, 'chat_id')
        elif len(text_list) == 2:
            if re.match('^[0-9]{4}$', text_list[1], re.S) is not None:
                await show_guest([text_list[1]], user_id, 'short_id')
            elif re.match('^[0-9]{4}.$', text_list[1], re.S) is not None:
                await show_guest([text_list[1]], user_id, 'std_id')
            else:
                await show_guest([text_list[1]], user_id, 'outname')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 查看girlc信息
@Client.on_message(filters.outgoing & filters.command("girlc"))
async def girlc(client, message):
    try:
        text_list = message.text.split(" ")
        chat_id = message.chat.id
        await message.delete()
        user_id = params['user_hugo']
        # 新增客户
        if len(text_list) == 1:
            # 查询的动物必须是普通用户
            if f_chats_id(chat_id)[1] != 'user':
                await clients['myhappygirlsbot'].send_message(
                    user_id,
                    "查询的女孩必须是普通用户： ${0:s}".format(f_chats_id(chat_id)[3]),
                    parse_mode=ParseMode.HTML)
            else:
                # 展示girlc信息
                await show_girlc(str(chat_id))
        elif len(text_list) == 2:
            await show_girlc(text_list[1])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# stop尽量展示作业
@Client.on_message(filters.outgoing & filters.command("stop"))
async def stop(client, message):
    try:
        if 'girl_' + str(message.chat.id) in params.keys():
            del params['girl_' + str(message.chat.id)]
        await message.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 手报空闲
@Client.on_message(filters.outgoing & filters.command("ava"))
async def ava(client, message):
    try:
        comm = message.text.upper()
        text_list = comm.split(" ")
        chat_id = message.chat.id
        await message.delete()
        ident = await identity(chat_id, True)
        # 不允许发girl信息给动物
        if ident[0] not in ('3', '4', '5', '6') and chat_id != 1957712323:
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                "<b>不允许</b>发 /ava 命令给非动物 <i>{0:s}</i>".format(ident[1]),
                parse_mode=ParseMode.HTML)
            return
        # 填写日志
        if comm == '/AVA' and chat_id != 1957712323:
            await log_girlc('3', chat_id)
            content = ["💦🍒😁Okay Beb, Im talking with guest now🫶, I recommend you😘 ",
                       "👍Good😘I will recommend you first to next guest, beb🤝",
                       "💦Okay😘my lovely baby. 😍I put you in available list💻",
                       "😊Okay, beb🫰I recommend you now💕",
                       "💋Nice, beb👍🐾I try to give you guest soon🍌🫦",
                       "🔥💧🫦Nice, beb💦😆",
                       "😘Good, beb😘 I will recommend you😆🤭",
                       "✅Okay, beb. I will keep recommending you in 3 hours📤"
                       ]
        elif re.match('^/AVA [0-9]{2,4}$', comm, re.S) is not None:
            await log_girlc('3', girl_id=int(text_list[1]))
            content = "动物 {0:s} 手报空闲成功".format(text_list[1])
        elif re.match('^/AVA [0-9]{2,4},.*', comm, re.S) is not None:
            girl_list = text_list[1].split(',')
            for girl_id in girl_list:
                await log_girlc('3', girl_id=int(girl_id))
            content = "动物 {0:s} 手报空闲成功".format(text_list[1])
        elif re.match('^/AVA K[0-9]+$', comm, re.S) is not None and chat_id != 1957712323:
            await log_girlc('3', chat_id, note=text_list[1])
            content = ["👌OKay, beb😘you are available for {0:s}🌹😘".format(text_list[1]),
                       "🌺Nice, beb💧you are available for requirement {0:s}❤".format(text_list[1]),
                       "✅Good, I will recommend you to the guest of {0:s}😉".format(text_list[1])
                       ]
        elif re.match('^/AVA D[0-9]+$', comm, re.S) is not None and chat_id != 1957712323:
            await log_girlc('5', chat_id, note=text_list[1])
            content = ["🙏OKay, beb💦you accept the research {0:s}🌹😘".format(text_list[1]),
                       "❤Nice, beb😉you meet the requirement {0:s}🫦".format(text_list[1]),
                       "👌Good, I will recommend you to the guest of {0:s}😉".format(text_list[1])
                       ]
        elif re.match('^/AVA K[0-9]+ [0-9]{2,4}$', comm, re.S) is not None and chat_id == 1957712323:
            await log_girlc('3', girl_id=int(text_list[2]), note=text_list[1])
            content = "动物 {0[2]:s} 手报空闲 {0[1]:s} 成功".format(text_list)
        elif re.match('^/AVA D[0-9]+ [0-9]{2,4}$', comm, re.S) is not None and chat_id == 1957712323:
            await log_girlc('5', girl_id=int(text_list[2]), note=text_list[1])
            content = "动物 {0[2]:s} 接受调查 {0[1]:s} 成功".format(text_list)
        else:
            content = "无效命令，请检查后再执行\n{0:s}".format(message.text)
        # 信息反馈
        if isinstance(content, str):
            await clients['myhappygirlsbot'].send_message(params['user_hugo'], content, parse_mode=ParseMode.HTML)
        else:
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
        # 提醒动物与bot2对话
        sql_str = "select count(1) from zoo_girlc a join zoo_girlc_log b on a.user_id=b.user_id where " \
                  "a.user_id={0:d} and a.status in ('30','40') and b.created_at>date_add(current_timestamp()," \
                  "interval - 72 hour)".format(chat_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] == 1:
            content = "You can use bot @myhappyzonebot\n\nEach time you click the <b>available</b> button, the system " \
                      "will recommend you first within <b>3</b> hours. No need to PM me every time"
            await asyncio.sleep(2)
            await client.send_message(chat_id, content, parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 手报忙
@Client.on_message(filters.outgoing & filters.command("noava"))
async def noava(client, message):
    try:
        comm = message.text.upper()
        text_list = comm.split(" ")
        chat_id = message.chat.id
        await message.delete()
        ident = await identity(chat_id, True)
        # 不允许发girl信息给动物
        if ident[0] not in ('3', '4', '5', '6') and chat_id != 1957712323:
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                "<b>不允许</b>发 /noava 命令给非动物 <i>{0:s}</i>".format(ident[1]),
                parse_mode=ParseMode.HTML)
            return
        # 填写日志
        if comm == '/NOAVA' and chat_id != 1957712323:
            await log_girlc('4', chat_id)
            content = ["👌Noted, beb. 😊pls update me again when you are available😘😘😘",
                       "🌺opo, enjoy your time, beb🏖",
                       "😁okay, beb👌 Pls tell me available again when you are ready💋🌹",
                       "👌okay, beb 😘",
                       "Noted, beb😘🫶Give you guest next time💦🍌",
                       "🫣Noted, beb. wait for your next update🫰",
                       "❎Oo, beb. hope you available soon😜"
                       ]
        elif re.match('^/NOAVA [0-9]{2,4}$', comm, re.S) is not None:
            await log_girlc('4', girl_id=int(text_list[1]))
            content = "动物 {0:s} 手报忙成功".format(text_list[1])
        elif re.match('^/NOAVA [0-9]{2,4},.*', comm, re.S) is not None:
            girl_list = text_list[1].split(',')
            for girl_id in girl_list:
                await log_girlc('4', girl_id=int(girl_id))
            content = "动物 {0:s} 手报忙成功".format(text_list[1])
        elif re.match('^/NOAVA K[0-9]+$', comm, re.S) is not None and chat_id != 1957712323:
            await log_girlc('4', chat_id, note=text_list[1])
            content = ["👌Noted, beb. you are busy for {0:s}❤️‍🩹".format(text_list[1]),
                       "🫣Sayang, beb. you are busy for requirement {0:s}🙂".format(text_list[1]),
                       "❎Oo, I will not recommend you to the guest of {0:s}😉".format(text_list[1])
                       ]
        elif re.match('^/NOAVA D[0-9]+$', comm, re.S) is not None and chat_id != 1957712323:
            await log_girlc('6', chat_id, note=text_list[1])
            content = ["✍️Noted beb. you refuse the research {0:s}🙏".format(text_list[1]),
                       "👋Okay, beb. 🥹you refuse the requirement {0:s}😅".format(text_list[1]),
                       "⛔️Oo, I will not recommend you to the guest of {0:s}☺️🥲".format(text_list[1])
                       ]
        elif re.match('^/NOAVA K[0-9]+ [0-9]{2,4}$', comm, re.S) is not None and chat_id == 1957712323:
            await log_girlc('4', girl_id=int(text_list[2]), note=text_list[1])
            content = "动物 {0[2]:s} 手报忙 {0[1]:s} 成功".format(text_list)
        elif re.match('^/NOAVA D[0-9]+ [0-9]{2,4}$', comm, re.S) is not None and chat_id == 1957712323:
            await log_girlc('6', girl_id=int(text_list[2]), note=text_list[1])
            content = "动物 {0[2]:s} 拒绝调查 {0[1]:s} 成功".format(text_list)
        else:
            content = "无效命令，请检查后再执行\n{0:s}".format(message.text)
        # 信息反馈
        if isinstance(content, str):
            await clients['myhappygirlsbot'].send_message(params['user_hugo'], content, parse_mode=ParseMode.HTML)
        else:
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 欢迎并获取动物信息
@Client.on_message(filters.outgoing & filters.command(["wel", "info"]))
async def welcome(client, message):
    try:
        chat_id = message.chat.id
        await message.delete()
        ident = await identity(chat_id, True)
        if ident[0] in ('1', '2', '3', '7'):
            return
        text1 = "Welcome to join us for oncall, my pretty girl😊❤️💋"
        text2 = "Please give me your information, I will show you up💋❤️👏"
        text3 = """💋💋💋My information💋💋💋
Name: must
Age: must
Height: must
Weight: must
Boobs: must
Baby: must
Location: makati/pasay/pasig/..
Special: Such as: 
1、Mixed race(dad and mom different country)
2、speak Chinese language
3、threesome, 
4、blow-j, 
5、massage, 
6、69, 
7、accept toy
8、have toy
9、Abuse sex(SM),
10、shower together, 
11、fair skin or white skin, 
12、cum in mouth(add 1k)
13、shot inside(add 2k)
14、Anal(anus sex)(add 2k)
15、Kiss
16、real boobs
17、good at drinking
18、nuru massage
19、what is your job before except oncall
20、if you have others you can do very well,you can also tell me
Phone: must
Wechat: not must
Telegram: not must
Facebook: not must
Price: your price ask
  A: 1 hours 1 pop(optional)
  B: 3 hours 2 pops
  C: 6 hours 3 pops
  D: one night unlimit pops(8-10hours)
Picture: 2 whole body pics/3 close up pic"""
        if message.text.lower() == "/wel":
            await client.send_message(chat_id, text1, parse_mode=ParseMode.HTML)
            for i in range(10):
                await asyncio.sleep(10)
                async for m in client.get_chat_history(chat_id, limit=1):
                    last_meg = m
                if last_meg.text != text1:
                    break
        await client.send_message(chat_id, text2, parse_mode=ParseMode.HTML)
        await asyncio.sleep(3)
        await client.send_message(chat_id, text3, parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 跟客人确认地址
@Client.on_message(filters.outgoing & ~filters.reply & filters.command(["addr"]))
async def address(client, message):
    try:
        chat_id = message.chat.id
        await message.delete()
        ident = await identity(chat_id, True)
        if ident[0] not in ('0', '1', '2'):
            return
        sql_str = "select addr,phone from zoo_guest where chat_id={0:d}".format(chat_id)
        rst = tg_connector.query(sql_str)
        addr = rst[0][0].split('/')[0].strip()
        if "人" not in ident[1]:
            if len(addr) == 0 and rst[0][1] == '':
                content = ["😉铁子，麻烦提供一下 🏩约会地址 和  📞紧急联系电话",
                           "🫡麻烦提供一下 <b>约会地址</b> 和 <b>紧急联系电话</b>",
                           "铁子，麻烦提供一下 见面地址 和 紧急联系电话",
                           "铁子，约会地址🏨 和 紧急联系电话☎️，麻烦提供一下",
                           "请提供一下 <b>见面地址</b> 和 <b>紧急联系电话</b>😊"
                           ]
                await client.send_message(chat_id, content[random.randint(0, len(content) - 1)],
                                          parse_mode=ParseMode.HTML)
            elif len(addr) == 0:
                content = ["😉铁子，麻烦提供一下 🏩约会地址",
                           "🫡麻烦提供一下 <b>约会地址</b>",
                           "铁子，麻烦提供一下 见面地址",
                           "铁子，约会地址🏨，麻烦提供一下",
                           "请提供一下 <b>见面地址</b>😊"
                           ]
                await client.send_message(chat_id, content[random.randint(0, len(content) - 1)],
                                          parse_mode=ParseMode.HTML)
            elif len(addr) > 0:
                msg = await client.send_message(chat_id, addr, parse_mode=ParseMode.HTML)
                content = ["😉铁子，老地方？",
                           "😊老地方？",
                           "铁子，🏩地址对吗？",
                           "还是原来的📍地址？",
                           "还是在这里见面？💞"
                           ]
                await asyncio.sleep(1)
                await msg.reply_text(content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
                if rst[0][1] == '':
                    await asyncio.sleep(2)
                    await msg.reply_text("麻烦也提供一下<b>紧急联系电话</b>\n放心，我平时不打扰你的，就怕妹子去到，客人没注意看手机，"
                                         "妹子在陌生环境找不到人，会很害怕的😅", parse_mode=ParseMode.HTML)
        else:
            if len(addr) == 0 and rst[0][1] == '':
                content = ["😉Bro, pls send me the 🏩address and 📞phone number",
                           "🫡pls send me <b>address</b> 和 <b>phone number</b>",
                           "Bro, address🏨 and phone number☎️, pls send to me"
                           ]
                await client.send_message(chat_id, content[random.randint(0, len(content) - 1)],
                                          parse_mode=ParseMode.HTML)
            elif len(addr) == 0:
                content = ["😉Bro, pls send me 🏩address",
                           "🫡Pls send me <b>address</b>",
                           "Bro, address🏨, pls send to me",
                           "Ple send me <b>address</b>😊"
                           ]
                await client.send_message(chat_id, content[random.randint(0, len(content) - 1)],
                                          parse_mode=ParseMode.HTML)
            elif len(addr) > 0:
                msg = await client.send_message(chat_id, addr, parse_mode=ParseMode.HTML)
                content = ["😉Bro, the same place",
                           "😊the same place?",
                           "Bro, 🏩is this address right?",
                           "Is it the same 📍address?",
                           "still meet at this place? 💞"
                           ]
                await asyncio.sleep(1)
                await msg.reply_text(content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
                if rst[0][1] == '':
                    await asyncio.sleep(2)
                    await msg.reply_text("pls send me <b>phone number</b>\nDon't worry, I will not bother you, just "
                                         "afraid of the girl arrive, and you don't pay attention to the phone, the "
                                         "girl in a strange environment can not find people, will be very afraid",
                                         parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 查询客人或者动物的电话
@Client.on_message(filters.outgoing & ~filters.reply & filters.command(["phone"]))
async def phone(client, message):
    try:
        chat_id = message.chat.id
        await message.delete()
        ident = await identity(chat_id, True)
        if ident[0] in ('0', '1', '2'):
            sql_str = "select phone from zoo_guest where chat_id={0:d}".format(chat_id)
            rst = tg_connector.query(sql_str)
            if rst[0][0] in ('', '-'):
                await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "客户 <i>{0[3]:s}</i> 没有登记的电话".format(f_chats_id(chat_id)),
                    parse_mode=ParseMode.HTML)
            else:
                msg = await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "客户 <i>{0[3]:s}</i> 的电话:".format(f_chats_id(chat_id)),
                    parse_mode=ParseMode.HTML)
                await msg.reply_text(rst[0][0].split('/')[0].strip())
        elif ident[0] == '3':
            girlc_exp = await explain_girlc(chat_id)
            sql_str = "select phone from zoo_info where id={0[3]:d}".format(girlc_exp)
            rst = tg_connector.query(sql_str)
            if rst[0][0] in ('', '-'):
                await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "动物 <i>{0[3]:s}</i> 没有登记的电话".format(f_chats_id(chat_id)),
                    parse_mode=ParseMode.HTML)
            else:
                msg = await clients['myhappygirlsbot'].send_message(
                    params['user_hugo'],
                    "动物 <i>{0[3]:s}</i> 的电话:".format(f_chats_id(chat_id)),
                    parse_mode=ParseMode.HTML)
                await msg.reply_text(rst[0][0].split('/')[0].strip())
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 登记地址和电话
@Client.on_message(filters.outgoing & filters.text & filters.reply & filters.command(['addr', 'phone'],['/', '']))
async def add_addr_phone(client, message):
    try:
        chat_id = message.chat.id
        text = message.text.lstrip('/')
        re_msg = message.reply_to_message
        input_text = re_msg.text.strip()
        await message.delete()
        ident = await identity(chat_id, True)
        if ident[0] not in ('0', '1', '2'):
            return
        sql_str = "select addr,phone from zoo_guest where chat_id={0:d}".format(chat_id)
        rst = tg_connector.query(sql_str)
        if text.lower() == 'addr':
            new_addr = input_text
            addr_list = rst[0][0].split('/')
            i = 1
            for item in addr_list:
                if input_text == item.strip():
                    continue
                if i > 2 or item in ('', '-'):
                    break
                new_addr += '/' + item.strip()
                i += 1
            sql_str = "update zoo_guest set addr='{1:s}' where chat_id={0:d}".format(chat_id, new_addr[:255])
            tg_connector.exe(sql_str)
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                "<i>{0[3]:s}</i> 的新地址：\n{1:s}".format(f_chats_id(chat_id), new_addr[:255]),
                parse_mode=ParseMode.HTML)
        else:
            new_phone = input_text
            phone_list = rst[0][1].split('/')
            i = 1
            for item in phone_list:
                if input_text == item.strip():
                    continue
                if i > 2 or item in ('', '-'):
                    break
                new_phone += '/' + item.strip()
                i += 1
            sql_str = "update zoo_guest set phone='{1:s}' where chat_id={0:d}".format(chat_id, new_phone[:50])
            tg_connector.exe(sql_str)
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                "<i>{0[3]:s}</i> 的新电话：\n{1:s}".format(f_chats_id(chat_id), new_phone[:50]),
                parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 查询客人或者动物的电话
@Client.on_message(filters.outgoing & ~filters.reply & filters.command(["c", "l", "e", "w"]))
async def pay(client, message):
    try:
        chat_id = message.chat.id
        command = message.text.lstrip('/').lower()
        await message.delete()
        ident = await identity(chat_id, True)
        if ident[0] not in ('0', '3', '4', '5', '6') or re.match('[clew] \S+ \S+ .*', command, re.S) is None:
            return
        match = re.match('([clew]) (\S+) (\S+) (.*)', command, re.S)
        # 敏感户收款限制
        if match.group(1) in ('c', 'l') and chat_id in (**********, **********, **********):
            await clients['myhappygirlsbot'].send_message(params['user_hugo'],
                                                          "请注意，敏感户要用<b>wgf</b>账户收款",
                                                          parse_mode=ParseMode.HTML)
            return
        account = {
            "c": "Send Gcash account\n9392401020\nHA***N L\n\n",
            "l": "Send Gcash account\n9776986953\nHA***G L\n\n",
            "w": "Send Gcash account\n9692175311\nW* GU**G N\n\n",
            "e": "Send Gcash account\n9456959557\nQi***g C\n\n"
        }
        acc_notice = "Never never transfer money directly through the machine of 711 convenience store. " \
                     "Please transfer money through Gcash's mobile app  "
        price = """To remind of the rules, please do not give your contact to the guest. I have deleted some girls who did it.

COUNT THE RIGHT MONEY BEFORE LEAVE

After stay with the guest <b>{0:s}</b> hours, guest will give you <b>{1:s}</b>, you get <b>{2:s}</b>, please transfer the rest to me via Gcash

(Please load money in your Gcash first and then send to me, because it need fee if load directly)"""\
            .format(match.group(2), match.group(3), match.group(4))
        msg1 = await client.send_message(chat_id, price, parse_mode=ParseMode.HTML)
        await asyncio.sleep(1)
        msg2 = await client.send_message(chat_id, account[match.group(1)] + acc_notice, parse_mode=ParseMode.HTML)
        await msg1.pin(False, True)
        await msg2.pin(False, True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 查询客人或者动物的电话
@Client.on_message(filters.outgoing & ~filters.reply &
                   filters.command(["meet", "grab", "arr", "paid", "join", "price1", "price2", "photo", "nuru", "av"]))
async def others(client, message):
    try:
        chat_id = message.chat.id
        command = message.text.lstrip('/').lower()
        await message.delete()
        ident = await identity(chat_id, True)
        if ident[0] in ('1', '2') and command == 'meet' and '人' in ident[1]:
            content = ["🎉Bro，Enjoy🍌Let me know if you have any problem💦",
                       "👏Bro，Enjoy🍌I online 24hrs, you can ask me if any problem💦",
                       "❤️💦Sex moment worths weight in gold💰Bro，Tell me if any problem📞",
                       "🎉Bro，Rise up🍌💦🔥contact me if any problem☎️"
                       ]
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
        elif ident[0] in ('1', '2') and command == 'meet':
            content = ["🎉铁子，好好享用🍌有任何问题随时找我💦",
                       "👏兄弟，好好享用🍌有任何问题随时找我💦",
                       "❤️💦春宵一刻值千金💰兄弟，有问题随时联系📞",
                       "🎉铁子，雄起🍌💦🔥有问题随时联系我☎️"
                       ]
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
        elif ident[0] in ('3', '4', '5') and command == 'meet':
            content = ["😘Enjoy,beb💋 Tell me if any problem📞",
                       "Enjoy, beb Tell me if any problem",
                       "🍌Enjoy beb💦 Tell me if any problem🫦",
                       "🌋🌊Enjoy beb💦 Tell me if any problem☎️"
                       ]
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
            await log_girlc('4', chat_id)
        elif ident[0] in ('3', '4', '5') and command == 'grab':
            await client.copy_message(chat_id, params['my_channel'], 60062, protect_content=True)
            await asyncio.sleep(5)
            content = ["😜Don't forget to bring <u><b>🎫VALID ID</b></u> and <u><b>🛟CONDOM</b></u>",
                       "🔊Remember to bring <u><b>🎫VALID ID</b></u> and <u><b>🛟CONDOM</b></u>",
                       "<u><b>🎫VALID ID</b></u> and <u><b>🛟CONDOM</b></u> is important💦",
                       "<u><b>🎫VALID ID</b></u> and <u><b>🛟CONDOM</b></u>, get it😘"
                       ]
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
        elif ident[0] in ('3', '4', '5') and command == 'arr':
            content = ["Beb, send me a 📷<b>photo of you</b> and a 📷<b>photo around you</b>🫴",
                       "👍Nice👍 Pls send me a 📷<b>photo of you</b> and a 📷<b>photo around you</b>",
                       "🎉Good, Pls send me photos:\n📷<b>photo of you</b>\n📷<b>photo around you</b>",
                       "Please send me a 📷<b>photo of you</b> and a 📷<b>photo around you</b>\n"
                       "So that guest can recognize you accurately😘💞"
                       ]
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
        elif ident[0] in ('3', '4', '5') and command == 'paid':
            content = ["🙏🙏🙏thank you my love. \nAre you available again?😜",
                       "👏Thank you beb. \n😁Do you want me recommend you again now?",
                       "😘🫦Thank you my love😘🫦 \n😉Want me recommend again?🙃",
                       "🥳Thank you for your hard work, beb🥳\n😆want guest again now?😂",
                       "😘Thank you beb. Happy to work with you.\n🤣Want to work again?"
                       ]
            await client.send_message(chat_id, content[random.randint(0, len(content) - 1)], parse_mode=ParseMode.HTML)
            await asyncio.sleep(1)
            async for m in client.get_chat_history(chat_id, limit=1, offset=1):
                last_meg = m
            await last_meg.react("🙏")
        elif ident[0] in ('0', '3', '4', '5', '6') and command == 'join':
            content = ["Join my channel @happyzonegirl \nAnd pls tell your friend to join the channel too, "
                       "pm me if have any questions.\n\nDon't worry, nobody can see you in the channel.",
                       "Beb, pls notice my message in channel, and pm me when available, "
                       "coz we only recommend girls who is available in 3 hours👏"
                       ]
            await client.send_message(chat_id, content[0], parse_mode=ParseMode.HTML)
            await asyncio.sleep(12)
            await client.send_message(chat_id, content[1], parse_mode=ParseMode.HTML)
            await asyncio.sleep(7)
            await client.copy_message(chat_id, params['my_channel'], 62086, protect_content=True)
        elif ident[0] in ('0', '3', '4', '5', '6') and command == 'price1':
            await client.send_message(chat_id,
                                      """We have 4 packages, give me your clean price for each package you want
  A: 1 hours 1 pop(optional)
  B: 3 hours 2 pops
  C: 6 hours 3 pops
  D: one night unlimit pops(8-10hours)

We add the same commission for each girl. If you want to put higher price, we respect, but too high price maybe less guests""",
                                      parse_mode=ParseMode.HTML)
        elif ident[0] in ('0', '3', '4', '5', '6') and command == 'price2':
            await client.send_message(chat_id,
                                      """Is this clean price OK for you? Many beautiful girls use this price.
Package
  A: 1 hours 1 pop(optional) 4k
  B: 3 hours 2 pops 5k
  C: 6 hours 3 pops 7k
  D: one night unlimit pops(8-10hours) 9k""",
                                      parse_mode=ParseMode.HTML)
        elif ident[0] in ('0', '3', '4', '5', '6') and command == 'photo':
            await client.send_message(chat_id,
                                      "Pls send me separate 📸photo & 📽video😘\n\n If you have photos about "
                                      "👙<b>uniform</b>, 🧦<b>stockings</b> or 🪇<b>toys</b>, pls send me too, "
                                      "it can attract guests easier💵",
                                      parse_mode=ParseMode.HTML)
        elif command == 'nuru':
            await client.copy_message(chat_id, params['my_channel'], 60238, protect_content=True)
        elif command == 'av':
            sql_str = "select a.orders,case when b.user_id is not null then b.chat_id when a.orders>0 then c.chat_id " \
                      "else 0 end, case when b.user_id is not null then '1' else '0' end from zoo_guest a left join " \
                      "zoo_guest_video b on a.chat_id=b.user_id join (select chat_id,count(1) from zoo_guest_video " \
                      "group by chat_id order by 2 limit 1) c on 1=1 where a.chat_id={0:d}".format(chat_id)
            rst = tg_connector.query(sql_str)
            link = {
                '-1001941346041': 'https://t.me/+RJfoplqmKOtmMjI9',
                '-1001824315964': 'https://t.me/+l53jvyd5BnE0YmNl',
                '-1001924419067': 'https://t.me/+aVCB6uA6Tjk2MzBl'
            }
            if len(rst) == 0 or rst[0][0] == 0:
                await clients['myhappygirlsbot'].send_message(params['user_hugo'],
                                                              "只有有效客户才能欣赏🎥精彩视频",
                                                              parse_mode=ParseMode.HTML)
            else:
                await client.send_message(
                    chat_id,
                    e_jin18 + "老铁，我们有色群🍌💦，有空可以去专研学习一下，👉<a href='{0:s}'>欢乐园福利</a>👈\n\n".format(link[str(rst[0][1])]) +
                    e_light + "温馨提醒：<u>申请加入</u>后<b>再次点击链接</b>即可进入频道",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True,
                    protect_content=True
                )
                if rst[0][2] == '0':
                    sql_str = "insert into zoo_guest_video values({0:d}, {1:d})".format(chat_id, rst[0][1])
                    tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取到客户信息
@Client.on_message(~filters.bot & filters.private & filters.incoming, group=-1)
async def get_guest_message(client, message):
    try:
        user_id = message.from_user.id
        # # 不良客户自动回复
        # if user_id in (1848022016, 5564475951, 5532668869, 5522271459):
        #     await asyncio.sleep(2)
        #     await client.read_chat_history(chat_id)
        #     await message.reply_chat_action(ChatAction.TYPING)
        #     await asyncio.sleep(5)
        #     await message.reply("你好，你不是有诚意下单的，请不要骚扰，我忙着呢，如果真要下单，请先转1000rmb订金！")
        # 根据客户身份确认是否要提醒服务
        sql_str = "select status_chat from zoo_guest where chat_id={0:d} and status_chat<>'0' ".format(user_id)
        rst = tg_connector.query(sql_str)
        if len(rst) == 0:
            is_remind = True
        else:
            async for m in client.get_chat_history(user_id, limit=1, offset=1):
                last_meg = m
            if rst[0][0] == '1' and (last_meg.date is None or (message.date - last_meg.date).total_seconds() > 3600):
                is_remind = True
            else:
                is_remind = False
        # 更新客户资料
        sql_str = "select count(1) from zoo_guest where chat_id={0:d}".format(user_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] == 0:
            sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_chat,remind_at,active_at," \
                      "created_at) values({0[0]:d},'{1:s}','{0[2]:s}','{0[5]:s}','{0[3]:s}','2',CURRENT_TIMESTAMP()," \
                      "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP())".format(f_chats_id(user_id), user_to_std(user_id))
            tg_connector.exe(sql_str)
        else:
            sql_str = "update zoo_guest set name='{0[2]:s}',username='{0[5]:s}',outname='{0[3]:s}',status_chat=" \
                      "if(status_chat in ('0','2'), '2', status_chat),active_at=CURRENT_TIMESTAMP() where chat_id={0[0]:d}"\
                .format(f_chats_id(user_id))
            tg_connector.exe(sql_str)
        # sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_chat,remind_at,active_at," \
        #           "created_at) values({0[0]:d},(select * from (select concat('{1:s}',char(65+(select count(1) from " \
        #           "zoo_guest where std_id like '{1:s}%')))) as k),'{0[2]:s}','{0[5]:s}','{0[3]:s}','2'," \
        #           "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP()) on duplicate key update " \
        #           "name='{0[2]:s}',username='{0[5]:s}',outname='{0[3]:s}',status_chat=if(status_chat in ('0','2'), " \
        #           "'2', status_chat),active_at=CURRENT_TIMESTAMP()" \
        #     .format(f_chats_id(user_id), str(user_id)[-4:])
        # tg_connector.exe(sql_str)
        # 对于需要提醒的客户，等待n分钟检查回复情况后提醒
        if is_remind:
            now = datetime.datetime.now()
            if now.hour in range(4, 16):
                params['remind_' + str(user_id)] = "您好，我是机器人小欢！\n客服人员开小差离开了，请拨打电话 09064745529"
            else:
                params['remind_' + str(user_id)] = "您好，我是机器人小欢！\n客服人员开小差离开了，请拨打电话 09275356701"
            await asyncio.sleep(120)
            if 'remind_' + str(user_id) in params.keys():
                await client.send_message(user_id, params['remind_' + str(user_id)], parse_mode=ParseMode.HTML)
                del params['remind_' + str(user_id)]
                # 测试收藏夹收到此消息的原因
                await message.forward(params['user_hugo'])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给客户发送信息
@Client.on_message(~filters.bot & filters.private & filters.outgoing, group=-1)
async def send_guest_message(client, message):
    try:
        chat_id = message.chat.id
        # 回复信息后，删除提醒记录
        if 'remind_' + str(chat_id) in params.keys():
            del params['remind_' + str(chat_id)]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# # 测试群成员异动
# @Client.on_chat_member_updated(group=-999)
# async def member_updated(client, chat_member_updated):
#     try:
#         print(chat_member_updated)
#     except Exception as e:
#         sys_log.write_log(traceback.format_exc(), 'a')


# 踢走主动加入“评论”群的客户
@Client.on_message(filters.new_chat_members & filters.chat(gm_pinglun.chat_id))
async def kick_join_disc(client, message):
    try:
        chat_id = message.chat.id
        for msg in message.new_chat_members:
            user_id = msg.id
            await clients['myhappygirlsbot'].send_message(chat_id,
                                                          "尊敬的客户 {0:s}，请关注 @happyzone168 选妃，不需要加入本群，谢谢您的支持！"
                                                          .format(f_chats_id(user_id)[6]))
            await asyncio.sleep(5)
            await message.delete()
            await asyncio.sleep(20)
            ban_msg = await client.ban_chat_member(chat_id, user_id, datetime.datetime.now() + datetime.timedelta(seconds=35))
            await asyncio.sleep(5)
            await ban_msg.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取评论区的“讨论”消息
@Client.on_message(filters.chat(gm_pinglun.chat_id) & (filters.photo | filters.video | filters.document) & filters.reply)
async def get_disc_media(client, message):
    try:
        # 只接受hugo发送的信息处理
        if message.sender_chat is None or message.sender_chat.id != gm_pinglun.chat_id:
            return
        rst_check = await check_media_group(client, message)
        if rst_check[0] == '2':
            return
        # 对于以文件形式上传的媒体，作特殊处理
        if message.document is not None:
            # 提示已上传媒体是文件，提示减少媒体尺寸
            send_msg = await message.reply_text("Tg以文件形式上传视频，不符合要求！")
            # 删除已上传文件
            await asyncio.sleep(15)
            if rst_check[0] == '1':
                list_del_msg = []
                for msg in rst_check[1]:
                    list_del_msg.append(msg.id)
                await client.delete_messages(message.chat.id, list_del_msg)
            else:
                await message.delete()
            # 删除原消息
            await asyncio.sleep(5)
            await send_msg.delete()
            return
        # 生成待发送的文本内容
        sql_str = "select a.ind,b.country from (select * from chat_message where chat_id={0:d} and kind='{1:s}' and " \
                  "msg_list = ',{2:d},') a join zoo_info b on a.ind=b.id"\
            .format(gm_pinglun.chat_id, gm_pinglun.kind, message.reply_to_message_id)
        rst = tg_connector.query(sql_str)
        # 下载并给媒体打水印
        id_list = []
        msg_id_list = []
        for msg in rst_check[1]:
            msg_id_list.append(msg.id)
            row_media_online = await media.save(client, msg, 'both')
            row_media = await media.get(row_media_online[0])
            id_list.append(row_media[0])
            adds = 'media/' + row_media[1] + '/' + row_media[3] + '.' + row_media[4]
            adds_tmp = 'media/' + row_media[1] + '/' + row_media[3] + '_tmp.' + row_media[4]
            if row_media[1] == 'photo':
                add_pic_mark(adds, 'media/' + row_media[1] + '/', "欢乐园 tg: happyzone168")
            else:
                # 调整打视频水印之后代码
                try:
                    loop = asyncio.get_running_loop()
                    # 在单独的线程中处理视频
                    process_thread = threading.Thread(target=add_video_mark, args=(adds, adds_tmp, "欢乐园 tg: happyzone168"))
                    process_thread.start()
                    # 设置处理的超时时长
                    await asyncio.wait_for(loop.run_in_executor(None, process_thread.join), timeout=60)
                    # 把新生成的视频代替原视频
                    await loop.run_in_executor(None, os.rename, adds_tmp, adds)
                    msg_text = "动物 {0:s} 的视频已处理".format(rst[0][0])
                except asyncio.TimeoutError:
                    msg_text = "动物 {0:s} 的视频无法打水印".format(rst[0][0])
        # 重新发送媒体到同步频道
        for id in id_list:
            try:
                await media.update_resend(id, 'happy_167')
            except FloodWait as e:
                await asyncio.sleep(e.value)
        if len(rst) > 0:
            content = "{0:s} 贵妃: {1[0]:s}-{2:s}\n".format(e_girl, rst[0], mean("country", rst[0][1]))
        else:
            content = "{0:s} 选妃须知 {0:s}\n".format(e_light)
        if message.text is not None:
            content += "{0:s} 评论: ".format(e_pinglun) + message.text
        elif message.caption is not None:
            content += "{0:s} 评论: ".format(e_pinglun) + message.caption
        else:
            content += "{0:s} 评论: 无".format(e_pinglun)
        # 发送媒体
        media_list = await media.get_media(client, map(str, id_list), content)
        try:
            send_msg = await client.send_media_group(message.chat.id,
                                                     media_list,
                                                     disable_notification=True,
                                                     reply_to_message_id=message.reply_to_message_id
                                                     )
            head_ind = str(rst[0][0]) if len(rst) > 0 else '0'
            gm_pinglun.add_row(head_ind+'#'+'|'.join(map(str, id_list)), send_msg, '讨论')
            # 删除原消息
            await asyncio.sleep(2)
            await client.delete_messages(gm_pinglun.chat_id, msg_id_list)
        except FloodWait as e:
            await asyncio.sleep(e.value)
        # 删除本地中已上传的文档
        for id in id_list:
            await media.del_file(id)
        # 把处理结果反馈给hugo
        disc_link = await get_disc_link(rst[0][0])
        msg_text += " <a href='{0[0]:s}'>详情</a>".format(disc_link)
        await clients['myhappyzonebot'].send_message(params['user_hugo'],
                                                     msg_text,
                                                     parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 删除信息同步
@Client.on_deleted_messages(filters.chat([gm_pinglun.chat_id, cm_pinglun.chat_id, cm[0].chat_id]))
async def del_disc_msg(client, messages):
    try:
        for message in messages:
            if message.chat.id == gm_pinglun.chat_id:
                chat_message = gm_pinglun
            elif message.chat.id == cm_pinglun.chat_id:
                chat_message = cm_pinglun
            elif message.chat.id == cm[0].chat_id:
                chat_message = cm[0]
            row_msg = chat_message.get_from_msg(",{0:d},".format(message.id))
            for row in row_msg:
                chat_message.del_row(row[3], row[2])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复翻译
@Client.on_message(filters.outgoing & filters.text & filters.reply & filters.command(['t', 'T'], ['/', '']))
async def trans_reply_message(client, message):
    try:
        re_msg = message.reply_to_message
        origin_text = re_msg.text.strip()
        await message.delete()
        dest_text = trans(origin_text)
        await clients['myhappygirlsbot'].send_message(
            params['user_hugo'],
            f"译文：{dest_text}\n\n原文：<i>{origin_text}</i>",
            parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发送翻译
@Client.on_message(filters.outgoing & filters.text & ~filters.reply & filters.command(['t', 'T']))
async def trans_send_message(client, message):
    try:
        origin_text = message.text[3:]
        chat_id = message.chat.id
        await message.delete()
        dest_text = trans(origin_text)
        await client.send_message(chat_id, dest_text)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复修改英文特点
@Client.on_message(filters.outgoing & filters.text & filters.command(['special_en', 'Special_en']) & filters.chat(params['bot_mygirl']))
async def reply_change_special(client, message):
    try:
        text = message.text.lower().replace("'", "").replace("delete", "").replace("update", "").replace("alter", "").replace("insert", "")
        if re.match('^/[s|S]pecial_en \d{2,4}\|.*', text, re.S) is not None:
            match = re.match('^/special_en (\d{2,4})\|(.*)', text, re.S)
            id = match.group(1)
            special_en = match.group(2)
            sql_str = f"update zoo_info set special_en='{special_en}' where id={id}"
            tg_connector.exe(sql_str)
            sql_str = f"select special_en from zoo_info where id={id}"
            special_new = read_connector.query(sql_str)[0][0]
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                f"动物 {id} 的special_en已经更新为：{special_new}")
            # 更新英文主频道
            await update_home_en(id)
            await show_board_en()
            await update_sub_chl_en()
        else:
            await clients['myhappygirlsbot'].send_message(
                params['user_hugo'],
                "输入格式错误，输入格式应该是：/special_en 1234|ABCD")
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 管理账号测试
@Client.on_message(filters.chat(1914064729))
async def test(client, message):
    try:
        print("hugo看到的消息")
        print(f"我目前使用的代理是 {client.proxy}")
        # print(message)
        # rst = await clients['myhappygirlsbot'].get_chat(-1002498182762)
        # print(rst)
        # rst = await clients['myhappygirlsbot'].send_message('happyzone666',
        #                                                     text='test1')
        # await asyncio.sleep(5)
        # await client.send_message(int(message.text),
        #                           text='1')

        await message.reply_text(message.text)

        chat_list = await client.get_common_chats(message.chat.id)
        for chat in chat_list:
            print(f"{chat.id}:{chat.type }->{chat.title}")

        for chat in chat_list:
            print(chat)


        # await clients['myhappygirlsbot'].send_message(int(message.text),
        #                                               text='2')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')




