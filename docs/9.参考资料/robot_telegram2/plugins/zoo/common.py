# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from classes2 import *
from datetime import datetime
from pyrogram.types import ReplyKeyboardMarkup, ReplyKeyboardRemove, ForceReply, InputMediaPhoto, InputMediaVideo, InputMediaDocument


sa = [1999226611]
# admin = [1999226611, 1969161034, 2070026339]
admin = [1999226611]
# 群组列表
group_chat_list = [-1001924419067, -1001824315964, -1001941346041]
# 参数池
params = {'user_hugo': 1999226611,
          'user_wendy': 925635997,
          'g_jiankong': -4506030350,
          'bot_yuanzhang': 2048921226,
          'bot_mygirl': 1957712323,
          'bot_mygirl2': 5404111465,
          'home_chat_id': -1001526949053,
          'home_chat_id2': -1001526949053,
          'group_chat_idx_video': 1,
          'my_channel': -1001569364480,
          'video_source': -1001603231468,
          'g_pinglun_id': -1001759477013,
          'c_pinglun_id': -1001655562503,
          'g_in_id': 2,
          'c_report_id': -1001510855731,
          'c_girlc_id': -1001782861239,
          'c_en': -1002498182762,
          'c_en2': -1002498182762,
          'c_new_en': -1002438138215,
          'c_avail': -1002350756936
          }
media = Media(params['my_channel'], 'zoo_in', ['zoo_in'])
# 由于加人功能无效，此类暂不用
# supplier = GroupSupply('zoo_user', 'zoo_client', -1001526949053)
# line = "----------------------\n"
# (e_market, e_myOffer, e_trade, e_report, e_safe, e_complaint, e_adv, e_service, e_remind, e_money,
#  e_return, e_home, e_warning, e_succ, e_to, e_left, e_right, e_up, e_down, e_fire,
#  e_left2, e_right2, e_up2, e_down2, e_ok, e_no, e_heart_black, e_heart_bad, e_heart_green, e_heart_orange,
#  e_heart_red, e_flower, e_lip, e_sex_wear, e_ruler, e_2girl, e_phone, e_girl, e_loudou, e_mail,
#  e_id, e_photo, e_comfirm, e_search, e_girls, e_write, e_baby, e_eyes, e_zoushi, e_traffic,
#  e_handup1, e_handup2, e_location, e_delete, e_light, e_manhat, e_girlhat, e_ice, e_prize, e_han,
#  e_set, beg_girl, e_left3, e_right3, e_up3, e_down3, e_gou, e_hezuo, e_travel, e_he,
#  e_hotheart, e_cycle, e_kong, e_gift, e_guests, e_sign, e_bell, e_man, e_man2, e_manhat2,
#  e_bot, e_time1, e_time2, e_time3, e_dna, e_add, e_reduce, e_circle, e_pinglun, e_fenlei,
#  e_shandian, e_video, e_cty, e_cty0, e_cty1, e_cty2, e_cty3, e_cty4, e_man3, e_star,
#  e_group, e_talk, e_cha, e_b, e_sound, e_help, e_clap, e_girl2, e_smile, e_outbox,
#  e_task, e_girl_, e_mai, e_change, e_inbox, e_msg, e_refuse, e_cty5, e_alert, e_noalert,
#  e_smile2, e_weiqu, e_report2, e_unknow, e_file, e_page, e_replace, e_falang, e_jin18) = \
# ("💹", "📝", "⚖", "💯", "🛡", "🈲", "📢", "💁‍♀‍", "🎈", "💰",
#  "🔙", "🏠", "⚠️", "🎉", "👉", "⬅", "➡", "⬆", "⬇", "🔥",
#  "◀️", "▶️", "🔼", "🔽", "👌", "❌", "🖤", "💔", "💚", "🧡",
#  "❤", "🌹", "💋", "👙", "📏", "👩‍👧", "📞", "👩 ", "⏳", "📮",
#  "🎫", "📸", "⁉️", "🔎", "👩‍👩‍👧‍👧", "✍️", "🤱", "👀", "📈", "🚥",
#  "🙋", "🙋‍♂️", "📍", "❌", "💡", "🎩", "👒", "❄️", "🏅", "💦",
#  "⚙️", "🙇‍♀️", "👈", "👉", "👆", "👇", "✅", "🤝", "🏖", "🈴",
#  "❤️‍🔥", "♻️", "🈳", "🎁", "👨‍👨‍👦‍👦", "📡", "🛎", "🙍🏻‍♂️", "💁🏻‍♂️", "🧢",
#  "🤖", "⏱", "⏰", "🕰", "🧬", "➕", "➖", "⭕️", "💬", "🗂",
#  "⚡️", "🎥", "🏳️‍⚧️", "🇺🇳", "🇵🇭", "🇨🇳", "🇻🇳", "🇷🇺", "🙋🏻‍♂️", "🌟",
#  "👯‍♀️", "🗣", "❎", "🅱️", "🔊", "🙏", "👏", "🙋‍♀️", "😊", "📤",
#  "🔰", "👩", "🎙", "🔄", "📥", "📧", "⛔️", "🇹🇭", "🔔", "🔕",
#  "😉", "🥺", "🚨", "❓", "🔖", "📜", "🎭", "💈", "🔞")
# e_num = ["0️⃣ ", "1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣", "🔟"]


# 字段转义
def mean(type, text='', point='to'):
    to_dic = {}
    # 设定转义内容
    if type == 'info_level':
        to_dic = {0: 'unknow', 1: 'very bad', 2: 'bad', 3: 'common', 4: 'good', 5: 'very good'}
    elif type == 'info_level_cn':
        to_dic = {0: e_ice, 1: e_fire*1, 2: e_fire*2, 3: e_fire*3, 4: e_fire*4, 5: e_fire*5}
    elif type == 'info_status':
        to_dic = {'0': 'block', '1': 'online', '2': 'onsale', '3': 'pause', '4': 'verifying'}
    elif type == 'info_status_cn':
        to_dic = {'0': '关闭', '1': '在线', '2': '忙碌', '3': '私家推荐', '4': '待验证'}
    elif type == 'info_status_en':
        to_dic = {'0': 'Close', '1': 'Online', '2': 'Busy', '3': 'Private Recommend', '4': 'Verifying'}
    elif type == 'info_baby':
        to_dic = {'0': 'no', '1': 'yes', '2': 'unknow'}
    elif type == 'info_baby_cn':
        to_dic = {'0': '没有孩子', '1': '有孩子', '2': '未知'}
    elif type == 'info_baby_en':
        to_dic = {'0': 'No', '1': 'Yes', '2': 'Unknow'}
    elif type == 'change_page':
        to_dic = {-1: 'PrePage', 1: 'NextPage'}
    elif type == 'page_cn':
        to_dic = {'PrePage': '上页', 'NextPage': '下页'}
    elif type == 'limit':
        to_dic = {'11': 'UnBanInBot', '10': 'BanInBot', '21': 'Ban选妃Bot', '20': 'UnBan选妃Bot',
                  '31': 'BanNew', '30': 'UnBanNew', '41': 'BanDidiBot', '40': 'UnBanDidiBot'}
    elif type == 'week_talk':
        to_dic = {0: '今天<b>周一</b>，打个<b>头炮</b>，为每一周的新生活开个好头！',
                  1: '<b>周二</b>是高质量<b>美女</b>的空档期，要<b>抓紧</b>罗！',
                  2: '谁说<b>周三</b>是离休息最远的？有<b>妹子陪</b>每天都是休息！',
                  3: '英文的<b>Thursday</b>，来源于日尔曼的风雨雷电之神Thor，<b>雷电交加</b>下你最想做什么？',
                  4: '终于等到<b>周五</b>了！约个<b>妹子</b>来<b>犒劳</b>一下自己！',
                  5: '<b>周末</b>啦！约个妹子生活<b>增色</b>不少！',
                  6: '<b>周日</b>是最后的<b>疯狂</b>，春宵一刻值千金！'}
    elif type == 'status_chat':
        to_dic = {'0': '未聊天', '1': '正常客户', '2': '非客户', '3': '女孩中心'}
    elif type == 'status_bot':
        to_dic = {'0': '未聊天', '1': '正常可提醒', '2': '正常不提醒', '3': '滴滴客户'}
    elif type == 'class_type':
        to_dic = {'0': ['全部贵妃', -1001526949053], '1': ['空闲在岗', -1001591180989], '2': ['异国风情', -1001751223063],
                  '3': ['颜值担当', -1001614268316], '4': ['大众点评', -1001719224432], '5': ['丰乳肥臀', -1001700709755],
                  '6': ['长腿模特', -1001540019055], '7': ['年轻稚嫩', -1001607225871], '8': ['特殊玩法', -1001777363956],
                  '9': ['新车试驾', -1001534229184], '10': ['高价专区', -1001697634992], '11': ['低价专区', -1001374199552],
                  '20': ['AllGirls', -1002498182762], '21': ['AvailGirls', -1002350756936], '22': ['NewGirls', -1002438138215]
                  }
    # elif type == 'class_type_en':
    #     to_dic = {'20': ['AllGirls', -1002498182762, 'happyzone666'], '21': ['AvailGirls', -1002350756936, 'happyzone667'],
    #               '22': ['NewGirls', -1002438138215, 'happyzone668']}
    elif type == 'country':
        to_dic = {'0': e_cty0 + "混血", '1': e_cty1 + "菲律宾", '2': e_cty2 + "中国", '3': e_cty3 + "越南",
                  '4': e_cty4 + "俄罗斯", '5': e_cty5 + "泰国"}
    elif type == 'country_en':
        to_dic = {'0': e_cty0 + "mixed-race", '1': e_cty1 + "Filipinos", '2': e_cty2 + "Chinese",
                  '3': e_cty3 + "Vietnamese", '4': e_cty4 + "Russian", '5': e_cty5 + "Thai"}
    elif type == 'girlc_log_type':
        to_dic = {'0': '未知', '1': '自报空闲', '2': '自报忙', '3': '手报空闲', '4': '手报忙', '5': '接受', '6': '拒绝', '7': '活跃成员'}
    elif type == 'girls_status_1':
        to_dic = {'0': '未收录', '1': '客户-未下单', '2': '客户-已下单', '3': '已上架', '4': '待上架',
                  '5': '待聊', '6': '忽略', '7': '禁止使用'}
    elif type == 'girls_status_2':
        to_dic = {'0': '未对话', '1': '对话提醒', '2': '对话不提醒'}
    # elif type == 'task_opt':
    #     to_dic = {'0': '待处理', '1': '已处理', '2': "无需处理", '3': "备注"}
    # elif type == 'task_emom':
    #     to_dic = {'0': e_loudou, '1': e_gou, '2': e_cha, '3': e_myOffer}
    # 确定转义方向
    if point == 'to':
        return to_dic.get(text, None)
    elif point == 'from':
        from_dict = {v: k for k, v in to_dic.items()}
        return from_dict.get(text, None)
    else:
        return None


# 个性化类
# if type == 'class_type':
    #         to_dic = {'0': ['全部贵妃', -1001662014767], '1': ['空闲在岗', -1001591180989], '2': ['异国风情', -1001751223063],
    #                   '3': ['颜值爆灯', -1001614268316], '4': ['可爱纯情', -1001719224432], '5': ['丰乳肥臀', -1001700709755],
    #                   '6': ['长腿模特', -1001540019055], '7': ['年轻稚嫩', -1001607225871], '8': ['特殊服务', -1001777363956],
    #                   '9': ['新车试驾', -1001534229184], '10': ['高价专区', -1001697634992], '11': ['低价专区', -1001374199552]}
gm_pinglun = ChatMessage(params['g_pinglun_id'], '动物列表')
cm_pinglun = ChatMessage(params['c_pinglun_id'], '动物列表')
# 群消息管理列表
cm = ['0'] * 23
for i in range(12):
    cm[i] = ChatMessage(mean('class_type', str(i))[1], '动物列表')
    # cm.append(ChatMessage(mean('class_type', str(i))[1], '动物列表'))
# 英文频道管理列表
# ce = ['0'] * 23
for i in range(20, 23):
    cm[i] = ChatMessage(mean('class_type', str(i))[1], '动物列表')
    # ce.append(ChatMessage(mean('class_type_en', str(i))[1], '动物列表'))
# 更新标识列表 0：待更新 1：请求更新 12345：以时间戳作为当前更新号
updateing = [0] * 23



# 获取广告语
def adv(index=0):
    try:
        if index == 0:
            return "欢迎<b>广告投放</b>或<b>资源互助</b>，任何形式合作都可以<b><a href='t.me/happy_167'>商谈</a></b>！"
        elif index == 1:
            return mean('week_talk', datetime.today().weekday())
        elif index == 2:
            return "这里有全菲<b>最实惠</b>的价格和<b>高质量</b>的服务，上千个美女长期稳定专业地为您服务！"
        elif index == 3:
            return "全部贵妃：贵妃一览无余 @happyzone168\n精选贵妃：个人喜好筛选 @happyzonebot"
        elif index == 4:
            return "<b>合作</b>共赢，接受个人或介绍美女入驻，利润共享，欢迎来<b><a href='t.me/happy_167'>商谈</a></b>"
        elif index == 5:
            return "专业修车<b>杜绝仙人跳</b>，这里所有的女孩都已<b>验明正身</b>，并<b>已打疫苗</b>，请放心选妃！"
        elif index == 6:
            return "<b>懂你的，才是最好的！<b>\n我们7*24小时营业，每时每刻在您有需要时都能为您提供一点点温存😉🍌💦"
        elif index == 7:
            return "快速通道：快速进入喜欢的妹子分类\n空闲在岗：让您的期待永远不落空"
        else:
            return ''
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# # 获取客户链接
# def link(param):
#     if isinstance(param, int):
#         my_chat = f_chats_id(param)
#     else:
#         my_chat = f_chats_name(param)
#     if my_chat[5] != '':
#         return 't.me/' + my_chat[5]
#     else:
#         return "tg://openmessage?user_id={0:d}".format(my_chat[0])


# 输入验证
async def input_cert(str_input='', style='None', client=None, user_id=0):
    try:
        rst_finder = fuzzy_finder(['.*delete.*', '.*drop.*', '.*alter.*', '.*add.*', '.*insert.*', '.*select.*'], [str_input.lower()])
        if len(rst_finder) > 0:
            await client.send_message(1999226611,
                                      text="用户 {2:s} 在渗透入侵，类型 {1:s}，内容 {0:s}，需要尽快应急防御"
                                      .format(str_input, style, f_chats_id(user_id)[2]))
            return [False, 'Pls input the right content']
        if style == 'name':
            if len(correct_char(str_input)) > 50:
                return [False, 'Name too long, length less than 50']
            else:
                return [True, correct_char(str_input)]
        elif style == 'sa_name':
            str_input = correct_char(str_input)
            if len(str_input) > 50:
                return [False, 'Name too long, length less than 50']
            sql_str = "select count(1) from zoo_info where name='{0:s}'".format(str_input)
            rst = read_connector.query(sql_str)
            if rst[0][0] != 0:
                return [False, 'Name {0:s} already exists'.format(str_input)]
            else:
                return [True, str_input]
        elif style == 'age':
            if re.match('^[1-3][0-9]$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, 'Your age don\'t meet our requirement']
        elif style == 'height':
            if re.match('^[12][0-9]{2}$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, 'Your height don\'t meet our requirement']
        elif style == 'weight':
            if re.match('^[3-6][0-9]$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, 'Your weight don\'t meet our requirement']
        elif style == 'boobs':
            if re.match('^[2-4][0-9][a-fA-F]$', str_input, re.S) is not None:
                return [True, str_input.upper()]
            else:
                return [False, 'Your boobs don\'t meet our requirement']
        elif style == 'location':
            if len(str_input) > 50:
                return [False, 'Location too long, length less than 50']
            else:
                return [True, correct_char(str_input)]
        elif style == 'special':
            if len(str_input) > 255:
                return [False, 'Special too long, length less than 255']
            else:
                return [True, correct_char(str_input)]
        elif style == 'phone':
            # if re.match('^0?9[0-9]{9}$', str_input, re.S) is not None:
            #     return [True, str_input]
            if len(str_input) <= 50:
                return [True, str_input]
            else:
                return [False, 'Correct phone like 09123456789/09123456780']
        elif style == 'wechat':
            if len(str_input) > 30:
                return [False, 'Wechat too long, length less than 30']
            else:
                return [True, correct_char(str_input)]
        elif style == 'telegram':
            if len(str_input) > 50:
                return [False, 'Telegram too long, length less than 30']
            else:
                return [True, correct_char(str_input)]
        elif style == 'facebook':
            if len(str_input) > 50:
                return [False, 'Facebook too long, length less than 30']
            else:
                return [True, correct_char(str_input)]
        elif style == 'price':
            if re.match('^[0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6}$', str_input, re.S) is not None:
                return [True, str_input]
            elif re.match('^[0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6}$', str_input, re.S) is not None:
                return [True, str_input]
            elif re.match('^[0-9]{1,6} [0-9]{1,6} [0-9]{1,6}$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, 'Please input 4 or 5 price\nSuch as 0 1000 2000 3000']
        elif style == 'search':
            if re.match('^[0-9]{1,4}$', str_input, re.S) is not None:
                return ['no', int(str_input)]
            elif re.match('^[09][0-9]{9,10}$', str_input.strip(), re.S) is not None:
                return ['phone', str_input.strip()]
            else:
                return ['name', str_input.strip()]
        elif style == 'offer':
            name_list = f_chats_name(str_input)
            if len(name_list) == 0:
                return [False, 'No this Name']
            else:
                return [True, name_list[2]]
        elif style == 'viewed':
            if re.match('^[0-9]*$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, 'viewed nust be number']
        elif style == 'note':
            if len(correct_char(str_input)) > 255:
                return [False, 'Note too long, length less than 50']
            else:
                return [True, correct_char(str_input)]
        elif style == 'age_range':
            if re.match('^[1-3][0-9]-[1-3][0-9]$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '请输入合理的年龄范围\n格式如：18-28']
        elif style == 'height_range':
            if re.match('^[12][0-9]{2}-[12][0-9]{2}$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '请输入合理的身高范围\n格式如：150-180']
        elif style == 'weight_range':
            if re.match('^[3-6][0-9]-[3-6][0-9]$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '请输入合理的体重范围\n格式如：40-55']
        elif style == 'boobs_range':
            if re.match('^[2-4][0-9]-[2-4][0-9]$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '请输入合理的胸围范围\n格式如：26-46']
        elif style == 'special_search':
            if len(str_input) > 255:
                return [False, '特点的长度过长\n要求：长度不能超过20个字']
            else:
                return [True, correct_char(str_input).replace('select', '').replace('drop', '')
                    .replace('delete', '').replace('insert', '')]
        elif style == 'price_range':
            if re.match('^[0-9]{1,6}-[0-9]{1,6}$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '请输入合理的价格范围\n格式如：1-30000']
        return [False, 'No this type']
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 内联按钮界面
def get_com_imp(index='-', param=None):
    try:
        if index == 'board':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_bot + "机器人", url="t.me/happyzonebot"),
                 InlineKeyboardButton(e_video + "精彩视频", callback_data='video'),
                 InlineKeyboardButton(e_han + "开始约妃", url="t.me/happy_167")]
            ])
        elif index == 'board_en':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_video + "Sex video", callback_data='video'),
                 InlineKeyboardButton(e_han + "Contect me", url="t.me/happy_167")]
            ])
        elif index == 'sa_guest':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_location + "地址", callback_data='sa_guest_addr'),
                 InlineKeyboardButton(e_phone + "电话", callback_data='sa_guest_phone'),
                 InlineKeyboardButton(e_bell + "下单", callback_data='sa_guest_order')],
                [InlineKeyboardButton(e_dna + "关系", callback_data='sa_guest_link'),
                 InlineKeyboardButton(e_myOffer + "备注", callback_data='sa_guest_note'),
                 # InlineKeyboardButton(e_cycle + "刷新", callback_data='sa_guest_refresh')]
                 InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
            ])
        elif index == 'girlcadmin':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_bot + "机器人", callback_data='girlc_bot'),
                 InlineKeyboardButton(e_page + "详情", callback_data='girlc_detail'),
                 InlineKeyboardButton(e_myOffer + "备注", callback_data='girlc_note')]
            ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


def get_content(rst):
    try:
        return e_lip * 3 + 'Girl\'s information!!' + e_lip * 3 + """
No: {0[0]:d}
Name: {0[1]:s}
Country: {4:s}
Age: {0[2]:d}
Height: {0[3]:d}cm
Weight: {0[4]:d}kg
Boobs: {0[5]:s}
Baby: {1:s}
Location: {0[7]:s}
Special: {0[8]:s}
Level: {2:s}
Phone: {0[10]:s}
Wechat: {0[11]:s}
Telegram: {0[12]:s}
Facebook: {0[13]:s}
Price_out: {0[14]:s}
Price_in: {0[15]:s}
Picture: {0[16]:s}
Viewed: {0[18]:d}
Status: {3:s}
Note: {0[20]:s}
""".format(rst[0], mean('info_baby', rst[0][6]), mean('info_level', rst[0][9]),
                          mean('info_status', rst[0][19]), mean('country', rst[0][17]))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 展示客户信息
def get_content_guest(rst):
    try:
        if rst[2] != '':
            outname = "tg://resolve?domain={0:s}".format(rst[2])
        else:
            outname = "tg://user?id={0:d}".format(rst[0])
        outname_text = '*' if rst[3] == '' else rst[3]
        phones = ''
        for phone in rst[5].split('/'):
            phones += '<code>' + phone + '</code>/'
        return e_manhat * 3 + '欢乐园客户' + e_manhat * 3 + """
{5[0]:s} 客户ID: <code>{0[13]:s}</code>
{5[1]:s} 内部名: {0[1]:s}
{5[2]:s} 外部名: <a href="{1:s}">{6:s}</a>
{5[3]:s} 地址: {0[4]:s}
{5[4]:s} 电话: {7:s}
{5[5]:s} 下单: {0[6]:d}
{5[6]:s} 关系: {2:s}
{5[7]:s} 机器人: {3:s}
{5[8]:s} 提醒时间: {0[9]:%Y-%m-%d %X}
{5[9]:s} 活跃时间: {0[10]:%Y-%m-%d %X}
{5[10]:s} 加入时间: {0[11]:%Y-%m-%d %X}
{5[11]:s} 备注: {0[12]:s}
""".format(rst, outname, mean('status_chat', rst[7]), mean('status_bot', rst[8]), str(rst[0])[-4:],
           (e_prize, e_man, e_man2, e_location, e_phone, e_bell, e_dna, e_bot, e_time1, e_time2, e_time3, e_myOffer),
           outname_text, phones.strip('/'))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 广告号对外发
def get_content_adv(rst, price=True):
    try:
        price_list = rst[0][10].split(' ')
        if price:
            if len(price_list) == 7:
                price_str = "{0:s} 价格:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      1小时   1炮       {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      3小时   2炮       {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      6小时   3炮       {0:s}p".format(price_list[2])
                if price_list[3] != '0':
                    price_str += "\n      8小时   无限炮 {0:s}p".format(price_list[3])
                if price_list[4] != '0':
                    price_str += "\n      10小时 无限炮 {0:s}p".format(price_list[4])
                if price_list[5] != '0':
                    price_str += "\n      12小时 无限炮 {0:s}p".format(price_list[5])
                if price_list[6] != '0':
                    price_str += "\n      24小时 无限炮 {0:s}p".format(price_list[6])
            elif len(price_list) == 4:
                price_str = "{0:s} 价格:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      1小时 1炮       {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      3小时 2炮       {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      6小时 3炮       {0:s}p".format(price_list[2])
                if price_list[3] != '0':
                    price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[3])
            elif len(price_list) == 3:
                price_str = "{0:s} 价格:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      3小时 1炮       {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      6小时 2炮       {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[2])
            elif len(price_list) == 1:
                price_str = "{1:s} 价格: 按摩 {0:s}p".format(rst[0][10], e_money)
            else:
                price_str = "{0:s} 价格: 暂无".format(e_money)
        else:
            if len(price_list) >= 3:
                price_str = "{0:s} 服务: 性爱".format(beg_girl)
            elif price_list[0] in ('', '-'):
                price_str = "{0:s} 服务: 未确定".format(beg_girl)
            else:
                price_str = "{0:s} 服务: 按摩".format(beg_girl)
        content = """选择我们的<b>理由</b>：
<b>全部贵妃</b>：贵妃一览无余 @happyzone168
<b>精选贵妃</b>：个人喜好筛选 @happyzonebot
<b>福利视频</b>：海量精彩视频 @happyzone176
<b>主题之夜</b>：每晚推荐不同特色美女
<b>空闲在线</b>：让您的期待永远不落空
"""
        return e_lip * 3 + "<b>欢乐园贵妃</b>" + e_lip * 3 + """
{4:s} 编号: {0[0]:d}
{6:s} 年龄: {0[2]:d}
{9:s} 胸围: {0[5]:s}
{12:s} 特点: {0[8]:s}
{13:s} 等级: {2:s}
{14:s}
{16:s}
""".format(rst[0], mean('info_baby_cn', rst[0][6]), mean('info_level_cn', rst[0][9]),
           mean('info_status_cn', rst[0][12]), e_prize, e_girl, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby,
           e_location, e_id, e_zoushi, price_str, e_traffic, content)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 主频道发送
async def get_content_home(rst, price=True):
    try:
        price_list = rst[0][10].split(' ')
        if price:
            if len(price_list) == 7:
                price_str = "{0:s} 价格:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      1小时   1炮       {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      3小时   2炮       {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      6小时   3炮       {0:s}p".format(price_list[2])
                if price_list[3] != '0':
                    price_str += "\n      8小时   无限炮 {0:s}p".format(price_list[3])
                if price_list[4] != '0':
                    price_str += "\n      10小时 无限炮 {0:s}p".format(price_list[4])
                if price_list[5] != '0':
                    price_str += "\n      12小时 无限炮 {0:s}p".format(price_list[5])
                if price_list[6] != '0':
                    price_str += "\n      24小时 无限炮 {0:s}p".format(price_list[6])
            elif len(price_list) == 4:
                price_str = "{0:s} 价格:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      1小时 1炮       {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      3小时 2炮       {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      6小时 3炮       {0:s}p".format(price_list[2])
                if price_list[3] != '0':
                    price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[3])
            elif len(price_list) == 3:
                price_str = "{0:s} 价格:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      3小时 1炮       {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      6小时 2炮       {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[2])
            elif len(price_list) == 1:
                price_str = "{1:s} 价格: 按摩 {0:s}p".format(rst[0][10], e_money)
            else:
                price_str = "{0:s} 价格: 暂无".format(e_money)
        else:
            if len(price_list) >= 3:
                price_str = "{0:s} 服务: 性爱".format(beg_girl)
            elif price_list[0] in ('', '-'):
                price_str = "{0:s} 服务: 未确定".format(beg_girl)
            else:
                price_str = "{0:s} 服务: 按摩".format(beg_girl)
        disc_link = await get_disc_link(str(rst[0][0]))
        # 获取评论字符串
        if disc_link[1] > 0:
            disc_str = "评论: <a href='{0[0]:s}'>查看 {0[1]:d}+</a>".format(disc_link)
        else:
            disc_str = "评论: <a href='{0[0]:s}'>查看</a>".format(disc_link)
        return e_lip * 3 + '欢乐园贵妃' + e_lip * 3 + """
{4:s} 编号: <code>{0[0]:d}</code>
{5:s} 国籍: {18:s}
{6:s} 年龄: {0[2]:d}
{7:s} 身高: {0[3]:d}cm
{8:s} 体重: {0[4]:d}kg
{9:s} 胸围: {0[5]:s}
{11:s} 住址: {0[7]:s}
{12:s} 特点: {0[8]:s}
{13:s} 等级: {2:s}
{14:s}
{15:s} 状态: {3:s}
{16:s} {17:s}

下单请联系： <b>@happy_167</b>
""".format(rst[0], mean('info_baby_cn', rst[0][6]), mean('info_level_cn', rst[0][9]),
           mean('info_status_cn', rst[0][12]), e_prize, e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby,
           e_location, e_id, e_zoushi, price_str, e_traffic, e_pinglun, disc_str, mean('country', rst[0][13]))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 英文主频道发送
async def get_content_home_en(rst, price=True):
    try:
        price_list = rst[0][10].split(' ')
        if price:
            if len(price_list) == 7:
                price_str = "{0:s} Price:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      1hr   1pop      {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      3hr   2pop      {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      6hr   3pop      {0:s}p".format(price_list[2])
                if price_list[3] != '0':
                    price_str += "\n      8hr   unlimit {0:s}p".format(price_list[3])
                if price_list[4] != '0':
                    price_str += "\n      10hr  unlimit {0:s}p".format(price_list[4])
                if price_list[5] != '0':
                    price_str += "\n      12hr  unlimit {0:s}p".format(price_list[5])
                if price_list[6] != '0':
                    price_str += "\n      24hr  unlimit {0:s}p".format(price_list[6])
            elif len(price_list) == 4:
                price_str = "{0:s} Price:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      1hr 1pop      {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      3hr 2pop      {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      6hr 3pop      {0:s}p".format(price_list[2])
                if price_list[3] != '0':
                    price_str += "\n      8hr unlimit {0:s}p".format(price_list[3])
            elif len(price_list) == 3:
                price_str = "{0:s} Price:".format(e_money)
                if price_list[0] != '0':
                    price_str += "\n      3hr 1pop      {0:s}p".format(price_list[0])
                if price_list[1] != '0':
                    price_str += "\n      6hr 2pop      {0:s}p".format(price_list[1])
                if price_list[2] != '0':
                    price_str += "\n      8hr unlimit {0:s}p".format(price_list[2])
            elif len(price_list) == 1:
                price_str = "{1:s} Price: massage {0:s}p".format(rst[0][10], e_money)
            else:
                price_str = "{0:s} Price: None".format(e_money)
        else:
            if len(price_list) >= 3:
                price_str = "{0:s} Service: Sex".format(beg_girl)
            elif price_list[0] in ('', '-'):
                price_str = "{0:s} Service: NotSure".format(beg_girl)
            else:
                price_str = "{0:s} Service: Massage".format(beg_girl)
        disc_link = await get_disc_link(str(rst[0][0]))
        # 获取评论字符串
        if disc_link[1] > 0:
            disc_str = "Evaluate: <a href='{0[0]:s}'>view {0[1]:d}+</a>".format(disc_link)
        else:
            disc_str = "Evaluate: <a href='{0[0]:s}'>view</a>".format(disc_link)
        # 更新Special_en
        if rst[0][14] == '':
            special_en = update_special_en(rst[0][8])
            sql_str = f"update zoo_info set special_en='{special_en}' where id={rst[0][0]}"
            tg_connector.exe(sql_str)
        else:
            special_en = rst[0][14]
        return e_lip * 3 + 'HappyZone Girl' + e_lip * 3 + """
{4:s} No: <code>{0[0]:d}</code>
{5:s} Nat: {18:s}
{6:s} Age: {0[2]:d}
{7:s} Height: {0[3]:d}cm
{8:s} Weight: {0[4]:d}kg
{9:s} Boobs: {0[5]:s}
{11:s} Address: {0[7]:s}
{12:s} Special: {19:s}
{13:s} Level: {2:s}
{14:s}
{15:s} Status: {3:s}
{16:s} {17:s}

Contact： <b>@happy_167</b>
""".format(rst[0], mean('info_baby_en', rst[0][6]), mean('info_level_cn', rst[0][9]),
           mean('info_status_en', rst[0][12]), e_prize, e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby,
           e_location, e_id, e_zoushi, price_str, e_traffic, e_pinglun, disc_str, mean('country_en', rst[0][13]),
           special_en)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 公告牌发送 format 是否格式化  show_in 空闲列表内外显示有所不同
def get_content_board(format=True, show_in=False):
    try:
        content = ''
        note = {}
        sql_str = "select * from zoo_note where id like 'board_%'"
        rst = tg_connector.query(sql_str)
        note.update(dict((col1, [col2, col3]) for (col1, col2, col3) in rst))
        # 不格式化
        if not format:
            content = "{0[0]:s}\n{0[1]:s}\n\n{1[0]:s}\n{1[1]:s}\n" \
                .format(note['board_notice'], note['board_available'])
            return content
        # 公告
        if note['board_notice'][1] not in ('', '-'):
            content += e_adv + "<b>{0[0]:s}</b>\n{0[1]:s}\n".format(note['board_notice'])
        if show_in and note['board_available'][1] not in ('', '-'):
            id_list = []
            name_list = []
            for item in note['board_available'][1].split(','):
                if item.isdigit():
                    id_list.append(item)
                else:
                    name_list.append(item)
            id_str = '0' if len(id_list) == 0 else ','.join(id_list)
            name_str = "''" if len(name_list) == 0 else "','".join(name_list)
            sql_str = "select a.id,a.name,right(ifnull(b.msg_list,''),locate(',',reverse(ifnull(b.msg_list,'')),2)-1) " \
                      "from (select * from zoo_info where status in ('1','2') and (id in ({0:s}) or name in " \
                      "('{1:s}'))) a left join (select ind,msg_list from chat_message where chat_id={2:d} and " \
                      "kind='动物列表') b on a.id=b.ind order by a.level desc,a.boobs desc,a.age,a.baby" \
                .format(id_str, name_str, cm[0].chat_id)
            rst = tg_connector.query(sql_str)
            if len(rst) > 0:
                content += '\n' + e_kong + "<b>{0[0]:s}</b>\n".format(note['board_available'])
                n = 1
                for row in rst:
                    content += "<a href='t.me/happyzone168/{1:s}'>{0[0]:0>3d}</a> . ".format(row, row[2].strip(','))
                    if n == 5:
                        content = content.rstrip('. ') + '\n'
                        n = 1
                    else:
                        n += 1
                content = content.rstrip('\n').rstrip('. ') + '\n'
        else:
            content += '\n' + e_shandian + "<b>快速通道</b>   "
            content += "<a href='https://t.me/happyzone888/133?comment=2015'>选妃须知</a>   "
            content += "<a href='https://t.me/happyzone168'>全部贵妃</a>\n\n" + e_fire
            content += "<b><a href='https://t.me/happyzone590'>空闲在岗</a></b>" + e_fire
            content += "<b><a href='https://t.me/happyzone188'>高端越妹</a></b>" + e_fire
            content += "<b><a href='https://t.me/happyzone_highPhi'>高端菲妹</a></b>\n\n"
            content += "<a href='https://t.me/happyzone_xinche'>新车试驾</a> . "
            content += "<a href='https://t.me/happyzone_yanzhi'>颜值担当</a> . "
            content += "<a href='https://t.me/happyzone_fengru'>丰乳肥臀</a> . "
            content += "<a href='https://t.me/happyzone_zhinen'>年轻稚嫩</a>\n\n"
            content += "<a href='https://t.me/happyzone_teshu'>特殊玩法</a> . "
            content += "<a href='https://t.me/happyzone_changtui'>长腿模特</a> . "
            content += "<a href='https://t.me/happyzone_dijia'>低价专区</a> . "
            content += "<a href='https://t.me/happyzone_dianping'>点评</a>\n\n"
        return content
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 公告牌发送 format 是否格式化  show_in 空闲列表内外显示有所不同
def get_content_board_en(format=True, show_in=False):
    try:
        content = ''
        note = {}
        sql_str = "select * from zoo_note where id like 'board_%'"
        rst = tg_connector.query(sql_str)
        note.update(dict((col1, [col2, col3]) for (col1, col2, col3) in rst))
        # 不格式化
        if not format:
            content = "{0[0]:s}\n{0[1]:s}\n\nAvailable\n{1[1]:s}\n" \
                .format(note['board_notice_en'], note['board_available'])
            return content
        # 公告
        if note['board_notice_en'][1] not in ('', '-'):
            content += e_adv + "<b>{0[0]:s}</b>\n{0[1]:s}\n".format(note['board_notice_en'])
        if show_in and note['board_available'][1] not in ('', '-'):
            id_list = []
            name_list = []
            for item in note['board_available'][1].split(','):
                if item.isdigit():
                    id_list.append(item)
                else:
                    name_list.append(item)
            id_str = '0' if len(id_list) == 0 else ','.join(id_list)
            name_str = "''" if len(name_list) == 0 else "','".join(name_list)
            sql_str = "select a.id,a.name,right(ifnull(b.msg_list,''),locate(',',reverse(ifnull(b.msg_list,'')),2)-1) " \
                      "from (select * from zoo_info where status in ('1','2') and (id in ({0:s}) or name in " \
                      "('{1:s}'))) a left join (select ind,msg_list from chat_message where chat_id={2:d} and " \
                      "kind='动物列表') b on a.id=b.ind order by a.level desc,a.boobs desc,a.age,a.baby" \
                .format(id_str, name_str, cm[20].chat_id)
            rst = tg_connector.query(sql_str)
            if len(rst) > 0:
                content += '\n' + e_kong + "<b>{0[0]:s}</b>\n".format(note['board_available'])
                n = 1
                for row in rst:
                    content += "<a href='t.me/happyzone666/{1:s}'>{0[0]:0>3d}</a> . ".format(row, row[2].strip(','))
                    if n == 5:
                        content = content.rstrip('. ') + '\n'
                        n = 1
                    else:
                        n += 1
                content = content.rstrip('\n').rstrip('. ') + '\n'
        else:
            content += '\n' + e_shandian + "<b>Fast track</b>   \n" + e_fire
            content += "<b><a href='https://t.me/happyzone666'>AllGirl</a></b> " + e_fire
            content += "<b><a href='https://t.me/happyzone667'>AvailGirl</a></b> " + e_fire
            content += "<b><a href='https://t.me/happyzone668'>NewGirl</a></b>\n\n"
        return content
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发送给平常客户
async def get_content_to_guest(row, price=True, partner=0):
    try:
        if len(row) == 15:
            price_list = row[10].split(' ')
            if price:
                if len(price_list) == 7:
                    price_str = "{0:s} Price:".format(e_money)
                    if price_list[0] != '0':
                        price_str += "\n      1hr   1pop      {0:s}p".format(price_list[0])
                    if price_list[1] != '0':
                        price_str += "\n      3hr   2pop      {0:s}p".format(price_list[1])
                    if price_list[2] != '0':
                        price_str += "\n      6hr   3pop      {0:s}p".format(price_list[2])
                    if price_list[3] != '0':
                        price_str += "\n      8hr   unlimit {0:s}p".format(price_list[3])
                    if price_list[4] != '0':
                        price_str += "\n      10hr  unlimit {0:s}p".format(price_list[4])
                    if price_list[5] != '0':
                        price_str += "\n      12hr  unlimit {0:s}p".format(price_list[5])
                    if price_list[6] != '0':
                        price_str += "\n      24hr  unlimit {0:s}p".format(price_list[6])
                elif len(price_list) == 4:
                    price_str = "{0:s} Price:".format(e_money)
                    if price_list[0] != '0':
                        price_str += "\n      1hr 1pop      {0:s}p".format(price_list[0])
                    if price_list[1] != '0':
                        price_str += "\n      3hr 2pop      {0:s}p".format(price_list[1])
                    if price_list[2] != '0':
                        price_str += "\n      6hr 3pop      {0:s}p".format(price_list[2])
                    if price_list[3] != '0':
                        price_str += "\n      8hr unlimit {0:s}p".format(price_list[3])
                elif len(price_list) == 3:
                    price_str = "{0:s} Price:".format(e_money)
                    if price_list[0] != '0':
                        price_str += "\n      3hr 1pop      {0:s}p".format(price_list[0])
                    if price_list[1] != '0':
                        price_str += "\n      6hr 2pop      {0:s}p".format(price_list[1])
                    if price_list[2] != '0':
                        price_str += "\n      8hr unlimit {0:s}p".format(price_list[2])
                elif len(price_list) == 1:
                    price_str = "{1:s} Price: massage {0:s}p".format(row[10], e_money)
                else:
                    price_str = "{0:s} Price: None".format(e_money)
            else:
                if len(price_list) >= 3:
                    price_str = "{0:s} Service: Sex".format(beg_girl)
                elif price_list[0] in ('', '-'):
                    price_str = "{0:s} Service: NotSure".format(beg_girl)
                else:
                    price_str = "{0:s} Service: Massage".format(beg_girl)
            disc_link = await get_disc_link(str(row[0]))
            # 获取评论字符串
            if disc_link[1] > 0:
                disc_str = "Evaluate: <a href='{0[0]:s}'>view {0[1]:d}+</a>".format(disc_link)
            else:
                disc_str = "Evaluate: <a href='{0[0]:s}'>view</a>".format(disc_link)
            # 更新Special_en
            if row[14] == '':
                special_en = update_special_en(row[8])
                sql_str = f"update zoo_info set special_en='{special_en}' where id={row[0]}"
                tg_connector.exe(sql_str)
            else:
                special_en = row[14]
            return e_lip * 3 + 'HappyZone Girl' + e_lip * 3 + """
{4:s} No: <code>{0[0]:d}</code>
{5:s} Nat: {18:s}
{6:s} Age: {0[2]:d}
{7:s} Height: {0[3]:d}cm
{8:s} Weight: {0[4]:d}kg
{9:s} Boobs: {0[5]:s}
{11:s} Address: {0[7]:s}
{12:s} Special: {19:s}
{13:s} Level: {2:s}
{14:s}
{15:s} Status: {3:s}
{16:s} {17:s}
""".format(row, mean('info_baby_en', row[6]), mean('info_level_cn', row[9]), mean('info_status_en', row[12]), e_prize,
           e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi, price_str, e_traffic,
           e_pinglun, disc_str, mean('country_en', row[13]), special_en)
        else:
            price_list = row[10].split(' ')
            if price:
                if len(price_list) == 7:
                    price_str = "{0:s} 价格:".format(e_money)
                    if price_list[0] != '0':
                        price_str += "\n      1小时   1炮       {0:s}p".format(price_list[0])
                    if price_list[1] != '0':
                        price_str += "\n      3小时   2炮       {0:s}p".format(price_list[1])
                    if price_list[2] != '0':
                        price_str += "\n      6小时   3炮       {0:s}p".format(price_list[2])
                    if price_list[3] != '0':
                        price_str += "\n      8小时   无限炮 {0:s}p".format(price_list[3])
                    if price_list[4] != '0':
                        price_str += "\n      10小时 无限炮 {0:s}p".format(price_list[4])
                    if price_list[5] != '0':
                        price_str += "\n      12小时 无限炮 {0:s}p".format(price_list[5])
                    if price_list[6] != '0':
                        price_str += "\n      24小时 无限炮 {0:s}p".format(price_list[6])
                elif len(price_list) == 4:
                    price_str = "{0:s} 价格:".format(e_money)
                    if price_list[0] != '0':
                        price_str += "\n      1小时 1炮       {0:s}p".format(price_list[0])
                    if price_list[1] != '0':
                        price_str += "\n      3小时 2炮       {0:s}p".format(price_list[1])
                    if price_list[2] != '0':
                        price_str += "\n      6小时 3炮       {0:s}p".format(price_list[2])
                    if price_list[3] != '0':
                        price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[3])
                elif len(price_list) == 3:
                    price_str = "{0:s} 价格:".format(e_money)
                    if price_list[0] != '0':
                        price_str += "\n      3小时 1炮       {0:s}p".format(price_list[0])
                    if price_list[1] != '0':
                        price_str += "\n      6小时 2炮       {0:s}p".format(price_list[1])
                    if price_list[2] != '0':
                        price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[2])
                elif len(price_list) == 1:
                    price_str = "{1:s} 价格: 按摩 {0:s}p".format(row[10], e_money)
                else:
                    price_str = "{0:s} 价格: 暂无".format(e_money)
            else:
                if len(price_list) >= 3:
                    price_str = "{0:s} 服务: 性爱".format(beg_girl)
                elif price_list[0] in ('', '-'):
                    price_str = "{0:s} 服务: 未确定".format(beg_girl)
                else:
                    price_str = "{0:s} 服务: 按摩".format(beg_girl)
            disc_link = await get_disc_link(str(row[0]))
            # 获取评论字符串
            if disc_link[1] > 0:
                disc_str = "评论: <a href='{0[0]:s}'>查看 {0[1]:d}+</a>".format(disc_link)
            else:
                disc_str = "评论: <a href='{0[0]:s}'>查看</a>".format(disc_link)
            if partner == 1:
                return e_lip * 3 + '欢乐联盟' + e_lip * 3 + """
{4:s} 编号: <code>{0[0]:d}</code>
{5:s} 国籍: {18:s}
{6:s} 年龄: {0[2]:d}
{7:s} 身高: {0[3]:d}cm
{8:s} 体重: {0[4]:d}kg
{9:s} 胸围: {0[5]:s}
{11:s} 住址: {0[7]:s}
{12:s} 特点: {0[8]:s}
{13:s} 等级: {2:s}
{14:s}
{15:s} 状态: {3:s}
{16:s} {17:s}
""".format(row, mean('info_baby_cn', row[6]), mean('info_level_cn', row[9]), mean('info_status_cn', row[12]), e_prize,
           e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi, price_str, e_traffic,
           e_pinglun, disc_str, mean('country', row[13]))
            elif partner == 2:
                return e_lip * 3 + '快乐园' + e_lip * 3 + """
{4:s} 编号: <code>{0[0]:d}</code>
{5:s} 国籍: {18:s}
{6:s} 年龄: {0[2]:d}
{7:s} 身高: {0[3]:d}cm
{8:s} 体重: {0[4]:d}kg
{9:s} 胸围: {0[5]:s}
{11:s} 住址: {0[7]:s}
{12:s} 特点: {0[8]:s}
{13:s} 等级: {2:s}
{14:s}
{15:s} 状态: {3:s}
{16:s} {17:s}
""".format(row, mean('info_baby_cn', row[6]), mean('info_level_cn', row[9]), mean('info_status_cn', row[12]), e_prize,
           e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi, price_str, e_traffic,
           e_pinglun, disc_str, mean('country', row[13]))
            else:
                return e_lip * 3 + '欢乐园贵妃' + e_lip * 3 + """
{4:s} 编号: <code>{0[0]:d}</code>
{5:s} 国籍: {18:s}
{6:s} 年龄: {0[2]:d}
{7:s} 身高: {0[3]:d}cm
{8:s} 体重: {0[4]:d}kg
{9:s} 胸围: {0[5]:s}
{11:s} 住址: {0[7]:s}
{12:s} 特点: {0[8]:s}
{13:s} 等级: {2:s}
{14:s}
{15:s} 状态: {3:s}
{16:s} {17:s}
""".format(row, mean('info_baby_cn', row[6]), mean('info_level_cn', row[9]), mean('info_status_cn', row[12]), e_prize,
           e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi, price_str, e_traffic,
           e_pinglun, disc_str, mean('country', row[13]))

#
#
#         price_list = row[10].split(' ')
#         if price:
#             if len(price_list) == 7:
#                 price_str = "{0:s} 价格:".format(e_money)
#                 if price_list[0] != '0':
#                     price_str += "\n      1小时   1炮       {0:s}p".format(price_list[0])
#                 if price_list[1] != '0':
#                     price_str += "\n      3小时   2炮       {0:s}p".format(price_list[1])
#                 if price_list[2] != '0':
#                     price_str += "\n      6小时   3炮       {0:s}p".format(price_list[2])
#                 if price_list[3] != '0':
#                     price_str += "\n      8小时   无限炮 {0:s}p".format(price_list[3])
#                 if price_list[4] != '0':
#                     price_str += "\n      10小时 无限炮 {0:s}p".format(price_list[4])
#                 if price_list[5] != '0':
#                     price_str += "\n      12小时 无限炮 {0:s}p".format(price_list[5])
#                 if price_list[6] != '0':
#                     price_str += "\n      24小时 无限炮 {0:s}p".format(price_list[6])
#             elif len(price_list) == 4:
#                 price_str = "{0:s} 价格:".format(e_money)
#                 if price_list[0] != '0':
#                     price_str += "\n      1小时 1炮       {0:s}p".format(price_list[0])
#                 if price_list[1] != '0':
#                     price_str += "\n      3小时 2炮       {0:s}p".format(price_list[1])
#                 if price_list[2] != '0':
#                     price_str += "\n      6小时 3炮       {0:s}p".format(price_list[2])
#                 if price_list[3] != '0':
#                     price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[3])
#             elif len(price_list) == 3:
#                 price_str = "{0:s} 价格:".format(e_money)
#                 if price_list[0] != '0':
#                     price_str += "\n      3小时 1炮       {0:s}p".format(price_list[0])
#                 if price_list[1] != '0':
#                     price_str += "\n      6小时 2炮       {0:s}p".format(price_list[1])
#                 if price_list[2] != '0':
#                     price_str += "\n      1整晚 无限炮 {0:s}p".format(price_list[2])
#             elif len(price_list) == 1:
#                 price_str = "{1:s} 价格: 按摩 {0:s}p".format(row[10], e_money)
#             else:
#                 price_str = "{0:s} 价格: 暂无".format(e_money)
#         else:
#             if len(price_list) >= 3:
#                 price_str = "{0:s} 服务: 性爱".format(beg_girl)
#             elif price_list[0] in ('', '-'):
#                 price_str = "{0:s} 服务: 未确定".format(beg_girl)
#             else:
#                 price_str = "{0:s} 服务: 按摩".format(beg_girl)
#         disc_link = await get_disc_link(str(row[0]))
#         # 获取评论字符串
#         if disc_link[1] > 0:
#             disc_str = "评论: <a href='{0[0]:s}'>查看 {0[1]:d}+</a>".format(disc_link)
#         else:
#             disc_str = "评论: <a href='{0[0]:s}'>查看</a>".format(disc_link)
#         if partner == 1:
#             return e_lip * 3 + '欢乐联盟' + e_lip * 3 + """
# {4:s} 编号: <code>{0[0]:d}</code>
# {5:s} 国籍: {18:s}
# {6:s} 年龄: {0[2]:d}
# {7:s} 身高: {0[3]:d}cm
# {8:s} 体重: {0[4]:d}kg
# {9:s} 胸围: {0[5]:s}
# {11:s} 住址: {0[7]:s}
# {12:s} 特点: {0[8]:s}
# {13:s} 等级: {2:s}
# {14:s}
# {15:s} 状态: {3:s}
# {16:s} {17:s}
# """.format(row, mean('info_baby_cn', row[6]), mean('info_level_cn', row[9]), mean('info_status_cn', row[12]), e_prize,
#            e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi, price_str, e_traffic,
#            e_pinglun, disc_str, mean('country', row[13]))
#         elif partner == 2:
#             return e_lip * 3 + '快乐园' + e_lip * 3 + """
# {4:s} 编号: <code>{0[0]:d}</code>
# {5:s} 国籍: {18:s}
# {6:s} 年龄: {0[2]:d}
# {7:s} 身高: {0[3]:d}cm
# {8:s} 体重: {0[4]:d}kg
# {9:s} 胸围: {0[5]:s}
# {11:s} 住址: {0[7]:s}
# {12:s} 特点: {0[8]:s}
# {13:s} 等级: {2:s}
# {14:s}
# {15:s} 状态: {3:s}
# {16:s} {17:s}
# """.format(row, mean('info_baby_cn', row[6]), mean('info_level_cn', row[9]), mean('info_status_cn', row[12]), e_prize,
#            e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi, price_str, e_traffic,
#            e_pinglun, disc_str, mean('country', row[13]))
#         else:
#             return e_lip * 3 + '欢乐园贵妃' + e_lip * 3 + """
# {4:s} 编号: <code>{0[0]:d}</code>
# {5:s} 国籍: {18:s}
# {6:s} 年龄: {0[2]:d}
# {7:s} 身高: {0[3]:d}cm
# {8:s} 体重: {0[4]:d}kg
# {9:s} 胸围: {0[5]:s}
# {11:s} 住址: {0[7]:s}
# {12:s} 特点: {0[8]:s}
# {13:s} 等级: {2:s}
# {14:s}
# {15:s} 状态: {3:s}
# {16:s} {17:s}
# """.format(row, mean('info_baby_cn', row[6]), mean('info_level_cn', row[9]),
#            mean('info_status_cn', row[12]), e_prize, e_cty, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby,
#            e_location, e_id, e_zoushi, price_str, e_traffic, e_pinglun, disc_str, mean('country',row[13]))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发送给动物
def get_content_to_girl(rst):
    try:
        price_list = rst[0][14].split(' ')
        if len(price_list) == 7:
            price = ""
            if price_list[0] != '0':
                price += "\n      1hour    1pops    {0:s}p".format(price_list[0])
            if price_list[1] != '0':
                price += "\n      3hour    2pops    {0:s}p".format(price_list[1])
            if price_list[2] != '0':
                price += "\n      6hour    3pops    {0:s}p".format(price_list[2])
            if price_list[3] != '0':
                price += "\n      8hour    unlimit  {0:s}p".format(price_list[3])
            if price_list[4] != '0':
                price += "\n      10hour  unlimit  {0:s}p".format(price_list[4])
            if price_list[5] != '0':
                price += "\n      12hour  unlimit  {0:s}p".format(price_list[5])
            if price_list[6] != '0':
                price += "\n      24hour  unlimit  {0:s}p".format(price_list[6])
        elif len(price_list) == 4:
            price = ""
            if price_list[0] != '0':
                price += "\n      1hour   1pop     {0:s}p".format(price_list[0])
            if price_list[1] != '0':
                price += "\n      3hour   2pops    {0:s}p".format(price_list[1])
            if price_list[2] != '0':
                price += "\n      6hour   3pops    {0:s}p".format(price_list[2])
            if price_list[3] != '0':
                price += "\n      1night  unlimit  {0:s}p".format(price_list[3])
        elif len(price_list) == 3:
            price = ""
            if price_list[0] != '0':
                price += "\n      3hour   1pop     {0:s}p".format(price_list[0])
            if price_list[1] != '0':
                price += "\n      6hour   2pops   {0:s}p".format(price_list[1])
            if price_list[2] != '0':
                price += "\n      1night  unlimit  {0:s}p".format(price_list[2])
        elif price_list[0] in ('', '-'):
            price = "No price"
        else:
            price = "massage {0:s}".format(rst[0][14])
        return e_lip * 3 + 'HappyZone\'s Girl' + e_lip * 3 + """
{1:s} No: {0[0]:d}
{2:s}Name: {0[1]:s}
{3:s} Age: {0[2]:d}
{4:s} Height: {0[3]:d}cm
{5:s} Weight: {0[4]:d}kg
{6:s} Boobs: {0[5]:s}
{7:s} Baby: {15:s}
{8:s} Location: {0[7]:s}
{9:s} Special: {0[8]:s}
{10:s} Level: {16:s}
{11:s} Phone: {0[10]:s}
{11:s} Wechat: {0[11]:s}
{11:s} Telegram: {0[12]:s}
{11:s} Facebook: {0[13]:s}
{12:s} Price: {18:s}
{13:s} Viewed: {0[16]:d}
{14:s} Status: {17:s}
""".format(rst[0], e_prize, e_girl, e_loudou, e_ruler, e_trade, e_sex_wear, e_baby, e_location, e_id, e_zoushi,
           e_phone, e_money, e_eyes, e_traffic, mean('info_baby', rst[0][6]), mean('info_level_cn', rst[0][9]),
           mean('info_status', rst[0][17]),  price)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 评论区发送主模板
def get_content_disc(ind):
    try:
        sql_str = "select id,name from zoo_info where id={0:s}".format(ind)
        rst = tg_connector.query(sql_str)
        return """{1:s} 编号: {0[0]:d}
<b><a href='https://t.me/happyzone168'>欢乐园</a></b> 下单请联系：<b>@happy_167</b>
""".format(rst[0], e_prize)
# {2:s}姓名: {0[1]:s}
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 插入权限表
def insert_limit(user_id, type):
    try:
        sql_str = "insert into zoo_limit(id,name,type,updated_at) " \
                  "values({0:d},'{1:s}','{2:s}',CURRENT_TIMESTAMP()) on duplicate key update " \
                  "updated_at=CURRENT_TIMESTAMP()" \
            .format(user_id, f_chats_id(user_id)[2], type)
        tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 检查权限表
def check_limit(user_id, type):
    try:
        sql_str = "select count(1) from zoo_limit where id={0:d} and type='{1:s}'".format(user_id, type)
        rst = tg_connector.query(sql_str)
        return rst[0][0] == 1
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发送动物信息
async def send_girl(sql_str, client, chat_id, reply_to_message_id=None, partner=0):
    # 在展示动物信息如报错，则需要整个命令停止，所以此处不抓取报错信息
    # try:
    #     pass
    # except Exception as e:
    #     sys_log.write_log(traceback.format_exc(), 'a')
    rst = read_connector.query(sql_str)
    if len(rst) == 0:
        rec_msg = [await client.send_message(chat_id,
                                             '没有找到相关的信息',
                                             parse_mode=ParseMode.HTML,
                                             reply_to_message_id=reply_to_message_id)]
    else:
        for row in rst:
            # 获取信息内容
            content = await get_content_to_guest(row, partner=partner)
            # 发送消息
            try:
                media_list = await media.get_media(client, row[11].split('|'), content)
                rec_msg = await client.send_media_group(chat_id,
                                                        media=media_list,
                                                        disable_notification=True,
                                                        reply_to_message_id=reply_to_message_id)

            except Exception as e:
                await media.update_reget(row[11].split('|'), f_client_username(client))
                media_list = await media.get_media(client, row[11].split('|'), content)
                rec_msg = await client.send_media_group(chat_id,
                                                        media=media_list,
                                                        disable_notification=True,
                                                        reply_to_message_id=reply_to_message_id)
            if len(rst) > 1:
                await asyncio.sleep(2)
    return rec_msg


# 设置动物分类
# add 增加  数字类型
# type 分类编号  字符类型
# delete:删除
def set_class(id=0, type='', opt='add'):
    try:
        sql_str = ''
        if opt == 'add':
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{1:s}' from zoo_info where " \
                      "id={0:d} on duplicate key update girl_name=girl_name".format(id, type)
        elif opt == 'delete' and id != 0 and type != '':
            sql_str = "delete from zoo_class where girl_id={0:d} and type='{1:s}'".format(id, type)
        elif opt == 'delete' and id == 0 and type != '':
            sql_str = "delete from zoo_class where type='{0:s}'".format(type)
        elif opt == 'delete' and id != 0 and type == '':
            sql_str = "delete from zoo_class where girl_id={0:d}".format(id)
        if sql_str != '':
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取动物分类情况
# id 字符类型
# type 字符类型
def get_class(id='', type=''):
    try:
        if id != '' and type == '':
            sql_str = "select girl_id,girl_name,type from zoo_class where girl_id={0:s} order by type+0".format(id)
        elif id == '' and type != '':
            sql_str = "select girl_id,girl_name,type from zoo_class where type='{0:s}' order by girl_id".format(type)
        else:
            sql_str = "select girl_id,girl_name,type from zoo_class where girl_id={0:s} and type='{1:s}' " \
                      "order by girl_id, type+0".format(id, type)
        return tg_connector.query(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 自动更新动物分类
# class_list 自动分类编号列表 字符类型
def auto_class(class_list=['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '21', '22']):
    try:
        # 空闲在岗 同步更新空闲列表
        class_type = '1'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "select GROUP_CONCAT(content) from zoo_note where id in ('board_available','auto_available')"
            rst = tg_connector.query(sql_str)
            if rst[0][0] not in ('', '-'):
                id_list = []
                name_list = []
                for item in rst[0][0].split(','):
                    if item.isdigit():
                        id_list.append(item)
                    else:
                        name_list.append(item)
                id_str = '0' if len(id_list) == 0 else ','.join(id_list)
                name_str = "''" if len(name_list) == 0 else "','".join(name_list)
                sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{2:s}' from zoo_info where " \
                          "status in ('1','2') and (id in ({0:s}) or name in ('{1:s}'))".format(id_str, name_str,
                                                                                                class_type)
                tg_connector.exe(sql_str)
        # 英文空闲在岗
        class_type = '21'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "select GROUP_CONCAT(content) from zoo_note where id in ('board_available','auto_available')"
            rst = tg_connector.query(sql_str)
            if rst[0][0] not in ('', '-'):
                id_list = []
                name_list = []
                for item in rst[0][0].split(','):
                    if item.isdigit():
                        id_list.append(item)
                    else:
                        name_list.append(item)
                id_str = '0' if len(id_list) == 0 else ','.join(id_list)
                name_str = "''" if len(name_list) == 0 else "','".join(name_list)
                sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{2:s}' from zoo_info where " \
                          "status in ('1','2') and (id in ({0:s}) or name in ('{1:s}'))".format(id_str, name_str,
                                                                                                class_type)
                tg_connector.exe(sql_str)
        # 异国风情 国籍 非混血 和 菲律宾
        # 改成高端越妹
        class_type = '2'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and country not in ('0','1')".format(class_type)
            tg_connector.exe(sql_str)
        # 丰乳肥臀：取胸围在36以上（含36）的动物
        class_type = '5'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and left(boobs, 2)>=36".format(class_type)
            tg_connector.exe(sql_str)
        # 长腿模特：取身高在165以上的动物
        class_type = '6'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and height>=165".format(class_type)
            tg_connector.exe(sql_str)
        # 年轻稚嫩：取年轻在20以下（含20岁）的动物
        class_type = '7'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and age<=20".format(class_type)
            tg_connector.exe(sql_str)
        # 特殊玩法：模糊匹配特色中有以下关键字的动物
        class_type = '8'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and (special like '%双飞%' or special like '%蛋%' or special like '%SM%' or " \
                      "special like '%口爆%' or special like '%内射%' or special like '%颜射%')".format(class_type)
            tg_connector.exe(sql_str)
        # 新车试驾：取最新的在线动物
        class_type = '9'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') order by id desc limit 40".format(class_type)
            tg_connector.exe(sql_str)
        # 英文新车试驾
        class_type = '22'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') order by id desc limit 40".format(class_type)
            tg_connector.exe(sql_str)
        # 高价专区：取3小时>10k  6小时>=14k 1晚>=18k的动物
        # 改成 高端菲妹
        # 高价专区：取3小时>14k  6小时>=18k 1晚>=22k的动物（需要调整的逻辑）
        class_type = '10'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and country in ('0','1') and (SUBSTRING_INDEX(price_out,' ',-1)>=18000 or " \
                      "SUBSTRING_INDEX(SUBSTRING_INDEX(price_out,' ',-2),' ',1)>=14000 or " \
                      "SUBSTRING_INDEX(SUBSTRING_INDEX(price_out,' ',-3),' ',1)>10000)".format(class_type)
            tg_connector.exe(sql_str)
        # 低价专区：取3小时<=7k  6小时<=10k 1晚<=12k的动物
        class_type = '11'
        if class_type in class_list:
            set_class(type=class_type, opt='delete')
            sql_str = "insert into zoo_class(girl_id,girl_name,type) select id,name,'{0:s}' from zoo_info where " \
                      "status in ('1','2') and (SUBSTRING_INDEX(price_out,' ',-1) between 1000 and 12000 or " \
                      "SUBSTRING_INDEX(SUBSTRING_INDEX(price_out,' ',-2),' ',1) between 1000 and 10000 or " \
                      "SUBSTRING_INDEX(SUBSTRING_INDEX(price_out,' ',-3),' ',1) between 1000 and 7000)".format(
                class_type)
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 展示客户消息  param_list: 类型所需要的内容   user_id转发人id
# type  chat_id 通过会话ID精准匹配 数值
#       std_id 通过格式"1234A"精确匹配
#       short_id 通过最后4位chat_id模糊匹配 字符串
#       name 通过内部名模糊匹配
#       outname 通过外部名模糊匹配
#       id_name 通过最后4位chat_id 和 name前缀模糊匹配 2个字符串参数
async def show_guest(param_list, user_id, type='chat_id'):
    try:
        if type == 'chat_id':
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where chat_id={0:d}".format(param_list[0])
        elif type == 'std_id':
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where std_id='{0:s}'".format(param_list[0])
        elif type == 'short_id':
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where std_id like '{0:s}%'".format(param_list[0])
        elif type == 'name':
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where name like '%{0:s}%'".format(param_list[0])
        elif type == 'outname':
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where outname like '%{0:s}%'".format(param_list[0])
        elif type == 'id_name':
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where std_id like '{0[0]:s}%' and name " \
                      "like '{0[1]:s}%'".format(param_list)
        else:
            return
        rst = tg_connector.query(sql_str)
        if len(rst) == 0:
            content = "没有该客户的信息：{0:s}".format(str(param_list[0]))
            await clients['myhappygirlsbot'].send_message(user_id, content, parse_mode=ParseMode.HTML)
        else:
            for row in rst:
                content = get_content_guest(row)
                prompt = e_remind + "请输入你需要的操作\n"
                await clients['myhappygirlsbot'].send_message(user_id,
                                                              content + line + prompt,
                                                              parse_mode=ParseMode.HTML,
                                                              disable_web_page_preview=True,
                                                              reply_markup=get_com_imp('sa_guest'))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取讨论区链接（正式代码，等修复好评论区代码后复用）
# ind 字符型编号
async def get_disc_link(ind):
    try:
        sql_str = "select count(1),max(case when chat_id={0:d} then msg_list else null end),max(case when chat_id={1:d} " \
                  "then msg_list else null end) from chat_message where chat_id in ({0:d}, {1:d}) and kind='{2:s}' and " \
                  "ind='{3:s}'".format(cm_pinglun.chat_id, gm_pinglun.chat_id, cm_pinglun.kind, ind)
        rst = tg_connector.query(sql_str)
        if rst[0][0] != 2:
            # 在评论频道发动物列表
            rec_msg = await clients['myhappygirlsbot'].send_message(cm_pinglun.chat_id,
                                                                    get_content_disc(ind),
                                                                    parse_mode=ParseMode.HTML,
                                                                    disable_web_page_preview=True,
                                                                    disable_notification=True)
            # 更新评论频道表
            cm_pinglun.add_row(ind, rec_msg)
            cnt_try = 0
            while cnt_try < 5:
                cnt_try += 1
                await asyncio.sleep(4)
                rst = tg_connector.query(sql_str)
                if rst[0][0] == 2:
                    break
        link = "https://t.me/{0:s}/{1:s}?comment={2:s}"\
            .format(f_chats_id(cm_pinglun.chat_id)[5], rst[0][1].strip(','), rst[0][2].strip(','))
        sql_str = "select count(1) from chat_message where chat_id={0:d} and kind='讨论' and ind like '{1:s}#%' "\
            .format(gm_pinglun.chat_id, ind)
        rst = tg_connector.query(sql_str)
        return [link, rst[0][0]]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 展示公告牌
async def show_board(force_resend=False):
    try:
        # 检查公告牌是否为最后的消息
        sql_str = "select case when kind='公告牌' then 1 else 0 end from chat_message where chat_id={0:d} order by " \
                  "updated_at desc limit 1".format(cm[0].chat_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] == 1 and not force_resend:
            return
        # 清理主频道中已有的公告牌
        await cm[0].del_msg('0', clients['happy_167'], "公告牌")
        # 发送新的公告牌
        content = get_content_board()
        rec_msg = await clients['myhappygirlsbot'].send_message(cm[0].chat_id, content,
                                                                parse_mode=ParseMode.HTML,
                                                                reply_markup=get_com_imp('board'),
                                                                disable_web_page_preview=True,
                                                                disable_notification=True)
        # 更新主频道表
        cm[0].add_row('0', rec_msg, "公告牌")
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 展示英文公告牌
async def show_board_en(force_resend=False):
    try:
        # 检查公告牌是否为最后的消息
        sql_str = "select case when kind='公告牌' then 1 else 0 end from chat_message where chat_id={0:d} order by " \
                  "updated_at desc limit 1".format(cm[20].chat_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] == 1 and not force_resend:
            return
        # 清理主频道中已有的公告牌
        await cm[20].del_msg('0', clients['happy_167'], "公告牌")
        # 发送新的公告牌
        content = get_content_board_en()
        rec_msg = await clients['myhappygirlsbot'].send_message(cm[20].chat_id, content,
                                                                parse_mode=ParseMode.HTML,
                                                                reply_markup=get_com_imp('board_en'),
                                                                disable_web_page_preview=True,
                                                                disable_notification=True)
        # 更新主频道表
        cm[20].add_row('0', rec_msg, "公告牌")
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新home信息  no 字符类型
async def update_home(no):
    try:
        # 清理主频道中已有的记录
        await cm[0].del_msg(no, clients['happy_167'])
        # 确定发送信息
        sql_str = "select a.id,a.name,a.age,a.height,a.weight,a.boobs,a.baby,a.location,a.special,a.level," \
                  "a.price_out,a.picture,a.status,a.country from zoo_info a where id={0:s} and " \
                  "status in ('1','2')".format(no)
        rst = tg_connector.query(sql_str)
        if len(rst) > 0:
            # 提取主频道内容
            content = await get_content_home(rst, True)
            # 发媒体
            rec_msg = ''
            try:
                media_list = await media.get_media(clients['myhappygirlsbot'], rst[0][11].split('|'), content)
                rec_msg = await clients['myhappygirlsbot'].send_media_group(cm[0].chat_id,
                                                                            media=media_list,
                                                                            disable_notification=True)
            except FloodWait as e:
                await asyncio.sleep(e.value)
            except Exception as e:
                await media.update_reget(rst[0][11].split('|'), 'myhappygirlsbot')
                media_list = await media.get_media(clients['myhappygirlsbot'], rst[0][11].split('|'), content)
                rec_msg = await clients['myhappygirlsbot'].send_media_group(cm[0].chat_id,
                                                                            media=media_list,
                                                                            disable_notification=True)
            # 更新主频道表
            cm[0].add_row(no, rec_msg)
            # 刚发送过妹子，需要暂缓时间
            await asyncio.sleep(2)
        else:
            # 删除主频道表中该信息
            cm[0].del_row(no)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新home_en信息  no 字符类型
async def update_home_en(no):
    try:
        # 清理英文主频道中已有的记录
        await cm[20].del_msg(no, clients['happy_167'])
        # 确定发送信息
        sql_str = "select a.id,a.name,a.age,a.height,a.weight,a.boobs,a.baby,a.location,a.special,a.level," \
                  "a.price_out,a.picture,a.status,a.country,a.special_en from zoo_info a where id={0:s} and " \
                  "status in ('1','2')".format(no)
        rst = tg_connector.query(sql_str)
        if len(rst) > 0:
            # 提取英文主频道内容
            content_en = await get_content_home_en(rst, True)
            # 发媒体
            rec_msg_en = ''
            try:
                media_list_en = await media.get_media(clients['myhappygirlsbot'], rst[0][11].split('|'), content_en)
                rec_msg_en = await clients['myhappygirlsbot'].send_media_group(cm[20].chat_id,
                                                                               media=media_list_en,
                                                                               disable_notification=True)
            except FloodWait as e:
                await asyncio.sleep(e.value)
            except Exception as e:
                # 重发英文频道的卡片
                media_list_en = await media.get_media(clients['myhappygirlsbot'], rst[0][11].split('|'), content_en)
                rec_msg_en = await clients['myhappygirlsbot'].send_media_group(cm[20].chat_id,
                                                                               media=media_list_en,
                                                                               disable_notification=True)
            # 更新英文主频道表
            cm[20].add_row(no, rec_msg_en)
            # 刚发送过妹子，需要暂缓时间
            await asyncio.sleep(2)
        else:
            # 删除英文主频道表中该信息
            cm[20].del_row(no)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从home复制公告牌到子频道
# type  分类编号 数字类型
async def copy_board(type, force_resend=False):
    try:
        # 检查公告牌是否为最后的消息
        sql_str = "select case when kind='公告牌' then 1 else 0 end from chat_message where chat_id={0:d} order by " \
                  "updated_at desc limit 1".format(cm[type].chat_id)
        rst = tg_connector.query(sql_str)
        if len(rst) > 0 and rst[0][0] == 1 and not force_resend:
            return
        # 清理子频道中已有的公告牌
        await cm[type].del_msg('0', clients['happy_167'], "公告牌")
        # 发送新的公告牌
        home_board = cm[0].get_row('0', "公告牌")
        if len(home_board) == 0:
            await asyncio.sleep(10)
            home_board = cm[0].get_row('0', "公告牌")
        try:
            await asyncio.sleep(3)
            rec_msg = await clients['myhappygirlsbot'].copy_message(cm[type].chat_id,
                                                                    cm[0].chat_id,
                                                                    int(home_board[0][4].split(',')[1]),
                                                                    reply_markup=get_com_imp('board'),
                                                                    disable_notification=False)
        except FloodWait as e:
            await asyncio.sleep(e.value + 1)
            rec_msg = await clients['myhappygirlsbot'].copy_message(cm[type].chat_id,
                                                                    cm[0].chat_id,
                                                                    int(home_board[0][4].split(',')[1]),
                                                                    reply_markup=get_com_imp('board'),
                                                                    disable_notification=False)
        except Exception as e:
            print_both("检查复制公告中断问题")
            print_both("{0:d} {1:d} {2:d}".format(cm[type].chat_id, cm[0].chat_id, int(home_board[0][4].split(',')[1])))
            sys_log.write_log(traceback.format_exc(), 'a')
        # 更新子频道的公告牌
        cm[type].add_row('0', rec_msg, "公告牌")
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从home复制公告牌到英文子频道
# type  分类编号 数字类型
async def copy_board_en(type, force_resend=False):
    try:
        # 检查公告牌是否为最后的消息
        sql_str = "select case when kind='公告牌' then 1 else 0 end from chat_message where chat_id={0:d} order by " \
                  "updated_at desc limit 1".format(cm[type].chat_id)
        rst = tg_connector.query(sql_str)
        if len(rst) > 0 and rst[0][0] == 1 and not force_resend:
            return
        # 清理子频道中已有的公告牌
        await cm[type].del_msg('0', clients['happy_167'], "公告牌")
        # 发送新的公告牌
        home_board = cm[20].get_row('0', "公告牌")
        if len(home_board) == 0:
            await asyncio.sleep(10)
            home_board = cm[20].get_row('0', "公告牌")
        try:
            await asyncio.sleep(3)
            rec_msg = await clients['myhappygirlsbot'].copy_message(cm[type].chat_id,
                                                                    cm[20].chat_id,
                                                                    int(home_board[0][4].split(',')[1]),
                                                                    reply_markup=get_com_imp('board_en'),
                                                                    disable_notification=False)
        except FloodWait as e:
            await asyncio.sleep(e.value + 1)
            rec_msg = await clients['myhappygirlsbot'].copy_message(cm[type].chat_id,
                                                                    cm[20].chat_id,
                                                                    int(home_board[0][4].split(',')[1]),
                                                                    reply_markup=get_com_imp('board_en'),
                                                                    disable_notification=False)
        except Exception as e:
            print_both("检查复制公告中断问题")
            print_both("{0:d} {1:d} {2:d}".format(cm[type].chat_id, cm[20].chat_id, int(home_board[0][4].split(',')[1])))
            sys_log.write_log(traceback.format_exc(), 'a')
        # 更新子频道的公告牌
        cm[type].add_row('0', rec_msg, "公告牌")
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从home复制信息到子频道
# no    动物编号 字符类型
# type  分类编号 数字类型
# 返回 [是否需要更新公告牌, 是否需要等待操作]
# 返回 是否需要等待操作
async def copy_to_class(no, type):
    try:
        # 若该动物没有此分类，则删除子频道中记录
        if len(get_class(no, str(type))) == 0:
            await cm[type].del_msg(no, clients['happy_167'])
            cm[type].del_row(no)
            return False
        # 检查库表中的记录情况
        msg_home = cm[0].get_row(no)
        msg_sub = cm[type].get_row(no)
        # 补充因copy导致的链接缺失问题，重新生成 captions
        sql_str = "select a.id,a.name,a.age,a.height,a.weight,a.boobs,a.baby,a.location,a.special,a.level,a.price_out," \
                  "a.picture,a.status,a.country from zoo_info a where id={0:s}".format(no)
        # (home无记录，sub无记录) 或 (home记录，sub记录，home更新时间早) 不操作
        if (len(msg_home) == 0 and len(msg_sub) == 0) or (len(msg_home) > 0 and len(msg_sub) > 0 and msg_home[0][5] <= msg_sub[0][5]):
            return False
        # home无记录，sub记录 删除记录
        elif len(msg_home) == 0 and len(msg_sub) > 0:
            await cm[type].del_msg(no, clients['happy_167'])
            cm[type].del_row(no)
            return False
        # home记录，sub无记录 新增记录
        elif len(msg_home) > 0 and len(msg_sub) == 0:
            # 重新生成 captions
            rst = tg_connector.query(sql_str)
            content = await get_content_home(rst, True)
            rec_msg = await clients['myhappygirlsbot'].copy_media_group(cm[type].chat_id,
                                                                        cm[0].chat_id,
                                                                        int(msg_home[0][4].split(',')[1]),
                                                                        captions=content,
                                                                        disable_notification=False)
            cm[type].add_row(no, rec_msg)
            return True
        # home记录，sub记录，home更新时间早 删除旧记录，新增记录
        elif len(msg_home) > 0 and len(msg_sub) > 0 and msg_home[0][5] > msg_sub[0][5]:
            # 重新生成 captions
            rst = tg_connector.query(sql_str)
            content = await get_content_home(rst, True)
            await cm[type].del_msg(no, clients['happy_167'])
            rec_msg = await clients['myhappygirlsbot'].copy_media_group(cm[type].chat_id,
                                                                        cm[0].chat_id,
                                                                        int(msg_home[0][4].split(',')[1]),
                                                                        captions=content,
                                                                        disable_notification=False)
            cm[type].add_row(no, rec_msg)
            return True
        else:
            return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从home复制信息到英文子频道
# no    动物编号 字符类型
# type  分类编号 数字类型
# 返回 [是否需要更新公告牌, 是否需要等待操作]
# 返回 是否需要等待操作
async def copy_to_class_en(no, type):
    try:
        # 若该动物没有此分类，则删除子频道中记录
        if len(get_class(no, str(type))) == 0:
            await cm[type].del_msg(no, clients['happy_167'])
            cm[type].del_row(no)
            return False
        # 检查库表中的记录情况
        msg_home = cm[20].get_row(no)
        msg_sub = cm[type].get_row(no)
        # 补充因copy导致的链接缺失问题，重新生成 captions
        sql_str = "select a.id,a.name,a.age,a.height,a.weight,a.boobs,a.baby,a.location,a.special,a.level,a.price_out," \
                  "a.picture,a.status,a.country,a.special_en from zoo_info a where id={0:s}".format(no)
        # (home无记录，sub无记录) 或 (home记录，sub记录，home更新时间早) 不操作
        if (len(msg_home) == 0 and len(msg_sub) == 0) or (len(msg_home) > 0 and len(msg_sub) > 0 and msg_home[0][5] <= msg_sub[0][5]):
            return False
        # home无记录，sub记录 删除记录
        elif len(msg_home) == 0 and len(msg_sub) > 0:
            await cm[type].del_msg(no, clients['happy_167'])
            cm[type].del_row(no)
            return False
        # home记录，sub无记录 新增记录
        elif len(msg_home) > 0 and len(msg_sub) == 0:
            # 重新生成 captions
            rst = tg_connector.query(sql_str)
            content = await get_content_home_en(rst, True)
            rec_msg = await clients['myhappygirlsbot'].copy_media_group(cm[type].chat_id,
                                                                        cm[20].chat_id,
                                                                        int(msg_home[0][4].split(',')[1]),
                                                                        captions=content,
                                                                        disable_notification=False)
            cm[type].add_row(no, rec_msg)
            return True
        # home记录，sub记录，home更新时间早 删除旧记录，新增记录
        elif len(msg_home) > 0 and len(msg_sub) > 0 and msg_home[0][5] > msg_sub[0][5]:
            # 重新生成 captions
            rst = tg_connector.query(sql_str)
            content = await get_content_home_en(rst, True)
            await cm[type].del_msg(no, clients['happy_167'])
            rec_msg = await clients['myhappygirlsbot'].copy_media_group(cm[type].chat_id,
                                                                        cm[20].chat_id,
                                                                        int(msg_home[0][4].split(',')[1]),
                                                                        captions=content,
                                                                        disable_notification=False)
            cm[type].add_row(no, rec_msg)
            return True
        else:
            return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从home复制信息到讨论区
# no    动物编号 字符类型
async def copy_to_disc(no):
    try:
        # 检查库表中的记录情况
        msg_home = cm[0].get_row(no)
        msg_disc = gm_pinglun.get_row(no, "卡片")
        sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture,status," \
                  "country from zoo_info where id={0:s}".format(no)
        # disc无记录，新增记录
        if len(msg_disc) == 0:
            msg_list = gm_pinglun.get_row(no, "动物列表")
            rec_msg = await send_girl(sql_str,
                                      clients['myhappygirlsbot'],
                                      gm_pinglun.chat_id,
                                      reply_to_message_id=int(msg_list[0][4].strip(',')))
            gm_pinglun.add_row(no, rec_msg, "卡片")
        # home记录，disc记录，disc更新时间早 删除旧记录，新增记录
        elif len(msg_home) > 0 and len(msg_disc) > 0 and msg_home[0][5] > msg_disc[0][5]:
            await gm_pinglun.del_msg(no, clients['happy_167'], "卡片")
            msg_list = gm_pinglun.get_row(no, "动物列表")
            rec_msg = await send_girl(sql_str,
                                      clients['myhappygirlsbot'],
                                      gm_pinglun.chat_id,
                                      reply_to_message_id=int(msg_list[0][4].strip(',')))
            gm_pinglun.add_row(no, rec_msg, "卡片")
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新子频道
# type_list  分类编号列表 数字类型
async def update_sub_chl(type_list=[1, 2, 3, 5, 6, 7, 8, 9, 10, 11]):
    try:
        for type in type_list:
            # 固定当前更新号
            cur_time = int(time.time())
            cnt = 0
            while updateing[type] != 0 and cnt < 15:
                # 申请更新子频道
                updateing[type] = 1
                cnt += 1
                await asyncio.sleep(2)
            else:
                # 设置当前更新号
                updateing[type] = cur_time
            # 提取主频道中有分类，待更新到子频道的记录
            sql_str = "select e.* from (select a.ind as girl_id,b.type,a.updated_at from chat_message a join " \
                      "zoo_class b on a.ind=b.girl_id where a.chat_id={0:d} and a.kind='动物列表') e left join (select " \
                      "c.ind as girl_id,d.value_str as type,c.updated_at from chat_message c join (select * from " \
                      "params where type='Zoo' and name='频道分类对应') d on c.chat_id = d.value_bigint where " \
                      "c.kind='动物列表') f on e.girl_id=f.girl_id and e.type=f.type where (f.girl_id is null or " \
                      "e.updated_at>f.updated_at) and e.type='{1:d}' order by 2,3".format(cm[0].chat_id, type)
            rst = tg_connector.query(sql_str)
            for row in rst:
                # 放弃当前的更新，退出
                if updateing[type] != cur_time:
                    updateing[type] = 0
                    return
                else:
                    wait_ind = await copy_to_class(row[0], int(row[1]))
                    if wait_ind:
                        await asyncio.sleep(1)
            # 提取子频道有，但主频道中没有分类要求的记录
            sql_str = "select f.* from (select a.ind as girl_id,b.type,a.updated_at from chat_message a join " \
                      "zoo_class b on a.ind=b.girl_id where a.chat_id={0:d} and a.kind='动物列表') e right join (select " \
                      "c.ind as girl_id,d.value_str as type,c.updated_at from chat_message c join (select * from " \
                      "params where type='Zoo' and name='频道分类对应') d on c.chat_id = d.value_bigint where " \
                      "c.kind='动物列表') f on e.girl_id=f.girl_id and e.type=f.type where e.girl_id is null and " \
                      "f.type='{1:d}' order by 2,3".format(cm[0].chat_id, type)
            rst = tg_connector.query(sql_str)
            for row in rst:
                # 放弃当前的更新，退出
                if updateing[type] != cur_time:
                    updateing[type] = 0
                    return
                else:
                    wait_ind = await copy_to_class(row[0], int(row[1]))
                    if wait_ind:
                        await asyncio.sleep(1)
            # 更新子频道的公告牌
            await copy_board(type)
            updateing[type] = 0
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新英文子频道
# type_list  分类编号列表 数字类型
async def update_sub_chl_en(type_list=[21, 22]):
    try:
        for type in type_list:
            # 固定当前更新号
            cur_time = int(time.time())
            cnt = 0
            while updateing[type] != 0 and cnt < 15:
                # 申请更新子频道
                updateing[type] = 1
                cnt += 1
                await asyncio.sleep(2)
            else:
                # 设置当前更新号
                updateing[type] = cur_time
            # 提取主频道中有分类，待更新到子频道的记录
            sql_str = "select e.* from (select a.ind as girl_id,b.type,a.updated_at from chat_message a join " \
                      "zoo_class b on a.ind=b.girl_id where a.chat_id={0:d} and a.kind='动物列表') e left join (select " \
                      "c.ind as girl_id,d.value_str as type,c.updated_at from chat_message c join (select * from " \
                      "params where type='Zoo' and name='频道分类对应') d on c.chat_id = d.value_bigint where " \
                      "c.kind='动物列表') f on e.girl_id=f.girl_id and e.type=f.type where (f.girl_id is null or " \
                      "e.updated_at>f.updated_at) and e.type='{1:d}' order by 2,3".format(cm[20].chat_id, type)
            rst = tg_connector.query(sql_str)
            for row in rst:
                # 放弃当前的更新，退出
                if updateing[type] != cur_time:
                    updateing[type] = 0
                    return
                else:
                    wait_ind = await copy_to_class_en(row[0], int(row[1]))
                    if wait_ind:
                        await asyncio.sleep(1)
            # 提取子频道有，但主频道中没有分类要求的记录
            sql_str = "select f.* from (select a.ind as girl_id,b.type,a.updated_at from chat_message a join " \
                      "zoo_class b on a.ind=b.girl_id where a.chat_id={0:d} and a.kind='动物列表') e right join (select " \
                      "c.ind as girl_id,d.value_str as type,c.updated_at from chat_message c join (select * from " \
                      "params where type='Zoo' and name='频道分类对应') d on c.chat_id = d.value_bigint where " \
                      "c.kind='动物列表') f on e.girl_id=f.girl_id and e.type=f.type where e.girl_id is null and " \
                      "f.type='{1:d}' order by 2,3".format(cm[20].chat_id, type)
            rst = tg_connector.query(sql_str)
            for row in rst:
                # 放弃当前的更新，退出
                if updateing[type] != cur_time:
                    updateing[type] = 0
                    return
                else:
                    wait_ind = await copy_to_class_en(row[0], int(row[1]))
                    if wait_ind:
                        await asyncio.sleep(1)
            # 更新子频道的公告牌
            await copy_board_en(type)
            updateing[type] = 0
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 把评论区消息展示到主频道
# 返回 Bool 是否需要等待操作
async def show_pinglun_home():
    try:
        wait_ind = False
        # 检查是否需要展示讨论消息，相隔n条信息
        sql_str = "select sum(case when kind='讨论' then 1 else 0 end) from (select * from chat_message where " \
                  "chat_id={0:d} and kind in ('动物列表','讨论') order by updated_at desc limit 3) a".format(cm[0].chat_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] > 0:
            return wait_ind
        # 提取需要展示的讨论消息
        sql_str = "select a.ind,a.msg_list,b.msg_list,case when a.ind like '0#%' and date_format(now(),'%w') in " \
                  "(1,3,5,6) and date_format(now(),'%k') in (19,20) then 0 else 1 end,case when b.ind is null then " \
                  "a.updated_at else '2000-01-01 00:00:00' end,case when b.ind is not null then b.updated_at else " \
                  "'2999-01-01 00:00:00' end from (select SUBSTRING_INDEX(e.ind,'#',1) id,e.* from chat_message e " \
                  "where chat_id={0:d} and kind='讨论') a left join (select * from zoo_info where status in ('1','2')) c " \
                  "on a.id=c.id left join (select * from chat_message where chat_id={1:d} and kind='讨论') b on " \
                  "a.ind = b.ind where a.updated_at<date_add(current_timestamp(),interval - 5 minute) and " \
                  "(a.id='0' or c.id is not null) order by 4,5 desc,6 limit 1"\
            .format(gm_pinglun.chat_id, cm[0].chat_id)
        rst = tg_connector.query(sql_str)
        # 若没有符合要求的待发送信息，退出
        if len(rst) == 0:
            return wait_ind
        # 提取文本内容
        msg_id = int(rst[0][1].split(',')[1])
        sent_msg = await clients['happy_167'].get_messages(gm_pinglun.chat_id, msg_id)
        if sent_msg.text is not None:
            content = sent_msg.text
        elif sent_msg.caption is not None:
            content = sent_msg.caption
        else:
            content = ''
        disc_link = await get_disc_link(rst[0][0].split('#')[0])
        if disc_link[1] > 0:
            content = content.replace("\n{0:s}".format(e_pinglun), "   <a href='{1[0]:s}'>更多评论 {1[1]:d}+</a>\n{0:s}"
                                      .format(e_pinglun, disc_link), 1)
        else:
            content = content.replace("\n{0:s}".format(e_pinglun), "   <a href='{1[0]:s}'>更多评论</a>\n{0:s}"
                                      .format(e_pinglun, disc_link), 1)
        # 提取媒体内容
        ind_list = rst[0][0].split('#')
        if ind_list[0] == '0':
            media_str = ind_list[1]
        else:
            sql_str = "select picture from zoo_info where id={0:s}".format(ind_list[0])
            rst2 = tg_connector.query(sql_str)
            media_str = rst2[0][0].split('|')[0] + '|' + ind_list[1]
        try:
            # 发送消息
            try:
                media_list = await media.get_media(clients['happy_167'], media_str.split('|'), content)
                send_msg = await clients['happy_167'].send_media_group(cm[0].chat_id,
                                                                       media=media_list,
                                                                       disable_notification=True)
            except Exception as e:
                await media.update_reget(media_str.split('|'), 'happy_167')
                media_list = await media.get_media(clients['happy_167'], media_str.split('|'), content)
                send_msg = await clients['happy_167'].send_media_group(cm[0].chat_id,
                                                                       media=media_list,
                                                                       disable_notification=True)
            cm[0].add_row(rst[0][0], send_msg, '讨论')
            wait_ind = wait_ind or True
            # 若主频道中原来已有消息，则删除
            if rst[0][2] is not None:
                await asyncio.sleep(2)
                await clients['happy_167'].delete_messages(cm[0].chat_id, list(map(int, rst[0][2].strip(',').split(','))))
            # 历史上最早的5条记录中，如果有评论信息，则删除
            sql_str = "select kind,ind,msg_list from chat_message where chat_id={0:d} order by updated_at limit 5"\
                .format(cm[0].chat_id)
            rst3 = tg_connector.query(sql_str)
            for row in rst3:
                if row[0] == '讨论' and rst[0][2] is not None:
                    await asyncio.sleep(2)
                    await clients['happy_167'].delete_messages(cm[0].chat_id, list(map(int, rst[0][2].strip(',').split(','))))
                else:
                    break
        except FloodWait as e:
            await asyncio.sleep(e.value)
        return wait_ind
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 把评论区消息展示到大众点评
# 更新的讨论数
# 返回 Bool 是否需要等待操作
async def show_pinglun_dianping(update_cnt=1):
    try:
        wait_ind = False
        # 检查是否有需要删除的讨论信息
        sql_str = "select a.* from (select SUBSTRING_INDEX(ind,'#',1) as girl_id, msg_list from chat_message where " \
                  "chat_id={0:d} and kind='讨论') a join zoo_info b on a.girl_id=b.id where b.status not in ('1','2')"\
            .format(cm[4].chat_id)
        rst = tg_connector.query(sql_str)
        for row in rst:
            clients['happy_167'].delete_messages(cm[4].chat_id, list(map(int, row[1].strip(',').split(','))))
            await asyncio.sleep(2)
        # 提取需要展示的讨论消息
        sql_str = "select a.ind,a.msg_list,c.msg_list,case when c.ind is null then a.updated_at else " \
                  "'2000-01-01 00:00:00' end,case when c.ind is not null then c.updated_at else '2999-01-01 00:00:00' " \
                  "end from (select *,SUBSTRING_INDEX(ind,'#',1) as girl_id from chat_message where chat_id={0:d} and " \
                  "kind='讨论') a join zoo_info b on a.girl_id=b.id left join (select * from chat_message where " \
                  "chat_id={1:d} and kind='讨论') c on a.ind=c.ind where b.status in ('1','2') and a.updated_at<" \
                  "date_add(current_timestamp(),interval - 5 minute) order by 4 desc,5 limit {2:d}"\
            .format(gm_pinglun.chat_id, cm[4].chat_id, update_cnt)
        rst = tg_connector.query(sql_str)
        for row in rst:
            # 提取文本内容
            msg_id = int(row[1].split(',')[1])
            sent_msg = await clients['happy_167'].get_messages(gm_pinglun.chat_id, msg_id)
            if sent_msg.text is not None:
                content = sent_msg.text
            elif sent_msg.caption is not None:
                content = sent_msg.caption
            else:
                content = ''
            disc_link = await get_disc_link(rst[0][0].split('#')[0])
            if disc_link[1] > 0:
                content = content.replace("\n{0:s}".format(e_pinglun), "   <a href='{1[0]:s}'>更多评论 {1[1]:d}+</a>\n{0:s}"
                                          .format(e_pinglun, disc_link), 1)
            else:
                content = content.replace("\n{0:s}".format(e_pinglun), "   <a href='{1[0]:s}'>更多评论</a>\n{0:s}"
                                          .format(e_pinglun, disc_link), 1)
            # 提取媒体内容
            ind_list = row[0].split('#')
            if ind_list[0] == '0':
                media_str = ind_list[1]
            else:
                sql_str = "select picture from zoo_info where id={0:s}".format(ind_list[0])
                rst2 = tg_connector.query(sql_str)
                media_str = rst2[0][0].split('|')[0] + '|' + ind_list[1]
            # 发送消息
            try:
                media_list = await media.get_media(clients['happy_167'], media_str.split('|'), content)
                send_msg = await clients['happy_167'].send_media_group(cm[4].chat_id,
                                                                       media=media_list,
                                                                       disable_notification=True)
            except Exception as e:
                await media.update_reget(media_str.split('|'), 'happy_167')
                media_list = await media.get_media(clients['happy_167'], media_str.split('|'), content)
                send_msg = await clients['happy_167'].send_media_group(cm[4].chat_id,
                                                                       media=media_list,
                                                                       disable_notification=True)
            cm[4].add_row(row[0], send_msg, '讨论')
            # 若主频道中原来已有消息，则删除
            if row[2] is not None:
                await asyncio.sleep(1)
                await clients['happy_167'].delete_messages(cm[4].chat_id, list(map(int, row[2].strip(',').split(','))))
            wait_ind = True
        return wait_ind
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 对下线的动物，清理主频道和大众点评中的评论
async def clean_status_pinglun():
    try:
        # 清理主频道中的评论
        sql_str = "select a.msg_list from (select *,SUBSTRING_INDEX(ind,'#',1) id from chat_message where " \
                  "chat_id={0:d} and kind='讨论') a join (select * from zoo_info where status not in ('1','2')) b on " \
                  "a.id=b.id".format(cm[0].chat_id)
        rst = tg_connector.query(sql_str)
        for row in rst:
            await clients['happy_167'].delete_messages(cm[0].chat_id, list(map(int, row[0].strip(',').split(','))))
            await asyncio.sleep(2)
        # 清理大众点评中的评论
        sql_str = "select a.msg_list from (select *,SUBSTRING_INDEX(ind,'#',1) id from chat_message where " \
                  "chat_id={0:d} and kind='讨论') a join (select * from zoo_info where status not in ('1','2')) b on " \
                  "a.id=b.id".format(cm[4].chat_id)
        rst = tg_connector.query(sql_str)
        for row in rst:
            await clients['happy_167'].delete_messages(cm[4].chat_id, list(map(int, row[0].strip(',').split(','))))
            await asyncio.sleep(2)
    except FloodWait as e:
        await asyncio.sleep(e.value)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新zoo_info中的special_en字段
def update_special_en(special):
    try:
        replacements = {
            '会一点中文': 'speak a little Chinese',
            '精通中文': 'speak Chinese well',
            '双飞': 'threesome',
            '口交': 'blow-job',
            '调情按摩': 'flirt massage',
            '日式按摩': 'nuru massage',
            '按摩': 'massage',
            '接受玩具': 'accept toy',
            '自带玩具': 'own toy',
            '鸳鸯浴': 'shower together',
            '白皮肤': 'white skin',
            '白皙皮肤': 'fair skin',
            '口爆': 'cum in mouth(plus fee)',
            '内射': 'sex without condom(plus fee)',
            '肛交': 'anus sex(plus fee)',
            '接吻': 'kiss',
            '亲吻': 'kiss',
            '真胸': 'real boobs',
            '擅长喝酒': 'good drinker',
            '善于喝酒': 'good drinker',
            '会喝一点酒': 'drink a little',
            '会喝酒': 'can drink',
            '可以喝酒': 'can drink',
            '舔蛋': 'Egg licking'
            }
        # 创建正则表达式，将所有要替换的子串组合为一个大的匹配模式
        pattern = re.compile("|".join(re.escape(k) for k in replacements.keys()))
        # 使用替换字典来替换匹配到的字符串
        special_tmp = pattern.sub(lambda m: replacements[m.group(0)], special)
        # 剩余中文统一翻译成英文
        special_en = trans(special_tmp, 'en')
        return special_en
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
