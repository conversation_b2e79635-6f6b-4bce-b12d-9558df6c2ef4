# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common import *
from classes2 import *
import asyncio
import re
import math
import random
import datetime
from pyrogram import Client, filters
from pyrogram.types import ReplyKeyboardMarkup, ReplyKeyboardRemove, ForceReply, InputMediaPhoto, InputMediaVideo

# 参数设定
params['adv_num'] = 8
params['adv_count'] = random.randint(0, params['adv_num']-1)


# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        if index == 'home':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_he + "全部贵妃", url="t.me/happyzone168"),
                 InlineKeyboardButton(e_hotheart + "新妃推荐", callback_data='next')],
                [InlineKeyboardButton(e_search + "精选贵妃", callback_data='search'),
                 InlineKeyboardButton(e_han + "开始约妃", url="t.me/happy_167")],
            ])
        elif index == 'contact':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_service + "联系我们", url="t.me/happy_167")]
            ])
        elif index == 'group':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_he + "全部贵妃", url="t.me/happyzone168"),
                 InlineKeyboardButton(e_bot + "机器人", url="t.me/happyzonebot"),
                 InlineKeyboardButton(e_han + "开始约妃", url="t.me/happy_167")]
            ])
        elif index == 'search':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_loudou + "年龄", callback_data='search_age'),
                 InlineKeyboardButton(e_ruler + "身高", callback_data='search_height'),
                 InlineKeyboardButton(e_trade + "体重", callback_data='search_weight')],
                [InlineKeyboardButton(e_sex_wear + "胸围", callback_data='search_boobs'),
                 InlineKeyboardButton(e_id + "特点", callback_data='search_special'),
                 InlineKeyboardButton(e_baby + "生育", callback_data='search_baby')],
                 # InlineKeyboardButton(e_zoushi + "等级", callback_data='search_level'),
                [InlineKeyboardButton(e_left + "上页", callback_data='search_PrePage'),
                 # InlineKeyboardButton(e_money + "价格", callback_data='search_price'),
                 InlineKeyboardButton(e_cty + "国籍", callback_data='search_country'),
                 InlineKeyboardButton(e_right + "下页", callback_data='search_NextPage')],
                [InlineKeyboardButton(e_home + "返回", callback_data='search_return'),
                 InlineKeyboardButton(e_cycle + "重选", callback_data='search'),
                 InlineKeyboardButton(e_han + "约妃", url="t.me/happy_167")]
            ])
        elif index == 'search_baby':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton("没有孩子", callback_data='search_baby_没有孩子'),
                 InlineKeyboardButton("有孩子", callback_data='search_baby_有孩子'),
                 InlineKeyboardButton("全部", callback_data='search_baby_全部')]
            ])
        elif index == 'search_country':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('country', '0'), callback_data='search_country_0'),
                 InlineKeyboardButton(mean('country', '1'), callback_data='search_country_1'),
                 InlineKeyboardButton(mean('country', '2'), callback_data='search_country_2')],
                [InlineKeyboardButton(mean('country', '3'), callback_data='search_country_3'),
                 InlineKeyboardButton(mean('country', '4'), callback_data='search_country_4'),
                 InlineKeyboardButton(mean('country', '5'), callback_data='search_country_5')]
            ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发送girl信息模板
async def send_model(client, user_id, chat_id):
    try:
        # 获取待发送信息
        sql_str = "select a.id,a.name,a.age,a.height,a.weight,a.boobs,a.baby,a.location,a.special,a.level,a.price_out," \
                  "a.picture,a.status,a.country,c.msg_list from zoo_info a join (select * from chat_message " \
                  "where chat_id={1:d} and kind='{2:s}') c on a.id=c.ind left join (select * from zoo_guest_rel where " \
                  "guest_id={0:d}) b on a.id=b.girl_id where a.status in ('1','2') order by ifnull(b.updated_at," \
                  "'2016-08-17 00:00:00'),a.viewed,a.id desc,a.level desc limit 1".format(user_id, cm[0].chat_id, cm[0].kind)
        rst = read_connector.query(sql_str)
        content = await get_content_home(rst, True)
        await client.copy_media_group(chat_id,
                                      cm[0].chat_id,
                                      int(rst[0][14].strip(',').split(',')[1]),
                                      captions=content,
                                      disable_notification=True)
        # 发送主界面
        await client.send_message(chat_id,
                                  e_flower * 3 + "  <b>寂寞</b> " + e_right3 + " <b>欢乐园</b>  " + e_flower * 3 +
                                  '\n' + adv(params['adv_count']),
                                  parse_mode=ParseMode.HTML,
                                  reply_markup=get_imp('home'),
                                  disable_web_page_preview=True
                                  )
        params['adv_count'] = (params['adv_count'] + 1) % params['adv_num']
        # 更新客户关系表
        sql_str = "insert into zoo_guest_rel(guest_id,guest_name,girl_id,girl_name,viewed,updated_at) " \
                  "values({1:d},'{2:s}',{0[0]:d},'{0[1]:s}',1,CURRENT_TIMESTAMP()) on duplicate key update " \
                  "girl_name='{0[1]:s}', viewed=viewed+1, updated_at=CURRENT_TIMESTAMP()" \
            .format(rst[0], user_id, f_chats_id(user_id)[2])
        tg_connector.exe(sql_str)
        # 更新zoo表
        if user_id not in admin:
            sql_str = "update zoo_info set viewed=viewed+1 where id={0[0]:d}".format(rst[0])
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新客户信息 page=0 是展示本页
def new_page(pre_content, user_id, page=0):
    try:
        sql_str_sub = "where a.status in ('1','2')"
        # 确认精选项目
        for match in re.findall(e_search + '(.*?): (.*?)\n', pre_content, re.S):
            if match[0] == '年龄':
                ages = match[1].split('-')
                sql_str_sub += " and age between {0[0]:s} and {0[1]:s}".format(ages)
            elif match[0] == '身高':
                height = match[1].split('-')
                sql_str_sub += " and height between {0[0]:s} and {0[1]:s}".format(height)
            elif match[0] == '体重':
                weight = match[1].split('-')
                sql_str_sub += " and weight between {0[0]:s} and {0[1]:s}".format(weight)
            elif match[0] == '胸围':
                boobs = match[1].split('-')
                sql_str_sub += " and substr(boobs,1,2) between {0[0]:s} and {0[1]:s}".format(boobs)
            elif match[0] == '生育':
                if match[1] == '全部':
                    sql_str_sub += " and baby in ('0', '1')"
                else:
                    baby = mean('info_baby_cn', match[1], 'from')
                    sql_str_sub += " and baby='{0:s}'".format(baby)
            elif match[0] == '特点':
                special = match[1]
                sql_str_sub += " and special like '%{0:s}%'".format(special)
            elif match[0] == '国籍':
                country_code = mean('country', match[1], 'from')
                sql_str_sub += " and country='{0:s}'".format(country_code)
        # 确定翻页起始位置
        if page == 0:
            match = re.match('.*\n' + e_right2 + ' 页数:.*?-(.*?) ' + e_left2 + '.*', pre_content, re.S)
            if match is None or match.group(1) == '0':
                page = 1
            else:
                page = int(match.group(1))
        start_row = (page - 1) * 10
        sql_str = "select a.id,a.name,a.age,a.boobs,a.level,right(ifnull(b.msg_list,''),locate(',',reverse(ifnull" \
                  "(b.msg_list,'')),2)-1),a.country from zoo_info a left join (select ind,msg_list,updated_at from chat_message " \
                  "where chat_id={3:d} and kind='动物列表') b on a.id=b.ind left join (select girl_id,updated_at from " \
                  "zoo_guest_rel where guest_id={0:d}) c on a.id=c.girl_id {1:s} order by ifnull(c.updated_at," \
                  "'2016-08-17 00:00:00'),a.level desc,a.age,a.boobs desc,b.updated_at desc limit {2:d}, 10"\
            .format(user_id, sql_str_sub, start_row, cm[0].chat_id)
        rst = read_connector.query(sql_str)
        if len(rst) == 0:
            content = e_girls + " <b>没有符合要求的贵妃!</b>\n"
            content += e_right2 + " 页数:0-0 " + e_left2 + '\n'
        else:
            content = e_girls + " <b>欢乐园贵妃如下:\n" + e_light + " 编号 - 年龄 - 胸围 - 国籍 - 等级</b>\n"
            n = 1
            for row in rst:
                content += e_num[n] + ": <a href='t.me/happyzone168/{2:s}'>{0[0]:d} - " \
                                      "{0[2]:d} - {0[3]:s} - {3:s} -{1:s}</a>\n" \
                    .format(row, mean('info_level_cn', row[4]), row[5].strip(','), mean('country',row[6])[:2])
                n += 1
            sql_str = "select count(1) from zoo_info a {0:s}".format(sql_str_sub)
            rst = read_connector.query(sql_str)
            all_page = math.ceil(rst[0][0] / 10)
            content += e_right2 + " 页数:{0:d}-{1:d} ".format(all_page, page) + e_left2 + '\n'
        content_list = pre_content.split(e_left2 + '\n')
        if len(content_list) == 2:
            search_str = content_list[1]
        else:
            search_str = ''
        return content + search_str
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面 (不知道为什么使用private会报错）
# @Client.on_callback_query(filters.private)
@Client.on_callback_query()
async def callback_interface(client, callback):
    try:
        user_id = callback.from_user.id
        if check_limit(user_id, '2'):
            await client.send_message(callback.message.chat.id,
                                      "若要商谈合作，请与我们联系",
                                      parse_mode=ParseMode.HTML,
                                      reply_markup=get_imp('contact'))
            return
        # 新妃推荐
        if callback.data == 'next':
            await send_model(client, user_id, callback.message.chat.id)
            update_guest(user_id)
        # 精选贵妃
        elif callback.data == 'search':
            prompt = line + e_remind + "请选择你要精选的内容"
            content = new_page('', user_id, 1)+prompt
            if callback.message.text != content:
                await callback.edit_message_text(content, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('search'), disable_web_page_preview=True)
            update_guest(user_id)
        # 返回主页
        elif callback.data == 'search_return':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_flower+" <b>欢迎来到欢乐园</b> "+e_flower+'\n'
            await callback.edit_message_text(new_page(pre_content, user_id) + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('home'), disable_web_page_preview=True)
        # 年龄
        elif callback.data == 'search_age':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请输入你要精选的年龄范围\n格式如：18-28"
            if callback.message.text != pre_content + prompt:
                re_msg = await callback.edit_message_text(pre_content + prompt,
                                                          parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('search'),
                                                          disable_web_page_preview=True)
                await re_msg.reply_text('回复此信息输入年龄范围', quote=True, reply_markup=ForceReply())
            else:
                await callback.message.reply_text('回复此信息输入年龄范围', quote=True, reply_markup=ForceReply())
        # 身高
        elif callback.data == 'search_height':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请输入你要精选的身高范围\n格式如：150-180"
            if callback.message.text != pre_content + prompt:
                re_msg = await callback.edit_message_text(pre_content + prompt,
                                                          parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('search'),
                                                          disable_web_page_preview=True)
                await re_msg.reply_text('回复此信息输入身高范围', quote=True, reply_markup=ForceReply())
            else:
                await callback.message.reply_text('回复此信息输入身高范围', quote=True, reply_markup=ForceReply())
        # 体重
        elif callback.data == 'search_weight':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请输入你要精选的体重范围\n格式如：40-55"
            if callback.message.text != pre_content + prompt:
                re_msg = await callback.edit_message_text(pre_content + prompt,
                                                          parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('search'),
                                                          disable_web_page_preview=True)
                await re_msg.reply_text('回复此信息输入体重范围', quote=True, reply_markup=ForceReply())
            else:
                await callback.message.reply_text('回复此信息输入体重范围', quote=True, reply_markup=ForceReply())
        # 胸围
        elif callback.data == 'search_boobs':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请输入你要精选的胸围范围\n格式如：26-46"
            if callback.message.text != pre_content + prompt:
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('search'), disable_web_page_preview=True)
                await re_msg.reply_text('回复此信息输入胸围范围', quote=True, reply_markup=ForceReply())
            else:
                await callback.message.reply_text('回复此信息输入胸围范围', quote=True, reply_markup=ForceReply())
        # 生育
        elif callback.data == 'search_baby':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请选择你要精选的生育情况"
            await callback.edit_message_text(new_page(pre_content, user_id) + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('search_baby'),
                                             disable_web_page_preview=True)
        # 生育 没有孩子/有孩子/未知 (0/1/2)
        elif re.match('^search_baby_', callback.data, re.S) is not None:
            result = callback.data.split('_')[2]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(' + e_search + '生育: .*?)\n.*', content, re.S)
            if match is None:
                content += e_search + '生育: ' + result + '\n'
            else:
                sour_str = match.group(1)
                content = re.sub(sour_str, e_search + '生育: ' + result, content, 1)
            content = new_page(content, user_id, 1)
            prompt = e_remind + "请选择你要精选的内容\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('search'), disable_web_page_preview=True)
        # 特点
        elif callback.data == 'search_special':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请输入你要匹配的特点\n要求：长度不能超过20个字"
            if callback.message.text != pre_content + prompt:
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('search'), disable_web_page_preview=True)
                await re_msg.reply_text('回复此信息输入特点', quote=True, reply_markup=ForceReply())
            else:
                await callback.message.reply_text('回复此信息输入特点', quote=True, reply_markup=ForceReply())
        # 国籍
        elif callback.data == 'search_country':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请选择你要精选的国籍"
            await callback.edit_message_text(new_page(pre_content, user_id) + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('search_country'),
                                             disable_web_page_preview=True)
        # 国籍 0/1/2/3/4
        elif re.match('^search_country_', callback.data, re.S) is not None:
            result = callback.data.split('_')[2]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(' + e_search + '国籍: .*?)\n.*', content, re.S)
            if match is None:
                content += e_search + '国籍: ' + mean('country', result) + '\n'
            else:
                sour_str = match.group(1)
                content = re.sub(sour_str, e_search + '国籍: ' + mean('country', result), content, 1)
            content = new_page(content, user_id, 1)
            prompt = e_remind + "请选择你要精选的内容\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('search'), disable_web_page_preview=True)
        # 翻页
        elif callback.data in ('search_PrePage', 'search_NextPage'):
            type = callback.data.split('_')[1]
            pre_content = callback.message.text.split(line)[0]
            match = re.match('.*\n' + e_right2 + ' 页数:(.*?)-(.*?) ' + e_left2 + '.*', pre_content, re.S)
            if match is None:
                content = new_page(pre_content, user_id)
                prompt = line + e_warning + "没有{0:s}".format(mean('page_cn', type))
            else:
                all_page = int(match.group(1))
                now_page = int(match.group(2))
                next_page = now_page + mean('change_page', type, 'from')
                if next_page == 0:
                    content = new_page(pre_content, user_id)
                    prompt = line + e_remind + "已到顶页，不能再上了"
                elif next_page > all_page:
                    content = new_page(pre_content, user_id)
                    prompt = line + e_remind + "已到底页，不能再下了"
                else:
                    content = new_page(pre_content, user_id, next_page)
                    prompt = line + e_remind + "你选择了第 {0:d} 页".format(next_page)
            if callback.message.text != content + prompt:
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('search'),
                                                 disable_web_page_preview=True)
            update_guest(user_id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复-页面
@Client.on_message(filters.private & filters.reply, group=-2)
async def reply_interface(client, message):
    try:
        user_id = message.from_user.id
        re_msg = message.reply_to_message
        pre_re_msg = await client.get_messages(message.chat.id, reply_to_message_ids=re_msg.id, replies=1)
        if re_msg.text == '回复此信息输入年龄范围':
            str_input = await input_cert(message.text, 'age_range', client, user_id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(' + e_search + '年龄: .*?)\n.*', content, re.S)
                if match is None:
                    content += e_search + '年龄: ' + str_input[1] + '\n'
                else:
                    sour_str = match.group(1)
                    content = re.sub(sour_str, e_search + '年龄: ' + str_input[1], content, 1)
                content = new_page(content, user_id, 1)
                prompt = e_remind + "请选择你要精选的内容\n"
            else:
                content = new_page(content, user_id)
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('search'), disable_web_page_preview=True)
            await message.delete()
            await re_msg.delete()
        elif re_msg.text == '回复此信息输入身高范围':
            str_input = await input_cert(message.text, 'height_range', client, user_id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(' + e_search + '身高: .*?)\n.*', content, re.S)
                if match is None:
                    content += e_search + '身高: ' + str_input[1] + '\n'
                else:
                    sour_str = match.group(1)
                    content = re.sub(sour_str, e_search + '身高: ' + str_input[1], content, 1)
                content = new_page(content, user_id, 1)
                prompt = e_remind + "请选择你要精选的内容\n"
            else:
                content = new_page(content, user_id)
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('search'), disable_web_page_preview=True)
            await message.delete()
            await re_msg.delete()
        elif re_msg.text == '回复此信息输入体重范围':
            str_input = await input_cert(message.text, 'weight_range', client, user_id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(' + e_search + '体重: .*?)\n.*', content, re.S)
                if match is None:
                    content += e_search + '体重: ' + str_input[1] + '\n'
                else:
                    sour_str = match.group(1)
                    content = re.sub(sour_str, e_search + '体重: ' + str_input[1], content, 1)
                content = new_page(content, user_id, 1)
                prompt = e_remind + "请选择你要精选的内容\n"
            else:
                content = new_page(content, user_id)
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('search'), disable_web_page_preview=True)
            await message.delete()
            await re_msg.delete()
        elif re_msg.text == '回复此信息输入胸围范围':
            str_input = await input_cert(message.text, 'boobs_range', client, user_id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(' + e_search + '胸围: .*?)\n.*', content, re.S)
                if match is None:
                    content += e_search + '胸围: ' + str_input[1] + '\n'
                else:
                    sour_str = match.group(1)
                    content = re.sub(sour_str, e_search + '胸围: ' + str_input[1], content, 1)
                content = new_page(content, user_id, 1)
                prompt = e_remind + "请选择你要精选的内容\n"
            else:
                content = new_page(content, user_id)
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('search'), disable_web_page_preview=True)
            await message.delete()
            await re_msg.delete()
        elif re_msg.text == '回复此信息输入特点':
            str_input = await input_cert(message.text, 'special_search', client, user_id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(' + e_search + '特点: .*?)\n.*', content, re.S)
                if match is None:
                    content += e_search + '特点: ' + str_input[1] + '\n'
                else:
                    sour_str = match.group(1)
                    content = re.sub(sour_str, e_search + '特点: ' + str_input[1], content, 1)
                content = new_page(content, user_id, 1)
                prompt = e_remind + "请选择你要精选的内容\n"
            else:
                content = new_page(content, user_id)
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('search'), disable_web_page_preview=True)
            await message.delete()
            await re_msg.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 主界面
@Client.on_message(filters.incoming & filters.text & ~filters.reply & ~filters.forwarded & filters.private & ~filters.bot)
async def welcome(client, message):
    try:
        user_id = message.from_user.id
        if check_limit(user_id, '2'):
            await client.send_message(message.chat.id,
                                      "若要商谈合作，请与我们联系",
                                      parse_mode=ParseMode.HTML,
                                      reply_markup=get_imp('contact'))
        else:
            await send_model(client, message.from_user.id, message.chat.id)
            update_guest(user_id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新客户信息
def update_guest(user_id):
    try:
        sql_str = "select count(1) from zoo_guest where chat_id={0:d}".format(user_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] == 0:
            sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_bot,remind_at,active_at," \
                      "created_at) values({0[0]:d},'{1:s}','{0[2]:s}','{0[5]:s}','{0[3]:s}','1',CURRENT_TIMESTAMP()," \
                      "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP())".format(f_chats_id(user_id), user_to_std(user_id))
            tg_connector.exe(sql_str)
        else:
            sql_str = "update zoo_guest set name='{0[2]:s}',username='{0[5]:s}',outname='{0[3]:s}',status_bot=" \
                      "if(status_bot in ('0','3'),'1',status_bot),active_at=CURRENT_TIMESTAMP() where chat_id={0[0]:d}"\
                .format(f_chats_id(user_id))
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')




