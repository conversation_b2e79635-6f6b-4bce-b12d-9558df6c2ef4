# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common_girlc import *
from classes2 import *
import asyncio
import re
import math
from pyrogram import Client, filters
from pyrogram.types import ReplyKeyboardMarkup, ReplyKeyboardRemove, ForceReply, InputMediaPhoto, InputMediaVideo
from pymysql.converters import escape_string


new_model = e_lip*3 + 'My information' + e_lip*3 + """
Name: must
Country: {0:s}
Age: must
Height: must
Weight: must
Boobs: must
Baby: must
Location: makati/pasay/pasig/..
Special: Mixed race/know Chinese/job/3P/no condom/white skin/massage/other service..
Phone: must
Wechat: not must
Telegram: not must
Facebook: not must
Price: your price ask
  Package1:
  1h-1p / 3h-2p / 6h-3p / 8h-unl
  Package2:
  1h-1p/3h-2p/6h-3p/8h-unl/10h-unl/12h-unl/24h-unl
Picture: Total 5 photos
""".format(mean('country', '1'))


# 更新客户信息 page=0 是展示本页
def guest_page(pre_content, page=0):
    try:
        sql_str_sub = 'where 1=1'
        # 确认精选项目
        for match in re.findall(e_search + '(.*?): (.*?)\n', pre_content, re.S):
            if match[0] == '关系':
                sql_str_sub += " and status_chat='{0:s}'".format(mean('status_chat', match[1], 'from'))
            elif match[0] == '机器人':
                sql_str_sub += " and status_bot='{0:s}'".format(mean('status_bot', match[1], 'from'))
        # 确定翻页起始位置
        if page == 0:
            match = re.match('.*\n' + e_right2 + ' 页数:.*?-(.*?) ' + e_left2 + '.*', pre_content, re.S)
            if match is None or match.group(1) == '0':
                page = 1
            else:
                page = int(match.group(1))
        start_row = (page - 1) * 10
        sql_str = "select right(chat_id,4),left(name,8),orders,status_chat,active_at,if(username=''," \
                  "concat('tg://user?id=',chat_id),concat('tg://resolve?domain=',username)) from zoo_guest " \
                  "{0:s} order by active_at desc limit {1:d}, 10".format(sql_str_sub, start_row)
        rst = tg_connector.query(sql_str)
        if len(rst) == 0:
            content = e_girls + " <b>没有符合要求的客户!</b>\n"
            content += e_right2 + " 页数:0-0 " + e_left2 + '\n'
        else:
            content = e_girls + " <b>客户如下:\n" + e_light + " ID - 内部名 - 下单 - 关系 - 活跃时间</b>\n"
            n = 1
            for row in rst:
                content += e_num[n] + ": <a href='{0[5]:s}'>{0[0]:s}-{0[1]:s}</a>-{0[2]:d}-{1:s}" \
                                      "-{0[4]:%m-%d %H:%M}\n" \
                    .format(row, mean('status_chat', row[3]))
                n += 1
            sql_str = "select count(1) from zoo_guest a {0:s}".format(sql_str_sub)
            rst = tg_connector.query(sql_str)
            all_page = math.ceil(rst[0][0] / 10)
            content += e_right2 + " 页数:{0:d}-{1:d} ".format(all_page, page) + e_left2 + '\n'
        content_list = pre_content.split(e_left2 + '\n')
        if len(content_list) == 2:
            search_str = content_list[1]
        else:
            search_str = ''
        return content + search_str
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        if index == 'sa_home':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_lip+"新增", callback_data='new_refresh'),
                 InlineKeyboardButton(e_girls + "我的女孩", callback_data='sa_query'),
                 InlineKeyboardButton(e_search + "查女孩", callback_data='sa_search')],
                [InlineKeyboardButton(e_set + "权限", callback_data='sa_limit'),
                 InlineKeyboardButton(e_guests + "我的客户", callback_data='sa_myguest'),
                 InlineKeyboardButton(e_search + "查客户", callback_data='sa_ckguest')],
                [InlineKeyboardButton(e_adv + "公告", callback_data='sa_board'),
                 InlineKeyboardButton(e_adv + "Notice", callback_data='sa_boarden'),
                 InlineKeyboardButton(e_cty3 + "越南", callback_data='sa_fixava'),
                 InlineKeyboardButton(e_kong + "空闲", callback_data='sa_ava')]
            ])
        elif index == 'new':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_girl + "姓名", callback_data='new_name'),
                 InlineKeyboardButton(e_loudou + "年龄", callback_data='new_age')],
                [InlineKeyboardButton(e_ruler + "身高", callback_data='new_height'),
                 InlineKeyboardButton(e_trade + "体重", callback_data='new_weight')],
                [InlineKeyboardButton(e_sex_wear + "胸围", callback_data='new_boobs'),
                 InlineKeyboardButton(e_baby + "生育", callback_data='new_baby')],
                [InlineKeyboardButton(e_location + "住址", callback_data='new_location'),
                 InlineKeyboardButton(e_id + "特点", callback_data='new_special')],
                [InlineKeyboardButton(e_phone + "电话", callback_data='new_phone'),
                 InlineKeyboardButton(e_phone + "微信", callback_data='new_wechat')],
                [InlineKeyboardButton(e_phone + "Telegram", callback_data='new_telegram'),
                 InlineKeyboardButton(e_phone + "Facebook", callback_data='new_facebook')],
                [InlineKeyboardButton(e_money + "内部价格", callback_data='new_price'),
                 InlineKeyboardButton(e_photo + "图片", callback_data='new_picture')],
                [InlineKeyboardButton(e_cty + "国家", callback_data='new_country'),
                 InlineKeyboardButton(e_report + "提交", callback_data='new_submit')]
            ])
        elif index == 'new__return':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='new'),
                 InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
            ])
        elif index == 'new_baby':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_gou + "有孩子", callback_data='new_baby_yes'),
                 InlineKeyboardButton(e_cha + "没有孩子", callback_data='new_baby_no')]
            ])
        elif index == 'new_country':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('country','0'), callback_data='new_country_0'),
                 InlineKeyboardButton(mean('country','1'), callback_data='new_country_1'),
                 InlineKeyboardButton(mean('country','2'), callback_data='new_country_2')],
                [InlineKeyboardButton(mean('country','3'), callback_data='new_country_3'),
                 InlineKeyboardButton(mean('country','4'), callback_data='new_country_4'),
                 InlineKeyboardButton(mean('country','5'), callback_data='new_country_5')]
            ])
        elif index == 'new_submit_verify':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_comfirm + "认证", callback_data='sa_verify')]
            ])
        elif index == 'sa_search':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
            ])
        elif index == 'sa_search_detail':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_write + "修改", callback_data='sa_search_detail_change'),
                 InlineKeyboardButton(e_fenlei + "分类", callback_data='sa_search_detail_class')],
                [InlineKeyboardButton(e_manhat + "展示客户", callback_data='sa_search_detail_guest'),
                 InlineKeyboardButton(e_girlhat + "展示女孩", callback_data='sa_search_detail_girl')],
                [InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
            ])
        elif index == 'sa_search_detail_change':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_girl + "姓名", callback_data='sa_change_name'),
                 InlineKeyboardButton(e_loudou + "年龄", callback_data='sa_change_age')],
                [InlineKeyboardButton(e_ruler + "身高", callback_data='sa_change_height'),
                 InlineKeyboardButton(e_trade + "体重", callback_data='sa_change_weight')],
                [InlineKeyboardButton(e_sex_wear + "胸围", callback_data='sa_change_boobs'),
                 InlineKeyboardButton(e_baby + "生育", callback_data='sa_change_baby')],
                [InlineKeyboardButton(e_location + "住址", callback_data='sa_change_location'),
                 InlineKeyboardButton(e_id + "特点", callback_data='sa_change_special')],
                [InlineKeyboardButton(e_phone + "电话", callback_data='sa_change_phone'),
                 InlineKeyboardButton(e_phone + "微信", callback_data='sa_sa_change_wechat')],
                [InlineKeyboardButton(e_phone + "Telegram", callback_data='sa_change_telegram'),
                 InlineKeyboardButton(e_phone + "Facebook", callback_data='sa_change_facebook')],
                [InlineKeyboardButton(e_money + "内部价格", callback_data='sa_change_pricein'),
                 InlineKeyboardButton(e_money + "外部价格", callback_data='sa_change_priceout')],
                [InlineKeyboardButton(e_photo + "图片", callback_data='sa_change_picture'),
                 InlineKeyboardButton(e_zoushi + "等级", callback_data='sa_change_level')],
                [InlineKeyboardButton(e_cty + "国家", callback_data='sa_change_country'),
                 InlineKeyboardButton(e_traffic + "状态", callback_data='sa_change_status')],
                [InlineKeyboardButton(e_myOffer + "备注", callback_data='sa_change_note'),
                 InlineKeyboardButton(e_report + "提交", callback_data='sa_change_submit')],
                [InlineKeyboardButton(e_return + "返回", callback_data='sa_search_detail__return'),
                 InlineKeyboardButton(e_replace + "优先", callback_data='sa_change_raise')]
            ])
        elif index == 'sa_change_baby':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton("有孩子", callback_data='sa_change_baby_yes'),
                 InlineKeyboardButton("没有孩子", callback_data='sa_change_baby_no')]
            ])
        elif index == 'sa_change_country':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('country','0'), callback_data='sa_change_country_0'),
                 InlineKeyboardButton(mean('country','1'), callback_data='sa_change_country_1'),
                 InlineKeyboardButton(mean('country','2'), callback_data='sa_change_country_2')],
                [InlineKeyboardButton(mean('country','3'), callback_data='sa_change_country_3'),
                 InlineKeyboardButton(mean('country','4'), callback_data='sa_change_country_4'),
                 InlineKeyboardButton(mean('country','5'), callback_data='sa_change_country_5')]
            ])
        elif index == 'sa_change_level':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('info_level', 5), callback_data='sa_change_level_5'),
                 InlineKeyboardButton(mean('info_level', 4), callback_data='sa_change_level_4'),
                 InlineKeyboardButton(mean('info_level', 3), callback_data='sa_change_level_3')],
                [InlineKeyboardButton(mean('info_level', 2), callback_data='sa_change_level_2'),
                 InlineKeyboardButton(mean('info_level', 1), callback_data='sa_change_level_1'),
                 InlineKeyboardButton(mean('info_level', 0), callback_data='sa_change_level_0')]
            ])
        elif index == 'sa_change_status':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('info_status', '4'), callback_data='sa_change_status_4'),
                 InlineKeyboardButton(mean('info_status', '1'), callback_data='sa_change_status_1')],
                [InlineKeyboardButton(mean('info_status', '2'), callback_data='sa_change_status_2'),
                 InlineKeyboardButton(mean('info_status', '3'), callback_data='sa_change_status_3'),
                 InlineKeyboardButton(mean('info_status', '0'), callback_data='sa_change_status_0')]
            ])
        elif index == 'sa_change__return':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='sa_search_detail_change'),
                 InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
            ])
        elif index == 'sa_search_detail_class':
            markup_list = []
            name_3 = mean('class_type', '3')[0]
            if '3' in param:
                markup_list.append([InlineKeyboardButton(e_gou + name_3, callback_data='sa_search_detail_class_31'),
                                    InlineKeyboardButton('非' + name_3, callback_data='sa_search_detail_class_30')])
            else:
                markup_list.append([InlineKeyboardButton(name_3, callback_data='sa_search_detail_class_31'),
                                    InlineKeyboardButton(e_gou + '非' + name_3, callback_data='sa_search_detail_class_30')])
            markup_list.append([InlineKeyboardButton(e_return + "返回", callback_data='sa_search_detail__return')])
            return InlineKeyboardMarkup(markup_list)
        elif index == 'sa_query':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_num[1], callback_data='sa_query_1'),
                 InlineKeyboardButton(e_num[2], callback_data='sa_query_2'),
                 InlineKeyboardButton(e_num[3], callback_data='sa_query_3'),
                 InlineKeyboardButton(e_num[4], callback_data='sa_query_4'),
                 InlineKeyboardButton(e_num[5], callback_data='sa_query_5')],
                [InlineKeyboardButton(e_num[6], callback_data='sa_query_6'),
                 InlineKeyboardButton(e_num[7], callback_data='sa_query_7'),
                 InlineKeyboardButton(e_num[8], callback_data='sa_query_8'),
                 InlineKeyboardButton(e_num[9], callback_data='sa_query_9'),
                 InlineKeyboardButton(e_num[10], callback_data='sa_query_10')],
                [InlineKeyboardButton(e_girl + "姓名", callback_data='sa_query_name'),
                 InlineKeyboardButton(e_cty + "国家", callback_data='sa_query_country'),
                 InlineKeyboardButton(e_traffic + "状态", callback_data='sa_query_status')],
                [InlineKeyboardButton(e_left + "上页", callback_data='sa_query_PrePage'),
                 InlineKeyboardButton(e_girls + "全部", callback_data='sa_query'),
                 InlineKeyboardButton(e_right + "下页", callback_data='sa_query_NextPage')],
                [InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
            ])
        elif index == 'sa_query_country':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('country', '0'), callback_data='sa_query_country_0'),
                 InlineKeyboardButton(mean('country', '1'), callback_data='sa_query_country_1'),
                 InlineKeyboardButton(mean('country', '2'), callback_data='sa_query_country_2')],
                [InlineKeyboardButton(mean('country', '3'), callback_data='sa_query_country_3'),
                 InlineKeyboardButton(mean('country', '4'), callback_data='sa_query_country_4'),
                 InlineKeyboardButton(mean('country', '5'), callback_data='sa_query_country_5')]
            ])
        elif index == 'sa_query_status':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('info_status', '4'), callback_data='sa_query_status_4'),
                 InlineKeyboardButton(mean('info_status', '1'), callback_data='sa_query_status_1')],
                [InlineKeyboardButton(mean('info_status', '2'), callback_data='sa_query_status_2'),
                 InlineKeyboardButton(mean('info_status', '3'), callback_data='sa_query_status_3'),
                 InlineKeyboardButton(mean('info_status', '0'), callback_data='sa_query_status_0')]
            ])
        elif index == 'sa_query__return':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='sa_query__return'),
                 InlineKeyboardButton(e_home + "主页", callback_data='sa_home')]
                ])
        elif index == 'sa_limit':
            markup_list = []
            if ('2', ) in param:
                markup_list.append([InlineKeyboardButton(e_gou + "Ban选妃Bot", callback_data='sa_limit_21'),
                                    InlineKeyboardButton("UnBan选妃Bot", callback_data='sa_limit_20')])
            else:
                markup_list.append([InlineKeyboardButton("Ban选妃Bot", callback_data='sa_limit_21'),
                                    InlineKeyboardButton(e_gou + "UnBan选妃Bot", callback_data='sa_limit_20')])
            markup_list.append([InlineKeyboardButton(e_home + "Home", callback_data='sa_home')])
            return InlineKeyboardMarkup(markup_list)
        elif index == 'sa_guest_order':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_add + e_num[1], callback_data='sa_guest_order_add'),
                 InlineKeyboardButton(e_reduce + e_num[1], callback_data='sa_guest_order_reduce')]
            ])
        elif index == 'sa_guest_link':
            num = mean('status_chat', param, 'from')
            index = [e_circle, e_circle, e_circle, e_circle]
            index[int(num)] = e_gou
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(index[1] + mean('status_chat', '1'), callback_data='sa_guest_link_1'),
                 InlineKeyboardButton(index[3] + mean('status_chat', '3'), callback_data='sa_guest_link_3')],
                [InlineKeyboardButton(index[0] + mean('status_chat', '0'), callback_data='sa_guest_link_0'),
                 InlineKeyboardButton(index[2] + mean('status_chat', '2'), callback_data='sa_guest_link_2')]
            ])
        elif index == 'sa_guest_return':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='sa_guest_return')]
            ])
        elif index == 'sa_myguest':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_num[1], callback_data='sa_myguest_1'),
                 InlineKeyboardButton(e_num[2], callback_data='sa_myguest_2'),
                 InlineKeyboardButton(e_num[3], callback_data='sa_myguest_3'),
                 InlineKeyboardButton(e_num[4], callback_data='sa_myguest_4'),
                 InlineKeyboardButton(e_num[5], callback_data='sa_myguest_5')],
                [InlineKeyboardButton(e_num[6], callback_data='sa_myguest_6'),
                 InlineKeyboardButton(e_num[7], callback_data='sa_myguest_7'),
                 InlineKeyboardButton(e_num[8], callback_data='sa_myguest_8'),
                 InlineKeyboardButton(e_num[9], callback_data='sa_myguest_9'),
                 InlineKeyboardButton(e_num[10], callback_data='sa_myguest_10')],
                [InlineKeyboardButton(e_dna + "关系", callback_data='sa_myguest_link'),
                 InlineKeyboardButton(e_guests + "全部", callback_data='sa_myguest_all'),
                 InlineKeyboardButton(e_bot + "机器人", callback_data='sa_myguest_bot')],
                [InlineKeyboardButton(e_left + "上页", callback_data='sa_myguest_PrePage'),
                 InlineKeyboardButton(e_home + "主页", callback_data='sa_home'),
                 InlineKeyboardButton(e_right + "下页", callback_data='sa_myguest_NextPage')]
            ])
        elif index == 'sa_myguest_link':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('status_chat', '1'), callback_data='sa_myguest_link_1'),
                 InlineKeyboardButton(mean('status_chat', '3'), callback_data='sa_myguest_link_3')],
                [InlineKeyboardButton(mean('status_chat', '0'), callback_data='sa_myguest_link_0'),
                 InlineKeyboardButton(mean('status_chat', '2'), callback_data='sa_myguest_link_2')]
            ])
        elif index == 'sa_myguest_bot':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('status_bot', '1'), callback_data='sa_myguest_bot_1'),
                 InlineKeyboardButton(mean('status_bot', '2'), callback_data='sa_myguest_bot_2')],
                [InlineKeyboardButton(mean('status_bot', '0'), callback_data='sa_myguest_bot_0'),
                 InlineKeyboardButton(mean('status_bot', '3'), callback_data='sa_myguest_bot_3')]
            ])
        elif index == 'girlc_bot':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_gou + "允许", callback_data='girlc_bot_allow'),
                 InlineKeyboardButton(e_cha + "禁止", callback_data='girlc_bot_forbid')]
            ])
        elif index == 'girlc_return':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='girlc_return')]
            ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面 成员
@Client.on_callback_query(~filters.user(sa), group=-2)
async def callback_interface_member(client, callback):
    try:
        # sa video
        if callback.data == 'video':
            user_id = callback.from_user.id
            sql_str = "select a.orders,case when b.user_id is not null then b.chat_id when a.orders>0 then c.chat_id " \
                      "else 0 end, case when b.user_id is not null then '1' else '0' end from zoo_guest a left join " \
                      "zoo_guest_video b on a.chat_id=b.user_id join (select chat_id,count(1) from zoo_guest_video " \
                      "group by chat_id order by 2 limit 1) c on 1=1 where a.chat_id={0:d}".format(user_id)
            rst = tg_connector.query(sql_str)
            if len(rst) == 0 or rst[0][0] == 0:
                contect = "抱歉~只有下过单的老铁才有资格观看精彩视频 ^_^ 下单请联系 @happy_167\nSorry, only those who have " \
                          "made an order are eligible to watch this video. Please contact @happy_167 to order"
                await callback.answer(contect, show_alert=True)
            elif rst[0][2] == '1':
                await callback.answer(url="t.me/myhappygirlsbot?start=" + str(rst[0][1]))
            else:
                await callback.answer(url="t.me/myhappygirlsbot?start=" + str(rst[0][1]))
                sql_str = "insert into zoo_guest_video values({0:d}, {1:d})".format(user_id, rst[0][1])
                tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面
@Client.on_callback_query(filters.user(sa), group=-2)
async def callback_interface(client, callback):
    try:
        # sa 主页
        if callback.data == 'sa_home':
            await callback.edit_message_text(e_flower+" Welcome to our HappyZone!! "+e_flower+'\n',
                                             parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_home'))
        # 新增_刷新
        elif callback.data == 'new_refresh':
            prompt = line + e_remind + "Please edit your information and submit it!\n"
            await callback.edit_message_text(new_model + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
        # 新增
        elif callback.data == 'new':
            content = callback.message.text.split(line)[0]
            prompt = e_remind + "Please edit your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
        # 新增-名字
        elif callback.data == 'new_name':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your name\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your name', quote=True, reply_markup=ForceReply())
        # 新增-年龄
        elif callback.data == 'new_age':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your age\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your age', quote=True, reply_markup=ForceReply())
        # 新增-身高
        elif callback.data == 'new_height':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Just input number(cm)\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your height(cm)', quote=True, reply_markup=ForceReply())
        # 新增-体重
        elif callback.data == 'new_weight':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Just input number(kg)\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your weight(kg)', quote=True, reply_markup=ForceReply())
        # 新增-xiong
        elif callback.data == 'new_boobs':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Boobs size such as 36C\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your boobs', quote=True, reply_markup=ForceReply())
        # 新增-孩子
        elif callback.data == 'new_baby':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Do you have baby?\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new_baby'))
        # 新增-孩子-是/否
        elif re.match('^new_baby_', callback.data, re.S) is not None:
            result = callback.data.split('_')[2]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(Baby: .*)\nLocation.*', content, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, 'Baby: ' + result, content, 1)
            prompt = e_remind + "Please edit your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
        # 新增-地址
        elif callback.data == 'new_location':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Simple location:makati/pasay/pasig/..\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your location', quote=True, reply_markup=ForceReply())
        # 新增-特长
        elif callback.data == 'new_special':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your special\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your special', quote=True, reply_markup=ForceReply())
        # 新增-Phone
        elif callback.data == 'new_phone':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Such as 09123456789\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your phone', quote=True, reply_markup=ForceReply())
        # 新增-Wechat
        elif callback.data == 'new_wechat':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your wechat\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your wechat', quote=True, reply_markup=ForceReply())
        # 新增-Telegram
        elif callback.data == 'new_telegram':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your telegram\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your telegram', quote=True, reply_markup=ForceReply())
        # 新增-Facebook
        elif callback.data == 'new_facebook':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your facebook\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your facebook', quote=True, reply_markup=ForceReply())
        # 新增-价格
        elif callback.data == 'new_price':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Input such as:\n 0 1000 2000 3000\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            await re_msg.reply_text('Reply this message input your price', quote=True, reply_markup=ForceReply())
        # 新增-照片
        elif callback.data == 'new_picture':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "For the following:\n2 whole body picture\n3 close up picture"
            re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('new__return'))
            # params['pic' + str(callback.message.chat.id)] = []
            await re_msg.reply_text('Reply this message input your picture', quote=True, reply_markup=ForceReply())
        # 新增-国籍
        elif callback.data == 'new_country':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Where are you come from?\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new_country'))
        # 新增-国籍-0/1/2/3/4...
        elif re.match('^new_country_', callback.data, re.S) is not None:
            result = callback.data.split('_')[2]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(Country: .*)\nAge.*', content, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, 'Country: ' + mean('country', result), content, 1)
            prompt = e_remind + "Please edit your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
        # 新增-提交
        elif callback.data == 'new_submit':
            content = callback.message.text.split(line)[0]
            # 提取信息栏
            match = re.match('.*My information.*(Name: .*)\n(Country: .*)\n(Age: .*)\n(Height: .*)\n(Weight: .*)\n(Boobs: .*)\n'
                             '(Baby: .*)\n(Location: .*)\n(Special: .*)\n(Phone: .*)\n(Wechat: .*)\n(Telegram: .*)\n'
                             '(Facebook: .*)\n(Price: .*)\n(Picture: .*)\n.*', content, re.S)
            name = match.group(1).split(': ')[1].title()
            country = match.group(2).split(': ')[1]
            age = match.group(3).split(': ')[1]
            height = match.group(4).split(': ')[1]
            weight = match.group(5).split(': ')[1]
            boobs = match.group(6).split(': ')[1]
            baby = match.group(7).split(': ')[1]
            location = match.group(8).split(': ')[1].title()
            special = match.group(9).split(': ')[1]
            phone = match.group(10).split(': ')[1]
            wechat = match.group(11).split(': ')[1]
            telegram = match.group(12).split(': ')[1]
            facebook = match.group(13).split(': ')[1]
            price = match.group(14).split(': ')[1].split('\n')[0]
            picture = match.group(15).split(': ')[1]
            # 必填项检查
            prompt = ''
            if name == 'must':
                prompt = line + e_warning + "Name is must\n"
            elif age == 'must':
                prompt = line + e_warning + "Age is must\n"
            elif height == 'must':
                prompt = line + e_warning + "Height is must\n"
            elif weight == 'must':
                prompt = line + e_warning + "Weight is must\n"
            elif boobs == 'must':
                prompt = line + e_warning + "Boobs is must\n"
            elif baby == 'must':
                prompt = line + e_warning + "Baby is must\n"
            elif price == "your price ask":
                prompt = line + e_warning + "Price is must\n"
            elif picture == 'Total 5 photos':
                prompt = line + e_warning + "Picture is must\n"
            if prompt != '':
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
                return
            # 信息栏整理
            country_code = mean('country', country, 'from')
            height = height[:3]
            weight = weight[:2]
            if special == 'Mixed race/know Chinese/job/3P/no condom/white skin/massage/other service..':
                special = '-'
            if phone =='must':
                phone = '-'
            if wechat == 'not must':
                wechat = '-'
            if telegram == 'not must':
                telegram = '-'
            if facebook == 'not must':
                facebook = '-'
            # 录入信息
            sql_str = "insert into zoo_info(name,age,height,weight,boobs,baby,location,special,phone,wechat,telegram," \
                      "facebook,price_in,picture,country) values('{0:s}',{1:s},{2:s},{3:s},'{4:s}','{5:s}'," \
                      "'{6:s}','{7:s}','{8:s}','{9:s}','{10:s}','{11:s}','{12:s}','{13:s}','{14:s}')"\
                .format(name, age, height, weight, boobs, mean('info_baby', baby ,'from'), location, special, phone,
                        wechat, telegram, facebook, price, picture, country_code)
            tg_connector.exe(sql_str)
            sql_str = "select max(id),count(1) from zoo_info where name='{0:s}'".format(name)
            rst = tg_connector.query(sql_str)
            # 若名字有重复，则需要改名
            if rst[0][1] > 1:
                name += str(rst[0][0])
                sql_str = "update zoo_info set name='{1:s}' where id={0:d}".format(rst[0][0], name)
                tg_connector.exe(sql_str)
            # 发送录入情况
            new_content = """No: {0:d}
Name: {1:s}
Country: {14:s}
Age: {2:s}
Height: {3:s}cm
Weight: {4:s}kg
Boobs: {5:s}
Baby: {6:s}
Location: {7:s}
Special: {8:s}
Phone: {9:s}
Wechat: {10:s}
Telegram: {11:s}
Facebook: {12:s}
Price: {13:s}
  Package1:
  1h-1p / 3h-2p / 6h-3p / 8h-unl
  Package2:
  1h-1p/3h-2p/6h-3p/8h-unl/10h-unl/12h-unl/24h-unl
Status: verifying
""".format(rst[0][0], name, age, height, weight, boobs, baby, location, special, phone, wechat, telegram, facebook, price, country)
            media_list = await media.get_media(client, picture.split('|'), new_content)
            # 发送详细信息 SA
            msg = await client.send_media_group(params['user_hugo'], media=media_list, disable_notification=True)
            # 提醒联系我们 SA
            await client.send_message(params['user_hugo'],
                                      'New girl need to verify',
                                      reply_to_message_id=msg[0].id,
                                      parse_mode=ParseMode.HTML,
                                      reply_markup=get_imp('new_submit_verify'))
            # 清理按钮
            prompt = line + e_remind + "You have add a new girl\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML)
            # # 清理无用参数
            # if 'pic'+str(callback.message.chat.id) in params.keys():
            #     del params['pic'+str(callback.message.chat.id)]
        # sa 查女孩
        elif callback.data == 'sa_search':
            content = callback.message.text.split(line)[0] + '\n'
            prompt = line + e_remind + "Pls input the no or name you search\n"
            re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_search'))
            await re_msg.reply_text('Reply this message input no or name!!', quote=True, reply_markup=ForceReply())
        # sa 修改
        elif callback.data == 'sa_search_detail_change':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "Please change your information and submit it!\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail_change'))
        # sa 修改-名字
        elif callback.data == 'sa_change_name':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "Please input your name\n"
            re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your name!!', quote=True, reply_markup=ForceReply())
        # sa 修改-年龄
        elif callback.data == 'sa_change_age':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your age\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your age!!', quote=True, reply_markup=ForceReply())
        # sa 修改-身高
        elif callback.data == 'sa_change_height':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Just input number(cm)\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your height(cm)!!', quote=True, reply_markup=ForceReply())
        # sa 修改-体重
        elif callback.data == 'sa_change_weight':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Just input number(kg)\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your weight(kg)!!', quote=True, reply_markup=ForceReply())
        # sa 修改-xiong
        elif callback.data == 'sa_change_boobs':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Boobs size such as 36C\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your boobs!!', quote=True, reply_markup=ForceReply())
        # sa 修改-孩子
        elif callback.data == 'sa_change_baby':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Does she have baby?\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_change_baby'))
        # sa 修改-孩子-是/否
        elif re.match('^sa_change_baby_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(Baby: .*)\nLocation.*', content, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, 'Baby: ' + result, content, 1)
            prompt = e_remind + "Please change your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail_change'))
        # sa 修改-地址
        elif callback.data == 'sa_change_location':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Simple location:makati/pasay/pasig/..\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your location!!', quote=True, reply_markup=ForceReply())
        # sa 修改-特长
        elif callback.data == 'sa_change_special':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your special\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your special!!', quote=True, reply_markup=ForceReply())
        # sa 修改-Phone
        elif callback.data == 'sa_change_phone':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Such as 09123456789\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your phone!!', quote=True, reply_markup=ForceReply())
        # sa 修改-Wechat
        elif callback.data == 'sa_sa_change_wechat':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your wechat\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your wechat!!', quote=True, reply_markup=ForceReply())
        # sa 修改-Telegram
        elif callback.data == 'sa_change_telegram':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your telegram\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your telegram!!', quote=True, reply_markup=ForceReply())
        # sa 修改-Facebook
        elif callback.data == 'sa_change_facebook':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your facebook\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your facebook!!', quote=True, reply_markup=ForceReply())
        # sa 修改-外部价格
        elif callback.data == 'sa_change_priceout':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Input such as:\n 0 1000 2000 3000\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your price_out!!', quote=True, reply_markup=ForceReply())
        # sa 修改-内部价格
        elif callback.data == 'sa_change_pricein':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Input such as:\n 0 1000 2000 3000\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your price_in!!', quote=True, reply_markup=ForceReply())
        # sa 修改-等级
        elif callback.data == 'sa_change_level':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"What level do you want to set?\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_change_level'))
        # sa 修改-等级-5/4/3/2/1/0
        elif re.match('^sa_change_level_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(Level: .*)\nPhone.*', content, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, 'Level: ' + mean('info_level', int(result)), content, 1)
            prompt = e_remind + "Please change your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail_change'))
        # sa 修改-照片
        elif callback.data == 'sa_change_picture':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "For the following:\n2 whole body picture\n3 close up picture\n1 video"
            re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            # params['pic' + str(callback.message.chat.id)] = []
            await re_msg.reply_text('Reply this message input your picture!!', quote=True, reply_markup=ForceReply())
        # sa 修改-国籍
        elif callback.data == 'sa_change_country':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Where is she from?\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_change_country'))
        # sa 修改-国籍-0/1/2/3/4
        elif re.match('^sa_change_country_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(Country: .*)\nAge.*', content, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, 'Country: ' + mean('country', result), content, 1)
            prompt = e_remind + "Please change your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail_change'))
        # sa 修改-状态
        elif callback.data == 'sa_change_status':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Choose your status\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_change_status'))
        # sa 修改-状态-0/1/2/3/4
        elif re.match('^sa_change_status_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(Status: .*)\nNote.*', content, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, 'Status: ' + mean('info_status', result), content, 1)
            prompt = e_remind + "Please change your information and submit it!\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail_change'))
        # sa 修改-备注
        elif callback.data == 'sa_change_note':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"Please input your note\n"
            re_msg = await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_change__return'))
            await re_msg.reply_text('Reply this message input your note!!', quote=True, reply_markup=ForceReply())
        # sa 修改-提交
        elif callback.data == 'sa_change_submit':
            content = callback.message.text.split(line)[0]
            # 提取信息栏
            match = re.match('.*information.*(No: .*)\n(Name: .*)\n(Country: .*)\n(Age: .*)\n(Height: .*)\n(Weight: .*)\n'
                             '(Boobs: .*)\n(Baby: .*)\n(Location: .*)\n(Special: .*)\n(Level: .*)\n(Phone: .*)\n(Wechat: .*)\n'
                             '(Telegram: .*)\n(Facebook: .*)\n(Price\_out: .*)\n(Price\_in: .*)\n(Picture: .*)\n'
                             '.*\n(Status: .*)\n(Note: .*)\n', content, re.S)
            no = match.group(1).split(': ')[1]
            name = match.group(2).split(': ')[1]
            country = match.group(3).split(': ')[1]
            age = match.group(4).split(': ')[1]
            height = match.group(5).split(': ')[1]
            weight = match.group(6).split(': ')[1]
            boobs = match.group(7).split(': ')[1]
            baby = match.group(8).split(': ')[1]
            location = match.group(9).split(': ')[1]
            special = match.group(10).split(': ')[1]
            level = match.group(11).split(': ')[1]
            phone = match.group(12).split(': ')[1]
            wechat = match.group(13).split(': ')[1]
            telegram = match.group(14).split(': ')[1]
            facebook = match.group(15).split(': ')[1]
            price_out = match.group(16).split(': ')[1]
            price_in = match.group(17).split(': ')[1]
            picture = match.group(18).split(': ')[1]
            status = match.group(19).split(': ')[1]
            note = match.group(20).split(': ')[1]
            # 信息栏整理
            country_code = mean('country', country, 'from')
            height = height[:3]
            weight = weight[:2]
            baby = mean('info_baby', baby, 'from')
            level = mean('info_level', level, 'from')
            status = mean('info_status', status, 'from')
            if price_out == '':
                price_out = '-'
            if note == '':
                note = '-'
            # 提取更新前的外部信息
            sql_str = "select age,height,weight,boobs,location,special,level,country,price_out,picture,status,special_en " \
                      "from zoo_info where id={0:s} ".format(no)
            rst = tg_connector.query(sql_str)
            # 录入信息
            sql_str = "update zoo_info set name='{1:s}', age={2:s}, height={3:s}, weight={4:s}, boobs='{5:s}', " \
                      "baby='{6:s}', location='{7:s}', special='{8:s}', level={9:d}, phone='{10:s}', wechat='{11:s}'," \
                      " telegram='{12:s}', facebook='{13:s}', price_out='{14:s}', price_in='{15:s}', picture='{16:s}'," \
                      " country='{17:s}', status='{18:s}', note='{19:s}' where id={0:s} "\
                .format(no, name, age, height, weight, boobs, baby, location, special, level, phone, wechat, telegram,
                        facebook, price_out, price_in, picture, country_code, status, note)
            tg_connector.exe(sql_str)
            prompt = line + e_remind + "Girl's information has changed\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail'))
            # 更新special_en的信息
            if rst[0][11] == '' or rst[0][5] != special:
                special_en = update_special_en(special)
                sql_str = f"update zoo_info set special_en='{special_en}' where id={no}"
                tg_connector.exe(sql_str)
            else:
                special_en = rst[0][11]
            await client.send_message(params['user_hugo'], f"/special_en {no}|{special_en}")
            # 外部名片只在有外部信息更新的情况下更新
            if rst[0][:11] != (int(age), int(height), int(weight), boobs, location, special, level, country_code,
                               price_out, picture, status):
                # 更新中文主频道
                await update_home(no)
                if await show_pinglun_home():
                    await asyncio.sleep(2)
                await show_board()
                # 更新英文主频道
                await update_home_en(no)
                await show_board_en()
                # 更新分类频道
                auto_class()
                await update_sub_chl()
                await update_sub_chl_en()
                await asyncio.sleep(2)
                # 对下线的动物，清理主频道和大众点评中的评论
                await clean_status_pinglun()
                # 更新讨论区名片
                await copy_to_disc(no)
        # sa 修改-优先
        elif callback.data == 'sa_change_raise':
            content = callback.message.text.split(line)[0]
            prompt = line+e_remind+"已对女孩做优先展示\n"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail'))
            match = re.match('.*\n(No: .*)\nName: .*', content, re.S)
            no = match.group(1).split(': ')[1]
            await update_home(no)
            if await show_pinglun_home():
                await asyncio.sleep(2)
            await show_board()
            # 更新英文主频道
            await update_home_en(no)
            await show_board_en()
            # 更新分类频道
            auto_class()
            await update_sub_chl()
            await update_sub_chl_en()
            await asyncio.sleep(2)
            # 对下线的动物，清理主频道和大众点评中的评论
            await clean_status_pinglun()
            # 更新讨论区名片
            await copy_to_disc(no)
        # sa 返回detail页面
        elif callback.data == 'sa_search_detail__return':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "Please select your operation\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail'))
        # sa 展示给客户
        elif callback.data == 'sa_search_detail_guest':
            content = callback.message.text.split(line)[0]
            match = re.match('.*(No: .*)\nName.*', content, re.S)
            no = match.group(1).split(': ')[1]
            # 获取个人信息
            sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,price_out,picture,status," \
                      "country from zoo_info where id={0:s}".format(no)
            await send_girl(sql_str, client, callback.message.chat.id)
        # sa 展示给女孩
        elif callback.data == 'sa_search_detail_girl':
            content = callback.message.text.split(line)[0]
            match = re.match('.*(No: .*)\nName.*', content, re.S)
            no = match.group(1).split(': ')[1]
            # 获取个人信息
            sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,phone,wechat,telegram," \
                      "facebook,price_in,picture,viewed,status from zoo_info where id={0:s}".format(no)
            rst = tg_connector.query(sql_str)
            content = get_content_to_girl(rst)
            # 发媒体
            media_list = await media.get_media(client, rst[0][15].split('|'), content)
            await client.send_media_group(callback.message.chat.id, media=media_list, disable_notification=True)
        # sa 验证
        elif callback.data == 'sa_verify':
            cur_message = callback.message
            pre_msg = await client.get_messages(cur_message.chat.id,
                                                   reply_to_message_ids=cur_message.id, replies=1)
            match = re.match('.*(No: .*)\nName.*', pre_msg.caption, re.S)
            no = match.group(1).split(': ')[1]
            # 获取个人信息
            sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,phone,wechat,telegram," \
                      "facebook,price_out,price_in,picture,country,viewed,status,note from zoo_info where " \
                      "id={0:s}".format(no)
            rst = tg_connector.query(sql_str)
            # 发详细信息
            content = get_content(rst)
            prompt = e_remind + "Please change your information and submit it!\n"
            await client.send_message(callback.message.chat.id,
                                      content + line + prompt,
                                      parse_mode=ParseMode.HTML,
                                      reply_markup=get_imp('sa_search_detail_change'))
        # sa 查询
        elif callback.data == 'sa_query':
            sql_str = "select id,name,country,status from zoo_info order by id desc limit 10"
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                content = e_girls + " No girl here!\n"
            else:
                content = e_girls + " Girls as follows:\n" + e_light + " No - Name - Country - Status\n"
                n = 1
                for row in rst:
                    content += e_num[n] + ": {0[0]:d} - {0[1]:s} - {2:s} - {1:s}\n"\
                        .format(row, mean('info_status', row[3]), mean('country', row[2])[:2])
                    n += 1
                sql_str = "select count(1) from zoo_info"
                rst = tg_connector.query(sql_str)
                all_page = math.ceil(rst[0][0] / 10)
                content += e_right2+" All:All | Page:{0:d}-1 ".format(all_page)+e_left2+"\n"
            prompt = line + e_remind + "Please select your operation"
            await callback.edit_message_text(content+prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_query'))
        # sa 查询-数字(1-10)
        elif re.match('^sa_query_\d{1,2}$', callback.data, re.S) is not None:
            pre_content = callback.message.text.split(line)[0]
            num = int(callback.data.split('_')[2])
            match = re.match('.*\n'+e_num[num]+': (\d*) - .*', pre_content, re.S)
            if match is None:
                prompt = line + e_warning + "Number {0:d} does not exist\n".format(num)
                await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_query'))
            else:
                no = match.group(1)
                # 获取个人信息
                sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,phone,wechat,telegram," \
                          "facebook,price_out,price_in,picture,country,viewed,status,note from zoo_info where " \
                          "id={0:s}".format(no)
                rst = tg_connector.query(sql_str)
                # 发媒体
                media_list = await media.get_media(client, rst[0][16].split('|'), "{0[1]:s}\'s medias".format(rst[0]))
                await client.send_media_group(callback.message.chat.id, media=media_list, disable_notification=True)
                # 发详细信息
                content = get_content(rst)
                prompt = e_remind + "Please select your operation!\n"
                await client.send_message(callback.message.chat.id,
                                          content + line + prompt,
                                          parse_mode=ParseMode.HTML,
                                          reply_markup=get_imp('sa_search_detail'))
                # 变更原消息按钮
                prompt = line + e_remind + "You chose number {0:d}\n".format(num)
                await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_query'))
        # sa 查询-主题
        elif re.match('^sa_query_[a-zA-Z]+$', callback.data, re.S) is not None:
            pre_content = callback.message.text.split(line)[0]
            type = callback.data.split('_')[2]
            if type == 'name':
                prompt = line + e_remind + "This is a name fuzzy query\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('sa_query__return'))
                await re_msg.reply_text('Reply this message input query name!!', quote=True, reply_markup=ForceReply())
            elif type == 'country':
                content = callback.message.text.split(line)[0]
                prompt = line + e_remind + "Choose The girl's country\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_query_country'))
            elif type == 'status':
                content = callback.message.text.split(line)[0]
                prompt = line + e_remind + "Choose The girl's status\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_query_status'))
            elif type in ('PrePage', 'NextPage'):
                content = callback.message.text.split(line)[0]
                match = re.match('.*\n' + e_right2 + ' (.*):(.*) \| Page:(.*)-(.*) ' + e_left2 + '.*', pre_content, re.S)
                if match is None:
                    prompt = line + e_warning + "No {0:s}".format(type)
                else:
                    col_name = match.group(1)
                    col_value = match.group(2)
                    all_page = int(match.group(3))
                    now_page = int(match.group(4))
                    next_page = now_page + mean('change_page', type, 'from')
                    if next_page == 0:
                        prompt = line + e_remind + "Page already to the top"
                    elif next_page > all_page:
                        prompt = line + e_remind + "Page already to the bottom"
                    else:
                        start_row = (next_page-1)*10
                        if col_name == 'All':
                            sql_str_sub = ''
                        elif col_name == 'Country':
                            sql_str_sub = "where {0:s}='{1:s}'".format(col_name, mean('country', col_value, 'from'))
                        elif col_name == 'Status':
                            sql_str_sub = "where {0:s}='{1:s}'"\
                                .format(col_name, mean('info_status', col_value, 'from'))
                        elif col_name == 'Name':
                            sql_str_sub = "where {0:s} like '%{1:s}%'".format(col_name, col_value)
                        else:
                            sql_str_sub = "where {0:s}='{1:s}'".format(col_name, col_value)
                        sql_str = "select id,name,country,status from zoo_info {1:s} order by id desc " \
                                  "limit {0:d},10".format(start_row, sql_str_sub)
                        rst = tg_connector.query(sql_str)
                        if len(rst) == 0:
                            content = e_girls + " No girl here!\n"
                        else:
                            content = e_girls + " Girls as follows:\n" + e_light + " No - Name - Country - Status\n"
                            n = 1
                            for row in rst:
                                content += e_num[n] + ": {0[0]:d} - {0[1]:s} - {2:s} - {1:s}\n" \
                                    .format(row, mean('info_status', row[3]), mean('country', row[2])[:2])
                                n += 1
                            sql_str = "select count(1) from zoo_info {0:s}".format(sql_str_sub)
                            rst = tg_connector.query(sql_str)
                            all_page = math.ceil(rst[0][0] / 10)
                            content += e_right2 + " {2:s}:{1:s} | Page:{0:d}-{3:d} "\
                                .format(all_page, col_value, col_name, next_page) + e_left2 + "\n"
                        prompt = line + e_remind + "You chose page {0:d}".format(next_page)
                if callback.message.text == content + prompt:
                     return
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_query'))
        # sa 查询-国籍-0/1/2/3/4
        elif re.match('^sa_query_country_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            sql_str = "select id,name,country,status from zoo_info where country={0:s} order by id desc " \
                      "limit 10".format(result)
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                content = e_girls + " No girl here!\n"
            else:
                content = e_girls + " Girls as follows:\n" + e_light + " No - Name - Country - Status\n"
                n = 1
                for row in rst:
                    content += e_num[n] + ": {0[0]:d} - {0[1]:s} - {2:s} - {1:s}\n" \
                        .format(row, mean('info_status', row[3]), mean('country', row[2])[:2])
                    n += 1
                sql_str = "select count(1) from zoo_info where country={0:s}".format(result)
                rst = tg_connector.query(sql_str)
                all_page = math.ceil(rst[0][0] / 10)
                content += e_right2 + " Country:{1:s} | Page:{0:d}-1 "\
                    .format(all_page, mean('country', result)) + e_left2 + "\n"
            prompt = line + e_remind + "Please select your operation"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_query'))
        # sa 查询-状态-4/3/2/1/0
        elif re.match('^sa_query_status_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            sql_str = "select id,name,country,status from zoo_info where status='{0:s}' order by id desc " \
                      "limit 10".format(result)
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                content = e_girls + " No girl here!\n"
            else:
                content = e_girls + " Girls as follows:\n" + e_light + " No - Name - Country - Status\n"
                n = 1
                for row in rst:
                    content += e_num[n] + ": {0[0]:d} - {0[1]:s} - {2:s} - {1:s}\n" \
                        .format(row, mean('info_status', row[3]), mean('country', row[2])[:2])
                    n += 1
                sql_str = "select count(1) from zoo_info where status='{0:s}'".format(result)
                rst = tg_connector.query(sql_str)
                all_page = math.ceil(rst[0][0] / 10)
                content += e_right2 + " Status:{1:s} | Page:{0:d}-1 "\
                    .format(all_page, mean('info_status', result)) + e_left2 + "\n"
            prompt = line + e_remind + "Please select your operation"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_query'))
        # sa 查询-返回
        elif callback.data == 'sa_query__return':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "Please select your operation\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_query'))
        # sa 分类 sa_search_detail_class
        elif callback.data == 'sa_search_detail_class':
            content = callback.message.text.split(line)[0]
            match = re.match('.*(No: .*)\n(Name: .*)\nAge: .*', content, re.S)
            no = match.group(1).split(': ')[1]
            name = match.group(2).split(': ')[1]
            # 获取动物当前分类状况
            class_no = []
            class_name = []
            for row in get_class(id=no):
                class_no.append(row[2])
                class_name.append(mean('class_type', row[2])[0])
            if len(class_name) > 0:
                prompt = line + e_remind + "女孩 {0:s} 目前的分类如下：\n{1:s}\n".format(name, '， '.join(class_name))
            else:
                prompt = line + e_remind + "女孩 {0:s} 暂无分类\n".format(name)
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_search_detail_class', class_no))
        # sa 分类-31/30/41/40/51/50  sa_search_detail_class_XX
        elif re.match('^sa_search_detail_class_', callback.data, re.S) is not None:
            content = callback.message.text.split(line)[0]
            match = re.match('.*(No: .*)\n(Name: .*)\nAge: .*', content, re.S)
            no = match.group(1).split(': ')[1]
            name = match.group(2).split(': ')[1]
            result = callback.data.split('_')[4]
            if result[1] == '1':
                set_class(int(no), result[0], 'add')
            else:
                set_class(int(no), result[0], 'delete')
            # 获取动物当前分类状况
            class_no = []
            class_name = []
            for row in get_class(id=no):
                class_no.append(row[2])
                class_name.append(mean('class_type', row[2])[0])
            if len(class_name) == 0:
                prompt = line + e_remind + "变更后女孩 {0:s} 暂无分类\n".format(name)
            else:
                prompt = line + e_remind + "女孩 {0:s} 分类变更为\n{1:s}\n".format(name, "，".join(class_name))
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_search_detail_class', class_no))
            wait_ind = await copy_to_class(no, int(result[0]))
            if wait_ind:
                await asyncio.sleep(2)
            await copy_board(int(result[0]))
        # sa 权限
        elif callback.data == 'sa_limit':
            # content = callback.message.text.split(line)[0] + '\n'
            # prompt = line + e_remind + "请输入你要查询权限的账号名字\n"
            content = "请输入你要查询权限的账号名字"
            re_msg = await callback.edit_message_text(content, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_search'))
            await re_msg.reply_text('Reply this message input limit name!!', quote=True, reply_markup=ForceReply())
        # sa 权限-11/10/21/20/31/30/41/40
        elif re.match('^sa_limit_', callback.data, re.S) is not None:
            match = re.match('.*【(.*)】.*', callback.message.text, re.S)
            name = match.group(1)

            # content = callback.message.text.split(line)[0]
            # name = callback.message.text.split(line)[1].split(' 的权限')[0][1:]
            user_id = f_chats_name(name)[0]
            result = callback.data.split('_')[2]
            if result[1] == '1':
                insert_limit(user_id, result[0])
            else:
                sql_str = "delete from zoo_limit where id={0:d} and type='{1:s}'" \
                    .format(user_id, result[0])
                tg_connector.exe(sql_str)
            sql_str = "select type from zoo_limit where id='{0:d}'".format(user_id)
            rst = tg_connector.query(sql_str)
            content = "【{1:s}】 {0:s} 已选择".format(mean('limit', result), name)
            await callback.edit_message_text(content, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_limit', rst))
        # # sa 公告牌
        # elif callback.data == 'sa_board':
        #     content = get_content_board(True, True)
        #     prompt = line + e_remind + "Please select your operation"
        #     await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
        #                                      disable_web_page_preview=True,
        #                                      reply_markup=get_imp('sa_board'))
        # # sa 调整公告栏信息
        # elif re.match('^sa_board_', callback.data, re.S) is not None:
        #     board = callback.data.split('_')
        #     content = get_content_board(False)
        #     prompt = line + e_remind + "Please input your board: {0[2]:s}_{0[3]:s}".format(board)
        #     re_msg = await callback.edit_message_text(content + prompt,
        #                                               parse_mode=ParseMode.DISABLED,
        #                                               disable_web_page_preview=True,
        #                                               reply_markup=get_imp('sa_search'))
        #     await re_msg.reply_text("Reply this message input your board: {0[2]:s}_{0[3]:s}!!".format(board),
        #                             quote=True, reply_markup=ForceReply())
        # sa 客户-下单
        elif callback.data == 'sa_guest_order':
            content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请选择 增加/减少 1单\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('sa_guest_order'))
        # sa 客户-下单 增加/减少
        elif re.match('^sa_guest_order_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            match = re.match('.*' + e_man + ' 内部名: (.*?)\n' + e_man2 + '.*', callback.message.text, re.S)
            name = match.group(1)
            if result == 'add':
                result_cn = "增加"
                sql_str = "update zoo_guest set orders=orders+1 where name = '{0:s}'".format(name)
            else:
                result_cn = "减少"
                sql_str = "update zoo_guest set orders=orders-1 where name = '{0:s}'".format(name)
            tg_connector.exe(sql_str)
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where name='{0:s}'".format(name)
            rst = tg_connector.query(sql_str)
            content = get_content_guest(rst[0])
            prompt = line + e_remind + "你刚才给客户 {0:s} 1单\n".format(result_cn)
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_com_imp('sa_guest'))
        # sa 客户-关系
        elif callback.data == 'sa_guest_link':
            content = callback.message.text.split(line)[0]
            match = re.match('.*' + e_dna + ' 关系: (.*?)\n' + e_bot + '.*', content, re.S)
            link = match.group(1)
            prompt = line + e_remind + "请选择客户关系\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('sa_guest_link', link))
        # sa 客户-关系 0:未聊天(默认)；1:正常客户；2:非客户；3:正常女孩
        elif re.match('^sa_guest_link_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            match = re.match('.*' + e_man + ' 内部名: (.*?)\n' + e_man2 + '.*', callback.message.text, re.S)
            name = match.group(1)
            sql_str = "update zoo_guest set status_chat='{1:s}' where name = '{0:s}'".format(name, result)
            tg_connector.exe(sql_str)
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where name='{0:s}'".format(name)
            rst = tg_connector.query(sql_str)
            content = get_content_guest(rst[0])
            prompt = line + e_remind + "目前客户关系为：{0:s}\n".format(mean('status_chat', result))
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_com_imp('sa_guest'))
            # 若关系设置为女孩中心，则展示相应界面，否则在女孩中心删除相应的记录
            if result == '3':
                await show_girlc(str(rst[0][0]))
            else:
                sql_str = "delete from zoo_girlc where user_id={0:d}".format(rst[0][0])
                tg_connector.exe(sql_str)
                if rst[0][0] in girlc.keys():
                    del girlc[rst[0][0]]
        # sa 客户-刷新
        elif callback.data == 'sa_guest_refresh':
            match = re.match('.*' + e_man + ' 内部名: (.*?)\n' + e_man2 + '.*', callback.message.text, re.S)
            name = match.group(1)
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where name='{0:s}'".format(name)
            rst = tg_connector.query(sql_str)
            content = get_content_guest(rst[0])
            prompt = line + e_remind + "客户的刷新时间： {0:s}\n"\
                .format(time.strftime('%H:%M:%S', time.localtime(time.time())))
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_com_imp('sa_guest'))
        # sa 客户-地址/电话/备注
        elif callback.data in ('sa_guest_addr', 'sa_guest_phone', 'sa_guest_note', 'sa_guest_return'):
            result = callback.data.split('_')[2]
            content = callback.message.text.split(line)[0]
            if result == 'addr':
                prompt = line + e_remind + "请输入客户地址\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp('sa_guest_return'))
                await re_msg.reply_text("回复本消息输入客户地址!!", quote=True, reply_markup=ForceReply())
            elif result == 'phone':
                prompt = line + e_remind + "请输入客户电话\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp('sa_guest_return'))
                await re_msg.reply_text("回复本消息输入客户电话!!", quote=True, reply_markup=ForceReply())
            elif result == 'note':
                prompt = line + e_remind + "请输入客户备注\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp('sa_guest_return'))
                await re_msg.reply_text("回复本消息输入客户备注!!", quote=True, reply_markup=ForceReply())
            elif result == 'return':
                prompt = line + e_remind + "客户返回操作\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_com_imp('sa_guest'))

        # 查客户
        elif callback.data == 'sa_ckguest':
            content = callback.message.text.split(line)[0] + '\n'
            prompt = line + e_remind + "请输入客户ID或者内部名\n"
            re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('sa_search'))
            await re_msg.reply_text('回复本消息输入需要查的客户ID或者内部名!!', quote=True, reply_markup=ForceReply())
        # sa 我的客户 主页
        elif callback.data == 'sa_myguest':
            content = e_left2 + '\n' + e_search + '关系: ' + mean('status_chat', '1') + '\n'
            content = guest_page(content)
            prompt = line + e_remind + "请选择你的操作"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_myguest'),
                                             disable_web_page_preview=True)
        # sa 我的客户-数字(1-10)
        elif re.match('^sa_myguest_\d{1,2}$', callback.data, re.S) is not None:
            pre_content = callback.message.text.split(line)[0]
            num = int(callback.data.split('_')[2])
            match = re.match('.*\n'+e_num[num]+': (\d{4})-(.*?)-.*', pre_content, re.S)
            if match is None:
                prompt = line + e_warning + "号码 {0:d} 不存在\n".format(num)
                await callback.edit_message_text(guest_page(pre_content) + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_myguest'), disable_web_page_preview=True)
            else:
                no = match.group(1)
                name = match.group(2)
                await show_guest([no, name], callback.from_user.id, 'id_name')
                # 变更原消息按钮
                prompt = line + e_remind + "你选择了号码 {0:d}\n".format(num)
                await callback.edit_message_text(guest_page(pre_content) + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_myguest'), disable_web_page_preview=True)
        # sa 我的客户 关系
        elif callback.data == 'sa_myguest_link':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请选择客户关系"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_myguest_link'),
                                             disable_web_page_preview=True)
        # sa 我的客户 关系  0/1/2/3
        elif re.match('^sa_myguest_link_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(' + e_search + '关系: .*?)\n.*', content, re.S)
            if match is None:
                content += e_search + '关系: ' + mean('status_chat', result) + '\n'
            else:
                sour_str = match.group(1)
                content = re.sub(sour_str, e_search + '关系: ' + mean('status_chat', result), content, 1)
            content = guest_page(content, 1)
            prompt = e_remind + "请选择你的操作\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_myguest'), disable_web_page_preview=True)
        # sa 我的客户 全部
        elif callback.data == 'sa_myguest_all':
            pre_content = callback.message.text.split(e_left2)[0] + e_left2
            prompt = line + e_remind + "请选择你的操作"
            await callback.edit_message_text(guest_page(pre_content) + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_myguest'),
                                             disable_web_page_preview=True)
        # sa 我的客户 机器人
        elif callback.data == 'sa_myguest_bot':
            pre_content = callback.message.text.split(line)[0]
            prompt = line + e_remind + "请选择机器人状态"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_myguest_bot'),
                                             disable_web_page_preview=True)
        # sa 我的客户 机器人  1/2/3/4
        elif re.match('^sa_myguest_bot_', callback.data, re.S) is not None:
            result = callback.data.split('_')[3]
            content = callback.message.text.split(line)[0]
            match = re.match('.*\n(' + e_search + '机器人: .*?)\n.*', content, re.S)
            if match is None:
                content += e_search + '机器人: ' + mean('status_bot', result) + '\n'
            else:
                sour_str = match.group(1)
                content = re.sub(sour_str, e_search + '机器人: ' + mean('status_bot', result), content, 1)
            content = guest_page(content, 1)
            prompt = e_remind + "请选择你的操作\n"
            await callback.edit_message_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('sa_myguest'), disable_web_page_preview=True)
        # sa 我的客户 - 翻页
        elif callback.data in ('sa_myguest_PrePage', 'sa_myguest_NextPage'):
            type = callback.data.split('_')[2]
            pre_content = callback.message.text.split(line)[0]
            match = re.match('.*\n' + e_right2 + ' 页数:(.*?)-(.*?) ' + e_left2 + '.*', pre_content, re.S)
            if match is None:
                content = guest_page(pre_content)
                prompt = line + e_warning + "没有{0:s}".format(mean('page_cn', type))
            else:
                all_page = int(match.group(1))
                now_page = int(match.group(2))
                next_page = now_page + mean('change_page', type, 'from')
                if next_page == 0:
                    content = guest_page(pre_content)
                    prompt = line + e_remind + "已到顶页，不能再上了"
                elif next_page > all_page:
                    content = guest_page(pre_content)
                    prompt = line + e_remind + "已到底页，不能再下了"
                else:
                    content = guest_page(pre_content, next_page)
                    prompt = line + e_remind + "你选择了第 {0:d} 页".format(next_page)
            if callback.message.text != content + prompt:
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('sa_myguest'),
                                                 disable_web_page_preview=True)
        # # sa 女孩中心
        # elif callback.data == 'sa_girlc':
        #     content = callback.message.text.split(line)[0] + '\n'
        #     prompt = line + e_remind + "请输入女孩中心查询内容\n"
        #     re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
        #                                               reply_markup=get_imp('sa_search'))
        #     await re_msg.reply_text('回复本消息输入女孩中心查询内容!!', quote=True, reply_markup=ForceReply())
        # 女孩中心_按钮
        elif re.match('^girlc_', callback.data, re.S) is not None:
            pre_content = callback.message.text.split(line)[0]
            match = re.match('.*' + e_star + ' ID: (.*?)\n' + e_man + '.*', pre_content, re.S)
            id = int(match.group(1))
            if callback.data == 'girlc_bot':
                prompt = line + e_remind + "请选择<b>是否允许</b>使用机器人"
                markup = get_imp('girlc_bot')
            elif callback.data == 'girlc_bot_allow':
                set_girlc_stauts(id, '0*')
                ident = await identity(id, False)
                set_girlc_stauts(id, ident[0] + '*')
                prompt = line + e_remind + "你已允许她使用机器人"
                markup = get_com_imp('girlcadmin')
            elif callback.data == 'girlc_bot_forbid':
                set_girlc_stauts(id, '7*')
                prompt = line + e_remind + "你已禁止她使用机器人"
                markup = get_com_imp('girlcadmin')
            elif callback.data == 'girlc_detail':
                markup = get_com_imp('girlcadmin')
                girl_id = get_girlc(id)[3]
                if girl_id > 0:
                    sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,phone,wechat," \
                              "telegram,facebook,price_out,price_in,picture,country,viewed,status,note from zoo_info " \
                              "where id={0:d}".format(girl_id)
                    rst = tg_connector.query(sql_str)
                    if len(rst) != 0:
                        # 发媒体
                        media_list = await media.get_media(client, rst[0][16].split('|'),
                                                           "{0[1]:s}\'s medias".format(rst[0]))
                        await client.send_media_group(params['user_hugo'], media=media_list, disable_notification=True)
                        # 发详细信息
                        content = get_content(rst)
                        prompt2 = e_remind + "Please select your operation!\n"
                        await client.send_message(params['user_hugo'],
                                                  content + line + prompt2,
                                                  parse_mode=ParseMode.HTML,
                                                  reply_markup=get_imp('sa_search_detail'))
                    prompt = line + e_remind + "已展示女孩详细信息 {0:s}\n".format(
                        time.strftime('%H:%M:%S', time.localtime(time.time())))
                    # 需要增加通过hugo名义发信息给动物！！！
                else:
                    prompt = line + e_remind + "找不到对应的女孩编号\n".format()
            elif callback.data == 'girlc_note':
                prompt = line + e_remind + "请输入女孩中心备注\n"
                content = get_content_girlc(id)
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp('girlc_return'))
                await re_msg.reply_text("回复本消息输入女孩中心备注!!", quote=True, reply_markup=ForceReply())
                return
            elif callback.data == 'girlc_return':
                prompt = line + e_remind + "女孩中心返回操作\n"
                markup = get_com_imp('girlcadmin')
            content = get_content_girlc(id)
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=markup, disable_web_page_preview=True)
        # 公告
        elif callback.data == 'sa_board':
            sql_str = "select content from zoo_note where id='board_notice'"
            rst = tg_connector.query(sql_str)
            content = "目前<b>公告<b/>：\n{0:s}".format(rst[0][0])
            re_msg = await callback.edit_message_text(content,
                                                      parse_mode=ParseMode.HTML,
                                                      disable_web_page_preview=True,
                                                      reply_markup=get_imp('sa_search'))
            await re_msg.reply_text("请输入公告内容!!", quote=True, reply_markup=ForceReply())
        # Notice
        elif callback.data == 'sa_boarden':
            sql_str = "select content from zoo_note where id='board_notice_en'"
            rst = read_connector.query(sql_str)
            content = "目前<b>英文公告<b/>：\n{0:s}".format(rst[0][0])
            re_msg = await callback.edit_message_text(content,
                                                      parse_mode=ParseMode.HTML,
                                                      disable_web_page_preview=True,
                                                      reply_markup=get_imp('sa_search'))
            await re_msg.reply_text("请输入英文公告内容!!", quote=True, reply_markup=ForceReply())
        # 越南动物
        elif callback.data == 'sa_fixava':
            sql_str = "select content from zoo_note where id='board_available'"
            rst = tg_connector.query(sql_str)
            content = "目前<b>越南动物</b>名单：\n{0:s}".format(rst[0][0])
            re_msg = await callback.edit_message_text(content,
                                                      parse_mode=ParseMode.HTML,
                                                      disable_web_page_preview=True,
                                                      reply_markup=get_imp('sa_search'))
            await re_msg.reply_text("请输入越南动物名单!!", quote=True, reply_markup=ForceReply())
        # 空闲名单
        elif callback.data == 'sa_ava':
            sql_str = "select content from zoo_note where id='board_available'"
            rst = tg_connector.query(sql_str)
            if rst[0][0] == '':
                fix_ava = '0'
            else:
                fix_ava = rst[0][0]
            sql_str = "select time,GROUP_CONCAT(girl_id order by created_at desc) from ( select b.time,b.girl_id," \
                      "b.created_at from ( select type,girl_id,created_at,ceil(time_to_sec(timediff(now(), " \
                      "created_at))/3600) as time, row_number() over(partition by girl_id order by created_at desc) " \
                      "as rn  from ( select type,girl_id,created_at from zoo_girlc_log where created_at>date_add(" \
                      "CURRENT_TIMESTAMP(), interval -3 hour) and girl_id<>0 and type in ('1','2','3','4') union " \
                      "select '3',id,date_add(CURRENT_TIMESTAMP(), interval -200 minute) from zoo_info where id in " \
                      "({0:s}) ) a ) b join zoo_info c  on b.girl_id=c.id where b.rn=1 and b.type in ('1','3') and " \
                      "c.status in ('1','2','3') order by b.time,b.created_at desc ) d group by time order by time".format(fix_ava)
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                await client.send_message(params['user_hugo'], "最近没有报空记录", parse_mode=ParseMode.HTML)
            else:
                dict_rst = {}
                dict_rst.update(dict((col[0], col[1]) for col in rst))
                for i in range(1,5):
                    if i in dict_rst.keys():
                        await client.send_message(params['user_hugo'], '/girl ' + dict_rst[i], parse_mode=ParseMode.HTML)
                    elif i != 4:
                        await client.send_message(params['user_hugo'], "{0:d}小时内没有报空记录".format(i),
                                                  parse_mode=ParseMode.HTML)
                    else:
                        await client.send_message(params['user_hugo'], "没有固定报空记录".format(i),
                                                  parse_mode=ParseMode.HTML)
                    await asyncio.sleep(1)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复-页面
@Client.on_message(filters.user(sa) & filters.reply, group=-2)
async def reply_interface(client, message):
    try:
        re_msg = message.reply_to_message
        pre_re_msg = await client.get_messages(message.chat.id, reply_to_message_ids=re_msg.id, replies=1)

        # 名字
        if re_msg.text == 'Reply this message input your name':
            str_input = await input_cert(message.text, 'name', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Name: .*)\nCountry.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Name: ' + str_input[1].title(), content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 年龄
        elif re_msg.text == 'Reply this message input your age':
            str_input = await input_cert(message.text, 'age', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Age: .*)\nHeight.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Age: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 身高
        elif re_msg.text == 'Reply this message input your height(cm)':
            str_input = await input_cert(message.text, 'height', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Height: .*)\nWeight.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Height: ' + str_input[1] + 'cm', content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 体重
        elif re_msg.text == 'Reply this message input your weight(kg)':
            str_input = await input_cert(message.text, 'weight', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Weight: .*)\nBoobs.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Weight: ' + str_input[1] + 'kg', content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # xiong
        elif re_msg.text == 'Reply this message input your boobs':
            str_input = await input_cert(message.text, 'boobs', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Boobs: .*)\nBaby.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Boobs: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 地址
        elif re_msg.text == 'Reply this message input your location':
            str_input = await input_cert(message.text, 'location', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Location: .*)\nSpecial.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Location: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 特长
        elif re_msg.text == 'Reply this message input your special':
            str_input = await input_cert(message.text, 'special', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Special: .*)\nPhone.*', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Special: ' + str_input[1], 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()        # Phone
        elif re_msg.text == 'Reply this message input your phone':
            str_input = await input_cert(message.text, 'phone', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Phone: .*)\nWechat.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Phone: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # Wechat
        elif re_msg.text == 'Reply this message input your wechat':
            str_input = await input_cert(message.text, 'wechat', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Wechat: .*)\nTelegram.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Wechat: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # Telegram
        elif re_msg.text == 'Reply this message input your telegram':
            str_input = await input_cert(message.text, 'telegram', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Telegram: .*)\nFacebook.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Telegram: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # Facebook
        elif re_msg.text == 'Reply this message input your facebook':
            str_input = await input_cert(message.text, 'facebook', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Facebook: .*)\nPrice.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Facebook: ' + str_input[1], content, 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 价格
        elif re_msg.text == 'Reply this message input your price':
            str_input = await input_cert(message.text, 'price', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Price: .*)\nPicture.*', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, "Price: {0:s}\n  Package1:\n  1h-1p / 3h-2p / 6h-3p / 8h-unl\n  "
                                                    "Package2:\n  1h-1p/3h-2p/6h-3p/8h-unl/10h-unl/12h-unl/24h-unl"
                                          .format(str_input[1]))
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            await message.delete()
            await re_msg.delete()
        # 照片
        elif re_msg.text == 'Reply this message input your picture':
            rst_check = await check_media_group(client, message)
            if rst_check[0] == '2':
                return
            content = pre_re_msg.text.split(line)[0]
            if rst_check[0] == '1':
                msg_list = await client.copy_media_group(media.to_chat, message.chat.id, rst_check[1][0].id)
                med_list = []
                for msg in msg_list:
                    msg_row = await media.save(client, msg, 'both')
                    med_list.append(str(msg_row[0]))
                match = re.match('.*\n(Picture: .*)\n', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Picture: ' + '|'.join(med_list), 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + "Photos don\'t meet the requirements\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('new'))
            delete_list = []
            for msg in rst_check[1]:
                delete_list.append(msg.id)
            await client.delete_messages(message.chat.id, delete_list)
            await re_msg.delete()
        # sa 查询
        elif re_msg.text == 'Reply this message input no or name!!':
            # 定位输入类型 并 查找
            str_input = await input_cert(message.text, 'search', client, message.from_user.id)
            if str_input[0] == 'no':
                sub_sql_str = " id={0:d}".format(str_input[1])
            elif str_input[0] == 'phone':
                sub_sql_str = " phone like '%{0:s}%'".format(str_input[1])
            else:
                sub_sql_str = " name='{0:s}'".format(str_input[1])
            sql_str = "select id,name,age,height,weight,boobs,baby,location,special,level,phone,wechat,telegram," \
                      "facebook,price_out,price_in,picture,country,viewed,status,note from zoo_info where" + sub_sql_str
            rst = tg_connector.query(sql_str)
            if len(rst) != 0:
                # 发媒体
                media_list = await media.get_media(client, rst[0][16].split('|'), "{0[1]:s}\'s medias".format(rst[0]))
                await client.send_media_group(message.chat.id, media=media_list, disable_notification=True)
                # 发详细信息
                content = get_content(rst)
                prompt = e_remind + "Please select your operation!\n"
                await client.send_message(message.chat.id,
                                          content + line + prompt,
                                          parse_mode=ParseMode.HTML,
                                          reply_markup=get_imp('sa_search_detail'))
                # 清理原消息的按钮
                await pre_re_msg.edit_reply_markup('')
            else:
                prompt = e_warning + "I can't find the girl {0:s} of {1:s}\n".format(str_input[0], message.text)

                content = pre_re_msg.text.split(line)[0]
                await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_home'))
            await message.delete()
            await re_msg.delete()
        # sa 名字
        if re_msg.text == 'Reply this message input your name!!':
            str_input = await input_cert(message.text, 'sa_name', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Name: .*)\nCountry.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Name: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 年龄
        elif re_msg.text == 'Reply this message input your age!!':
            str_input = await input_cert(message.text, 'age', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Age: .*)\nHeight.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Age: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 身高
        elif re_msg.text == 'Reply this message input your height(cm)!!':
            str_input = await input_cert(message.text, 'height', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Height: .*)\nWeight.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Height: ' + str_input[1] + 'cm', content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 体重
        elif re_msg.text == 'Reply this message input your weight(kg)!!':
            str_input = await input_cert(message.text, 'weight', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Weight: .*)\nBoobs.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Weight: ' + str_input[1] + 'kg', content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa xiong
        elif re_msg.text == 'Reply this message input your boobs!!':
            str_input = await input_cert(message.text, 'boobs', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Boobs: .*)\nBaby.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Boobs: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 地址
        elif re_msg.text == 'Reply this message input your location!!':
            str_input = await input_cert(message.text, 'location', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Location: .*)\nSpecial.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Location: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 特长
        elif re_msg.text == 'Reply this message input your special!!':
            str_input = await input_cert(message.text, 'special', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Special: .*)\nLevel.*', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Special: ' + str_input[1], 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa Phone
        elif re_msg.text == 'Reply this message input your phone!!':
            str_input = await input_cert(message.text, 'phone', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Phone: .*)\nWechat.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Phone: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa Wechat
        elif re_msg.text == 'Reply this message input your wechat!!':
            str_input = await input_cert(message.text, 'wechat', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Wechat: .*)\nTelegram.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Wechat: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa Telegram
        elif re_msg.text == 'Reply this message input your telegram!!':
            str_input = await input_cert(message.text, 'telegram', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Telegram: .*)\nFacebook.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Telegram: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa Facebook
        elif re_msg.text == 'Reply this message input your facebook!!':
            str_input = await input_cert(message.text, 'facebook', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Facebook: .*)\nPrice\_out.*', content, re.S)
                sour_str = match.group(1)
                content = re.sub(sour_str, 'Facebook: ' + str_input[1], content, 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 外部价格
        elif re_msg.text == 'Reply this message input your price_out!!':
            str_input = await input_cert(message.text, 'price', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Price\_out: .*)\nPrice\_in.*', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Price_out: ' + str_input[1], 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 内部价格
        elif re_msg.text == 'Reply this message input your price_in!!':
            str_input = await input_cert(message.text, 'price', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Price\_in: .*)\nPicture.*', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Price_in: ' + str_input[1], 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 照片
        elif re_msg.text == 'Reply this message input your picture!!':
            rst_check = await check_media_group(client, message)
            if rst_check[0] == '2':
                return
            content = pre_re_msg.text.split(line)[0]
            if rst_check[0] == '1':
                msg_list = await client.copy_media_group(media.to_chat, message.chat.id, rst_check[1][0].id)
                med_list = []
                for msg in msg_list:
                    msg_row = await media.save(client, msg, 'both')
                    med_list.append(str(msg_row[0]))
                match = re.match('.*\n(Picture: .*)\nViewed.*', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Picture: ' + '|'.join(med_list), 1)
                prompt = e_remind + "Please edit your information and submit it!\n"
            else:
                prompt = e_warning + "Photos don\'t meet the requirements\n"
            await pre_re_msg.edit_text(content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            delete_list = []
            for msg in rst_check[1]:
                delete_list.append(msg.id)
            await client.delete_messages(message.chat.id, delete_list)
            await re_msg.delete()
        # sa 备注
        elif re_msg.text == 'Reply this message input your note!!':
            str_input = await input_cert(message.text, 'note', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                match = re.match('.*\n(Note: .*)\n', content, re.S)
                sour_str = match.group(1)
                content = content.replace(sour_str, 'Note: ' + str_input[1], 1)
                prompt = e_remind + "Please change your information and submit it!\n"
            else:
                prompt = e_warning + str_input[1] + "\n"
            await pre_re_msg.edit_text(content + line + prompt, parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('sa_search_detail_change'))
            await message.delete()
            await re_msg.delete()
        # sa 查询-名字
        elif re_msg.text == 'Reply this message input query name!!':
            str_input = message.text
            sql_str = "select id,name,country,status from zoo_info where name like '%{0:s}%' order by id desc " \
                      "limit 10".format(str_input)
            rst = tg_connector.query(sql_str)
            if len(rst) == 0:
                content = e_girls + " No girl here!\n"
            else:
                content = e_girls + " Girls as follows:\n" + e_light + " No - Name - Country - Status\n"
                n = 1
                for row in rst:
                    content += e_num[n] + ": {0[0]:d} - {0[1]:s} - {2:s} - {1:s}\n" \
                        .format(row, mean('info_status', row[3]), mean('country', row[2])[:2])
                    n += 1
                sql_str = "select count(1) from zoo_info where name like '%{0:s}%'".format(str_input)
                rst = tg_connector.query(sql_str)
                all_page = math.ceil(rst[0][0] / 10)
                content += e_right2 + " Name:{1:s} | Page:{0:d}-1 ".format(all_page, str_input) + e_left2 + "\n"
            prompt = line + e_remind + "Please select your operation\n"
            await pre_re_msg.edit_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_query'))
            await message.delete()
            await re_msg.delete()
        # sa 权限-name
        elif re_msg.text == 'Reply this message input limit name!!':
            str_input = await input_cert(message.text, 'offer', client, message.from_user.id)
            content = pre_re_msg.text.split(line)[0]
            if str_input[0]:
                sql_str = "select type from zoo_limit where name='{0:s}'".format(str_input[1])
                rst = tg_connector.query(sql_str)
                prompt = line + e_remind + "【{0:s}】的权限如下\n".format(str_input[1])
                await pre_re_msg.edit_text(content + prompt, parse_mode=ParseMode.HTML,
                                           reply_markup=get_imp('sa_limit', rst))
            else:
                prompt = line + e_warning + str_input[1] + "\n"
                await pre_re_msg.edit_text(content + prompt, parse_mode=ParseMode.HTML, reply_markup=get_imp('sa_home'))
            await message.delete()
            await re_msg.delete()
        # sa board 公告内容
        elif re_msg.text == "请输入公告内容!!":
            sql_str = "update zoo_note set content='{0:s}' where id='board_notice'".format(escape_string(message.text.html))
            tg_connector.exe(sql_str)
            content = "你已经把<b>公告</b>修改为：\n{0:s}".format(message.text.html)
            await pre_re_msg.edit_text(content,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('sa_home'))
            await message.delete()
            await re_msg.delete()
            await show_board(True)
            for i in range(1, 12):
                await asyncio.sleep(2)
                await copy_board(i, True)
        # sa board 英文公告内容
        elif re_msg.text == "请输入英文公告内容!!":
            sql_str = "update zoo_note set content='{0:s}' where id='board_notice_en'".format(
                escape_string(message.text.html))
            tg_connector.exe(sql_str)
            content = "你已经把<b>英文公告</b>修改为：\n{0:s}".format(message.text.html)
            await pre_re_msg.edit_text(content,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('sa_home'))
            await message.delete()
            await re_msg.delete()
            await show_board_en(True)
            for i in [21, 22]:
                await asyncio.sleep(2)
                await copy_board_en(i, True)
        # sa fixava 越南动物
        elif re_msg.text == "请输入越南动物名单!!":
            if re.match('^(\d{2,4},)*\d{2,4}$', message.text, re.S) is None:
                content = "你输入的<b>越南名单</b>格式不正确"
            else:
                sql_str = f"update zoo_note set content='{message.text}' where id='board_available'"
                tg_connector.exe(sql_str)
                content = f"你已经把<b>越南名单</b>修改为：\n{message.text}"
            await pre_re_msg.edit_text(content,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('sa_home'))
            await message.delete()
            await re_msg.delete()
            auto_class(['1', '21'])
            await update_sub_chl([1])
            await update_sub_chl_en([21])
        # sa 修改 客户-地址/电话/备注
        elif re.match('^回复本消息输入客户.*?!!$', re_msg.text, re.S) is not None:
            match = re.match('^回复本消息输入客户(.*?)!!$', re_msg.text, re.S)
            opt = match.group(1)
            match = re.match('.*' + e_man + ' 内部名: (.*?)\n' + e_man2 + '.*', pre_re_msg.text, re.S)
            name = match.group(1)
            if opt == '地址':
                sql_str = "update zoo_guest set addr='{0:s}' where name = '{1:s}'".format(message.text, name)
            elif opt == '电话':
                sql_str = "update zoo_guest set phone='{0:s}' where name = '{1:s}'".format(message.text, name)
            elif opt == '备注':
                sql_str = "update zoo_guest set note='{0:s}' where name = '{1:s}'".format(message.text, name)
            else:
                return
            tg_connector.exe(sql_str)
            sql_str = "select chat_id,name,username,outname,addr,phone,orders,status_chat,status_bot,remind_at," \
                      "active_at,created_at,note,std_id from zoo_guest where name='{0:s}'".format(name)
            rst = tg_connector.query(sql_str)
            content = get_content_guest(rst[0])
            prompt = line + e_remind + "修改客户信息成功：{0:s}\n".format(opt)
            await pre_re_msg.edit_text(content + prompt, parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_com_imp('sa_guest'))
            await message.delete()
            await re_msg.delete()
        # sa 查客户
        elif re_msg.text == '回复本消息输入需要查的客户ID或者内部名!!':
            if re.match('^[0-9]{4}$', message.text, re.S) is not None:
                await show_guest([message.text], message.chat.id, 'short_id')
            elif re.match('^[0-9]{4}.$', message.text, re.S) is not None:
                await show_guest([message.text], message.chat.id, 'std_id')
            else:
                await show_guest([message.text], message.chat.id, 'outname')
            await message.delete()
            await re_msg.delete()
        # # sa 女孩中心
        # elif re_msg.text == '回复本消息输入女孩中心查询内容!!':
        #     await show_girlc(message.text)
        #     await message.delete()
        #     await re_msg.delete()
        # 女孩中心备注
        elif re.match('^回复本消息输入女孩中心备注!!$', re_msg.text, re.S) is not None:
            match = re.match('.*' + e_star + ' ID: (.*?)\n' + e_man + '.*', pre_re_msg.text, re.S)
            id = int(match.group(1))
            girl_id = get_girlc(id)[3]
            if girl_id > 0:
                sql_str = "update zoo_info set note='{0:s}' where id={1:d}".format(message.text, girl_id)
                tg_connector.exe(sql_str)
                sql_str = "update zoo_girlc set note='' where user_id={0:d}".format(id)
                tg_connector.exe(sql_str)
            else:
                sql_str = "update zoo_girlc set note='{0:s}' where user_id={1:d}".format(message.text, id)
                tg_connector.exe(sql_str)
            get_girlc(id, True)
            content = get_content_girlc(id)
            prompt = line + e_remind + "已修改备注完成\n"
            await pre_re_msg.edit_text(content + prompt, parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_com_imp('girlcadmin'))
            await message.delete()
            await re_msg.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 主界面 成员
@Client.on_message(filters.regex('^/start .*') & ~filters.user(sa))
async def welcome_member(client, message):
    try:
        id = message.text.split(' ')[1]
        link = {
            '-1001941346041': 'https://t.me/+RJfoplqmKOtmMjI9',
            '-1001824315964': 'https://t.me/+l53jvyd5BnE0YmNl',
            '-1001924419067': 'https://t.me/+aVCB6uA6Tjk2MzBl'
            }
        content = e_jin18 + "欢迎老铁！<b>点击进入</b>精彩视频频道\nWelcome bro! <b>Click to enter</b> Exciting video " \
                            "channel\n 👉<a href='{0:s}'>欢乐园福利</a>👈\n\n".format(link[id]) + \
                  e_light + "温馨提醒：<u>申请加入</u>后<b>再次点击链接</b>即可进入频道\nWarm reminder: <u> apply to join </u> " \
                            "and then <b> click the link </b> again to enter the channel"
        await message.reply_text(
            content,
            parse_mode=ParseMode.HTML,
            disable_web_page_preview=True,
            protect_content=True
            )
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 主界面
@Client.on_message(~filters.bot & filters.text & ~filters.reply & ~filters.forwarded & ~filters.regex('^/') & filters.user(sa))
async def welcome(client, message):
    try:
        await client.send_message(message.chat.id,
                                  e_flower+" Welcome to our HappyZone!! "+e_flower+'\n',
                                  parse_mode=ParseMode.HTML,
                                  reply_markup=get_imp('sa_home'))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 查询chat.name
@Client.on_message(filters.forwarded & filters.chat(sa))
async def find_name(client, message):
    try:
        chat_id = message.chat.id
        if message.forward_from is not None:
            from_user_id = message.forward_from.id
            try:
                user_tmp = await clients['happy_167'].get_users(from_user_id)
                await update_chat(clients['happy_167'], user_tmp)
            except Exception as e:
                sys_log.write_log("happy_167 无法获取到 user_id={0:d} 的信息".format(from_user_id), 'a')
            outname = f_chats_id(from_user_id)[3]
            # 检查用户是否girlc的成员
            sql_str = "select count(1) from zoo_guest where chat_id={0:d} and status_chat='3'".format(from_user_id)
            rst = tg_connector.query(sql_str)
            if re.match('^\D\S* \d-\d+(?: .*|$)', outname, re.S) is not None or \
                    re.match('^.*(?:待上架|待聊|不上架).*', outname, re.S) is not None or \
                    rst[0][0] > 0:
                await show_girlc(str(from_user_id))
            else:
                await show_guest([from_user_id], chat_id, 'chat_id')
        else:
            await client.send_message(chat_id, "无法获取到客户的相关信息: {0:s}\n请在客户会话中通过 /guest 或 /girlc 查询"
                                      .format(message.forward_sender_name))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 在“评论”群抓取从关联频道forward过来的信息
@Client.on_message(filters.linked_channel & filters.chat(gm_pinglun.chat_id))
async def update_link_msg(client, message):
    try:
        rst_check = await check_media_group(client, message)
        if rst_check[0] == '2':
            return
        # 根据关联频道同步群消息
        for row in cm_pinglun.get_from_msg(',' + str(message.forward_from_message_id) + ','):
            gm_pinglun.add_row(row[3], rst_check[1], row[2])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

