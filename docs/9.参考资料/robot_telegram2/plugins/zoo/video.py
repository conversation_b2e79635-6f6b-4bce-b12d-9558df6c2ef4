# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common import *
from classes2 import *
import traceback
import asyncio
import datetime
import random
from pyrogram import Client, filters
from pyrogram.enums import ParseMode


# 参数设定
params['adv_num_video'] = 8
params['adv_count_video'] = random.randint(0, params['adv_num_video']-1)


def get_imp():
    try:
        return InlineKeyboardMarkup([
            [InlineKeyboardButton(e_he + "全部贵妃", url="t.me/happyzone168"),
             InlineKeyboardButton(e_bot + "机器人", url="t.me/happyzonebot"),
             InlineKeyboardButton(e_han + "开始约妃", url="t.me/happy_167")]
        ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# bot接收并发布视频
@Client.on_message(filters.video & filters.chat('daqiang2014') & filters.forwarded)
async def rec_forward_video(client, message):
    try:
        group_str = ''
        for i in range(3):
            group_chat_id = group_chat_list[params['group_chat_idx_video']]
            adv_str = adv(params['adv_count_video'])
            rmp = get_imp()
            try:
                await client.send_video(group_chat_list[params['group_chat_idx_video']],
                                        message.video.file_id,
                                        caption=adv_str,
                                        parse_mode=ParseMode.HTML,
                                        reply_markup=rmp)
            except:
                sys_log.write_log(traceback.format_exc(), 'a')
            group_str += " " + f_chats_id(group_chat_id)[3]
            params['adv_count_video'] = (params['adv_count_video'] + 1) % params['adv_num_video']
            params['group_chat_idx_video'] = (params['group_chat_idx_video'] + 1) % len(group_chat_list)
            await asyncio.sleep(20)
        sys_log.write_log("从 {0[3]:s} 收到的视频转发给:{1:s}".format(f_chats_id(message.chat.id), group_str), 'a')
        await message.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 转发视频给bot
@Client.on_message(filters.video & filters.chat(params['video_source']) & filters.bot & filters.inline_keyboard, group=-3)
async def forward_video(client, message):
    try:
        sys_log.write_log("从 {0[3]:s} 获取视频转发给 {1[3]:s}"
                          .format(f_chats_id(message.from_user.id), f_chats_id(params['bot_yuanzhang'])), 'a')
        await client.forward_messages("happyzoneadvbot", message.chat.id, message.id, True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 加群审核
@Client.on_chat_join_request(filters.chat(group_chat_list))
async def join_video(client, request):
    try:
        user_id = request.from_user.id
        chat_id = request.chat.id
        sql_str = "select orders from zoo_guest where chat_id={0:d}".format(user_id)
        rst = tg_connector.query(sql_str)
        if len(rst) > 0 and rst[0][0] > 0:
            await request.approve()
            sql_str = "insert into zoo_guest_video values({0:d}, {1:d}) on duplicate key update user_id=user_id"\
                .format(user_id, chat_id)
            tg_connector.exe(sql_str)
        else:
            await request.decline()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 启动拉人进群
@Client.on_message(filters.regex('test'))
async def add_member(client, message):
    try:
        print("启动test测试")
        peer = await client.resolve_peer(peer_id="happy_167")
        print(peer)
        # print(await supplier.add_member())
        # await clients['lashou01'].add_chat_members(-1001526949053, 1976413572)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')




