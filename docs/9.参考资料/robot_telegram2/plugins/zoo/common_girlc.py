# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common import *

# 变量设置
# 女孩控制字典
girlc = {}
inter_group = [-1001769286050, -1001728796561, -1001698367975, -1001671326152, -1001698806812]
inter_channel = [-1001695898408, -1001529721237, -1001760202075, -1001569279923, -1001497351729, -1001776354848]

# 获取girlc
# force:强制更新
def get_girlc(user_id, force=False):
    try:
        if len(girlc) > 2000:
            sys_log.write_log('重新初始化girlc', 'a')
            girlc.clear()
        if user_id not in girlc.keys() or force:
            sql_str = "select user_id,username,outname,girl_id,girl_name,group_id,status,note" \
                      " from zoo_girlc where user_id={0:d}".format(user_id)
            rst = tg_connector.query(sql_str)
            if len(rst) != 0:
                girlc.update({rst[0][0]: rst[0]})
        return girlc.get(user_id, [])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 确认身份
# reget_name: 是否要重新获取最新名字
# [结果字符串, first_name]
# 结果字符串
# 0:未知
# 1:客户-无业绩 2:客户-有效
# 3:动物-已上架 4:动物-待上架 5:动物-待聊 6:动物-不上架并忽略 7:动物-禁止使用
async def identity(user_id, reget_name=True):
    try:
        ind = '0'
        if reget_name:
            try:
                user_tmp = await clients['happy_167'].get_users(user_id)
                await update_chat(clients['happy_167'], user_tmp)
            except Exception as e:
                pass
                # sys_log.write_log("identity中【happy_167】无法获取到 user_id={0:d} 的信息".format(user_id), 'a')
        user = f_chats_id(user_id)
        # 0:未知
        if len(user) == 0 or user[3] == '':
            return [ind, '']
        else:
            name = user[3]
        # 1:客户-无业绩 2:客户-有效
        if re.match('^\d{4}(?:\D.*|)-.*', name, re.S) is not None:
            sql_str = "select count(1) from zoo_guest where chat_id={0:d} and status_chat='1'".format(user_id)
            rst = tg_connector.query(sql_str)
            if rst[0][0] == 1:
                ind = '2'
            else:
                ind = '1'
        # 3:动物-已上架 4:动物-待上架 5:动物-待聊 6:动物-不上架并忽略 7:动物-禁止使用
        else:
            if re.match('^\D\S* \d-\d{2,4}(?: .*|$)', name, re.S) is not None:
                ind = '3'
            elif re.match('^.*待上架.*', name, re.S) is not None:
                ind = '4'
            elif re.match('^.*待聊.*', name, re.S) is not None:
                ind = '5'
            elif re.match('^.*不上架.*', name, re.S) is not None:
                ind = '6'
            sql_str = "select count(1) from zoo_girlc where user_id={0:d} and status like '7%'".format(user_id)
            rst = tg_connector.query(sql_str)
            if ind != '0' and rst[0][0] == 1:
                ind = '7'
        return ind, name
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新女孩活跃时间
async def active_girlc(user_id, use_bot=True):
    try:
        # 确定客户聊天状态
        ident = await identity(user_id)
        if ident == '0':
            status_chat = '0'
        elif ident == '1':
            status_chat = '2'
        elif ident == '2':
            status_chat = '1'
        else:
            status_chat = '3'
        # 更新客户表
        sql_str = "select count(1) from zoo_guest where chat_id={0:d}".format(user_id)
        rst = tg_connector.query(sql_str)
        if rst[0][0] == 0:
            sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_chat,remind_at,active_at," \
                      "created_at) values({0[0]:d},'{1:s}','{0[2]:s}','{0[5]:s}','{0[3]:s}','{2:s}'," \
                      "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP())"\
                .format(f_chats_id(user_id), user_to_std(user_id), status_chat)
            tg_connector.exe(sql_str)
        else:
            sql_str = "update zoo_guest set name='{0[2]:s}',username='{0[5]:s}',outname='{0[3]:s}'," \
                      "status_chat='{1:s}',active_at=CURRENT_TIMESTAMP() where chat_id={0[0]:d}"\
                .format(f_chats_id(user_id), status_chat)
            tg_connector.exe(sql_str)
        # sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_chat,remind_at,active_at,created_at) " \
        #           "values({0[0]:d},(select * from (select concat('{1:s}',char(65+(select count(1) from zoo_guest where " \
        #           "std_id like '{1:s}%')))) as k),'{0[2]:s}','{0[5]:s}','{0[3]:s}','{2:s}',CURRENT_TIMESTAMP()," \
        #           "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP()) on duplicate key update name='{0[2]:s}',username='{0[5]:s}'," \
        #           "outname='{0[3]:s}',status_chat='{2:s}',active_at=CURRENT_TIMESTAMP()" \
        #     .format(f_chats_id(user_id), str(user_id)[-4:], status_chat)
        # tg_connector.exe(sql_str)
        # 更新 zoo_girlc 中机器人对话情况
        if ck_girlc_stauts(user_id, '*0') and use_bot:
            set_girlc_stauts(user_id, '*1')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 设置girlc状态
def set_girlc_stauts(user_id, status="**"):
    try:
        girl = get_girlc(user_id)
        if len(girl) == 0:
            return ''
        new_status = ''
        old_status = girl[6]
        for i in range(0, min(len(old_status), len(status))):
            if status[i] == '*':
                new_status = new_status + old_status[i]
            else:
                new_status = new_status + status[i]
        sql_str = "update zoo_girlc set status='{1:s}' where user_id={0:d}".format(user_id, new_status)
        tg_connector.exe(sql_str)
        get_girlc(user_id, True)
        return new_status
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 检查girlc状态
# status_list str|list 单个状态检查 或者 多个状态关系 “或”
def ck_girlc_stauts(user_id, status_list="**"):
    try:
        if isinstance(status_list, str):
            status_list = [status_list]
        girl = get_girlc(user_id)
        if len(girl) == 0:
            print_both("user_id不存在，检查girlc状态无效：{0:d}-{1:s}".format(user_id, status_list[0]))
            return False
        status = girl[6]
        for ck_status in status_list:
            result = True
            for i in range(0, min(len(status), len(ck_status))):
                if ck_status[i] != '*' and ck_status[i] != status[i]:
                    result = False
                    break
            if result:
                break
        return result
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 解释girlc名字
async def explain_girlc(user_id, reget_name=True):
    try:
        if reget_name:
            try:
                user_tmp = await clients['happy_167'].get_users(user_id)
                await update_chat(clients['happy_167'], user_tmp)
            except Exception as e:
                pass
                # sys_log.write_log("explain_girlc中【happy_167】无法获取到 user_id={0:d} 的信息".format(user_id), 'a')
        user = f_chats_id(user_id)
        if len(user) == 0:
            return []
        username = user[5]
        outname = user[3]
        match = re.match('^(\D\S*) (\d)-(\d{2,4})(?: .*|$)', outname, re.S)
        if match is not None:
            girl_id = match.group(3)
            girl_name = match.group(1)
            group_id = match.group(2)
        else:
            girl_id = '0'
            girl_name = ''
            group_id = '0'
        sql_str = "insert into zoo_girlc(user_id,username,outname,girl_id,girl_name,group_id) values({0:d},'{1:s}'," \
                  "'{2:s}',{3:s},'{4:s}',{5:s}) on duplicate key update username='{1:s}',outname='{2:s}',girl_id={3:s}," \
                  "girl_name='{4:s}',group_id={5:s}" \
            .format(user_id, username, outname, girl_id, girl_name, group_id)
        tg_connector.exe(sql_str)
        # 设定动物状态
        ident = await identity(user_id, False)
        set_girlc_stauts(user_id, ident[0] + '*')
        return get_girlc(user_id, True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取girlc内容
def get_content_girlc(user_id):
    try:
        sql_str = "select a.user_id,a.username,a.outname,a.girl_id,a.girl_name,a.group_id,a.status,a.note," \
                  "ifnull(b.phone,''),ifnull(b.price_out,''),ifnull(b.price_in,''),ifnull(b.note,'') from zoo_girlc a " \
                  "left join zoo_info b on a.girl_id = b.id where a.user_id={0:d}".format(user_id)
        rst = tg_connector.query(sql_str)
        if len(rst) == 0:
            return 'No content'
        else:
            girl = rst[0]
        if girl[1] != '':
            name_link = "tg://resolve?domain={0:s}".format(girl[1])
        else:
            name_link = "tg://user?id={0:d}".format(girl[0])
        sql_str = "select type,note,created_at from zoo_girlc_log where girl_id={0:d} order by created_at desc " \
                  "limit 1".format(girl[3])
        rst = tg_connector.query(sql_str)
        if len(rst) == 1:
            girlc_log = (mean('girlc_log_type', rst[0][0]) + rst[0][1], "{0:%Y-%m-%d %X}".format(rst[0][2]))
        else:
            girlc_log = ('未知', '2000-01-01 00:00:00')
        phones = ''
        for phone in girl[8].split('/'):
            phones += '<code>' + phone + '</code>/'
        return e_girlhat * 3 + '女孩中心' + e_girlhat * 3 + """
{4[0]:s} ID: {0[0]:d}
{4[1]:s} 名字: <a href="{2:s}">{0[2]:s}</a>
{4[2]:s} 关系: {3:s} - {5:s}
{4[3]:s} 女孩编号: {0[3]:d}
{4[4]:s}女孩名字: {0[4]:s}
{4[5]:s} 电话: {6:s}
{4[6]:s} 外部价格: <code>{0[9]:s}</code>
{4[6]:s} 内部价格: <code>{0[10]:s}</code>
{4[7]:s} 活跃状态: {1[0]:s}
{4[8]:s} 活跃时间: {1[1]:s}
{4[9]:s} 备注: {0[11]:s}{0[7]:s}
""".format(girl, girlc_log, name_link, mean('girls_status_1', girl[6][0]),
           (e_star, e_man, e_dna, e_prize, e_girl, e_phone, e_money, e_traffic, e_time2, e_myOffer),
           mean('girls_status_2', girl[6][1]), phones.strip('/'))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 展示girlc消息
async def show_girlc(user_txt):
    try:
        user_list = []
        # 判断输入类型，操作不同解决方案
        if re.match('^\d{2,4}$', user_txt, re.S) is not None:
            user_type = '女孩编号'
            sql_str = "select user_id from zoo_girlc where girl_id= '{0:s}'".format(user_txt)
            rst = tg_connector.query(sql_str)
            if len(rst) > 0:
                user_list.append(rst[0][0])
        elif re.match('^0?9\d{9}$', user_txt, re.S) is not None:
            user_type = '电话号码'
            sql_str = "select user_id from zoo_girlc where outname like '%{0:s}%'".format(user_txt.lstrip('0'))
            rst = tg_connector.query(sql_str)
            for row in rst:
                user_list.append(row[0])
        elif re.match('^\d{6,10}$', user_txt, re.S) is not None:
            user_type = 'ID'
            user_list.append(int(user_txt))
        elif re.match('^@\w+', user_txt, re.S) is not None:
            user_type = '用户名'
            sql_str = "select user_id from zoo_girlc where username='{0:s}'".format(user_txt.lstrip('@'))
            rst = tg_connector.query(sql_str)
            if len(rst) > 0:
                user_list.append(rst[0][0])
            else:
                try:
                    peer = await clients['happy_167'].resolve_peer(peer_id=user_txt.lstrip('@'))
                    user_tmp = await clients['happy_167'].get_users(peer.user_id)
                    await update_chat(clients['happy_167'], user_tmp)
                    user_list.append(peer.user_id)
                except Exception as e:
                    sys_log.write_log("通过username取id失败:{0:s}".format(user_txt), 'a')
        else:
            user_type = '女孩名字'
            sql_str = "select user_id from zoo_girlc where girl_name='{0:s}'".format(user_txt)
            rst = tg_connector.query(sql_str)
            for row in rst:
                user_list.append(row[0])
        if len(user_list) == 0:
            content = "没有相关女孩的信息： {0:s}-{1:s}".format(user_type, user_txt)
            await clients['myhappygirlsbot'].send_message(params['user_hugo'], content, parse_mode=ParseMode.HTML)
        else:
            for user_id in user_list:
                rst = await explain_girlc(user_id)
                if len(rst) == 0:
                    continue
                content = get_content_girlc(user_id)
                prompt = e_remind + "请输入你需要的操作\n"
                await clients['myhappygirlsbot'].send_message(params['user_hugo'],
                                                              content + line + prompt,
                                                              parse_mode=ParseMode.HTML,
                                                              disable_web_page_preview=True,
                                                              reply_markup=get_com_imp('girlcadmin'))
                if len(user_list) > 1:
                    await asyncio.sleep(2)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 记录girlc操作的日志
# type 默认'0'：未知；1：自报空闲；2：自报忙；3：手报空闲；4：手报忙；5：接受；6：拒绝；7：活跃成员
# note 备注信息
async def log_girlc(type:str, user_id=0, girl_id=0,  note=''):
    try:
        if user_id != 0:
            girl = await explain_girlc(user_id)
            if len(girl) >0:
                girl_id = girl[3]
            else:
                girl_id = 0
        sql_str = "insert into zoo_girlc_log(type,user_id,girl_id,note,created_at) values({0:s}, {1:d}, {2:d}, " \
                  "'{3:s}', CURRENT_TIMESTAMP())".format(type, user_id, girl_id, note)
        tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 记录展示日志
def log_show(user_id:int, girl_id:int, note:str=''):
    try:
        sql_str = "insert into zoo_show_log(guest_user_id,girl_id,note,created_at) " \
                  "values({0:d}, {1:d}, '{2:s}', CURRENT_TIMESTAMP())".format(user_id, girl_id, note)
        tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')




