# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.automate.common import *

codes = {}

# 登录后台-第1步
@Client.on_message(~filters.bot & filters.incoming & filters.text & filters.private & filters.regex('^/login_db #N\d{1,3}_\d+', re.I))
async def login_db_1(client, message):
    try:
        print("接受到命令：{0:s}".format(message.text))
        name = message.text.split('N')[1]
        print(name)
        myclients[name] = RegClient()
        await myclients[name].login_client_db_1(name)
        # rst = await myclients[name].login_client_db_1(name)
        # await message.reply_text("#N{0:s}|{1:s}".format(name, rst))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 登录后台-第2步
@Client.on_message(~filters.bot & filters.incoming & filters.text & filters.private & filters.regex('^[A-Z]{5}$', re.I))
async def login_db_2(client, message):
    try:
        last_msg = None
        last_msg_id = message.id - 1
        for i in range(10):
            msg = await client.get_messages(message.chat.id, last_msg_id-i)
            if msg.text is not None and re.match('^/login_db #N\d{1,3}_\d+$', msg.text, re.S) is not None:
                last_msg = msg
                break
            await asyncio.sleep(0.5)
        if last_msg is None:
            await message.reply_text("没有需要接收验证码")
            return
        match = re.match('^/login_db #N(\d{1,3}_\d+)$', last_msg.text, re.S)
        name = match.group(1)
        print(name)
        if name not in myclients.keys():
            await message.reply_text("#N{0:s}|上一次验证码无效，需重新申请".format(name))
            return
        print(message.text)
        decode = ''
        for c in message.text:
            decode = decode + explain('JIAMI', c, 'from')
        print(decode)
        rst = await myclients[name].login_client_db_2(decode)
        await message.reply_text("#N{0:s}|{1:s}".format(name, rst))
        await asyncio.sleep(5)
        await myclients[name].stop()
        del myclients[name]
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

# 测试
@Client.on_message(group=-3)
async def test(client, message):
    try:
        pass
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

