# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.skc.common import *


# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        if index == 'news_opt':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_gou + "发布", callback_data='news_opt_1'),
                 InlineKeyboardButton(e_myOffer + "初稿", callback_data='news_opt_3'),
                 InlineKeyboardButton(e_cha + "不发布", callback_data='news_opt_2')],
                [InlineKeyboardButton(e_warning + "待处理", callback_data='news_opt_0'),
                 InlineKeyboardButton(e_return + "返回", callback_data='news_opt_r')]
            ])
        elif index == 'news_return':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='news_return')]
            ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给图片打水印
async def pic_mark(client, msg):
    try:
        row_media_online = await my_media.save(client, msg, 'both')
        row_media = await my_media.get(row_media_online[0])
        adds = 'media/' + row_media[1] + '/' + row_media[3] + '.' + row_media[4]
        add_pic_mark(adds,
                     'media/' + row_media[1] + '/', "Tg: skcc_news",
                     color="#FF7256",
                     size=18,
                     opacity=0.6,
                     angle=28,
                     space=int(msg.photo.width / 3))
        # 重新发送媒体到同步频道
        try:
            return await my_media.update_resend(row_media[0], 'skckefu')
        except FloodWait as e:
            await asyncio.sleep(e.value)
            return await my_media.update_resend(row_media[0], 'skckefu')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面
@Client.on_callback_query(filters.user(params['skckefu']))
async def callback_interface(client, callback):
    try:
        # news opt[0|1|2]
        if re.match('^news_[0|1|2|3]', callback.data, re.S) is not None:
            content = callback.message.text.html.split(line)[0]
            opt = callback.data.split('_')[1]
            prompt = line + e_remind + "目前<b>{0:s}</b>，请选择你需要的操作".format(mean('task_opt', opt))
            await callback.edit_message_text(content+prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('news_opt'))
        # news opt_[0|1|2|r]
        elif re.match('^news_opt_[0|1|2|3|r]', callback.data, re.S) is not None:
            content = callback.message.text.html.split(line)[0]
            opt = callback.data.split('_')[2]
            if opt in ('1', '3'):
                # 提取消息编号
                match = re.match(r'.*编号：(.*)\n新闻来源：.*', callback.message.text, flags=re.S)
                if ',' in match.group(1):
                    ids = list(map(int, match.group(1).split(',')))
                else:
                    ids = [int(match.group(1))]
                # 提取消息
                news_msg = await clients['skckefu'].get_messages(params['g_daiban'], ids)
                # 添加后续内容
                content_adv = "\n\n{0:s}<a href='t.me/happyzone168'>【<b>欢乐园后宫</b>】</a>菲最多最专业修车店\n".format(e_sound)
                content_adv += "{0:s}<a href='t.me/billywin'>【<b>移民局业务</b>】</a>直接对接官员处理\n".format(e_sound)
                content_adv += "{0:s}新闻来源：@skcc_news\n{1:s}<b>投稿爆料</b>：@skckefu\n".format(e_outbox, e_phone2)
                # 展示目标chat 和 提醒
                obj_chat, prompt_tmp = (params['c_news'], "新闻已发布") if opt == '1' else (params['g_daiban'], "新闻初稿")
                prompt = line + e_remind + prompt_tmp
                # 处理带有text的消息
                if len(news_msg) == 1 and news_msg[0].text is not None:
                    await clients['skckefu'].send_message(obj_chat,
                                                          text=news_msg[0].text.html + content_adv,
                                                          parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          disable_notification=False)
                # 处理单个图片的消息
                elif len(news_msg) == 1 and news_msg[0].photo is not None:
                    resend_media = await pic_mark(clients['skckefu'], news_msg[0])
                    await clients['skckefu'].send_photo(
                        obj_chat,
                        photo=resend_media[5],
                        caption=news_msg[0].caption.html + content_adv if news_msg[0].caption is not None else None,
                        parse_mode=ParseMode.HTML,
                        disable_notification=False
                    )
                # 处理多个媒体的消息
                elif len(news_msg) > 1 and news_msg[0].media_group_id is not None:
                    media_list = []
                    for msg in news_msg:
                        if msg.photo is not None:
                            resend_media = await pic_mark(clients['skckefu'], msg)
                            media_list.append(InputMediaPhoto(
                                resend_media[5],
                                caption=msg.caption.html + content_adv if msg.caption is not None else None,
                                parse_mode=ParseMode.HTML
                            ))
                        elif msg.video is not None:
                            media_list.append(InputMediaVideo(
                                media=msg.video.file_id,
                                thumb=msg.video.thumbs[0] if len(msg.video.thumbs)>0 else None,
                                caption=msg.caption.html + content_adv if msg.caption is not None else None,
                                parse_mode=ParseMode.HTML,
                                width=msg.video.width,
                                height=msg.video.height,
                                duration=msg.video.duration,
                                supports_streaming=msg.video.supports_streaming
                            ))
                        elif msg.audio is not None:
                            media_list.append(InputMediaAudio(
                                media=msg.audio.file_id,
                                thumb=msg.audio.thumbs[0] if len(msg.audio.thumbs) > 0 else None,
                                caption=msg.caption.html + content_adv if msg.caption is not None else None,
                                parse_mode=ParseMode.HTML,
                                duration=msg.audio.duration,
                                performer=msg.audio.performer,
                                title=msg.audio.title
                            ))
                        elif msg.document is not None:
                            media_list.append(InputMediaDocument(
                                media=msg.document.file_id,
                                thumb=msg.document.thumbs[0] if len(msg.document.thumbs) > 0 else None,
                                caption=msg.caption.html + content_adv if msg.caption is not None else None,
                                parse_mode=ParseMode.HTML
                            ))
                    # 发送多媒体消息
                    await clients['skckefu'].send_media_group(obj_chat,
                                                              media=media_list,
                                                              disable_notification=False)
                # 处理其它消息
                else:
                    await news_msg[0].copy(
                        obj_chat,
                        caption=news_msg[0].caption.html + content_adv if news_msg[0].caption is not None else None,
                        parse_mode=ParseMode.HTML,
                        disable_notification=False
                    )
            elif opt == '2':
                prompt = line + e_remind + "新闻不发布"
            elif opt == '0':
                prompt = line + e_remind + "新闻<b>待发布</b>"
            elif opt == 'r':
                match = re.match(r'.*目前(.*)，请选择你需要的操作', callback.message.text.split(line)[1], flags=re.S)
                prompt = line + e_remind + "新闻" + match.group(1)
                opt = mean('task_opt', match.group(1), 'from')
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_com_imp('news', opt))
        # news_noted
        elif callback.data == 'news_noted':
            re_msg = await callback.edit_message_text(callback.message.text.html, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('news_return'))
            await re_msg.reply_text('回复本消息输入新闻备注信息', quote=True, reply_markup=ForceReply())
        # news_return
        elif callback.data == 'news_return':
            match = re.match(r'.*新闻(.*)', callback.message.text.split(line)[1], flags=re.S)
            opt = mean('task_opt', match.group(1), 'from')
            await callback.edit_message_text(callback.message.text.html, parse_mode=ParseMode.HTML,
                                             reply_markup=get_com_imp('news', opt))

    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复-页面
@Client.on_message(filters.reply & ~filters.command('news', '') & filters.user(params['skckefu']), group=-2)
async def reply_interface(client, message):
    try:
        re_msg = message.reply_to_message
        pre_re_msg = await client.get_messages(message.chat.id, reply_to_message_ids=re_msg.id, replies=1)
        # news 备注
        if re_msg.text == '回复本消息输入新闻备注信息':
            content = pre_re_msg.text.html
            match = re.match(r".*\n(备注：.*)\n\-+.*新闻(.*)", pre_re_msg.text, re.S)
            sour_str = match.group(1)
            content = re.sub(sour_str, '备注：' + message.text, content, 1)
            opt = mean('task_opt', match.group(2), 'from')
            await pre_re_msg.edit_text(content, parse_mode=ParseMode.HTML, reply_markup=get_com_imp('news', opt))
            await message.delete()
            await re_msg.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')





