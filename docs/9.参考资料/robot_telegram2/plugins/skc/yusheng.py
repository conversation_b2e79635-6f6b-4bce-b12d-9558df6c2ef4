# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.skc.common import *


def get_content(msg):
    try:
        if msg.text is not None:
            return msg.text.html
        elif msg.caption is not None:
            return msg.caption.html
        else:
            return ''
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 判断目标消息 True:是 False:否
def ojb_message(msg_list):
    try:
        chats = f_chats_id(msg_list[0].chat.id)
        for msg in msg_list:
            content = get_content(msg)
            # print(content)
            if content == '':
                continue
            if chats[2] == '菲龙网新闻频道':
                if "投稿爆料请联系" in content:
                    return True
            elif chats[2] == '菲律宾大事件':
                if "➖" in content:
                    return True
            elif chats[2] == '博讯华人日记':
                if "投稿有奖" in content:
                    return True
            else:
                return True
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# yusheng把新闻源发到“明细”群
# 测试主频道： -1001662014767
@Client.on_message(filters.chat([params['c_boxun'], params['c_remen'], params['c_feilong'], params['c_anwei'], params['c_dajianshi'], -1001662014767]), group=-2)
async def news_to_mingxi(client, message):
    try:
        # print("有新消息")
        # 目标群“明细”
        rst_check = await check_media_group(client, message)
        if rst_check[0] == '2':
            return
        elif rst_check[0] == '0':
            if not ojb_message(rst_check[1]):
                return
            sent_msg = await client.forward_messages(params['g_mingxi'], message.chat.id, message.id, disable_notification=True)
        elif rst_check[0] == '1':
            if not ojb_message(rst_check[1]):
                return
            medias = []
            for msg in rst_check[1]:
                if msg.media == MessageMediaType.PHOTO:
                    medias.append(InputMediaPhoto(
                        msg.photo.file_id, caption=msg.caption.html if msg.caption is not None else None))
                elif msg.media == MessageMediaType.VIDEO:
                    medias.append(InputMediaVideo(
                        msg.video.file_id, caption=msg.caption.html if msg.caption is not None else None))
                elif msg.media == MessageMediaType.AUDIO:
                    medias.append(InputMediaAudio(
                        msg.audio.file_id, caption=msg.caption.html if msg.caption is not None else None))
                elif msg.media == MessageMediaType.DOCUMENT:
                    medias.append(InputMediaDocument(
                        msg.document.file_id, caption=msg.caption.html if msg.caption is not None else None))
            sent_msg = await client.send_media_group(params['g_mingxi'], media=medias, disable_notification=True)
            sent_msg = sent_msg[0]
        sys_log.write_log("余笙 从 {0[3]:s} 获取新闻转发给 {1[3]:s}"
                          .format(f_chats_id(message.sender_chat.id), f_chats_id(params['g_mingxi'])), 'a')
        await asyncio.sleep(1)
        await sent_msg.reply_text("news " + f_chats_id(message.chat.id)[2])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')





