# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from classes2 import *
from datetime import datetime
from pyrogram.types import ReplyKeyboardMarkup, ReplyKeyboardRemove, ForceReply, InputMediaPhoto, InputMediaVideo, InputMediaDocument


# 参数池
params = {
    'c_test': -1001662014767,
    'skckefu': 5107788198,
    'g_mingxi': -1001753095456,
    'g_daiban': -1001690578564,
    'c_boxun': -1001954248273,
    'c_remen':-1001138540789,
    'c_feilong': -1001746975734,
    'c_anwei': -1001222514820,
    'c_dajianshi': -1001624582361,
    'c_news': -1001618262897,
    'c_mychannel': -1001943947547
}
my_media = Media(params['c_mychannel'], 'skc_in', ['skc_in'])


# 字段转义
def mean(type, text='', point='to'):
    to_dic = {}
    # 设定转义内容
    if type == 'task_opt':
        to_dic = {'0': '待发布', '1': '已发布', '2': '不发布', '3': '初稿'}
    elif type == 'task_opt_e':
        to_dic = {'0': e_warning, '1': e_gou, '2': e_cha, '3': e_myOffer}
    # 确定转义方向
    if point == 'to':
        return to_dic.get(text, None)
    elif point == 'from':
        from_dict = {v: k for k, v in to_dic.items()}
        return from_dict.get(text, None)
    else:
        return None


# 内联按钮界面
def get_com_imp(index='-', param=None):
    try:
        if index == 'news':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(mean('task_opt_e', param) + mean('task_opt', param), callback_data='news_'+param),
                 InlineKeyboardButton(e_myOffer + "备注", callback_data='news_noted')]
            ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

