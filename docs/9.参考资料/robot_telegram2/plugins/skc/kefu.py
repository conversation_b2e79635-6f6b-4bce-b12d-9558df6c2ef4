# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.skc.common import *


# 客服账号把明细群的新闻消息转发到代办群
@Client.on_message(filters.reply & filters.command('news', '') & filters.chat(params['g_mingxi']))
async def mingxi_to_daiban_news(client, message):
    try:
        if len(message.text.split(' ')) != 2:
            return
        chats = f_chats_name(message.text.split(' ')[1])
        sql_str = "select status from grab_msg where type='news' and sour_id={0[0]:d} and obj_id={1:d}"\
            .format(chats, params['c_news'])
        rst = tg_connector.query(sql_str)
        # 只处理[执行中]和[等待XX]的消息
        if len(rst) == 0 or rst[0][0][0] not in ('4', '5'):
            return
        # 获取所指向的消息
        sour_msg = await client.get_messages(message.chat.id, reply_to_message_ids=message.id, replies=1)
        print(sour_msg)
        # 确认消息性质
        rst_check = await check_media_group(client, sour_msg)
        # 获取消息内容
        new_caption = ''
        # 对于多媒体消息，搜遍所有的媒体caption
        if rst_check[0] != '0':
            if rst_check[0] == '1':
                rec_msg = rst_check[1]
            else:
                rec_msg = await client.get_media_group(sour_msg.chat.id, sour_msg.id)
            for msg in rec_msg:
                if msg.caption is not None:
                    new_caption = msg.caption.html
                    break
        # 对于有媒体消息
        elif sour_msg.caption is not None:
            new_caption = sour_msg.caption.html
        # 对于纯文本消息
        elif sour_msg.text is not None:
            new_caption = sour_msg.text.html
        print("\n\n处理前：\n")
        print(new_caption)
        # 调整内容
        if chats[2] == '博讯华人日记':
            new_caption = re.sub(r'【.*$', '', new_caption, flags=re.S)
            new_caption = re.sub(r'(?:<a.*?http.*?/a>|🔗)', '', new_caption, flags=re.S) + '</b>'
        elif chats[2] == '热门话题':
            new_caption = re.sub(r'本内容由热门话题报道.*$', '', new_caption, flags=re.S)
        elif chats[2] == '菲龙网新闻频道':
            new_caption = re.sub(r'📝.*$', '', new_caption, flags=re.S)
            new_caption = re.sub(r'菲龙网', '三棵葱', new_caption, flags=re.S)
        elif chats[2] == '菲律宾大事件':
            new_caption = re.sub(r'➖.*$', '', new_caption, flags=re.S)
            new_caption = re.sub(r'<a.*?jinbeitiyu.*$', '', new_caption, flags=re.S)
            new_caption = re.sub(r'<a.*?http.*?/a>', '', new_caption, flags=re.S)
        print("\n\n处理后：\n")
        print(new_caption)
        # 转发消息
        if sour_msg.text is not None:
            sent_msg = \
                await client.send_message(params['g_daiban'], text=new_caption, disable_web_page_preview=True,
                                          disable_notification=True)
        elif rst_check[0] == '0':
            sent_msg = \
                await client.copy_message(params['g_daiban'], sour_msg.chat.id, sour_msg.id,caption=new_caption,
                                          disable_notification=True, reply_to_message_id=None)
        elif rst_check[0] == '1':
            sent_msg = \
                await client.copy_media_group(params['g_daiban'], sour_msg.chat.id, sour_msg.id, captions=new_caption,
                                              disable_notification=True, reply_to_message_id=None)
        sys_log.write_log("skckefu 从 {0[3]:s} 获取新闻转发给 {1[3]:s}"
                          .format(f_chats_id(sour_msg.chat.id), f_chats_id(params['g_daiban'])), 'a')
        # 发送机器人信息
        if isinstance(sent_msg, list):
            id_list = []
            for msg in sent_msg:
                id_list.append(str(msg.id))
            msg_str = ','.join(id_list)
        else:
            msg_str = str(sent_msg.id)
        content = "编号：{1:s}\n新闻来源：<a href='https://t.me/c/{3:s}/{2:d}'>{0:s}</a>\n备注：暂无\n"\
            .format(chats[3], msg_str, sour_msg.id, str(params['g_mingxi'])[4:])
        prompt = line + e_remind + "新闻<b>待发布</b>"
        await clients['skcmanbot'].send_message(params['g_daiban'],
                                                content+prompt,
                                                parse_mode=ParseMode.HTML,
                                                disable_web_page_preview=True,
                                                disable_notification=False,
                                                reply_markup=get_com_imp('news', '0'))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 把工作待办群的消息，登记为新闻待办
@Client.on_message(filters.reply & filters.command('news', '') & filters.chat(params['g_daiban']))
async def daiban_news(client, message):
    try:
        # 获取所指向的消息
        re_msg = message.reply_to_message
        # 整理消息列表
        if re_msg.media_group_id is not None:
            media_msgs = await client.get_media_group(message.chat.id, re_msg.id)
            id_list = []
            for msg in media_msgs:
                id_list.append(str(msg.id))
            msg_str = ','.join(id_list)
        else:
            msg_str = str(re_msg.id)
        # 发送机器人信息
        content = "编号：{0:s}\n新闻来源：<a href='https://t.me/c/1690578564/{1:d}'>工作待办</a>\n备注：暂无\n"\
            .format(msg_str, re_msg.id)
        prompt = line + e_remind + "新闻<b>待发布</b>"
        await clients['skcmanbot'].send_message(params['g_daiban'],
                                                content + prompt,
                                                parse_mode=ParseMode.HTML,
                                                disable_web_page_preview=True,
                                                disable_notification=False,
                                                reply_markup=get_com_imp('news', '0'))
        # 删除本消息
        await message.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
