# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common import *

# # 恢复guest信息
# @Client.on_message(filters.incoming & filters.command("b") & filters.chat(-630680673))
# async def guest(client, message):
#     try:
#         await message.reply("在处理恢复guest的工作")
#         all_cnt = await client.get_chat_history_count(1999226611)
#         print(all_cnt)
#     except Exception as e:
#         sys_log.write_log(traceback.format_exc(), 'a')

