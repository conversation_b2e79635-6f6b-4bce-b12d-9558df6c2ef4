# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.zoo.common import *
import re

# 测试
@Client.on_message(filters.command("test") & filters.chat(-630680673))
async def contact(client, message):
    try:
        await message.reply("测试信息开始")
        await update_contact('happy_167')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')




# 把客户临时标号改成标准编号
@Client.on_message(filters.command("g3") & filters.chat(-630680673))
async def guest3(client, message):
    try:
        await message.reply("把客户临时标号改成标准编号3")
        # # 更新通讯录 （步骤1）
        # await update_contact('happy_167')
        # # 把带有临时标号的客户，统一整理入表
        # sql_str = "select * from contacts where outname like 'G00%'"
        # rst = tg_connector.query(sql_str)
        # for row in rst:
        #     sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_chat,remind_at,active_at," \
        #               "created_at) values({0[0]:d},(select * from (select concat('{1:s}',char(65+(select count(1) from " \
        #               "zoo_guest where std_id like '{1:s}%')))) as k),'{0[2]:s}','{0[5]:s}','{0[3]:s}','2'," \
        #               "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP()) on duplicate key update " \
        #               "name='{0[2]:s}',username='{0[5]:s}',outname='{0[3]:s}',status_chat=if(status_chat in ('0','2'), " \
        #               "'2', status_chat)" \
        #         .format(f_chats_id(row[1]), str(row[1])[-4:])
        #     tg_connector.exe(sql_str)

        # # 改临时编号 （步骤2）
        # sql_str = "select * from zoo_guest where outname like 'G00%'"
        # rst = tg_connector.query(sql_str)
        # for row in rst:
        #     chat_id = row[0]
        #     old_name = row[4]
        #     new_name = row[1] + row[4][5:]
        #     # 改通讯录名字
        #     await client.add_contact(chat_id, first_name=new_name, last_name='')
        #     print(chat_id)
        #     print(old_name)
        #     print(new_name)

        # 把剩余的通讯录成员，统一整理入表 （步骤3）
        await update_contact('happy_167')
        sql_str = "select * from contacts "
        rst = tg_connector.query(sql_str)
        for row in rst:
            sql_str = "insert into zoo_guest(chat_id,std_id,name,username,outname,status_chat,remind_at,active_at," \
                      "created_at) values({0[0]:d},(select * from (select concat('{1:s}',char(65+(select count(1) from " \
                      "zoo_guest where std_id like '{1:s}%')))) as k),'{0[2]:s}','{0[5]:s}','{0[3]:s}','2'," \
                      "CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP()) on duplicate key update " \
                      "name='{0[2]:s}',username='{0[5]:s}',outname='{0[3]:s}',status_chat=if(status_chat in ('0','2'), " \
                      "'2', status_chat)" \
                .format(f_chats_id(row[1]), str(row[1])[-4:])
            tg_connector.exe(sql_str)
        # 对历史4位数进行调整
        sql_str = "select * from zoo_guest"
        rst = tg_connector.query(sql_str)
        for row in rst:
            if re.match('^'+row[1][:4], row[4], re.S) is not None and re.match('^'+row[1], row[4], re.S) is None:
                print((row[1], row[4]))
                new_name = row[1] + row[4][4:]
                # print(new_name)
                try:
                    await client.add_contact(row[0], first_name=new_name, last_name='')
                    await asyncio.sleep(7)
                except Exception as e:
                    sys_log.write_log(traceback.format_exc(), 'a')
                    await asyncio.sleep(7)
                    continue

    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给客户资料设定标准编号
@Client.on_message(filters.command("g2") & filters.chat(-630680673))
async def guest2(client, message):
    try:
        await message.reply("给客户资料设定标准编号")
        sql_str = "select chat_id,right(chat_id,4) from zoo_guest where std_id is null"
        rst = tg_connector.query(sql_str)
        for row in rst:
            sql_str = "update zoo_guest set std_id=(select * from (select concat('{0[1]:s}',char(65+(select count(1) " \
                      "from zoo_guest where std_id like '{0[1]:s}%')))) as k) where chat_id={0[0]:d}"\
                .format(row)
            tg_connector.exe(sql_str)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从机器人中爬取客户资料入库
@Client.on_message(filters.command("g") & filters.chat(-630680673))
async def guest(client, message):
    try:
        await message.reply("从机器人中爬取客户资料入库")
        # all_cnt = await client.get_chat_history_count('myhappygirlsbot')
        # print(all_cnt)
        # 146621
        async for msg in client.get_chat_history('myhappygirlsbot', limit=200000, offset_id=74970):
            last_id = msg.id
            last_date = msg.date
            # print((msg.id, str(msg.date)))
            if re.match(e_manhat * 3 + '欢乐园客户' + e_manhat * 3 , str(msg.text), re.S) is not None:
                # print(msg.text)
                match = re.match(
                    '.*欢乐园客户.* (外部名: .*)\n.* (地址: .*)\n.* (电话: .*)\n.* (下单: .*)\n.* (关系: .*)\n.* (机器人: .*)'
                    '\n.* (提醒时间: .*)\n.* (活跃时间: .*)\n.* (加入时间: .*)\n.* (备注: .*)\n'+line+'.*', str(msg.text), re.S)
                if match is None:
                    print("分析字段失败，具体如下：")
                    print((msg.id, str(msg.date)))
                    print(msg.text)
                    break
                # 分析各字段信息
                outname = match.group(1).split(': ')[1]
                addr = match.group(2).split(': ')[1]
                phone = match.group(3).split(': ')[1]
                orders = match.group(4).split(': ')[1]
                status_chat = match.group(5).split(': ')[1]
                status_bot = match.group(6).split(': ')[1]
                remind_at = match.group(7).split(': ')[1]
                active_at = match.group(8).split(': ')[1]
                created_at = match.group(9).split(': ')[1]
                note = match.group(10).split(': ')[1]
                # print((outname, addr, phone, orders, status_chat, status_bot, remind_at, active_at, created_at, note))
                # 确认身份
                sql_str = "select case when b.chat_id is null then 0 else 1 end,a.* from chats a left join zoo_guest " \
                          "b on a.id=b.chat_id where a.outname like '%{0:s}%'".format(outname)
                rst = tg_connector.query(sql_str)
                if len(rst) != 1:
                    sql_str = "select count(1) from tmp where note='{0:s}'".format(outname)
                    rst = tg_connector.query(sql_str)
                    if rst[0][0] == 0:
                        if orders != '0':
                            print("确认身份失败，具体如下：")
                            print((msg.id, str(msg.date)))
                            print(msg.text)
                            print((outname, addr, phone, orders, status_chat, status_bot, remind_at, active_at,
                                   created_at,
                                   note))
                            print("本次执行到的最后ID： {0:d}".format(last_id))
                            print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
                            input()
                        sql_str = "insert into tmp values('{0:s}')".format(outname)
                        tg_connector.exe(sql_str)
                    continue
                # 入库信息
                if rst[0][0] == 0:
                    # print((rst, outname, addr, phone, orders, mean('status_chat', status_chat, 'from'),
                    #             mean('status_bot', status_bot, 'from'), remind_at, active_at, created_at, note))
                    sql_str = "insert into zoo_guest(chat_id,name,username,outname,addr,phone,orders,status_chat," \
                              "status_bot,remind_at,active_at,created_at,note) " \
                              "values({0[1]:d}, '{0[3]:s}', '{0[6]:s}', '{0[4]:s}', '{2:s}', '{3:s}', {4:s}, '{5:s}'," \
                              " '{6:s}', '{7:s}', '{8:s}', '{9:s}', '{10:s}')"\
                        .format(rst[0], outname, addr, phone, orders, mean('status_chat', status_chat, 'from'),
                                mean('status_bot', status_bot, 'from'), remind_at, active_at, created_at, note)
                    tg_connector.exe(sql_str)
        print("本次执行到的最后ID： {0:d}".format(last_id))
        print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从机器人中爬取动物资料入库
@Client.on_message(filters.command("girl") & filters.chat(-630680673))
async def girl(client, message):
    try:
        await message.reply("从机器人中爬取动物资料入库")
        # all_cnt = await client.get_chat_history_count('myhappygirlsbot')
        # print(all_cnt)
        # 146621  开始 1086226
        async for msg in client.get_chat_history('myhappygirlsbot', limit=50000, offset_id=252703):
            last_id = msg.id
            last_date = msg.date
            # print((msg.id, str(msg.date)))
            if msg.text is not None and re.match('.*s information!!' + e_lip * 3, msg.text, re.S) is not None:
                # print(msg.text)
                match = re.match(
                    '.*information.*(No: .*)\n(Name: .*)\n(Country: .*)\n(Age: .*)\n(Height: .*)\n(Weight: .*)\n'
                    '(Boobs: .*)\n(Baby: .*)\n(Location: .*)\n(Special: .*)\n(Level: .*)\n(Phone: .*)\n(Wechat: .*)\n'
                    '(Telegram: .*)\n(Facebook: .*)\n(Price\_out: .*)\n(Price\_in: .*)\n(Picture: .*)\n'
                    '.*\n(Status: .*)\n(Note: .*)\n'+line+'.*', msg.text, re.S)
                if match is None:
                    print("分析字段失败，具体如下：")
                    print((msg.id, str(msg.date)))
                    print(msg.text)
                    continue
                no = match.group(1).split(': ')[1]
                name = match.group(2).split(': ')[1]
                country = match.group(3).split(': ')[1]
                age = match.group(4).split(': ')[1]
                height = match.group(5).split(': ')[1]
                weight = match.group(6).split(': ')[1]
                boobs = match.group(7).split(': ')[1]
                baby = match.group(8).split(': ')[1]
                location = match.group(9).split(': ')[1]
                special = match.group(10).split(': ')[1]
                level = match.group(11).split(': ')[1]
                phone = match.group(12).split(': ')[1]
                wechat = match.group(13).split(': ')[1]
                telegram = match.group(14).split(': ')[1]
                facebook = match.group(15).split(': ')[1]
                price_out = match.group(16).split(': ')[1]
                price_in = match.group(17).split(': ')[1]
                picture = match.group(18).split(': ')[1]
                status = match.group(19).split(': ')[1]
                note = match.group(20).split(': ')[1]
                # 信息栏整理
                country_code = mean('country', country, 'from')
                height = height[:3]
                weight = weight[:2]
                baby = mean('info_baby', baby, 'from')
                level = mean('info_level', level, 'from')
                status = mean('info_status', status, 'from')
                if price_out == '':
                    price_out = '-'
                if note == '':
                    note = '-'
                # 分析后的表内容
                # print((no, name, age, height, weight, boobs, baby, location, special, level, phone, wechat, telegram,
                #         facebook, price_out, price_in, picture, country_code, status, note))
                sql_str = "select count(1) from zoo_info where id= {0:s}".format(no)
                rst = tg_connector.query(sql_str)
                # 如果已经入库则不需要操作
                if rst[0][0] > 0:
                    continue
                # 提取媒体信息
                pre_msg_id = last_id-1
                pre_msg = await client.get_messages('myhappygirlsbot', pre_msg_id)
                # 如果上一条记录不是 media_group 则不需要操作
                if pre_msg.media_group_id is None:
                    continue
                # 复制媒体消息到频道
                msg_list = await client.copy_media_group(media.to_chat, 'myhappygirlsbot', pre_msg_id)
                med_list = []
                for msg in msg_list:
                    msg_row = await media.save(client, msg, 'online')
                    med_list.append(str(msg_row[0]))
                # print(med_list)
                # 把信息入库
                sql_str = "insert into zoo_info values(" \
                          "{0:s},'{1:s}',{2:s},{3:s},{4:s},'{5:s}','{6:s}','{7:s}','{8:s}',{9:d},'{10:s}','{11:s}'," \
                          "'{12:s}','{13:s}','{14:s}','{15:s}','{16:s}','{17:s}','{18:s}','{19:s}',{20:s},'{21:s}','{22:s}')"\
                    .format(no, name, age, height, weight, boobs, baby, location, special, level, phone, wechat,
                            telegram, facebook,country_code,price_out,price_in,'|'.join(med_list),'Hugo','Hugo','0',status,note)
                # print(sql_str)
                tg_connector.exe(sql_str)

                print((last_id, last_date, no))

        print("本次执行到的最后ID： {0:d}".format(last_id))
        print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 从机器人中爬取动物资料入库2 旧模板没有所属国家
@Client.on_message(filters.command("girl2") & filters.chat(-630680673))
async def girl2(client, message):
    try:
        await message.reply("从机器人中爬取动物资料入库2")
        # all_cnt = await client.get_chat_history_count('myhappygirlsbot')
        # print(all_cnt)
        # 146621  开始 1086226
        async for msg in client.get_chat_history('myhappygirlsbot', limit=50000, offset_id=252703):
            last_id = msg.id
            last_date = msg.date
            # print((msg.id, str(msg.date)))
            if msg.text is not None and re.match('.*s information!!' + e_lip * 3, msg.text, re.S) is not None:
                # print(msg.text)
                match = re.match(
                    '.*information.*(No: .*)\n(Name: .*)\n(.*)(Age: .*)\n(Height: .*)\n(Weight: .*)\n'
                    '(Boobs: .*)\n(Baby: .*)\n(Location: .*)\n(Special: .*)\n(Level: .*)\n(Phone: .*)\n(Wechat: .*)\n'
                    '(Telegram: .*)\n(Facebook: .*)\n(Price\_out: .*)\n(Price\_in: .*)\n(Picture: .*)\n'
                    '.*\n(Status: .*)\n(Note: .*)\n'+line+'.*', msg.text, re.S)
                if match is None:
                    print("分析字段失败，具体如下：")
                    print((msg.id, str(msg.date)))
                    print(msg.text)
                    continue
                no = match.group(1).split(': ')[1]
                name = match.group(2).split(': ')[1]
                # country = match.group(3).split(': ')[1]
                age = match.group(4).split(': ')[1]
                height = match.group(5).split(': ')[1]
                weight = match.group(6).split(': ')[1]
                boobs = match.group(7).split(': ')[1]
                baby = match.group(8).split(': ')[1]
                location = match.group(9).split(': ')[1]
                special = match.group(10).split(': ')[1]
                level = match.group(11).split(': ')[1]
                phone = match.group(12).split(': ')[1]
                wechat = match.group(13).split(': ')[1]
                telegram = match.group(14).split(': ')[1]
                facebook = match.group(15).split(': ')[1]
                price_out = match.group(16).split(': ')[1]
                price_in = match.group(17).split(': ')[1]
                picture = match.group(18).split(': ')[1]
                status = match.group(19).split(': ')[1]
                note = match.group(20).split(': ')[1]
                # 信息栏整理

                if re.match('混血', special, re.S) is not None:
                    country_code = '0'
                else:
                    country_code = '1'
                height = height[:3]
                weight = weight[:2]
                baby = mean('info_baby', baby, 'from')
                level = mean('info_level', level, 'from')
                status = mean('info_status', status, 'from')
                if price_out == '':
                    price_out = '-'
                if note == '':
                    note = '-'
                # 分析后的表内容
                # print((no, name, age, height, weight, boobs, baby, location, special, level, phone, wechat, telegram,
                #         facebook, price_out, price_in, picture, country_code, status, note))
                sql_str = "select count(1) from zoo_info where id= {0:s}".format(no)
                rst = tg_connector.query(sql_str)
                # 如果已经入库则不需要操作
                if rst[0][0] > 0:
                    continue
                # 提取媒体信息
                pre_msg_id = last_id-1
                pre_msg = await client.get_messages('myhappygirlsbot', pre_msg_id)
                # 如果上一条记录不是 media_group 则不需要操作
                if pre_msg.media_group_id is None:
                    continue
                # 复制媒体消息到频道
                msg_list = await client.copy_media_group(media.to_chat, 'myhappygirlsbot', pre_msg_id)
                med_list = []
                for msg in msg_list:
                    msg_row = await media.save(client, msg, 'online')
                    med_list.append(str(msg_row[0]))
                # print(med_list)
                # 把信息入库
                sql_str = "insert into zoo_info values(" \
                          "{0:s},'{1:s}',{2:s},{3:s},{4:s},'{5:s}','{6:s}','{7:s}','{8:s}',{9:d},'{10:s}','{11:s}'," \
                          "'{12:s}','{13:s}','{14:s}','{15:s}','{16:s}','{17:s}','{18:s}','{19:s}',{20:s},'{21:s}','{22:s}')"\
                    .format(no, name, age, height, weight, boobs, baby, location, special, level, phone, wechat,
                            telegram, facebook,country_code,price_out,price_in,'|'.join(med_list),'Hugo','Hugo','0',status,note)
                # print(sql_str)
                tg_connector.exe(sql_str)

                print((last_id, last_date, no))

        print("本次执行到的最后ID： {0:d}".format(last_id))
        print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 取图片编号
@Client.on_message(filters.forwarded & filters.chat(-1001569364480))
async def girl3(client, message):
    try:
        print("收录到新媒体")
        msg_row = await media.save(client, message, 'online')
        print(msg_row[0])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 抓取群欢乐园贵妃评论区消息
@Client.on_message(filters.command("msg2") & filters.chat(-630680673))
async def msg2(client, message):
    try:
        await message.reply("抓取群欢乐园贵妃评论区消息")
        all_cnt = await client.get_chat_history_count(-1001759477013)
        print(all_cnt)
        # 669
        # 4336
        async for msg in client.get_chat_history(-1001759477013, limit=200000):
            last_id = msg.id
            last_date = msg.date
            if msg.text is not None and re.match('.*编号: .*\n欢乐园.*', msg.text, re.S) is not None:
                match = re.match('.*(编号: .*)\n欢乐园.*', msg.text, re.S)
                no = match.group(1).split(': ')[1]
                sql_str = "select count(1) from chat_message where chat_id= -1001759477013 and ind='{0:s}'".format(no)
                rst = tg_connector.query(sql_str)
                if rst[0][0] == 0:
                    sql_str = "insert into chat_message values(-1001759477013, '讨论', '动物列表', '{0:s}', '," \
                              "{1:d},','{2:%Y-%m-%d %X}','{2:%Y-%m-%d %X}')" \
                        .format(no, last_id, last_date)
                    tg_connector.exe(sql_str)

        print("本次执行到的最后ID： {0:d}".format(last_id))
        print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 抓取主频道动物列表消息
@Client.on_message(filters.command("msg3") & filters.chat(-630680673))
async def msg3(client, message):
    try:
        await message.reply("欢乐园-颜值担当")
        all_cnt = await client.get_chat_history_count(-1001374199552)
        print(all_cnt)
        # 669
        # 4336
        async for msg in client.get_chat_history(-1001374199552, limit=300000):
            last_id = msg.id
            last_date = msg.date
            if msg.caption is not None and re.match('.*欢乐园贵妃.*(编号: .*)\n.*国籍.*', msg.caption, re.S) is not None:
                match = re.match('.*欢乐园贵妃.*(编号: .*)\n.*国籍.*', msg.caption, re.S)
                no = match.group(1).split(': ')[1]
                sql_str = "select count(1) from chat_message where chat_id= -1001374199552 and kind='动物列表' and ind='{0:s}'".format(no)
                rst = tg_connector.query(sql_str)
                if rst[0][0] == 0:
                    msg_list = await client.get_media_group(-1001374199552, last_id)
                    msg_tmp = []
                    for m in msg_list:
                        msg_tmp.append(str(m.id))
                    sql_str = "insert into chat_message values(-1001374199552, '欢乐园-低价专区', '动物列表', '{0:s}', ',{1:s},'," \
                              "'{2:%Y-%m-%d %X}','{2:%Y-%m-%d %X}')" \
                        .format(no, ','.join(msg_tmp), last_date)
                    tg_connector.exe(sql_str)

        print("本次执行到的最后ID： {0:d}".format(last_id))
        print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 抓取评论区讨论消息
@Client.on_message(filters.command("msg4") & filters.chat(-630680673))
async def msg4(client, message):
    try:
        await message.reply("抓取评论区讨论消息")
        all_cnt = await client.get_chat_history_count(-1001759477013)
        print(all_cnt)
        # 669
        # 4336
        async for msg in client.get_chat_history(-1001759477013, limit=200000):
            last_id = msg.id
            last_date = msg.date
            if msg.caption is not None and re.match('.*(贵妃: .*)-.*\n.*评论:.*', msg.caption, re.S) is not None:
                match = re.match('.*(贵妃: .*)-.*\n.*评论:.*', msg.caption, re.S)
                no = match.group(1).split(': ')[1]
                if msg.media_group_id is not None:
                    # 提取消息信息
                    msg_list = await client.get_media_group(-1001759477013, last_id)
                    msg_tmp = []
                    for m in msg_list:
                        msg_tmp.append(str(m.id))
                    msg_list_str = ','.join(msg_tmp)
                else:
                    msg_list_str = str(last_id)
                sql_str = "select count(1) from chat_message where chat_id= -1001759477013 and kind='讨论' and msg_list='{0:s}'".format(msg_list_str)
                rst = tg_connector.query(sql_str)
                if rst[0][0] == 0:
                    if msg.media_group_id is not None:
                        # 复制媒体消息到频道
                        msg_list = await client.copy_media_group(media.to_chat, -1001759477013, last_id)
                        med_list = []
                        for msg in msg_list:
                            msg_row = await media.save(client, msg, 'online')
                            med_list.append(str(msg_row[0]))
                        ind_str = "{0:s}#{1:s}".format(no, ','.join(med_list))
                    else:
                        msg = await client.copy_message(media.to_chat, -1001759477013, last_id)
                        med = await media.save(client, msg, 'online')
                        ind_str = "{0:s}#{1:d}".format(no, med[0])
                    sql_str = "insert into chat_message values(-1001759477013, '评论', '讨论', '{0:s}', ',{1:s},'," \
                              "'{2:%Y-%m-%d %X}','{2:%Y-%m-%d %X}')" \
                        .format(ind_str, msg_list_str, last_date)
                    tg_connector.exe(sql_str)
        print("本次执行到的最后ID： {0:d}".format(last_id))
        print("本次执行到的最后日期： {0:%Y-%m-%d %X}".format(last_date))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


