# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from monit_zoo import *
from monit_public import *
# from monit_yy1 import *
# from monit_yy2 import *
from func import *
import time
import asyncio
import traceback


class Monit:
    def __init__(self, c_list=client_list):
        try:
            self.freq = 30
            self.valid = True
            # 初始化监控列表
            self.client_str = "','".join(c_list)
            sql_str = "update monits set run_time=(case when init_time is null then now() else date_add(init_time," \
                      "interval ceil(timestampdiff(minute,init_time,now())/freq)*freq minute) end) where status='1' " \
                      "and bot_name in ('{0:s}')".format(self.client_str)
            tg_connector.exe(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 启动监控模块
    async def start(self):
        try:
            while self.valid:
                now_time = time.localtime(time.time())
                time_to_str = time.strftime('%Y-%m-%d %H:%M:%S', now_time)
                sql_str = "select id,name,bot_name,chat_list from monits where status='1' and bot_name in ('{0:s}') " \
                          "and '{1:s}'>=run_time".format(self.client_str, time_to_str)
                monits = tg_connector.query(sql_str)
                if len(monits) > 0:
                    sql_str = "update monits set run_time=date_add(run_time,interval floor(timestampdiff(minute," \
                              "run_time,'{1:s}')/freq)*freq+freq minute) where status='1' and bot_name in ('{0:s}')" \
                              " and '{1:s}'>=run_time".format(self.client_str, time_to_str)
                    tg_connector.exe(sql_str)
                    for monit in monits:
                        # PUBLIC
                        if monit[0] in ['PUBLIC01', 'PUBLIC02']:
                            await do_monit_public(monit)
                        elif monit[0] in ['ZOOHOME01', 'ZOOHOME01_EN', 'ZOOHOME02', 'ZOODIANPING', 'ZOOAVA01', 'ZOOCLEAN01']:
                            await do_monit_zoo(monit)
                await asyncio.sleep(self.freq)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


