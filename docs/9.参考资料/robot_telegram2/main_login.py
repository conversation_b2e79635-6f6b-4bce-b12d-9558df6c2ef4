# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from init import *
from func import *
from classes2 import *
import asyncio
from pyrogram import idle


async def main():
    try:
        sys_log.write_log('The Login is begining...', 'a')
        reg_client = RegClient()
        while 1:
            print("""
请选择你要进行的操作：
1、通过 客户端 协助 后台 登录
2、通过 后台 协助 客户端 登录
3、通过 字符串 登录 后台
""")
            text = input("选择是：")
            if text == '1':
                print_both("通过 客户端 协助 后台 登录")
                show = "肯尼亚:[8|254]  哥斯達黎加:[93|506]  加纳:[38|233] 格林納達:[127|299]\n" \
                       "尼加拉瓜:[90|505] 巴基斯坦:[66|92] 孟加拉国:[60|880]  美国（虚拟）:[12|1]\n"
                name = input(show + "要登录后台的号码(国家区号_号码)：").strip()
                rst = await reg_client.login_client_db_1(name)
                while rst == "输入格式不正确(国家区号_号码)":
                    name = input(rst + "：").strip()
                    rst = await reg_client.login_client_db_1(name)
                # 只有第一步走到最后才需要接收验证码
                if rst == "等待接收验证码":
                    code = input("输入验证码：")
                    rst = await reg_client.login_client_db_2(code)
                print(rst)
            elif text == '2':
                print_both("通过 后台 协助 客户端 登录")
                await reg_client.login_db_client()
            elif text == '3':
                print_both("通过 字符串 登录 后台")
                await reg_client.login_via_str()
            elif text == '0':
                print_both("测试")
                await reg_client.run()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())
