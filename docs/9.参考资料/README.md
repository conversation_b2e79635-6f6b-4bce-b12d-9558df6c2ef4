# 参考资料

## 概述

本目录包含技术参考资料、外部链接、学习资源等支撑项目开发的参考文档。

## 文档分类

### 技术文档
- 官方技术文档
- API参考手册
- 框架使用指南
- 工具使用说明

### 学习资源
- 在线教程
- 视频课程
- 技术博客
- 开源项目

### 标准规范
- 编码规范
- 设计规范
- 安全规范
- 行业标准

### 外部链接
- 官方网站
- 社区论坛
- 技术支持
- 相关工具

## 当前文档列表

> 暂无文档

## 技术栈参考

### Python 3.11.9
- [Python官方文档](https://docs.python.org/3.11/)
- [Python编码规范 PEP 8](https://pep8.org/)
- [Python最佳实践](https://docs.python-guide.org/)

### QML/Qt
- [Qt官方文档](https://doc.qt.io/)
- [QML参考手册](https://doc.qt.io/qt-6/qmlreference.html)
- [Qt Quick Controls](https://doc.qt.io/qt-6/qtquickcontrols-index.html)

### 开发工具
- [Git官方文档](https://git-scm.com/doc)
- [pytest测试框架](https://docs.pytest.org/)
- [Black代码格式化](https://black.readthedocs.io/)

## 设计参考

### 架构设计
- [领域驱动设计](https://domainlanguage.com/ddd/)
- [微服务架构](https://microservices.io/)
- [设计模式](https://refactoring.guru/design-patterns)

### UI/UX设计
- [Material Design](https://material.io/design)
- [Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)
- [Fluent Design System](https://www.microsoft.com/design/fluent/)

## 开发规范参考

### 代码规范
- [Google Python Style Guide](https://google.github.io/styleguide/pyguide.html)
- [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- [Clean Code原则](https://clean-code-developer.com/)

### 测试规范
- [测试金字塔](https://martinfowler.com/articles/practical-test-pyramid.html)
- [TDD最佳实践](https://www.agilealliance.org/glossary/tdd/)
- [单元测试指南](https://unittest-docs.readthedocs.io/)

## 文档模板

### 参考资料记录模板
```markdown
# [资料标题]

## 资料类型
## 资料链接
## 内容摘要
## 适用场景
## 重要程度
## 更新时间
## 备注说明
```

## 资源分类

### 官方资源
- 技术官方文档
- 官方示例代码
- 官方最佳实践
- 版本更新说明

### 社区资源
- 开源项目
- 技术博客
- 论坛讨论
- 经验分享

### 学习资源
- 在线课程
- 技术书籍
- 视频教程
- 实战项目

### 工具资源
- 开发工具
- 测试工具
- 部署工具
- 监控工具

## 资料评估标准

### 权威性
- ⭐⭐⭐⭐⭐ 官方文档
- ⭐⭐⭐⭐ 知名技术专家
- ⭐⭐⭐ 技术社区认可
- ⭐⭐ 个人博客
- ⭐ 未验证来源

### 时效性
- 🟢 最新版本
- 🟡 较新版本
- 🟠 版本较旧
- 🔴 版本过时

## 更新指南

1. **发现有用资源时**，及时添加到相应分类
2. **定期检查链接**，确保资源可访问
3. **评估资源质量**，标注权威性和时效性
4. **每次更新后**，请更新本README.md文件的文档列表

---

**最后更新时间**：$(date +"%Y-%m-%d")
