# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from classes import *
# from main import *
from pyrogram import filters
from pyrogram.errors import ChatAdminRequired
from pyrogram.errors import Flood<PERSON>ait
from pyrogram import raw
import asyncio
import json
import traceback
from watermarker.marker import add_mark
from moviepy.editor import *
from moviepy.video.VideoClip import TextClip
import websockets
from functools import wraps


# 通过 chat_id 取 users.id 函数
def get_users_id(chat_id):
    try:
        sql_str = f"select id from user_ids where chat_id={chat_id}"
        rst = read_connector.run(sql_str)
        if len(rst) == 0:
            sql_str_insert = f"insert into user_ids(chat_id) values({chat_id})"
            exe_connector.run(sql_str_insert)
            rst = read_connector.run(sql_str)
        return rst[0][0]
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# user_id 转 std_id
def user_to_std(user_id):
    try:
        user_id = str(user_id)
        str_sql = "select count(1),ifnull(max(case when user_id={0:s} then std_id else '' end),'') from user_std " \
                  "where short_id=right({0:s},4)".format(user_id)
        rst = read_connector.run(str_sql)
        if rst[0][1] != '':
            std_id = rst[0][1]
        else:
            encode = {0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F', 6: 'G', 7: 'H', 8: 'I', 9: 'J', 10: 'K', 11: 'L',
                      12: 'M', 13: 'N', 14: 'O', 15: 'P', 16: 'Q', 17: 'R', 18: 'S', 19: 'T', 20: 'U', 21: 'V', 22: 'W',
                      23: 'X', 24: 'Y', 25: 'Z'}
            std_id = user_id[-4:] + encode[rst[0][0]]
            str_sql = "insert into user_std(user_id,short_id,std_id) values({0:s},right({0:s},4),'{1:s}')" \
                .format(user_id, std_id)
            exe_connector.run(str_sql)
        return std_id
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# std_id 转 user_id
def std_to_user(std_id):
    try:
        str_sql = "select user_id from user_std where std_id='{0:s}'".format(std_id)
        rst = read_connector.run(str_sql)
        if len(rst) == 0:
            return 0
        else:
            return rst[0][0]
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 检查user_std中的两个id是否存在
def user_std_exist(id):
    try:
        if re.match('^\d{6,}$', str(id), re.S) is None:
            str_sql = "select count(1) from user_std where std_id='{0:s}'".format(str(id))
        else:
            str_sql = "select count(1) from user_std where user_id={0:s}".format(str(id))
        rst = read_connector.run(str_sql)
        return rst[0][0] == 1
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取客户链接
def link(param):
    if isinstance(param, int):
        mychat = myChats[param]
    else:
        mychat = myChats.name(param)
    if mychat.username != '':
        return 'https://t.me/' + mychat.username
    else:
        return f"tg://user?id={mychat.id}"
        # return f"tg://openmessage?user_id={mychat.id}"


# 批量模糊匹配
def fuzzy_finder(patterns: list, contents: list, flags=re.S):
    collects = []
    for pattern in patterns:
        for content in contents:
            match = re.match(pattern, content, flags)
            if match is not None:
                collects.append([pattern, content, match.groups])
    return collects


# 获取可用代理
def get_proxy():
    try:
        str_sql2 = "update proxys set status='{1:s}',used_at=now() where addr='{0:s}'"
        while 1:
            str_sql = "select addr,port from proxys where status<>'2' order by status,used_at limit 1"
            rst = read_connector.run(str_sql)
            if len(rst) == 1:
                row = rst[0]
                if test_ip(row[0], int(row[1])):
                    str_sql = str_sql2.format(row[0], '1')
                    exe_connector.run(str_sql)
                    return row
                else:
                    str_sql = str_sql2.format(row[0], '2')
                    exe_connector.run(str_sql)
            else:
                return ()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 取随机数 order_by['updated_at','cnt','rand()']
def get_random(no, order_by='updated_at'):
    try:
        str_sql = "select * from randoms where id='{0:s}' order by {1:s} limit 1".format(no, order_by)
        rst = read_connector.run(str_sql)
        str_sql = "update randoms set updated_at=CURRENT_TIMESTAMP(), cnt=cnt+1 where id='{0[0]:s}' and " \
                  "value='{0[1]:s}'".format(rst[0])
        exe_connector.run(str_sql)
        if len(rst) > 0:
            return rst[0]
        else:
            return ()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 在屏幕和日志中都显示
def print_both(text: str):
    try:
        print(text)
        sys_log.write_log(text, 'a')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# user_list验证
def user_cert(user_list, user_id):
    try:
        if user_list.count('|') == 0:
            if user_list.upper() == 'ALL':
                return True
            else:
                for name in user_list.split(','):
                    if user_id == myChats.name(name).id:
                        return True
        elif user_list.count('|') == 1:
            for name in user_list.split('|')[1].split(','):
                if user_id == myChats.name(name).id:
                    return False
            return True
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 代理数据 客户数据 特殊处理
def comm_spec_deal(command):
    if command[0][0:4] in ('客户数据', '代理数据') and len(command[0]) > 4:
        new_command = [command[0][0:4], command[0][4:]]
        new_command.extend(command[1:])
        return new_command
    else:
        return command


# 权限验证
def flt_command_detail(flt, _, query):
    try:
        if type(query) == Message:
            name = comm_spec_deal(query.matches[0].group(1).split(" "))[0]
            str_id = "','".join(flt.id_list)
            tpye = query.chat.type
            str_sql = "select chat_name,user_list from talks where id in ('{0:s}') and name = '{1:s}' and " \
                      "type = '{2:s}' and status = '1' limit 1".format(str_id, name, tpye)
            rst = read_connector.run(str_sql)
            if len(rst) == 0:
                return False
            # chat_name验证
            user_id = query.from_user.id
            chat_id = query.chat.id
            if rst[0][0].count('|') == 0:
                if rst[0][0].upper() == 'ALL':
                    return user_cert(rst[0][1], user_id)
                else:
                    for chat_name in rst[0][0].split(','):
                        if chat_id == myChats.name(chat_name).id:
                            return user_cert(rst[0][1], user_id)
            elif rst[0][0].count('|') == 1:
                for chat_name in rst[0][0].split('|')[1].split(','):
                    if chat_id == myChats.name(chat_name).id:
                        return False
                return user_cert(rst[0][1], user_id)
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立权限验证筛选器
def flt_command(id_list):
    try:
        return filters.create(flt_command_detail, id_list=id_list)
    except Exception as e:
        sys_log.write_log('{0:s}'.format(traceback.format_exc()), 'a')
        return False


# 文本验证
def flt_text_detail(flt, query):
    try:
        if type(query) == Message and query.text is not None:
            if query.text in flt.text_list:
                return True
        else:
            return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立文本验证筛选器
def flt_text(text_list):
    try:
        return filters.create(flt_text_detail, text_list=text_list)
    except Exception as e:
        sys_log.write_log('{0:s}'.format(traceback.format_exc()), 'a')
        return False


# 回调查询权限验证
def flt_callback_detail(flt, _, query):
    try:
        if type(query) == CallbackQuery:
            str_id = "','".join(flt.id_list)
            tpye = query.message.chat.type
            name = [flt.data, query.data][flt.data == '']
            str_sql = "select chat_name,user_list from talks where id in ('{0:s}') and name = '{1:s}' and " \
                      "type = '{2:s}' and status = '1' limit 1".format(str_id, name, tpye)
            rst = read_connector.run(str_sql)
            if len(rst) == 0:
                return False
            # chat_name验证
            user_id = query.from_user.id
            chat_id = query.message.chat.id
            if rst[0][0].count('|') == 0:
                if rst[0][0].upper() == 'ALL':
                    return user_cert(rst[0][1], user_id)
                else:
                    for chat_name in rst[0][0].split(','):
                        if chat_id == myChats.name(chat_name).id:
                            return user_cert(rst[0][1], user_id)
            elif rst[0][0].count('|') == 1:
                for chat_name in rst[0][0].split('|')[1].split(','):
                    if chat_id == myChats.name(chat_name).id:
                        return False
                return user_cert(rst[0][1], user_id)
        return False
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立回调查询权限验证筛选器
def flt_callback(id_list, data=''):
    try:
        return filters.create(flt_callback_detail, id_list=id_list, data=data)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 建立Callback私发筛选器
def pv_callback(_, __, callback):
    return callback.message.chat.type == ChatType.PRIVATE


privateCallbackFilter = filters.create(pv_callback)


# 提取inputPeer
# id: 可以是user_id或者username
# client： 通过client来解析，每个client解析不一样
# join_group： 目标群的username，如果不赋值，取不到就不取，
#              如果赋值auto，使用user_id取不到时，client加入库表中的目标群，重新解析
#              如果赋某个特点值，使用user_id取不到时，client加入特定的目标群，重新解析
async def get_input_peer(id: Union[int, str], client=None, join_group=''):
    try:
        try:
            if re.match('^-?\d+$', id, re.S) is not None:
                id = int(id)
            # 直接解析
            peer = await client.resolve_peer(peer_id=id)
            return True, peer
        except Exception as e:
            err = "直接解释错误： {0:s}".format(str(id))
            if isinstance(id, int) and myChats[id].username != '':
                try:
                    # 通过id取username后解析
                    username = myChats[id].username
                    peer = await client.resolve_peer(peer_id=username)
                    return True, peer
                except Exception as e:
                    err = "通过id取username后，解析错误: {0:s}-{1:s}".format(str(id), username)
        # 如果赋值auto，使用user_id取不到时，client加入库表中的目标群，重新解析
        if join_group == 'auto' and isinstance(id, int):
            sql_str = "select b.username,a.chat_id,a.chat_name from (select * from chat_user where user_id={0:d}) a " \
                      "join chats b on a.chat_id=b.id where b.username<>'' order by a.joined_at desc limit 1".format(id)
            rst = read_connector.run(sql_str)
            if len(rst) == 1:
                try:
                    await client.join_chat(rst[0][0])
                    try:
                        await asyncio.sleep(3)
                        peer = await client.resolve_peer(peer_id=id)
                        return True, peer
                    except Exception as e:
                        err = "加入库表中的群后，解析错误: {0:s}-({1[0]:s},{1[1]:d},{1[2]:s})".format(str(id), rst[0])
                except Exception as e:
                    err = "加入库表中的群失败: {0:s}-({1[0]:s},{1[1]:d},{1[2]:s})".format(str(id), rst[0])
        elif join_group != '':
            try:
                await client.join_chat(join_group)
                try:
                    await asyncio.sleep(3)
                    peer = await client.resolve_peer(peer_id=id)
                    return True, peer
                except Exception as e:
                    err = "加入目标群后，解析错误: {0:s}-{1:s}".format(str(id), join_group)
            except Exception as e:
                err = "加入目标群失败: {0:s}-{1:s}".format(str(id), join_group)
        return False, err
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发监控信息
async def send_monit_msg(monit, check_ind, msg, parse_mode=ParseMode.HTML, disable_web_page_preview=None,
                         disable_notification=False, reply_to_message_id=None, schedule_date=None, reply_markup=None):
    try:
        sql_str = "select count(1) from monit_crol where ck_key='{0:s}'".format(check_ind)
        if read_connector.run(sql_str)[0][0] == 0:
            # send_log.write_log("{0[0]:s}|{0[1]:s}:{0[2]:s}->{0[3]:s}\n{1:s}\n".format(monit, msg), 'a')
            for chat_name in monit[3].split(','):
                await clients[monit[2]].send_message(myChats.name(chat_name).id, msg,
                                                     parse_mode=parse_mode,
                                                     disable_web_page_preview=disable_web_page_preview,
                                                     disable_notification=disable_notification,
                                                     reply_to_message_id=reply_to_message_id,
                                                     schedule_date=schedule_date,
                                                     reply_markup=reply_markup)
            sql_str = "insert into monit_crol(ck_key,created_at) values('{0:s}', now())".format(check_ind)
            exe_connector.run(sql_str)
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发会话信息
async def send_talk_msg(client, message, msg, parse_mode=ParseMode.HTML, disable_web_page_preview=None,
                        disable_notification=False, reply_to_message_id=None, schedule_date=None, reply_markup=None):
    try:
        if msg != '':
            user_id = message.from_user.id
            user_name = myChats[user_id].name
            chat_id = message.chat.id
            chat_name = myChats[chat_id].name
            # send_log.write_log("{0:s}~{1:s}:{2:s}\n{3:s}\n".format(user_name, chat_name, message.text, msg), 'a')
            return await client.send_message(message.chat.id, msg,
                                             parse_mode=parse_mode,
                                             disable_web_page_preview=disable_web_page_preview,
                                             disable_notification=disable_notification,
                                             reply_to_message_id=reply_to_message_id,
                                             schedule_date=schedule_date,
                                             reply_markup=reply_markup)
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取 media_group 中的消息列表（含过滤后续message作用）
# ['1',  media_group列表] media_group的第一条消息，获取全部消息列表
# ['2',  原消息]           media_group的非第一条消息，返回原消息
# ['0',  原消息]           不是media_group消息，返回原消息
async def check_media_group(client, message):
    try:
        # 处理media_group的第一条记录
        if message.media_group_id is not None and message.media_group_id != params['past_media_group_id']:
            params['past_media_group_id'] = message.media_group_id
            rec_msg = await client.get_media_group(message.chat.id, message.id)
            return ['1', rec_msg]
        elif message.media_group_id is not None and message.media_group_id == params['past_media_group_id']:
            return ['2', [message]]
        else:
            params['past_media_group_id'] = 0
            return ['0', [message]]
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给图片打水印
def add_pic_mark(in_file, out_dir, mark, color="#F11B11", size=18, opacity=0.4, angle=30, space=250):
    try:
        add_mark(file=in_file,
                 out=out_dir,
                 mark=mark,
                 color=color,
                 size=size,
                 opacity=opacity,
                 angle=angle,
                 space=space)
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 给视频打水印
def add_video_mark(in_file, out_file, mark, color="#FF7F50", fontsize=16, opacity=0.5, position=(0.382, 0.618)):
    try:
        video = VideoFileClip(in_file)
        logo = (TextClip(mark, color=color, fontsize=fontsize, font="/etc/fonts/truetype/msyh.ttc")
                .set_duration(video.duration)
                .margin(opacity=opacity)
                .set_position(position, True)
                )
        final = CompositeVideoClip([video, logo])
        final.write_videofile(out_file, fps=30, threads=1, codec="libx264")
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 更新通讯录
async def update_contact(username):
    try:
        # 提取该客户端所有的通讯录成员
        contact_list = await clients[username].get_contacts()
        # 先把目前所有成员设置为非好友
        sql_str = "update contacts set status='0',updated_at=now() where owner_username='{0:s}' and status='1'".format(username)
        exe_connector.run(sql_str)
        # 逐个检查通讯录情况
        for user in contact_list:
            # await update_chat(clients[username], user)
            myChats.update_chat(user)
            sql_str = "insert into contacts(owner_username,id,name,outname,status,updated_at,created_at) " \
                      "values('{0:s}', {1[0]:d}, '{1[2]:s}', '{1[3]:s}','1',now(),now()) on duplicate key update " \
                      "name='{1[2]:s}', outname='{1[3]:s}', status='1', updated_at=now()"\
                .format(username, myChats[user.id].to_list())
            exe_connector.run(sql_str)
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 删除本消息的后续回复消息 检查后续cnt个记录，是否要all检查
async def delete_reply_message(client, message, cnt, is_all=False):
    try:
        i = 1
        for id in range(message.id + 1, message.id+15):
            pre_msg = await client.get_messages(message.chat.id, reply_to_message_ids=id, replies=1)
            if pre_msg is None:
                continue
            if pre_msg.id == message.id:
                await client.delete_messages(message.chat.id, id)
                if not is_all:
                    break
            i += 1
            if i == cnt:
                break
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


async def send_to_ws(message_text: str):
    """用于在特定场景下发送消息到 WebSocket 服务器"""
    async with websockets.connect("ws://localhost:6789") as websocket:
        await websocket.send(message_text)


async def listen_ws(module_name):
    """持续监听 WebSocket 服务器的消息并调用处理函数"""
    async with websockets.connect("ws://localhost:6789") as websocket:
        async for ws_message in websocket:
            # 仅调用指定模块的 WebSocket 处理函数
            if module_name in ws_handlers:
                for handler in ws_handlers[module_name]:
                    await handler(ws_message)


# 接收消息装饰器
def websocket_handler(sender=None, receiver=None, module_name=None):
    """装饰器：用于标记 WebSocket 消息处理函数，并根据条件过滤消息"""
    def decorator(func):
        @wraps(func)
        async def wrapper(ws_message):
            # WebSocket 消息的格式为 "sender_id|receiver|message_content"
            # 将消息分成 sender receiver 和内容
            try:
                sender_text, receiver_text, content = ws_message.split("|", 2)
            except ValueError:
                # 无法分离 sender, receiver 和内容，跳过处理
                return
            # 根据 sender 和 receiver 过滤消息
            if sender is not None:
                # 如果 sender 不匹配，忽略消息
                if isinstance(sender, list) and sender_text in sender:
                    pass
                elif isinstance(sender, str) and sender == sender_text:
                    pass
                else:
                    return
            if receiver is not None:
                # 如果 receiver 不匹配，忽略消息
                if isinstance(receiver, list) and receiver_text in receiver:
                    pass
                elif isinstance(receiver, str) and receiver == receiver_text:
                    pass
                else:
                    return
            # 通过所有过滤条件，执行实际处理函数
            await func(sender_text, receiver_text, content)
        # 根据模块名称注册处理函数
        if module_name is not None:
            if module_name not in ws_handlers:
                ws_handlers[module_name] = []
            ws_handlers[module_name].append(wrapper)
        return wrapper
    return decorator


if __name__ == "__main__":
    print(myChats[-1001474111980])


