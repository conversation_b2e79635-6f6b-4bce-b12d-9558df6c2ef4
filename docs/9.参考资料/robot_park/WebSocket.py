# -*- coding: utf-8 -*-
import asyncio
import websockets

clients = set()


# WebSocket 服务器
async def handler(websocket, path):
    clients.add(websocket)
    try:
        async for message in websocket:
            # 广播消息到所有客户端（除了发送消息的那个）
            await asyncio.gather(
                *[client.send(message) for client in clients if client != websocket]
                )
    finally:
        clients.remove(websocket)


async def start_server():
    async with websockets.serve(handler, "localhost", 6789):
        await asyncio.Future()  # 保持服务器运行


if __name__ == "__main__":
    asyncio.run(start_server())
