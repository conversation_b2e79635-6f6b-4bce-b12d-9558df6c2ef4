# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from monit_redbag import *
from monit_public import *
from monit_asst import *
from func import *
import time
import asyncio
import traceback


class Monit:
    def __init__(self, c_list=client_list):
        try:
            self.freq = 30
            self.valid = True
            # 初始化监控列表
            self.client_str = "','".join(c_list)
            sql_str = "update monits set run_time=(case when init_time is null then now() else date_add(init_time," \
                      "interval ceil(timestampdiff(minute,init_time,now())/freq)*freq minute) end) where status='1' " \
                      "and bot_name in ('{0:s}')".format(self.client_str)
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 启动监控模块
    async def start(self):
        try:
            # 监控启动前，等待bot的启动
            await asyncio.sleep(3)
            while self.valid:
                now_time = time.localtime(time.time())
                time_to_str = time.strftime('%Y-%m-%d %H:%M:%S', now_time)
                sql_str = "select id,name,bot_name,chat_list from monits where status='1' and bot_name in ('{0:s}') " \
                          "and '{1:s}'>=run_time".format(self.client_str, time_to_str)
                monits = read_connector.run(sql_str)
                if len(monits) > 0:
                    sql_str = "update monits set run_time=date_add(run_time,interval floor(timestampdiff(minute," \
                              "run_time,'{1:s}')/freq)*freq+freq minute) where status='1' and bot_name in ('{0:s}')" \
                              " and '{1:s}'>=run_time".format(self.client_str, time_to_str)
                    exe_connector.run(sql_str)
                    for monit in monits:
                        if monit[2] == "bowin_redbagbot":
                            # 使用 asyncio.ensure_future() 来并行执行任务，不使用await，不需要等待任务完成继续执行后面代码
                            asyncio.ensure_future(do_monit_redbag(monit))
                        elif monit[2] == "bowin_asstbot":
                            print(f"bowin_asstbot 监控开始\n{monit}")
                            asyncio.ensure_future(do_monit_asst(monit))
                await asyncio.sleep(self.freq)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


