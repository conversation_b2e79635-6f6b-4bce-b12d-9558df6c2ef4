# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.bowin.redbag import *
import re
import time
import datetime
import traceback
import os
import random


async def do_monit_redbag(monit):
    try:
        now_time = time.localtime(time.time())
        sys_log.write_log('{0[0]:s}|{0[1]:s}:{0[2]:s}->{0[3]:s}'.format(monit), 'a')
        # 监控任务
        # 更新群成员信息
        if monit[0] == 'UPDATEMEM01':
            await chatUser.poll_client([monit[2]])
        # 智能发包
        elif monit[0] == 'AISENT':
            if len(projRedbags.unfinish(params['redbag_group_id'])) < 2:
                amt = random.randint(5, params['sent_amt'])
                ray = random.randint(0, 9)
                await send_redbag(params['redbag_group_id'], myUsers.random_bot(amt), amt, ray)
                # 设定下一次执行时间
                n = random.randint(23, params['sent_freq'])
                current_time = datetime.datetime.now()
                next_time = (current_time + datetime.timedelta(seconds=n)).strftime('%Y-%m-%d %H:%M:%S')
                monits['AISENT'].run_time = next_time
            #
            # t = 0
            # wait = 38
            # while t < 60:
            #     now_odds = projRedbags.last_send_time(group_id=params['redbag_group_id']) * 100 // params['sent_freq']
            #     # 需要发包
            #     if random.randint(1, 100) <= now_odds and len(projRedbags.unfinish(params['redbag_group_id'])) < 2:
            #         amt = random.randint(5, params['sent_amt'])
            #         ray = random.randint(0, 9)
            #         await send_redbag(params['redbag_group_id'], myUsers.random_bot(amt), amt, ray)
            #     await asyncio.sleep(wait)
            #     t += wait
        # 智能抢包
        elif monit[0] == 'AIGET':
            t = 0
            wait = 6
            while t < 60:
                for proj in projRedbags.unfinish(params['redbag_group_id']):
                    now_odds = projRedbags.last_get_time(id=proj.id) * 100 // params['get_freq']
                    # 需要抢包
                    if random.randint(1, 100) <= now_odds:
                        user_id = myUsers.random_bot(proj.bet_amt * Decimal('1.8'))
                        feedback = getRedbags.new(proj.id, user_id)
                        if feedback[0]:
                            new_proj = projRedbags[proj.id]
                            if new_proj.get_cnt == 6:
                                # 抢包人数到达后，结算抢包结果
                                await projRedbags.close(new_proj.id, get_imp('main', new_proj.owner_id))
                            else:
                                # 否则，只修改抢包数量
                                await clients['bowin_redbagbot'].edit_message_reply_markup(
                                    new_proj.group_id,
                                    new_proj.msg_id,
                                    reply_markup=get_imp('send_redbag', new_proj)
                                    )
                await asyncio.sleep(wait)
                t += wait
        # 结束抢包
        elif monit[0] == 'GETCLOSE':
            t = 0
            wait = 25
            while t < 60:
                for proj in projRedbags.unfinish():
                    if projRedbags.last_send_time(id=proj.id) >= params['get_end']:
                        await projRedbags.close(proj.id, get_imp('main', proj.owner_id))
                await asyncio.sleep(wait)
                t += wait












    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
