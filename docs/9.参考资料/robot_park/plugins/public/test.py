# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
# from classes2 import *
from plugins.bowin.common import *


# test收到信息
@Client.on_message(group=3)
async def f_on_message(client, message):
    try:
        print('on_message')
        input = message.text.split(' ')
        print(input)
        # await message.reply("我要发红包！", quote=False)
        await client.send_message(int(message.text),
                                  text='1')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# # on_raw_update
# @Client.on_raw_update(group=3)
# async def f_on_raw_update(client, update, users, chats):
#     try:
#         print('on_raw_update')
#         print(update)
#         print("----------------")
#         print(users)
#         print("----------------")
#         print(chats)
#     except Exception as e:
#         sys_log.write_log(traceback.format_exc(), 'a')


# on_raw_update
@Client.on_chat_member_updated(group=3)
async def f_on_chat_member_updated(client, chat_member_updated):
    try:
        print('on_chat_member_updated')
        print(chat_member_updated)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


