# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from plugins.bowin.common import *


dp_desc = f"""{e_money}充值说明：
本平台支持USDT(TRC20)的自动充值服务，具体流程：
1、回复以下消息输入需要充值的USDT数量
2、按要求在<b>15分钟内</b>转入<b>准确的实际到账金额</b>，切记是扣除手续费后的金额
3、把充值截图发到此聊天会话中
4、等待平台确认后上分

温馨提醒：
1、若需要其它充值方式，请咨询客服 @bowin_boss 
2、您也可以跟朋友、上级、群主等客户通过转账方式进行充值，但本平台不承担相关风险
"""

# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        # 设置键盘按钮
        if index == 'keyboard':
            return ReplyKeyboardMarkup([
                [KeyboardButton(e_money + "充值"),
                 KeyboardButton(e_atm + "提现"),
                 KeyboardButton(e_money3 + "转账")
                 ],
                [KeyboardButton(e_me + "我的"),
                 KeyboardButton(e_market + "报表"),
                 KeyboardButton(e_redbag + "抢红包")]
                ],
                resize_keyboard=True
                )
            # return ReplyKeyboardMarkup([
            #     [KeyboardButton(e_me + "我的"),
            #      KeyboardButton(e_money + "充值"),
            #      KeyboardButton(e_atm + "提现"),
            #      KeyboardButton(e_money3 + "转账")
            #      ],
            #     [KeyboardButton(e_market + "报表"),
            #      KeyboardButton(e_redbag + "抢红包"),
            #      KeyboardButton(e_he + "六合彩"),
            #      KeyboardButton(e_cheng + "担保")]
            #     ],
            #     resize_keyboard=True
            #     )
        # 转账
        elif index == 'transfer':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_money3 + "转账转出", callback_data='transfer_out'),
                 InlineKeyboardButton(e_log + "转账历史", callback_data='transfer_his')]
                ])
        # 转账 - 返回
        elif index == 'transfer_return':
            return InlineKeyboardMarkup([[InlineKeyboardButton(e_return + "返回", callback_data='transfer_return')]])
        # 转账 - 确认
        elif index == 'transfer_confirm':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_cha + "取消", callback_data='transfer_return'),
                 InlineKeyboardButton(e_gou + "确认", callback_data='transfer_confirm')]
                ])
        # 转账 - 完成
        elif index == "transfer_finish":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_suanpan + "查看明细", callback_data=f"transfer_detail")]
                ])
        # 我的
        elif index == 'mine':
            if param.is_remind:
                remind_button = InlineKeyboardButton(e_gou + "接受提醒", callback_data="mine_noremind")
            else:
                remind_button = InlineKeyboardButton(e_no + "拒绝提醒", callback_data="mine_remind")
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_msg+"配置邮箱", callback_data="mine_email"),
                 InlineKeyboardButton(e_key+"配置密码", callback_data="mine_pwd"),
                 InlineKeyboardButton(e_money2+"USDT地址", callback_data="mine_usdt")],
                [remind_button,
                 InlineKeyboardButton(e_ace2+"上级", callback_data="mine_parent"),
                 InlineKeyboardButton(e_king+"商务", url="t.me/bowin_boss")]
                ])
        elif index == 'mine_return':
            return InlineKeyboardMarkup([[InlineKeyboardButton(e_return + "返回", callback_data="mine_return"),
                                          InlineKeyboardButton(e_handup2 + "管理员", url="t.me/bowin_boss")]])
        elif index == 'pwd_return':
            return InlineKeyboardMarkup([[InlineKeyboardButton(e_return + "返回", callback_data="mine_return"),
                                          InlineKeyboardButton(e_what + "忘记密码", callback_data="pwd_forget")]])
        elif index == 'report':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_date + "当天", callback_data="report_当天"),
                 InlineKeyboardButton(e_date + "昨天", callback_data="report_昨天"),
                 InlineKeyboardButton(e_date + "前天", callback_data="report_前天")],
                [InlineKeyboardButton(e_month + "本月", callback_data="report_本月"),
                 InlineKeyboardButton(e_month + "上月", callback_data="report_上月"),
                 InlineKeyboardButton(e_month + "上2月", callback_data="report_上2月")]
                ])
        elif index == "redbag":
            ind = 1
            markup_list = []
            for row in param:
                markup_list.append([InlineKeyboardButton(f"{ind} 介绍", callback_data=f"redbag_intr_{row[0]}"),
                                    InlineKeyboardButton(f"{ind} 推广", callback_data=f"redbag_link_{row[0]}"),
                                    InlineKeyboardButton(f"{ind} 发包", callback_data=f"redbag_send_{row[0]}"),
                                    InlineKeyboardButton(f"{ind} 进群", url=games[row[0]].link)])
                ind += 1
            return InlineKeyboardMarkup(markup_list)
        elif index == 'deposit':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_phone3 + "重发截图", callback_data=f"deposit_resend_{param}"),
                 InlineKeyboardButton(e_cha + "取消充值", callback_data=f"deposit_cancel_{param}")]
                ])
        # 提现
        elif index == 'withdraw':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_atm + "确认提现", callback_data='withdraw'),
                 InlineKeyboardButton(e_log + "提现历史", callback_data='withdraw_his')]
                ])
        # 提现 - 返回
        elif index == 'withdraw_return':
            return InlineKeyboardMarkup([[InlineKeyboardButton(e_return + "返回", callback_data='withdraw_return')]])
        # 提现 - 确认
        elif index == 'withdraw_confirm':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_guests + "客户资料", callback_data='withdraw_confirm_guest'),
                 InlineKeyboardButton(e_gou + "确认放款", callback_data='withdraw_confirm_yes')],
                [InlineKeyboardButton(e_outbox + "出款账号", callback_data='withdraw_confirm_usdt'),
                 InlineKeyboardButton(e_cha + "拒绝放款", callback_data='withdraw_confirm_no')]
                ])
        # 提现 - 确认 - 出款账号
        elif index == "withdraw_confirm_usdt":
            button = []
            chosed = 0
            wd = withdraws[param]
            for row in uaccs.all():
                uacc = TUaccs(row)
                if uacc.name == wd.from_name:
                    button.append(InlineKeyboardButton(e_gou + uacc.name, callback_data=f"withdraw_confirm_usdt_{uacc.name}"))
                    chosed = 1
                else:
                    button.append(InlineKeyboardButton(uacc.name, callback_data=f"withdraw_confirm_usdt_{uacc.name}"))
            if chosed:
                button.append(InlineKeyboardButton('无', callback_data=f"withdraw_confirm_usdt_no"))
            else:
                button.append(InlineKeyboardButton(e_gou + '无', callback_data=f"withdraw_confirm_usdt_no"))
            button.append(InlineKeyboardButton(e_return + "返回", callback_data="withdraw_confirm_usdt_return"))
            return InlineKeyboardMarkup([button[i:i+3] for i in range(0, len(button), 3)])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


@Client.on_message(
    ~filters.bot & ~filters.business & ~filters.forwarded & filters.incoming & filters.private & ~filters.reply &
    filters.text)
async def private_text_message(client, message):
    try:
        # 更新活跃状态，修正名字和用户名，重新提取数据库数据
        chat_id = message.from_user.id
        myUsers.active(chat_id)
        try:
            my_user = myUsers[chat_id, True]
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')
        await send_to_ws(f"助理|秘书|更新myUsers|{chat_id}")
        await send_to_ws(f"助理|发包员|更新myUsers|{chat_id}")
        # 检查客户使用权限
        if my_user.status == 5:
            status = mean("users.status", my_user.status)
            boss_chat = myChats[params['boss_id']]
            await message.reply(
                f"{my_user.outname}，目前您的状态是<b>{status}</b>，助理无法提供服务，请与<a href='{link(boss_chat.id)}'>"
                f"{boss_chat.outname}</a>联系",
                quote=False,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True)
            return
        # 按下按钮
        if message.text == e_me + "我的":
            prompt = line + e_remind + "请选择您需要的操作\n"
            await message.reply(content_mine(my_user) + prompt,
                                quote=False,
                                parse_mode=ParseMode.HTML,
                                reply_markup=get_imp("mine", my_user))
        elif message.text == e_money + "充值":
            prompt = line + e_remind + "请输入需要充值的金额\n"
            re_msg = await message.reply(dp_desc + prompt,
                                         quote=False,
                                         parse_mode=ParseMode.HTML)
            await re_msg.reply_text('回复本消息输入充值金额', quote=True, reply_markup=ForceReply())
        elif message.text == e_atm + "提现":
            prompt = line + e_remind + "请选择您需要的操作\n"
            await message.reply(content_withdraw(my_user) + prompt,
                                quote=False,
                                parse_mode=ParseMode.HTML,
                                reply_markup=get_imp("withdraw"))
        elif message.text == e_money3 + "转账":
            prompt = line + e_remind + "请选择您需要的操作\n"
            await message.reply(content_transfer(my_user) + prompt,
                                quote=False,
                                parse_mode=ParseMode.HTML,
                                reply_markup=get_imp("transfer"))
        elif message.text == e_market + "报表":
            prompt = line + e_remind + "请选择您需要的操作\n"
            await message.reply(content_report(my_user) + prompt,
                                quote=False,
                                parse_mode=ParseMode.HTML,
                                reply_markup=get_imp("report"))
        elif message.text == e_redbag + "抢红包":
            content, rst = content_redbag_join(my_user.chat_id)
            for row in rst:
                if games[row[0]].link == '':
                    if myChats[row[0]].username == '':
                        chat_invite_link = await client.create_chat_invite_link(row[0], name='asst',
                                                                          creates_join_request=False)
                        group_link = chat_invite_link.invite_link
                    else:
                        group_link = f"https://t.me/{myChats[row[0]].username}"
                    games[row[0]] = ('link', group_link)
                    await send_to_ws(f"助理|秘书|更新games|{row[0]}")
                    await send_to_ws(f"助理|发包员|更新games|{row[0]}")
            prompt = line + e_remind + "请选择您需要的操作\n"
            await message.reply(content + prompt,
                                quote=False,
                                parse_mode=ParseMode.HTML,
                                reply_markup=get_imp("redbag", rst))
        elif message.text == e_he + "liuhecai":
            pass
        elif message.text == e_cheng + "担保":
            pass
        else:
            pre_msg = await client.get_messages(chat_id, message.id - 1)
            if (pre_msg.text is not None and pre_msg.text.startswith("请发支付后截图")) or (
                pre_msg.caption is not None and pre_msg.caption.startswith("充值地址(TRC20)：")):
                await message.reply("请发支付后截图到本会话",
                                    quote=False,
                                    parse_mode=ParseMode.HTML)
            else:
                myUsers.refresh(chat_id)
                await message.reply(e_down3 + "请<b>点击按钮</b>进行操作",
                                    quote=False,
                                    parse_mode=ParseMode.HTML,
                                    reply_markup=get_imp("keyboard"))



    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面
@Client.on_callback_query()
async def callback_interface(client, callback):
    try:
        user_id = callback.from_user.id
        user = myUsers[user_id]
        if callback.message.text is not None:
            content = callback.message.text.html.split(line)[0]
        elif callback.message.caption is not None:
            content = callback.message.caption.html.split(line)[0]
        else:
            content = ''
        now_time = datetime.datetime.now().strftime('%H:%M:%S')
        if user.status == 5:
            status = mean("users.status", user.status)
            boss_chat = myChats[params['boss_id']]
            await client.send_message(
                user_id,
                f"{user.outname}，目前您的状态是<b>{status}</b>，暂时无法操作，请与<a href='{link(boss_chat.id)}'>"
                f"{boss_chat.outname}</a>联系",
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True)
            return
        # 充值确认 - 重发截图
        if re.match('^deposit_resend_\d+', callback.data, re.S) is not None:
            dep_id = int(callback.data.split('_')[2])
            dep = deposits[dep_id]
            dep.status = 0
            await client.send_message(
                dep.chat_id,
                "请发支付后截图到本会话，之前发的截图不符合要求",
                parse_mode=ParseMode.HTML)
            await callback.edit_message_text(content + "已要求客户<b>重新发支付截图</b>",
                                             parse_mode=ParseMode.HTML)
        # 充值确认 - 取消充值
        elif re.match('^deposit_cancel_\d+', callback.data, re.S) is not None:
            dep_id = int(callback.data.split('_')[2])
            dep = deposits[dep_id]
            dep.status = 3
            await client.send_message(
                dep.chat_id,
                f"{e_cha}本次充值失败，请确认原因并正确支付",
                parse_mode=ParseMode.HTML)
            await callback.edit_message_text(content + f"{e_cha}<b>本次充值失败</b>",
                                             parse_mode=ParseMode.HTML)
        # 助理端 提现 - 确认提现
        elif callback.data == "withdraw":
            content = content_withdraw(myUsers[user_id])
            if user.role == 3:
                prompt = line + e_remind + f"您目前<b>没有提款权限</b>，请与 @bowin_boss 联系 {now_time}\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('withdraw'))
            elif accs[user_id].wlimit > 0 and user.role != 2:
                prompt = line + e_remind + f"您还<b>没有打够流水</b>，暂时无法提现 {now_time}\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('withdraw'))
            elif user.fund_pwd == '':
                prompt = line + e_remind + f"您还<b>没有配置资金密码</b>，暂时无法提现 {now_time}\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('withdraw'))
            elif user.usdt == '':
                prompt = line + e_remind + f"您还<b>没有配置USDT地址</b>，暂时无法提现 {now_time}\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('withdraw'))
            else:
                prompt = line + e_remind + "请输入<b>需要提现的金额</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('withdraw_return'))
                await re_msg.reply_text('回复本消息输入提现金额', quote=True, reply_markup=ForceReply())
        #  助理端 提现 - 返回
        elif callback.data == "withdraw_return":
            prompt = line + e_remind + "请选择您需要的操作\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("withdraw"))
        # 助理端 - 提现历史
        elif callback.data == "withdraw_his":
            content = content_withdraw_his(user_id)
            prompt = line + e_remind + f"显示最近的<b>10</b>条记录 {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('withdraw'))
        # 秘书端 提现确认 - 客户资料
        elif callback.data == 'withdraw_confirm_guest':
            match = re.match(f'.*/ <code>(.*)</code>\n{e_man}.*', content, re.S)
            chat_id = int(match.group(1))
            await send_to_ws(f"助理|秘书|展示客户|{chat_id}")
        # 秘书端 提现确认 - 出款账号
        elif callback.data == 'withdraw_confirm_usdt':
            match = re.match(f'.*申请ID：<code>(.*)</code>\n{e_prize}.*', content, re.S)
            id = int(match.group(1))
            prompt = line + e_remind + f"请选择出款账号 {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('withdraw_confirm_usdt', id))
        # 秘书端 提现确认 - 出款账号 - 选择
        elif re.match('^withdraw_confirm_usdt_[a-z]{2,}', callback.data, re.S) is not None:
            chosed = callback.data.split('_')[3]
            match = re.match(f'.*申请ID：<code>(.*)</code>\n{e_prize}.*', content, re.S)
            id = int(match.group(1))
            if chosed == "return":
                prompt = line + e_remind + "请选择需要的操作\n"
            elif chosed != "no":
                withdraws[id].from_name = chosed
                withdraws[id].from_acc = uaccs[chosed].addr
                prompt = line + e_remind + f"收款账号你选择了<b>{chosed}</b>\n"
            else:
                withdraws[id].from_name = ''
                withdraws[id].from_acc = ''
                prompt = line + e_remind + f"你<b>没有选择</b>收款账号\n"
            await callback.edit_message_text(content_withdraw_approve(withdraws[id]) + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('withdraw_confirm'))
        # 秘书端 提现确认 - 确认放款
        elif callback.data == 'withdraw_confirm_yes':
            match = re.match(f'.*申请ID：<code>(.*)</code>\n{e_prize}.*', content, re.S)
            id = int(match.group(1))
            wd = withdraws[id]
            # 产生提现解冻账变
            trans.transact(wd.chat_id, 6, wd.amt_apply, issue=str(wd.id), tag=wd.from_name, note='确认放款')
            # 产生在线提现账变
            trans.transact(wd.chat_id, 2, wd.amt_apply, issue=str(wd.id), tag=wd.from_name)
            # 改变提现状态
            wd.status = 1
            # 修改信息
            prompt = line + e_remind + "#放款中\n"
            await callback.edit_message_text(content_withdraw_send(wd) + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True)
            # 启动充值检查监控
            monits['WDCHECK'].init_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            monits['WDCHECK'].status = '1'
            # myOKX = OKX(wd.from_name)
            # rst = await myOKX.withdraw(wd)
            # if rst[0]:
            #     prompt = line + e_remind + "#放款中\n"
            #     await callback.edit_message_text(content_withdraw_send(wd) + prompt,
            #                                      parse_mode=ParseMode.HTML,
            #                                      disable_web_page_preview=True)
            #     # 启动充值检查监控
            #     monits['WDCHECK'].init_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            #     monits['WDCHECK'].status = '1'
            # else:
            #     prompt = line + e_remind + f"{rst[1]} {now_time}\n"
            #     await callback.edit_message_text(content_withdraw_approve(wd) + prompt,
            #                                      parse_mode=ParseMode.HTML,
            #                                      disable_web_page_preview=True,
            #                                      reply_markup=get_imp('withdraw_confirm'))
        # 秘书端 提现确认 - 拒绝放款
        elif callback.data == 'withdraw_confirm_no':
            match = re.match(f'.*申请ID：<code>(.*)</code>\n{e_prize}.*', content, re.S)
            id = int(match.group(1))
            wd = withdraws[id]
            wd.status = 3
            # 产生提现解冻账变
            trans.transact(wd.chat_id, 6, wd.amt_apply, issue=str(wd.id), tag=wd.from_name, note='拒绝放款')
            prompt = line + e_remind + f"#已拒绝放款 {now_time}\n"
            await callback.edit_message_text(content_withdraw_approve(wd) + prompt,
                                             parse_mode=ParseMode.HTML)
            await clients['bowin_asstbot'].send_message(wd.chat_id,
                                                        f"您刚才的提款<b>审核没通过</b>，如有疑问请联系 @bowin_boss 沟通",
                                                        parse_mode=ParseMode.HTML)
        # 转账 - 转账转出
        elif callback.data == "transfer_out":
            if accs[user_id].wlimit > 0 and user.role != 2:
                prompt = line + e_remind + f"您还<b>没有打够流水</b>，暂时无法转账 {now_time}\n"
                await callback.edit_message_text(content_transfer(user) + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('transfer'))
            else:
                prompt = line + e_remind + "请输入<b>转入方ID</b>\n"
                re_msg = await callback.edit_message_text(content_transfer(user) + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('transfer_return'))
                await re_msg.reply_text('回复本消息输入转入方ID', quote=True, reply_markup=ForceReply())
        # 转账 - 返回
        elif callback.data == "transfer_return":
            await delete_reply_message(client, callback.message, 3)
            prompt = line + e_remind + "请选择您需要的操作\n"
            await callback.edit_message_text(content_transfer(user) + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("transfer"))
        # 转账 - 确认
        elif callback.data == "transfer_confirm":
            if user.fund_pwd == '':
                prompt = line + e_remind + f"请先操作<b>{e_key}配置密码</b>，然后进行转账 {now_time}\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp('transfer_confirm'))
                # 发送配置密码页面
                content = content_mine(user)
                prompt = line + e_remind + "请输入需要配置的<b>新密码</b>\n"
                re_msg = await client.send_message(user_id,
                                                   content + prompt,
                                                   parse_mode=ParseMode.HTML,
                                                   disable_web_page_preview=True,
                                                   reply_markup=get_imp('pwd_return'))
                await re_msg.reply_text('回复本消息输入密码', quote=True, reply_markup=ForceReply())
            else:
                prompt = line + e_remind + "请输入<b>资金密码</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp('transfer_return'))
                await re_msg.reply_text('回复本消息输入资金密码', quote=True, reply_markup=ForceReply())
        # 转账 - 明细
        elif callback.data == "transfer_detail":
            match = re.match(f".*流水号：<code>(.*)</code>.*", content, re.S)
            issue = match.group(1)
            content = content_transfer_detail(user_id, issue)
            await callback.message.reply_text(content,
                                              quote=False,
                                              parse_mode=ParseMode.HTML,
                                              disable_web_page_preview=True)
        # 转账 - 历史
        elif callback.data == "transfer_his":
            rst = trans.get_from_type(user_id, [7, 8, 9, 10], 10)
            content = content_transfer_his(rst)
            prompt = line + e_remind + f"显示最近的<b>10</b>条记录 {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('transfer'))
        # 我的 - 邮箱
        elif callback.data == "mine_email":
            if user.email == '':
                prompt = line + e_remind + "请输入<b>邮箱</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('mine_return'))
                await re_msg.reply_text('回复本消息输入邮箱', quote=True, reply_markup=ForceReply())
            else:
                prompt = line + e_remind + "请先验证<b>旧邮箱</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('mine_return'))
                await re_msg.reply_text('回复本消息输入旧邮箱', quote=True, reply_markup=ForceReply())
        # 我的 - 密码
        elif callback.data == "mine_pwd":
            if user.fund_pwd == '':
                prompt = line + e_remind + "请输入<b>密码</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('pwd_return'))
                await re_msg.reply_text('回复本消息输入密码', quote=True, reply_markup=ForceReply())
            else:
                prompt = line + e_remind + "请先验证<b>旧密码</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('pwd_return'))
                await re_msg.reply_text('回复本消息输入旧密码', quote=True, reply_markup=ForceReply())
        # 我的 - 忘记密码
        elif callback.data == "pwd_forget":
            await delete_reply_message(client, callback.message, 3)
            prompt = line + e_remind + "请先<b>验证邮箱</b>\n"
            re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('mine_return'))
            await re_msg.reply_text('回复本消息输入验证邮箱', quote=True, reply_markup=ForceReply())
        # 我的 - USDT地址
        elif callback.data == "mine_usdt":
            if user.usdt == '':
                prompt = line + e_remind + "请输入<b>USDT地址(trc20)</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('mine_return'))
                await re_msg.reply_text('回复本消息输入USDT地址', quote=True, reply_markup=ForceReply())
            else:
                prompt = line + e_remind + "请先<b>验证密码</b>\n"
                re_msg = await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                          reply_markup=get_imp('mine_return'))
                await re_msg.reply_text('回复本消息输入验证密码', quote=True, reply_markup=ForceReply())
        # 我的 - 返回
        elif callback.data == "mine_return":
            await delete_reply_message(client, callback.message, 3)
            prompt = line + e_remind + "请选择您需要的操作\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("mine", user))
        # 我的 - 不提醒
        elif callback.data == "mine_noremind":
            prompt = line + e_remind + "已设置为<b>拒绝提醒</b>状态\n"
            myUsers[user_id] = ('is_remind', 0)
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("mine", myUsers[user_id]))
            await send_to_ws(f"助理|秘书|更新myUsers|{user_id}")
        # 我的 - 提醒
        elif callback.data == "mine_remind":
            prompt = line + e_remind + "已设置为<b>接受提醒</b>状态\n"
            myUsers[user_id] = ('is_remind', 1)
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("mine", myUsers[user_id]))
            await send_to_ws(f"助理|秘书|更新myUsers|{user_id}")
        # 我的 - 上级
        elif callback.data == "mine_parent":
            parent = myChats[user.parent_id]
            parent_str = f"<a href='{link(parent.id)}'>{parent.outname}</a>"
            user_str = f"<a href='{link(user_id)}'>{user.outname}</a>"
            parent_content = ''
            if parent.username != '':
                content = f"您的上级是{parent_str}{e_left3}<i>请点击名字联系</i>"
            elif user.username != '' and myUsers[parent.id].is_remind:
                content = f"已通知上级 {parent_str} 主动联系您"
                parent_content = f"由于您没有设置用户名，下级无法联系您，请您主动联系他{e_right3}{user_str}"
            elif user.username != '' and not myUsers[parent.id].is_remind:
                content = f"上级没有设置用户名，请尝试联系他{e_right3}{parent_str}"
            elif user.username == '' and myUsers[parent.id].is_remind:
                content = f"您没有设置用户名，上级 {parent_str} 无法主动联系您"
                parent_content = f"由于您没有设置用户名，下级无法联系您，请您尝试主动联系他{e_right3}{user_str}"
            else:
                content = f"您没有设置用户名，上级 {parent_str} 无法主动联系您"
            await client.send_message(user_id, content, parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            if parent_content != '':
                try:
                    await client.send_message(parent.id, parent_content, parse_mode=ParseMode.HTML,
                                              disable_web_page_preview=True)
                except (Forbidden, BadRequest) as e:
                    myUsers[parent.id] = ('is_remind', 0)

        # 报表
        elif re.match('^report_.*', callback.data, re.S) is not None:
            type = callback.data.split('_')[1]
            content = content_report(user, type)
            prompt = line + e_remind + f"更新时间 {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('report'))
        # 抢红包 - 介绍
        elif re.match('^redbag_intr_-\d+', callback.data, re.S) is not None:
            group_id = int(callback.data.split('_')[2])
            content, rst = content_redbag_join(user_id)
            content += f"\n<b>{myChats[group_id].outname}</b> 的介绍：\n{games[group_id].introduce}\n"
            prompt = line + e_remind + f"已展示介绍 {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("redbag", rst))
        # 抢红包 - 推广
        elif re.match('^redbag_link_-\d+', callback.data, re.S) is not None:
            group_id = int(callback.data.split('_')[2])
            content, rst = content_redbag_join(user_id)
            group_link = myLinks.get_link(user_id, group_id)
            if group_link is None:
                chat_invite_link = await client.create_chat_invite_link(group_id, name=str(user_id),
                                                                        creates_join_request=False)
                myLinks.new(user_id, group_id, chat_invite_link.invite_link)
                group_link = myLinks.get_link(user_id, group_id)
            # 统一展示
            content += f"\n<b>{myChats[group_id].outname}</b> 的推广链接{e_down3}<i>点击复制</i>\n<code>" \
                       f"{group_link.url}</code>\n"
            prompt = line + e_remind + f"已展示推广链接 {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("redbag", rst))
        # 抢红包 - 发包
        elif re.match('^redbag_send_-\d+', callback.data, re.S) is not None:
            if user.status in (3, 5):
                status = mean("users.status", user.status)
                boss_chat = myChats[params['boss_id']]
                await client.send_message(
                    user_id,
                    f"{user.outname}，目前您的状态是<b>{status}</b>，暂时不能游戏，请与<a href='{link(boss_chat.id)}'>"
                    f"{boss_chat.outname}</a>联系",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True)
                return
            group_id = int(callback.data.split('_')[2])
            content = f"回复本消息向 <b>{games[group_id].outname}</b> 远程发红包\n格式：红包金额-中雷数字\n例如：100-2"
            await client.send_message(user_id, content, parse_mode=ParseMode.HTML, reply_markup=ForceReply())


    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复-页面
@Client.on_message(filters.reply)
async def reply_interface(client, message):
    try:
        user_id = message.from_user.id
        user = myUsers[user_id]
        re_msg = message.reply_to_message
        if re_msg.from_user is None or re_msg.from_user.username != clients.get_username(client):
            return
        # pre_re_msg = await client.get_messages(message.chat.id, reply_to_message_ids=re_msg.id, replies=1)
        pre_re_msg = await client.get_messages(message.chat.id, message_ids=re_msg.id, reply=True, replies=1)
        if pre_re_msg is not None:
            pre_content = pre_re_msg.text.html.split(line)[0]
        else:
            pre_content = ''
        now_time = datetime.datetime.now().strftime('%H:%M:%S')
        # 检查操作权限
        if user.status == 5:
            await message.delete()
            await re_msg.delete()
            status = mean("users.status", user.status)
            boss_chat = myChats[params['boss_id']]
            await client.send_message(
                user_id,
                f"{user.outname}，目前您的状态是<b>{status}</b>，暂时无法操作，请与<a href='{link(boss_chat.id)}'>"
                f"{boss_chat.outname}</a>联系",
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True)
            return
        # 充值 - 输入金额
        if re_msg.text == '回复本消息输入充值金额':
            input_rst = await input_cert(message.text, 'dp_amt', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                prompt = e_remind + f"您需要充值{input_rst[1]}U {now_time}"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True)
                dep = deposits.apply(user_id, input_rst[1])
                if uaccs.dp is not None and dep[0]:
                    await client.copy_message(user_id,
                                              params['my_channel'],
                                              uaccs.dp.msg_id,
                                              caption=dep[1],
                                              parse_mode=ParseMode.HTML
                                              )
                else:
                    await client.send_message(user_id,
                                              dep[1],
                                              parse_mode=ParseMode.HTML,
                                              disable_web_page_preview=True
                                              )
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True)
                await pre_re_msg.reply_text('回复本消息输入充值金额', quote=True, reply_markup=ForceReply())
        # 提现 - 输入金额
        elif re_msg.text == '回复本消息输入提现金额':
            input_rst = await input_cert(message.text, 'withdraw_amt', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                content = content_withdraw_amt(user, input_rst[1])
                prompt = e_remind + "请输入<b>资金密码</b>\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('withdraw_return'))
                await pre_re_msg.reply_text('回复本消息输入提现密码', quote=True, reply_markup=ForceReply())
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}"
                re_msg = await pre_re_msg.edit_text(pre_content + line + prompt,
                                                    parse_mode=ParseMode.HTML,
                                                    disable_web_page_preview=True,
                                                    reply_markup=get_imp('withdraw_return'))
                await re_msg.reply_text("回复本消息输入提现金额", quote=True, reply_markup=ForceReply())
        # 提现 - 资金密码
        elif re_msg.text == '回复本消息输入提现密码':
            input_rst = await input_cert(message.text, 'pwd', client, user_id)
            await message.delete()
            await re_msg.delete()
            # 检查是否有转账权限
            if user.status in (4, 5):
                status = mean("users.status", user.status)
                boss_chat = myChats[params['boss_id']]
                await client.send_message(
                    user_id,
                    f"{user.outname}，目前您的状态是<b>{status}</b>，暂时不能出金，请与<a href='{link(boss_chat.id)}'>"
                    f"{boss_chat.outname}</a>联系",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True)
                return
            # 符合放款条件
            if input_rst[0] and myPwd.check(user_id, input_rst[1]):
                match = re.match(f".*提现金额：(.*)U\n{e_money}.*", pre_re_msg.text, re.S)
                amt = int(match.group(1))
                if accs[user_id].avail >= amt and user.role != 3:
                    wd = withdraws.apply(user_id, amt)
                    prompt = line + e_remind + "#提现待审核"
                    msg = await client.send_message(params['log_id'],
                                                    content_withdraw_approve(wd) + prompt,
                                                    parse_mode=ParseMode.HTML,
                                                    disable_web_page_preview=True,
                                                    reply_markup=get_imp('withdraw_confirm')
                                                    )
                    wd.msg_id = msg.id
                    prompt = line + e_remind + "已提交审核，请稍等"
                    await pre_re_msg.edit_text(pre_content + prompt,
                                               parse_mode=ParseMode.HTML,
                                               disable_web_page_preview=True)
                elif accs[user_id].avail < amt:
                    content = content_withdraw(user)
                    prompt = e_remind + f"当前<b>余额不够</b>，无法提现 {now_time}\n"
                    await pre_re_msg.edit_text(content + line + prompt,
                                               parse_mode=ParseMode.HTML,
                                               disable_web_page_preview=True,
                                               reply_markup=get_imp('withdraw'))
                else:
                    content = content_withdraw(user)
                    prompt = e_remind + f"请联系<a href='t.me/bowin_boss'>管理员</a>咨询情况\n"
                    await pre_re_msg.edit_text(content + line + prompt,
                                               parse_mode=ParseMode.HTML,
                                               disable_web_page_preview=True)
                return
            elif input_rst[0]:
                prompt = e_remind + f"<b>密码输入错误</b>，请重新输入 {now_time}\n"
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            re_msg = await pre_re_msg.edit_text(pre_content + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                disable_web_page_preview=True,
                                                reply_markup=get_imp('withdraw_return'))
            await re_msg.reply_text('回复本消息输入提现密码', quote=True, reply_markup=ForceReply())
        # 转账 - 转入方ID
        elif re_msg.text == '回复本消息输入转入方ID':
            input_rst = await input_cert(message.text, 'receive_id', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                content = content_transfer_id(user, input_rst[1])
                prompt = e_remind + "请<b>确认转入方</b>后输入转账金额\n"
                reply_text = "回复本消息输入转账金额"
            else:
                content = content_transfer(user)
                prompt = e_remind + f"{input_rst[1]} {now_time}"
                reply_text = "回复本消息输入转入方ID"
            re_msg = await pre_re_msg.edit_text(content + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                disable_web_page_preview=True,
                                                reply_markup=get_imp('transfer_return'))
            await re_msg.reply_text(reply_text, quote=True, reply_markup=ForceReply())
        # 转账 - 金额
        elif re_msg.text == '回复本消息输入转账金额':
            input_rst = await input_cert(message.text, 'transfer_amt', client, user_id)
            await message.delete()
            await re_msg.delete()
            match = re.match(f".*转入方.*ID号：(.*){e_left3}.*", pre_re_msg.text, re.S)
            receive_id = int(match.group(1))
            if input_rst[0]:
                content = content_transfer_amt(user, myUsers[receive_id], input_rst[1])
                prompt = e_remind + "请<b>确认</b>是否转账\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('transfer_confirm'))
            else:
                content = content_transfer_id(user, myUsers[receive_id])
                prompt = e_remind + f"{input_rst[1]} {now_time}"
                re_msg = await pre_re_msg.edit_text(content + line + prompt,
                                                    parse_mode=ParseMode.HTML,
                                                    disable_web_page_preview=True,
                                                    reply_markup=get_imp('transfer_return'))
                await re_msg.reply_text("回复本消息输入转账金额", quote=True, reply_markup=ForceReply())
        # 转账 - 资金密码
        elif re_msg.text == '回复本消息输入资金密码':
            input_rst = await input_cert(message.text, 'pwd', client, user_id)
            await message.delete()
            await re_msg.delete()
            # 检查是否有转账权限
            if user.status in (4, 5):
                status = mean("users.status", user.status)
                boss_chat = myChats[params['boss_id']]
                await client.send_message(
                    user_id,
                    f"{user.outname}，目前您的状态是<b>{status}</b>，暂时不能出金，请与<a href='{link(boss_chat.id)}'>"
                    f"{boss_chat.outname}</a>联系",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True)
                return
            if input_rst[0] and myPwd.check(user_id, input_rst[1]):
                match = re.match(f".*转入方.*ID号：(.*){e_left3}.*转出金额：(.*)U\n{e_money}.*", pre_re_msg.text, re.S)
                receive_id = int(match.group(1))
                amt = int(match.group(2))
                if accs[user_id].avail >= amt and user.role != 3 and myUsers[receive_id].role != 3:
                    # 使用issue来记录账变对
                    issue = str(round(time.time() * 10000))
                    # 变更账户表和账变表 - 转账转出
                    trans.transact(user_id, 8, amt, issue=issue, tag=str(receive_id))
                    # 变更账户表和账变表 - 转账转出
                    trans.transact(receive_id, 7, amt, issue=issue, tag=str(user_id))
                    content = content_transfer_finish(user_id, receive_id, amt, issue)
                    try:
                        prompt = e_remind + "恭喜，您收到一笔转账！\n"
                        await client.send_message(receive_id,
                                                  content + line + prompt,
                                                  parse_mode=ParseMode.HTML,
                                                  disable_web_page_preview=True,
                                                  disable_notification=False,
                                                  reply_markup=get_imp('transfer_finish'))
                        prompt = e_remind + "放心，转账结果已通知对方！\n"
                    except Exception as e:
                        prompt = e_remind + "对方未与博盈助理沟通过，无法通知对方\n请转发此消息给对方，并提醒他与<b><a " \
                                            "href='t.me/bowin_asstbot'>博盈助理</a></b>沟通\n"
                    await pre_re_msg.edit_text(content + line + prompt,
                                               parse_mode=ParseMode.HTML,
                                               disable_web_page_preview=True,
                                               reply_markup=get_imp('transfer_finish'))
                elif accs[user_id].avail < amt:
                    content = content_transfer_amt(user, myUsers[receive_id], amt)
                    prompt = e_remind + f"当前<b>余额不够</b>，无法转账 {now_time}\n"
                    await pre_re_msg.edit_text(content + line + prompt,
                                               parse_mode=ParseMode.HTML,
                                               disable_web_page_preview=True,
                                               reply_markup=get_imp('transfer_confirm'))
                else:
                    content = content_transfer_not_finish(user_id, receive_id, amt)
                    prompt = e_remind + f"请联系<a href='t.me/bowin_boss'>管理员</a>咨询情况\n"
                    await pre_re_msg.edit_text(content + line + prompt,
                                               parse_mode=ParseMode.HTML,
                                               disable_web_page_preview=True)
                return
            elif input_rst[0]:
                prompt = e_remind + f"<b>密码输入错误</b>，请重新输入 {now_time}\n"
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            content = pre_re_msg.text.html.split(line)[0]
            re_msg = await pre_re_msg.edit_text(content + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                disable_web_page_preview=True,
                                                reply_markup=get_imp('transfer_return'))
            await re_msg.reply_text('回复本消息输入资金密码', quote=True, reply_markup=ForceReply())
        # 我的 - 邮箱/新邮箱
        elif re_msg.text in ('回复本消息输入邮箱', '回复本消息输入新邮箱'):
            input_rst = await input_cert(message.text, 'email', client, user_id)
            if input_rst[0]:
                myUsers[user_id] = ('email', input_rst[1])
                prompt = e_remind + "<b>已配置</b>邮箱\n"
                await send_to_ws(f"助理|秘书|更新myUsers|{user_id}")
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            user = myUsers[user_id]
            await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('mine', user))
            await message.delete()
            await re_msg.delete()
        # 我的 - 旧邮箱
        elif re_msg.text == '回复本消息输入旧邮箱':
            input_rst = await input_cert(message.text, 'email', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0] and user.email == input_rst[1]:
                prompt = e_remind + "旧邮箱<b>验证成功</b>，请输入<b>新邮箱</b>\n"
            else:
                prompt = e_remind + "旧邮箱<b>验证失败</b>，请重新操作\n"
            re_msg = await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                reply_markup=get_imp('mine', user))
            if input_rst[0] and user.email == input_rst[1]:
                await re_msg.reply_text('回复本消息输入新邮箱', quote=True, reply_markup=ForceReply())
        # 我的 - 密码/新密码
        elif re_msg.text in ('回复本消息输入密码', '回复本消息输入新密码'):
            input_rst = await input_cert(message.text, 'pwd', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                myPwd[user_id] = input_rst[1]
                prompt = e_remind + "<b>已配置</b>密码\n"
                await send_to_ws(f"助理|秘书|更新myUsers|{user_id}")
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('mine', user))
        # 我的 - 旧密码
        elif re_msg.text == '回复本消息输入旧密码':
            input_rst = await input_cert(message.text, 'pwd', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0] and myPwd.check(user_id, input_rst[1]):
                prompt = e_remind + "旧密码<b>验证成功</b>，请输入<b>新密码</b>\n"
            elif input_rst[0]:
                prompt = e_remind + f"旧密码<b>验证失败</b> {now_time}\n"
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            re_msg = await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                reply_markup=get_imp('mine', user))
            if input_rst[0] and myPwd.check(user_id, input_rst[1]):
                await re_msg.reply_text('回复本消息输入新密码', quote=True, reply_markup=ForceReply())
        # 我的 - 验证邮箱
        elif re_msg.text == '回复本消息输入验证邮箱':
            input_rst = await input_cert(message.text, 'email', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0] and user.email == input_rst[1]:
                prompt = e_remind + "邮箱<b>验证成功</b>，请输入<b>新密码</b>\n"
            elif input_rst[0]:
                prompt = e_remind + f"邮箱<b>验证失败</b> {now_time}\n"
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            re_msg = await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                reply_markup=get_imp('mine', user))
            if input_rst[0] and user.email == input_rst[1]:
                await re_msg.reply_text('回复本消息输入新密码', quote=True, reply_markup=ForceReply())
        # 我的 - USDT地址/新USDT地址
        elif re_msg.text in ('回复本消息输入USDT地址', '回复本消息输入新USDT地址'):
            input_rst = await input_cert(message.text, 'usdt', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                myUsers[user_id] = ('usdt', input_rst[1])
                prompt = e_remind + "<b>已配置</b>USDT地址\n"
                await send_to_ws(f"助理|秘书|更新myUsers|{user_id}")
            else:
                prompt = e_remind + input_rst[1] + "\n"
            await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       reply_markup=get_imp('mine', user))
        # 我的 - 验证密码
        elif re_msg.text == '回复本消息输入验证密码':
            input_rst = await input_cert(message.text, 'pwd', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0] and myPwd.check(user_id, input_rst[1]):
                prompt = e_remind + "密码<b>验证成功</b>，请输入<b>新USDT(trc20)地址</b>\n"
            elif input_rst[0]:
                prompt = e_remind + f"密码<b>验证失败</b> {now_time}\n"
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
            re_msg = await pre_re_msg.edit_text(content_mine(user) + line + prompt,
                                                parse_mode=ParseMode.HTML,
                                                reply_markup=get_imp('mine', user))
            if input_rst[0] and myPwd.check(user_id, input_rst[1]):
                await re_msg.reply_text('回复本消息输入新USDT地址', quote=True, reply_markup=ForceReply())
        # 抢红包 - 发包 - 金额
        elif re.match('^回复本消息向 .* 远程发红包.*$', re_msg.text, re.S) is not None:
            if re.match('^\d{1,4}[/-]\d$', message.text, re.S) is None:
                await client.send_message(user_id, "您输入的格式不正确，请重新输入")
            else:
                match = re.match('^回复本消息向 (.*) 远程发红包.*$', re_msg.text, re.S)
                outname = match.group(1)
                my_game = games.outname(outname)
                if my_game is None:
                    await client.send_message(user_id, "找不到该红包群，请再次确认")
                else:
                    await send_to_ws(f"助理|发包员|远程发包|{user_id}|{message.text}|{my_game.group_id}")
                await message.delete()
                await re_msg.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 收到图片信息
@Client.on_message(filters.incoming & filters.photo & filters.private)
async def receive_pic(client, message):
    try:
        user_id = message.from_user.id
        pre_msg = await client.get_messages(user_id, message.id - 1)
        if (pre_msg.text is not None and pre_msg.text.startswith("请发支付后截图")) or (
                pre_msg.caption is not None and pre_msg.caption.startswith("充值地址(TRC20)：")):
            dep = deposits.last(user_id)
            # 检查近期是否有充值记录
            if dep is None:
                await message.reply(f"您在<b>15分钟内没有</b>申请充值记录，若已充值，请联系客服 @bowin_boss 确认", quote=False,
                                    parse_mode=ParseMode.HTML,
                                    disable_web_page_preview=True)
                return
            await message.reply(f"{e_help}请稍等，我们会尽快为您确认充值情况", quote=False, parse_mode=ParseMode.HTML,
                                disable_web_page_preview=True)
            # 更新充值状态为确认
            dep.status = 1
            # 发送截图到账单群
            prompt = line + e_remind + f"客户申请充值确认"
            msg = await message.copy(
                params['bill_id'],
                caption=f"C{dep.id} <code>{myUsers[dep.chat_id].id:05}</code> {e_to} <code>{dep.uacc_name}</code>\n"
                        f"{dep.amt_require:,.2f}U {dep.created_at}\n" + prompt,
                parse_mode=ParseMode.HTML,
                reply_markup=get_imp('deposit', dep.id)
                )
            dep.msg_id = msg.id
            # 启动充值检查监控
            monits['DEPCHECK'].init_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            monits['DEPCHECK'].status = '1'
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 处理 WebSocket 消息
@websocket_handler(receiver='助理', module_name="bowin_asstbot")
async def get_websocket_message(sender, receiver, content):
    try:
        print_both(f"助理接收到WebSocket信息 {sender}, {receiver}, {content}")
        command = content.split('|')
        if command[0] == '发消息':
            # 发消息|user_id|content    如：发消息|1914064729|我是消息
            (user_id, content) = command[1:]
            try:
                await clients['bowin_asstbot'].send_message(user_id, content, parse_mode=ParseMode.HTML,
                                                            disable_web_page_preview=True)
            except (Forbidden, BadRequest) as e:
                myUsers[user_id] = ('is_remind', 0)
        elif command[0] == '更新myUsers':
            # 更新myUsers|chat_id
            _ = myUsers[int(command[1]), True]
        elif command[0] == '更新uaccs':
            # 更新uaccs
            uaccs.reload()
        elif command[0] == '更新games':
            # 更新games|group_id
            _ = games[int(command[1]), True]
        elif command[0] == '发起充值':
            prompt = line + e_remind + "请输入需要充值的金额\n"
            re_msg = await clients['bowin_asstbot'].send_message(int(command[1]),
                                                                 dp_desc + prompt,
                                                                 parse_mode=ParseMode.HTML,
                                                                 disable_web_page_preview=True
                                                                 )
            await re_msg.reply_text('回复本消息输入充值金额', quote=True, reply_markup=ForceReply())
        elif command[0] == '群组介绍':
            (user_id, group_id) = command[1:]
            game = games[int(group_id)]
            content = f"群组<b>{game.outname}</b>的介绍如下：\n{game.introduce}"
            await clients['bowin_asstbot'].send_message(int(user_id),
                                                        content,
                                                        parse_mode=ParseMode.HTML,
                                                        disable_web_page_preview=True
                                                        )



    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# test收到信息
@Client.on_message()
async def test_on_message(client, message):
    try:
        # print(message.text)
        print(f"asst_bot 收到 {myChats[message.from_user.id].outname} 的消息")
        sql_str = "select * from users where created_at > '2024-12-06'"
        rst = read_connector.run(sql_str)
        for row in rst:
            _ = accs[row[0]]

        # sql_str = "select user_id from (select * from chat_user where chat_id=-1002093645173) a left join users b on " \
        #           "a.user_id=b.chat_id where b.chat_id is null limit 1000"
        # rst = read_connector.run(sql_str)
        # for row in rst:
        #     _ = myUsers[row[0]]
        # print(message)
        # await message.forward(params['carter_id'])
        # input = message.text.split(' ')
        # print(input)
        # await message.reply("我要发红包！", quote=False)
        # await client.send_message(int(message.text),
        #                           text='1')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')

