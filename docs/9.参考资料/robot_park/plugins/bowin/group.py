# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from plugins.bowin.common import *


# 新增群成员时，定义成员归属上级
@Client.on_chat_member_updated(filters.group & filters.new_chat_members)
async def add_chat_member(client, updated):
    try:
        # 需要在有效群组中，才能定义账号归属
        if not games.is_effected(updated.chat.id):
            return
        if updated.new_chat_member is not None:
            invited_id = updated.new_chat_member.user.id
            if updated.invite_link is not None and updated.invite_link.name is not None and \
                    updated.invite_link.name.isdigit() and updated.invite_link.creator is not None:
                if updated.invite_link.creator.username == 'bowin_asstbot':
                    chat_id = int(updated.invite_link.name)
                else:
                    chat_id = updated.invite_link.creator.id
            elif updated.from_user.id != invited_id:
                chat_id = updated.from_user.id
            else:
                return
            group_id = updated.chat.id
            myLinks.invite(chat_id, group_id, invited_id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')







