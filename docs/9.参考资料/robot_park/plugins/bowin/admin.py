# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'

from plugins.bowin.common import *


# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        if index == "admin":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_set + "配置", callback_data="set"),
                 InlineKeyboardButton(e_guests + "客户", callback_data="guest"),
                 InlineKeyboardButton(e_search + "查客户", callback_data="guest_search")],
                [InlineKeyboardButton(e_baobiao + "财报", callback_data="report"),
                 InlineKeyboardButton(e_game + "群组", callback_data="group_list"),
                 InlineKeyboardButton(e_search + "查群组", callback_data="group_search")]
                ])
        # 返回
        elif index == "return":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='return')]
                ])
        # 配置
        elif index == "set":
            if monits['AISENT'].status == '1':
                button = InlineKeyboardButton(e_gou + "AI红包", callback_data="set_noaibag")
            else:
                button = InlineKeyboardButton(e_no + "AI红包", callback_data="set_aibag")
            return InlineKeyboardMarkup([
                [button,
                 InlineKeyboardButton(e_pinlv + "发包频率", callback_data="set_sentfreq"),
                 InlineKeyboardButton(e_money + "发包金额", callback_data="set_sentamt")],
                [InlineKeyboardButton(e_time4 + "抢包频率", callback_data="set_getfreq"),
                 InlineKeyboardButton(e_endtime + "抢包时长", callback_data="set_getend"),
                 InlineKeyboardButton(e_bankcard + "资金管理", callback_data="set_uacc")],
                [InlineKeyboardButton(e_return + "返回", callback_data='return')]
                ])
        # 配置 - 资金管理
        elif index == "set_uacc":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_inbox + "收款账号", callback_data="set_uacc_dp"),
                 InlineKeyboardButton(e_outbox + "付款账号", callback_data="set_uacc_wd")],
                [InlineKeyboardButton(e_cycle + "刷新余额", callback_data="set_uacc_bal"),
                 InlineKeyboardButton(e_return + "返回", callback_data="set_uacc_return")]
                ])
        # 配置 - 资金管理 - 收款账号
        elif index == "set_uacc_dp":
            button = []
            chosed = 0
            for row in uaccs.all():
                uacc = TUaccs(row)
                if uacc.is_dp:
                    button.append(InlineKeyboardButton(e_gou + uacc.name, callback_data=f"set_uacc_dp_{uacc.name}"))
                    chosed = 1
                else:
                    button.append(InlineKeyboardButton(uacc.name, callback_data=f"set_uacc_dp_{uacc.name}"))
            if chosed:
                button.append(InlineKeyboardButton('无', callback_data=f"set_uacc_dp_no"))
            else:
                button.append(InlineKeyboardButton(e_gou + '无', callback_data=f"set_uacc_dp_no"))
            button.append(InlineKeyboardButton(e_return + "返回", callback_data="set_uacc_dp_return"))
            return InlineKeyboardMarkup([button[i:i+3] for i in range(0, len(button), 3)])
        # 配置 - 资金管理 - 付款账号
        elif index == "set_uacc_wd":
            button = []
            chosed = 0
            for row in uaccs.all():
                uacc = TUaccs(row)
                if uacc.is_wd:
                    button.append(InlineKeyboardButton(e_gou + uacc.name, callback_data=f"set_uacc_wd_{uacc.name}"))
                    chosed = 1
                else:
                    button.append(InlineKeyboardButton(uacc.name, callback_data=f"set_uacc_wd_{uacc.name}"))
            if chosed:
                button.append(InlineKeyboardButton('无', callback_data=f"set_uacc_wd_no"))
            else:
                button.append(InlineKeyboardButton(e_gou + '无', callback_data=f"set_uacc_wd_no"))
            button.append(InlineKeyboardButton(e_return + "返回", callback_data="set_uacc_wd_return"))
            return InlineKeyboardMarkup([button[i:i+3] for i in range(0, len(button), 3)])
        # 配置 - 返回
        elif index == "set_return":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='set_return')]
                ])
        # 客户 - 返回
        elif index == "guest_return":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data=f"guest_return_{param}")]
                ])
        # 客户 - 报表
        elif index == "guest_report":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_date + "当天", callback_data="guest_report_当天"),
                 InlineKeyboardButton(e_date + "昨天", callback_data="guest_report_昨天"),
                 InlineKeyboardButton(e_date + "前天", callback_data="guest_report_前天")],
                [InlineKeyboardButton(e_month + "本月", callback_data="guest_report_本月"),
                 InlineKeyboardButton(e_month + "上月", callback_data="guest_report_上月"),
                 InlineKeyboardButton(e_month + "上2月", callback_data="guest_report_上2月")],
                [InlineKeyboardButton(e_date2 + "自选", callback_data="guest_report_自选"),
                 InlineKeyboardButton(e_return + "返回", callback_data="guest_report_return")]
                ])
        # 客户 - 报表 - 返回
        elif index == "guest_report_自选_return":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data='guest_report_自选_return')]
                ])
        # 客户 - 详细
        elif index == "guest_detail":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_ace2 + "上级", callback_data=f"guest_detail_parent_{param}"),
                 InlineKeyboardButton(e_dna + "角色", callback_data=f"guest_detail_role_{param}"),
                 InlineKeyboardButton(e_alert + "提醒", callback_data=f"guest_detail_remind_{param}")],
                [InlineKeyboardButton(e_key + "密码", callback_data=f"guest_detail_pwd_{param}"),
                 InlineKeyboardButton(e_msg + "邮箱", callback_data=f"guest_detail_email_{param}"),
                 InlineKeyboardButton(e_money2 + "USDT", callback_data=f"guest_detail_usdt_{param}"),
                 InlineKeyboardButton(e_return + "返回", callback_data=f"guest_detail_return_{param}")],
                ])
        # 客户 - 详细 - 角色
        elif index == "guest_detail_role":
            emo_list = [e_circle] * 4
            emo_list[myUsers[param].role] = e_gou
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(emo_list[0] + mean('users.role', 0), callback_data=f"guest_detail_role_{param}_0"),
                 InlineKeyboardButton(emo_list[1] + mean('users.role', 1), callback_data=f"guest_detail_role_{param}_1")],
                [InlineKeyboardButton(emo_list[2] + mean('users.role', 2), callback_data=f"guest_detail_role_{param}_2"),
                 InlineKeyboardButton(emo_list[3] + mean('users.role', 3), callback_data=f"guest_detail_role_{param}_3")]
                ])
        # 客户 - 详细 - 提醒
        elif index == "guest_detail_remind":
            if myUsers[param].is_remind:
                first_button = InlineKeyboardButton(e_no + "拒绝提醒", callback_data=f"guest_detail_remind_{param}_no")
            else:
                first_button = InlineKeyboardButton(e_gou + "接受提醒", callback_data=f"guest_detail_remind_{param}_yes")
            return InlineKeyboardMarkup([
                    [first_button,
                     InlineKeyboardButton(e_return + "返回", callback_data=f"guest_detail_remind_{param}_return")]
                    ])
        # 客户 - 详细 - 密码
        elif index == "guest_detail_pwd":
            return InlineKeyboardMarkup([
                    [InlineKeyboardButton(e_cycle + "重置密码", callback_data=f"guest_detail_pwd_{param}_reset"),
                     InlineKeyboardButton(e_return + "返回", callback_data=f"guest_detail_pwd_{param}_return")]
                    ])
        # 客户 - 详细 - 邮箱
        elif index == "guest_detail_email":
            return InlineKeyboardMarkup([
                    [InlineKeyboardButton(e_cycle + "重置邮箱", callback_data=f"guest_detail_email_{param}_reset"),
                     InlineKeyboardButton(e_return + "返回", callback_data=f"guest_detail_email_{param}_return")]
                    ])
        # 客户 - 详细 - USDT
        elif index == "guest_detail_usdt":
            return InlineKeyboardMarkup([
                    [InlineKeyboardButton(e_cycle + "重置USDT", callback_data=f"guest_detail_usdt_{param}_reset"),
                     InlineKeyboardButton(e_return + "返回", callback_data=f"guest_detail_usdt_{param}_return")]
                    ])
        # 客户 - 详细 - 上级 - 返回
        elif index == "guest_detail_parent_return":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data=f"guest_detail_parent_{param}_return")]
                ])
        # 客户 - 状态
        elif index == "guest_status":
            emo_list = [e_circle] * 6
            emo_list[myUsers[param].status] = e_gou
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(emo_list[0] + mean('users.status', 0), callback_data=f"guest_status_{param}_0"),
                 InlineKeyboardButton(emo_list[1] + mean('users.status', 1), callback_data=f"guest_status_{param}_1"),
                 InlineKeyboardButton(emo_list[2] + mean('users.status', 2), callback_data=f"guest_status_{param}_2")],
                [InlineKeyboardButton(emo_list[3] + mean('users.status', 3), callback_data=f"guest_status_{param}_3"),
                 InlineKeyboardButton(emo_list[4] + mean('users.status', 4), callback_data=f"guest_status_{param}_4"),
                 InlineKeyboardButton(emo_list[5] + mean('users.status', 5), callback_data=f"guest_status_{param}_5")]
                ])
        # 群组
        elif index == 'group':
            btn_num = ['group_0'] * 10
            i = 0
            for row in param:
                btn_num[i] = f"group_{row[0]}"
                i += 1
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_num[1], callback_data=btn_num[0]),
                 InlineKeyboardButton(e_num[2], callback_data=btn_num[1]),
                 InlineKeyboardButton(e_num[3], callback_data=btn_num[2]),
                 InlineKeyboardButton(e_num[4], callback_data=btn_num[3]),
                 InlineKeyboardButton(e_num[5], callback_data=btn_num[4])],
                [InlineKeyboardButton(e_num[6], callback_data=btn_num[5]),
                 InlineKeyboardButton(e_num[7], callback_data=btn_num[6]),
                 InlineKeyboardButton(e_num[8], callback_data=btn_num[7]),
                 InlineKeyboardButton(e_num[9], callback_data=btn_num[8]),
                 InlineKeyboardButton(e_num[10], callback_data=btn_num[9])],
                [InlineKeyboardButton("上页", callback_data='group_PrePage'),
                 InlineKeyboardButton("新建", callback_data='group_new'),
                 InlineKeyboardButton("返回", callback_data='return'),
                 InlineKeyboardButton("下页", callback_data='group_NextPage')]
                ])
        # 群组 - 新增
        elif index == 'group_new':
            mk_list = [[InlineKeyboardButton("返回", callback_data='group_return')]]
            for row in param:
                mk_list.append([InlineKeyboardButton(myChats[row[0]].outname, callback_data=f"group_new_{row[0]}")])
            return InlineKeyboardMarkup(mk_list)
        # 群组 - 详细
        elif index == 'group_detail':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_ace1 + "群主", callback_data='group_owner'),
                 InlineKeyboardButton(e_detail + "介绍", callback_data='group_introduce'),
                 InlineKeyboardButton(e_cty0 + "语言", callback_data='group_language')],
                [InlineKeyboardButton(e_pinlv + "系统抽佣", callback_data='group_sysrate'),
                 InlineKeyboardButton(e_pinlv + "群主抽佣", callback_data='group_ownerrate'),
                 InlineKeyboardButton(e_pinlv + "上级抽佣", callback_data='group_parentrate')],
                [InlineKeyboardButton(e_bank + "租金金额", callback_data='group_rentamt'),
                 InlineKeyboardButton(e_time1 + "租金时间", callback_data='group_rentdate'),
                 InlineKeyboardButton(e_traffic + "状态", callback_data='group_status')]])
        # 群组 - 详细 - 上级 - 返回
        elif index == "group_detail_return":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_return + "返回", callback_data=f"group_detail_return_{param}"),]
                ])
        # 群组 - 详细 - 语言
        elif index == "group_language":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_cty2 + "中文", callback_data=f"group_language_0"),
                 InlineKeyboardButton(e_cty6 + "英文", callback_data=f"group_language_1"),
                 InlineKeyboardButton(e_return + "返回", callback_data=f"group_language_return")]
                ])
        # 群组 - 详细 - 状态
        elif index == "group_status":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton("未启动", callback_data=f"group_status_0"),
                 InlineKeyboardButton("运营中", callback_data=f"group_status_1"),
                 InlineKeyboardButton("待收租", callback_data=f"group_status_2")],
                [InlineKeyboardButton("暂停", callback_data=f"group_status_3"),
                 InlineKeyboardButton("关闭", callback_data=f"group_status_4"),
                 InlineKeyboardButton("返回", callback_data=f"group_language_return")]
                ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面
@Client.on_callback_query()
async def callback_interface(client, callback):
    try:
        pre_content = callback.message.text.html.split(line)[0]
        now_time = datetime.datetime.now().strftime('%H:%M:%S')
        # 返回
        if callback.data == "return":
            content = "您好！我是您的秘书\n"
            prompt = line + e_remind + "请选择您需要的操作\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("admin"))
        # 配置 / 配置 - 返回
        elif callback.data in ("set", "set_return"):
            prompt = line + e_remind + "请选择您需要的配置\n"
            await callback.edit_message_text(content_set() + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("set"))
        # 配置 - 关闭AI红包
        elif callback.data == "set_noaibag":
            # monits['AISENT'] = ("status", '0')
            # monits['AIGET'] = ("status", '0')
            monits['AISENT'].status = '0'
            monits['AIGET'].status = '0'
            prompt = line + e_remind + f"<b>已关闭</b>AI红包 {now_time}\n"
            await callback.edit_message_text(content_set() + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("set"))
        # 配置 - 启动AI红包
        elif callback.data == "set_aibag":
            # monits['AISENT'] = ("status", '1')
            # monits['AIGET'] = ("status", '1')
            monits['AISENT'].status = '1'
            monits['AIGET'].status = '1'
            prompt = line + e_remind + f"<b>已启动</b>AI红包 {now_time}\n"
            await callback.edit_message_text(content_set() + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp("set"))
        # 配置 - 最低发包频率
        elif callback.data == "set_sentfreq":
            prompt = line + e_remind + "请输入<b>最低发包频率</b>\n"
            re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('set_return'))
            await re_msg.reply_text('回复本消息输入最低发包频率', quote=True, reply_markup=ForceReply())
        # 配置 - 最高发包金额
        elif callback.data == "set_sentamt":
            prompt = line + e_remind + "请输入<b>最高发包金额</b>\n"
            re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('set_return'))
            await re_msg.reply_text('回复本消息输入最高发包金额', quote=True, reply_markup=ForceReply())
        # 配置 - 最低抢包频率
        elif callback.data == "set_getfreq":
            prompt = line + e_remind + "请输入<b>最低抢包频率</b>\n"
            re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('set_return'))
            await re_msg.reply_text('回复本消息输入最低抢包频率', quote=True, reply_markup=ForceReply())
        # 配置 - 结束抢包时长
        elif callback.data == "set_getend":
            prompt = line + e_remind + "请输入<b>结束抢包时长</b>\n"
            re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('set_return'))
            await re_msg.reply_text('回复本消息输入结束抢包时长', quote=True, reply_markup=ForceReply())
        # 配置 - 资金管理
        elif callback.data == "set_uacc":
            prompt = line + e_remind + "请选择需要的操作\n"
            await callback.edit_message_text(content_set_uacc() + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('set_uacc'))
        # 配置 - 资金管理 - 收款账号
        elif callback.data == "set_uacc_dp":
            prompt = line + e_remind + "请选择<b>收款账号</b>\n"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('set_uacc_dp'))
        # 配置 - 资金管理 - 付款账号
        elif callback.data == "set_uacc_wd":
            prompt = line + e_remind + "请选择<b>付款账号</b>\n"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('set_uacc_wd'))
        # 配置 - 资金管理 - 收款账号/付款账号 - 选择
        elif re.match('^set_uacc_[dpw]{2}_[a-z]{2,}', callback.data, re.S) is not None:
            opt = callback.data.split('_')[2]
            chosed = callback.data.split('_')[3]
            if chosed == "return":
                prompt = line + e_remind + "请选择需要的操作\n"
                await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                 reply_markup=get_imp('set_uacc'))
                return
            if opt == 'dp':
                if chosed != "no":
                    uaccs[chosed].is_dp = 1
                elif chosed == "no" and uaccs.dp is not None:
                    uaccs.dp.is_dp = 0
                prompt = line + e_remind + f"收款账号选择了<b>{chosed}</b>\n"
            else:
                if chosed != "no":
                    uaccs[chosed].is_wd = 1
                elif chosed == "no" and uaccs.wd is not None:
                    uaccs.wd.is_wd = 0
                prompt = line + e_remind + f"提现账号选择了<b>{chosed}</b>\n"
            await callback.edit_message_text(content_set_uacc() + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('set_uacc'))
            uaccs.reload()
            await send_to_ws("秘书|助理|更新uaccs")
        # 配置 - 资金管理 - 刷新余额
        elif callback.data == "set_uacc_bal":
            try:
                for row in uaccs.all():
                    uacc = TUaccs(row)
                    okx = OKX(uacc.name)
                    avail = await okx.avail
                    uacc.balance = avail
            except Exception as e:
                sys_log.write_log(traceback.format_exc(), 'a')
            prompt = line + e_remind + f"余额已刷新 {now_time}\n"
            await callback.edit_message_text(content_set_uacc() + prompt, parse_mode=ParseMode.HTML,
                                             reply_markup=get_imp('set_uacc'))
        # 配置 - 资金管理 - 返回
        elif callback.data == "set_uacc_return":
            prompt = line + e_remind + "请选择您需要的配置\n"
            await callback.edit_message_text(content_set() + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True, reply_markup=get_imp("set"))
        # 查客户
        elif callback.data == "guest_search":
            prompt = line + e_remind + "请输入你要查询的客户信息\n"
            re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                      reply_markup=get_imp('return'))
            await re_msg.reply_text("回复本消息查询客户", quote=True, reply_markup=ForceReply())
        # 客户 - 返回
        elif re.match('^guest_return_\d{6,14}', callback.data, re.S) is not None:
            chat_id = int(callback.data.split('_')[2])
            prompt = line + e_remind + "请选择您需要的操作\n"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True, reply_markup=get_com_imp("guest", chat_id))
        # 客户 - 报表
        elif re.match('^guest_report_\d{6,14}', callback.data, re.S) is not None:
            chat_id = int(callback.data.split('_')[2])
            prompt = line + e_remind + f"更新时间 {now_time}\n"
            await callback.edit_message_text(content_report(myUsers[chat_id]) + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True, reply_markup=get_imp('guest_report'))
        # 客户 - 报表 - 返回
        elif callback.data == "guest_report_return":
            match = re.match(f'.*\nID号：<code>(.*)</code>{e_left3}.*', pre_content, re.S)
            chat_id = int(match.group(1))
            prompt = line + e_remind + "请选择您需要的操作\n"
            await callback.edit_message_text(content_guest(myUsers[chat_id]) + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True, reply_markup=get_com_imp("guest", chat_id))
        # 客户 - 报表 - 自选 - 返回
        elif callback.data == "guest_report_自选_return":
            prompt = line + e_remind + f"更新时间 {now_time}\n"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True, reply_markup=get_imp("guest_report"))
        # 客户 - 报表 - 选时间
        elif re.match('^guest_report_.*', callback.data, re.S) is not None:
            type = callback.data.split('_')[2]
            if type != "自选":
                match = re.match(f'.*\nID号：<code>(.*)</code>{e_left3}.*', pre_content, re.S)
                chat_id = int(match.group(1))
                content = content_report(myUsers[chat_id], type)
                prompt = line + e_remind + f"更新时间 {now_time}\n"
                await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True, reply_markup=get_imp('guest_report'))
            else:
                prompt = line + e_remind + "请输入自选时间段 格式：yyyy-mm-dd yyyy-mm-dd\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("guest_report_自选_return"))
                await re_msg.reply_text("回复本消息输入自选时间段", quote=True, reply_markup=ForceReply())
        # 客户 - 详细
        elif re.match('^guest_detail_\d{6,14}$', callback.data, re.S) is not None:
            chat_id = int(callback.data.split('_')[2])
            prompt = line + e_remind + f"请修改详细信息\n"
            await callback.edit_message_text(content_guest_detail(myUsers[chat_id]) + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('guest_detail', chat_id))
        # 客户 - 详细 - 按钮
        elif re.match('^guest_detail_[a-z]+_\d{6,14}', callback.data, re.S) is not None:
            match = re.match('^guest_detail_([a-z]+)_(\d{6,14})', callback.data, re.S)
            command = match.group(1)
            chat_id = int(match.group(2))
            # 客户 - 详细 - 上级 - 返回
            if re.match('^guest_detail_parent_\d{6,14}_return', callback.data, re.S) is not None:
                prompt = line + e_remind + "请选择您需要的操作\n"
                await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("guest_detail", chat_id))
            # 上级
            elif command == "parent":
                if callback.from_user.id != params['carter_id']:
                    prompt = line + e_remind + f"此功能请让 <b>Carter</b> 操作 {now_time}\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp('guest_detail', chat_id))
                else:
                    prompt = line + e_remind + "请输入新上级\n"
                    re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                              disable_web_page_preview=True,
                                                              reply_markup=get_imp("guest_detail_parent_return",
                                                                                   chat_id))
                    await re_msg.reply_text("回复本消息输入新上级", quote=True, reply_markup=ForceReply())
            # 客户 - 详细 - 角色 - chat_id - 按钮
            elif re.match('^guest_detail_role_\d{6,14}_\d$', callback.data, re.S) is not None:
                idx = int(callback.data.split('_')[4])
                user = myUsers.set_role(chat_id, idx)
                role = mean("users.role", idx)
                prompt = line + e_remind + f"你已修改角色为 <b>{role}</b>\n"
                await callback.edit_message_text(content_guest_detail(user) + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("guest_detail", chat_id))
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
                await send_to_ws(f"秘书|发包员|更新myUsers|{chat_id}")
                await client.send_message(
                    params['log_id'],
                    f"客户 {user.outname}(<code>{user.id:05}</code>/<code>{user.chat_id}</code>) 角色修改为 <b>{role}</b>",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                    )
            # 角色
            elif command == "role":
                if callback.from_user.id != params['carter_id']:
                    prompt = line + e_remind + f"此功能请让 <b>Carter</b> 操作 {now_time}\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp('guest_detail', chat_id))
                else:
                    prompt = line + e_remind + "请选择角色\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp("guest_detail_role", chat_id))
            # 客户 - 详细 - 多个操作 - chat_id - 返回
            elif re.match('^guest_detail_[a-z]+_\d{6,14}_return$', callback.data, re.S) is not None:
                prompt = line + e_remind + "请选择您需要的操作\n"
                await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("guest_detail", chat_id))
            # 客户 - 详细 - 提醒 - chat_id - 接受
            elif re.match('^guest_detail_remind_\d{6,14}_yes$', callback.data, re.S) is not None:
                prompt = line + e_remind + "已设置为<b>接受提醒</b>状态\n"
                myUsers[chat_id] = ('is_remind', 1)
                await callback.edit_message_text(content_guest_detail(myUsers[chat_id]) + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp('guest_detail', chat_id))
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
            # 客户 - 详细 - 提醒 - chat_id - 拒绝
            elif re.match('^guest_detail_remind_\d{6,14}_no$', callback.data, re.S) is not None:
                prompt = line + e_remind + "已设置为<b>拒绝提醒</b>状态\n"
                myUsers[chat_id] = ('is_remind', 0)
                await callback.edit_message_text(content_guest_detail(myUsers[chat_id]) + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp('guest_detail', chat_id))
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
            # 提醒
            elif command == "remind":
                prompt = line + e_remind + "是否需要调整<b>提醒状态</b>\n"
                await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("guest_detail_remind", chat_id))
            # 客户 - 详细 - 密码 - chat_id - 重置
            elif re.match('^guest_detail_pwd_\d{6,14}_reset$', callback.data, re.S) is not None:
                prompt = line + e_remind + "已经<b>重置密码</b>\n"
                myUsers[chat_id] = ('fund_pwd', '')
                user = myUsers[chat_id]
                await callback.edit_message_text(content_guest_detail(user) + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp('guest_detail', chat_id))
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
                if accs[chat_id].avail > 0:
                    head_content = f"#提醒 客户密码已重置，<b>余额为 {accs[chat_id].avail:,.2f}U</b>\n"
                else:
                    head_content = f"客户密码已重置\n"
                await client.send_message(
                    params['log_id'],
                    f"{head_content}客户：{user.outname}(<code>{user.id:05}</code>/<code>{user.chat_id}</code>)\n"
                    f"操作员：{myUsers[callback.from_user.id].outname}",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                    )
            # 密码
            elif command == "pwd":
                if callback.from_user.id == params['boss_id']:
                    prompt = line + e_remind + f"此功能请使用 <b>个人账号</b> 操作 {now_time}\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp('guest_detail', chat_id))
                else:
                    prompt = line + e_remind + "是否需要<b>重置密码</b>\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp("guest_detail_pwd", chat_id))

            # 客户 - 详细 - 邮箱 - chat_id - 重置
            elif re.match('^guest_detail_email_\d{6,14}_reset$', callback.data, re.S) is not None:
                prompt = line + e_remind + "已经<b>重置邮箱</b>\n"
                myUsers[chat_id] = ('email', '')
                user = myUsers[chat_id]
                await callback.edit_message_text(content_guest_detail(user) + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp('guest_detail', chat_id))
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
                if accs[chat_id].avail > 0:
                    head_content = f"#提醒 客户邮箱已重置，<b>余额为 {accs[chat_id].avail:,.2f}U</b>\n"
                else:
                    head_content = f"客户邮箱已重置\n"
                await client.send_message(
                    params['log_id'],
                    f"{head_content}客户：{user.outname}(<code>{user.id:05}</code>/<code>{user.chat_id}</code>)\n"
                    f"操作员：{myUsers[callback.from_user.id].outname}",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                    )
            # 邮箱
            elif command == "email":
                if callback.from_user.id == params['boss_id']:
                    prompt = line + e_remind + f"此功能请使用 <b>个人账号</b> 操作 {now_time}\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp('guest_detail', chat_id))
                else:
                    prompt = line + e_remind + "是否需要<b>重置邮箱</b>\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp("guest_detail_email", chat_id))

            # 客户 - 详细 - USDT - chat_id - 重置
            elif re.match('^guest_detail_usdt_\d{6,14}_reset$', callback.data, re.S) is not None:
                prompt = line + e_remind + "已经<b>重置USDT</b>\n"
                myUsers[chat_id] = ('usdt', '')
                user = myUsers[chat_id]
                await callback.edit_message_text(content_guest_detail(user) + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp('guest_detail', chat_id))
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
                if accs[chat_id].avail > 0:
                    head_content = f"#提醒 客户USDT已重置，<b>余额为 {accs[chat_id].avail:,.2f}U</b>\n"
                else:
                    head_content = f"客户USDT已重置\n"
                await client.send_message(
                    params['log_id'],
                    f"{head_content}客户：{user.outname}(<code>{user.id:05}</code>/<code>{user.chat_id}</code>)\n"
                    f"操作员：{myUsers[callback.from_user.id].outname}",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                    )
            # USDT
            elif command == "usdt":
                if callback.from_user.id == params['boss_id']:
                    prompt = line + e_remind + f"此功能请使用 <b>个人账号</b> 操作 {now_time}\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp('guest_detail', chat_id))
                else:
                    prompt = line + e_remind + "是否需要<b>重置USDT</b>\n"
                    await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                     disable_web_page_preview=True,
                                                     reply_markup=get_imp("guest_detail_usdt", chat_id))
            # 客户 - 详细 - 返回
            elif command == "return":
                prompt = line + e_remind + "请选择您需要的操作\n"
                await callback.edit_message_text(content_guest(myUsers[chat_id]) + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_com_imp("guest", chat_id))
        # 客户 - 状态
        elif re.match('^guest_status_\d{6,14}$', callback.data, re.S) is not None:
            chat_id = int(callback.data.split('_')[2])
            prompt = line + e_remind + f"请点击需要修改的状态\n"
            await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('guest_status', chat_id))
        # 客户 - 状态 0：正常；1：禁止加群；2：禁止发言；3：禁止游戏；4：禁用资金；5：禁用全部
        elif re.match('^guest_status_\d{6,14}_\d{1}', callback.data, re.S) is not None:
            chat_id = int(callback.data.split('_')[2])
            idx = int(callback.data.split('_')[3])
            myUsers[chat_id] = ("status", idx)
            status = mean("users.status", idx)
            prompt = line + e_remind + f"你已修改状态为 <b>{status}</b>\n"
            await callback.edit_message_text(content_guest(myUsers[chat_id]) + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True, reply_markup=get_com_imp("guest", chat_id))
            if idx == 0:
                try:
                    await clients['bowin_boss'].unban_chat_member(params['redbag_group_id'], chat_id)
                    await clients['bowin_boss'].restrict_chat_member(params['redbag_group_id'], chat_id,
                                                                     ChatPermissions(can_send_messages=True))
                except Exception as e:
                    sys_log.write_log(traceback.format_exc(), 'a')
            elif idx == 1:
                try:
                    await clients['bowin'].ban_chat_member(params['redbag_group_id'], chat_id)
                except Exception as e:
                    sys_log.write_log(traceback.format_exc(), 'a')
            elif idx in (2, 5):
                try:
                    await clients['bowin'].restrict_chat_member(params['redbag_group_id'], chat_id,
                                                                ChatPermissions(can_send_messages=False))
                except Exception as e:
                    sys_log.write_log(traceback.format_exc(), 'a')
            await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
            await send_to_ws(f"秘书|发包员|更新myUsers|{chat_id}")
            await client.send_message(
                params['log_id'],
                f"客户 {myUsers[chat_id].outname}(<code>{myUsers[chat_id].id:05}</code>/<code>"
                f"{myUsers[chat_id].chat_id}</code>) 状态修改为 <b>{status}</b>",
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
                )
        # 客户 - 备注
        elif re.match('^guest_note_\d{6,14}', callback.data, re.S) is not None:
            chat_id = int(callback.data.split('_')[2])
            prompt = line + e_remind + "请输入客户的备注信息\n"
            re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                      disable_web_page_preview=True,
                                                      reply_markup=get_imp("guest_return", chat_id))
            await re_msg.reply_text("回复本消息输入客户的备注信息", quote=True, reply_markup=ForceReply())
        # 群组
        elif callback.data == 'group_list':
            contents = content_group_page(pre_content, "First")
            await callback.edit_message_text(contents[0], parse_mode=ParseMode.HTML, disable_web_page_preview=True,
                                             reply_markup=get_imp('group', contents[1]))
        # 群组 - 选择
        elif re.match('^group_[0-9a-zA-Z\-]{1,18}$', callback.data, re.S) is not None:
            choose = callback.data.split('_')[1]
            # 群组 - 上页/下页
            if choose in ('PrePage', 'NextPage'):
                contents = content_group_page(pre_content, choose)
                await callback.edit_message_text(contents[0], parse_mode=ParseMode.HTML, disable_web_page_preview=True,
                                                 reply_markup=get_imp('group', contents[1]))
            # 群组 - 返回
            elif choose == 'return':
                contents = content_group_page(pre_content, "First")
                await callback.edit_message_text(contents[0], parse_mode=ParseMode.HTML, disable_web_page_preview=True,
                                                 reply_markup=get_imp('group', contents[1]))
            # 群组 - 数字
            elif re.fullmatch(r"-\d+", choose):
                content = content_group_detail(int(choose))
                prompt = line + e_remind + "请选择你需要的操作\n"
                await client.send_message(
                    params['studio_id'],
                    content + prompt,
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True,
                    reply_markup=get_imp('group_detail')
                    )
            # 群组 - 新建
            elif choose == 'new':
                chat_list = await clients['bowin'].get_common_chats('bowin_redbagbot')
                id_list = []
                for chat in chat_list:
                    id_list.append(chat.id)
                contents = content_group_new(id_list)
                prompt = line + e_remind + "选择需要新增的群组\n"
                await callback.edit_message_text(contents[0] + prompt, parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("group_new", contents[1]))
            # 群组 - 详细 - 群主
            elif choose == 'owner':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入群主信息\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入群主信息", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 介绍
            elif choose == 'introduce':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入介绍信息\n"
                re_msg = await callback.edit_message_text(content_group_introduce(group_id) + prompt,
                                                          parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入介绍信息", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 语言
            elif choose == 'language':
                prompt = line + e_remind + "请选择语言\n"
                await callback.edit_message_text(pre_content + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("group_language"))
            # 群组 - 详细 - 系统抽佣比例
            elif choose == 'sysrate':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入系统抽佣比例，范围[0, 5.0]%\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入系统抽佣比例", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 群主抽佣比例
            elif choose == 'ownerrate':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入群主抽佣比例，范围[0, 5.0]%\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入群主抽佣比例", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 上级抽佣比例
            elif choose == 'parentrate':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入上级抽佣比例，范围[0, 5.0]%\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入上级抽佣比例", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 租金金额
            elif choose == 'rentamt':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入租金金额，范围[10, 1000]\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入租金金额", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 租金时间
            elif choose == 'rentdate':
                match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
                group_id = int(match.group(1))
                prompt = line + e_remind + "请输入租金时间，格式 YYYY-MM-DD\n"
                re_msg = await callback.edit_message_text(pre_content + prompt, parse_mode=ParseMode.HTML,
                                                          disable_web_page_preview=True,
                                                          reply_markup=get_imp("group_detail_return", group_id))
                await re_msg.reply_text("回复本消息输入租金时间", quote=True, reply_markup=ForceReply())
            # 群组 - 详细 - 状态
            elif choose == 'status':
                prompt = line + e_remind + "请设定状态\n"
                await callback.edit_message_text(pre_content + prompt,
                                                 parse_mode=ParseMode.HTML,
                                                 disable_web_page_preview=True,
                                                 reply_markup=get_imp("group_status"))
        # 群组 - 详细 - 新增 - 选择
        elif re.match('^group_new_-\d+', callback.data, re.S) is not None:
            group_id = callback.data.split('_')[2]
            content = content_group_detail(group_id)
            prompt = line + e_remind + "请选择你需要的操作\n"
            await client.send_message(
                params['studio_id'],
                content + prompt,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True,
                reply_markup=get_imp('group_detail')
                )
        # 群组 - 详细 - 选择 - 返回
        elif re.match('^group_detail_return_-\d{6,14}', callback.data, re.S) is not None:
            group_id = int(callback.data.split('_')[3])
            content = content_group_detail(group_id)
            prompt = line + e_remind + "请选择你需要的操作\n"
            await callback.edit_message_text(content + prompt, parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp("group_detail"))
        # 群组 - 详细 - 语言 - 选择/返回
        elif callback.data in ("group_language_0", "group_language_1", "group_language_return"):
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            lan = callback.data.split('_')[2]
            if lan in ('0', '1'):
                games[group_id] = ("is_en", int(lan))
                prompt = line + e_remind + f"你已经修改语言为<b>{mean('games.is_en', int(lan))}</b> {now_time}\n"
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
            else:
                prompt = line + e_remind + f"你没有进行操作 {now_time}\n"
            content = content_group_detail(group_id)
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('group_detail'))
        # 群组 - 详细 - 状态 - 选择
        elif re.match('^group_status_\d', callback.data, re.S) is not None:
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            status = callback.data.split('_')[2]
            games[group_id] = ("status", int(status))
            content = content_group_detail(group_id)
            prompt = line + e_remind + f"你已修改状态为<b>{mean('games.status', int(status))}</b> {now_time}\n"
            await callback.edit_message_text(content + prompt,
                                             parse_mode=ParseMode.HTML,
                                             disable_web_page_preview=True,
                                             reply_markup=get_imp('group_detail'))
            await send_to_ws(f"秘书|助理|更新games|{group_id}|")
            await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 回复-页面
@Client.on_message(filters.reply)
async def reply_interface(client, message):
    try:
        user_id = message.from_user.id
        re_msg = message.reply_to_message
        if re_msg.from_user is None or re_msg.from_user.username != clients.get_username(client):
            return
        pre_re_msg = await client.get_messages(message.chat.id, reply_to_message_ids=re_msg.id, replies=1)
        now_time = datetime.datetime.now().strftime('%H:%M:%S')
        pre_content = pre_re_msg.text.html.split(line)[0]
        # 配置 - 最低发包频率
        if re_msg.text == "回复本消息输入最低发包频率":
            input_rst = await input_cert(message.text, 'sent_freq', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                params['sent_freq'] = input_rst[1]
                pre_content = content_set()
                prompt = e_remind + f"已修改<b>最低发包频率</b> {now_time}"
                await send_to_ws(f"秘书|发包员|更新params|sent_freq|")
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}"
            await pre_re_msg.edit_text(pre_content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('set'))
        # 配置 - 最高发包金额
        elif re_msg.text == "回复本消息输入最高发包金额":
            input_rst = await input_cert(message.text, 'sent_amt', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                params['sent_amt'] = input_rst[1]
                pre_content = content_set()
                prompt = e_remind + f"已修改<b>最高发包金额</b> {now_time}"
                await send_to_ws(f"秘书|发包员|更新params|sent_amt|")
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}"
            await pre_re_msg.edit_text(pre_content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('set'))
        # 配置 - 最低抢包频率
        elif re_msg.text == "回复本消息输入最低抢包频率":
            input_rst = await input_cert(message.text, 'get_freq', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                params['get_freq'] = input_rst[1]
                pre_content = content_set()
                prompt = e_remind + f"已修改<b>最低抢包频率</b> {now_time}"
                await send_to_ws(f"秘书|发包员|更新params|get_freq|")
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}"

            await pre_re_msg.edit_text(pre_content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('set'))
        # 配置 - 结束抢包时长
        elif re_msg.text == "回复本消息输入结束抢包时长":
            input_rst = await input_cert(message.text, 'get_end', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                params['get_end'] = input_rst[1]
                pre_content = content_set()
                prompt = e_remind + f"已修改<b>结束抢包时长</b> {now_time}"
                await send_to_ws(f"秘书|发包员|更新params|get_end|")
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}"
            await pre_re_msg.edit_text(pre_content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('set'))
        # 查客户
        elif re_msg.text == "回复本消息查询客户":
            input_str = message.text
            await message.delete()
            await re_msg.delete()
            my_user = myUsers.query(input_str)
            if my_user is not None:
                # 发详细信息
                content = content_guest(my_user)
                prompt = e_remind + "请选择您需要的操作\n"
                await client.send_message(message.chat.id,
                                          content + line + prompt,
                                          parse_mode=ParseMode.HTML,
                                          disable_web_page_preview=True,
                                          reply_markup=get_com_imp('guest', my_user.chat_id))
                # 处理之前的消息
                prompt = e_remind + f"已查到客户 {input_str} 的信息\n"
            else:
                prompt = e_warning + f"查不到客户 {input_str} 的信息\n"
            await pre_re_msg.edit_text(pre_content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('admin'))
        # 客户 - 报表 - 自选
        elif re_msg.text == "回复本消息输入自选时间段":
            input_rst = await input_cert(message.text, 'guest_report_自选', client, user_id)
            await message.delete()
            await re_msg.delete()
            if input_rst[0]:
                match = re.match(f'.*\nID号：<code>(.*)</code>{e_left3}.*', pre_content, re.S)
                chat_id = int(match.group(1))
                content = content_report(myUsers[chat_id], input_rst[1:])
                prompt = e_remind + f"更新时间 {now_time}\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('guest_report'))
            else:
                prompt = e_warning + f"{input_rst[1]}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('guest_report'))

        # 客户 - 详细 - 上级 - 输入
        elif re_msg.text == "回复本消息输入新上级":
            match = re.match(f'.*{e_prize}ID：.* / <code>(.*)</code>\n{e_man}.*', pre_content, re.S)
            chat_id = int(match.group(1))
            user = myUsers[chat_id]
            pre_parent = myUsers.query(str(user.parent_id))
            new_parent = myUsers.query(message.text)
            await message.delete()
            await re_msg.delete()
            if new_parent is not None:
                _ = myUsers[chat_id, True, new_parent.chat_id]
                await send_to_ws(f"秘书|助理|更新myUsers|{chat_id}")
                await send_to_ws(f"秘书|发包员|更新myUsers|{chat_id}")
                prompt = e_remind + f"你已经修改了上级\n"
                await client.send_message(
                    params['log_id'],
                    f"#提醒 客户上级已修改\n"
                    f"客户：{user.outname}(<code>{user.id:05}</code>/<code>{user.chat_id}</code>)\n"
                    f"原上级：{pre_parent.outname}(<code>{pre_parent.id:05}</code>/<code>{pre_parent.chat_id}</code>)\n"
                    f"新上级：{new_parent.outname}(<code>{new_parent.id:05}</code>/<code>{new_parent.chat_id}</code>)\n"
                    f"操作员：{myChats[user_id].outname}",
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True
                    )
            else:
                prompt = e_warning + f"没有找到上级<b>{message.text}</b>的信息\n"
            content = content_guest_detail(myUsers[chat_id])
            await pre_re_msg.edit_text(content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_imp('guest_detail', chat_id))
        # 客户 - 备注 输入
        elif re_msg.text == "回复本消息输入客户的备注信息":
            match = re.match(f'.*{e_prize}ID：.* / <code>(.*)</code>\n{e_man}.*', pre_content, re.S)
            chat_id = int(match.group(1))
            myUsers[chat_id] = ["note", message.text]
            await message.delete()
            await re_msg.delete()
            content = content_guest(myUsers[chat_id])
            prompt = e_remind + f"你已经修改了备注信息\n"
            await pre_re_msg.edit_text(content + line + prompt,
                                       parse_mode=ParseMode.HTML,
                                       disable_web_page_preview=True,
                                       reply_markup=get_com_imp('guest'))
        # 群组 - 详细 - 群主 - 输入
        elif re_msg.text == "回复本消息输入群主信息":
            user = myUsers.query(message.text)
            await message.delete()
            await re_msg.delete()
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            if user is not None:
                games[group_id] = ("owner_id", user.chat_id)
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
                content = content_group_detail(group_id)
                prompt = e_remind + f"你已修改了群主信息\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
            else:
                prompt = e_remind + f"你输入的群主信息不存在 {now_time}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
        # 群组 - 详细 - 介绍 - 输入
        elif re_msg.text == "回复本消息输入介绍信息":
            match = re.match(f'.*群组<b>(.*)</b>的介绍如下.*', pre_content, re.S)
            group_name = match.group(1)
            game = games.outname(group_name)
            if game is not None:
                games[game.group_id] = ("introduce", message.text.html)
                await send_to_ws(f"秘书|助理|更新games|{game.group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{game.group_id}|")
                content = content_group_introduce(game.group_id)
                prompt = e_remind + f"你已修改了介绍信息\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp("group_detail_return", game.group_id))
            await message.delete()
            await re_msg.delete()
        # 群组 - 详细 - 系统抽佣比例 - 输入
        elif re_msg.text == "回复本消息输入系统抽佣比例":
            input_rst = await input_cert(message.text, 'sysrate', client, user_id)
            await message.delete()
            await re_msg.delete()
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            if input_rst[0]:
                games[group_id] = ("sys_rate", input_rst[1])
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
                content = content_group_detail(group_id)
                prompt = e_remind + f"你已修改了系统抽佣比例\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
        # 群组 - 详细 - 群主抽佣比例 - 输入
        elif re_msg.text == "回复本消息输入群主抽佣比例":
            input_rst = await input_cert(message.text, 'sysrate', client, user_id)
            await message.delete()
            await re_msg.delete()
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            if input_rst[0]:
                games[group_id] = ("owner_rate", input_rst[1])
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
                content = content_group_detail(group_id)
                prompt = e_remind + f"你已修改了群主抽佣比例\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
        # 群组 - 详细 - 上级抽佣比例 - 输入
        elif re_msg.text == "回复本消息输入上级抽佣比例":
            input_rst = await input_cert(message.text, 'sysrate', client, user_id)
            await message.delete()
            await re_msg.delete()
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            if input_rst[0]:
                games[group_id] = ("parent_rate", input_rst[1])
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
                content = content_group_detail(group_id)
                prompt = e_remind + f"你已修改了上级抽佣比例\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
        # 群组 - 详细 - 租金金额 - 输入
        elif re_msg.text == "回复本消息输入租金金额":
            input_rst = await input_cert(message.text, 'rentamt', client, user_id)
            await message.delete()
            await re_msg.delete()
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            if input_rst[0]:
                games[group_id] = ("rent_amt", input_rst[1])
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
                content = content_group_detail(group_id)
                prompt = e_remind + f"你已修改了租金金额\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
        # 群组 - 详细 - 租金时间 - 输入
        elif re_msg.text == "回复本消息输入租金时间":
            input_rst = await input_cert(message.text, 'rentdate', client, user_id)
            await message.delete()
            await re_msg.delete()
            match = re.match(f'.*群ID：<code>(.*)</code>\n{e_redbag}.*', pre_content, re.S)
            group_id = int(match.group(1))
            if input_rst[0]:
                games[group_id] = ("rent_date", input_rst[1])
                await send_to_ws(f"秘书|助理|更新games|{group_id}|")
                await send_to_ws(f"秘书|发包员|更新games|{group_id}|")
                content = content_group_detail(group_id)
                prompt = e_remind + f"你已修改了租金时间\n"
                await pre_re_msg.edit_text(content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
            else:
                prompt = e_remind + f"{input_rst[1]} {now_time}\n"
                await pre_re_msg.edit_text(pre_content + line + prompt,
                                           parse_mode=ParseMode.HTML,
                                           disable_web_page_preview=True,
                                           reply_markup=get_imp('group_detail'))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 查看guest信息 - 非主账号发的信息
@Client.on_message(
    filters.chat(params['studio_id']) & filters.command("guest") & filters.incoming & ~filters.user(params['boss_id']))
async def guest(client, message):
    try:
        text_list = message.text.split(" ")
        prompt = e_remind + "请选择您需要的操作\n"
        if len(text_list) == 2:
            my_user = myUsers.query(text_list[1])
            if my_user is not None:
                content = content_guest(my_user)
                await client.send_message(
                    params['studio_id'],
                    content + line + prompt,
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True,
                    reply_markup=get_com_imp("guest", my_user.chat_id))
            else:
                await client.send_message(
                    params['studio_id'],
                    f"查不到客户 {text_list[1]} 的信息\n",
                    disable_web_page_preview=True,
                    parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 处理工作室的文本消息
@Client.on_message(~filters.bot & filters.chat(
    params['studio_id']) & ~filters.forwarded & filters.incoming & ~filters.reply & filters.text)
async def studio_message(client, message):
    try:
        # user = myChats[message.from_user.id]
        if len(message.text) == 1:
            content = "您好！我是您的秘书\n"
            prompt = line + e_remind + "请选择您需要的操作\n"
            await message.reply(content + prompt,
                                quote=False,
                                parse_mode=ParseMode.HTML,
                                reply_markup=get_imp("admin"))
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 处理 WebSocket 消息
@websocket_handler(receiver='秘书', module_name="bowin_adminbot")
async def get_websocket_message(sender, receiver, content):
    try:
        print_both(f"秘书接收到WebSocket信息 {sender}, {receiver}, {content}")
        command = content.split('|')
        if command[0] == '更新myUsers':
            # 更新myUsers|chat_id
            _ = myUsers[int(command[1]), True]
        elif command[0] == '展示客户':
            # 助理|秘书|展示客户|{chat_id}
            my_user = myUsers.query(int(command[1]))
            content = content_guest(my_user)
            prompt = e_remind + "请选择您需要的操作\n"
            await clients['bowin_adminbot'].send_message(
                params['studio_id'],
                content + line + prompt,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True,
                reply_markup=get_com_imp("guest", my_user.chat_id)
                )
        elif command[0] == '更新games':
            # 更新games|group_id
            _ = games[int(command[1]), True]



    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# test收到信息
@Client.on_message()
async def f_on_message(client, message):
    try:
        print(f"admin_bot 收到 {myChats[message.from_user.id].outname} 的消息")
        myOKX = OKX('carter')
        print(await myOKX.load_dep(30))
        # print(f"我目前使用的代理是 {client.proxy}")
        # if message.text == 'test':
        #     print(f"bowin目前使用的代理是 {clients['bowin'].proxy}")
            # await clients['bowin'].send_message(params['studio_id'], message.text)
        # input = message.text
        # print(input)
        # await message.reply(f"message->{input}", quote=False)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
