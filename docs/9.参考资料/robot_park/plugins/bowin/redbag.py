# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from plugins.bowin.common import *


# 内联按钮界面
def get_imp(index='-', param=None):
    try:
        # 参数[progress, bet_amt, ray, proj.id, proj.owner_id]
        if index == 'send_redbag':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(f"{e_redbag}抢红包[6/{param.get_cnt}]总 {param.bet_amt:.0f}U {e_ray}雷{param.ray}",
                                      callback_data=f"redbag_{param.id}")],
                [InlineKeyboardButton("充值", callback_data="deposit"),
                 InlineKeyboardButton("介绍", callback_data='introduce'),
                 InlineKeyboardButton("余额", callback_data='avail')],
                [InlineKeyboardButton("客服", url=link(param.owner_id)),
                 InlineKeyboardButton("助理", url="t.me/bowin_asstbot"),
                 InlineKeyboardButton("商务", url="t.me/bowin_boss")]
                ])
        # 参数[proj.owner_id]
        elif index == 'main':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton("充值", callback_data='deposit'),
                 InlineKeyboardButton("介绍", callback_data='introduce'),
                 InlineKeyboardButton("余额", callback_data='avail')],
                [InlineKeyboardButton("客服", url=link(param)),
                 InlineKeyboardButton("常规操作", url="t.me/bowin_asstbot"),
                 InlineKeyboardButton("商务", url="t.me/bowin_boss")]
                ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发红包函数
async def send_redbag(group_id, user_id, bet_amt, ray):
    try:
        game = games[group_id]
        user = myUsers[user_id, False, game.owner_id]
        myUsers.active(user_id)
        # 检查群权限
        if not (game.game_type == 1 and game.status == 1):
            contect = (f"本群无法玩<b>抢红包</b>游戏，请群主与<a href='{link(params['boss_id'])}'>"
                       f"{myUsers[params['boss_id']].outname}</a> 联系")
            await clients['bowin_redbagbot'].send_message(group_id, f"尊敬的 {myUsers[user_id].outname}\n{contect}",
                                                          parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            return contect
        # 检查红包金额范围
        if not (5 <= bet_amt <= 2000):
            contect = "红包金额应该在 <b>[5, 2000]</b> 之间，请重新发"
            await clients['bowin_redbagbot'].send_message(group_id, f"尊敬的 {myUsers[user_id].outname}\n{contect}",
                                                          parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            return contect
        # 检查avail是否有足够多钱
        if bet_amt > accs[user_id].avail:
            contect = f"{myUsers[user_id].outname}，您余额不够，请联系 <a href='{link(game.owner_id)}'>" \
                      f"{myUsers[game.owner_id].outname}</a> 进行充值"
            await clients['bowin_redbagbot'].send_message(group_id, f"尊敬的 {myUsers[user_id].outname}\n{contect}",
                                                          parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            return contect
        # 登记注单
        proj = projRedbags.new(group_id, user_id, bet_amt, ray)
        # 变更账户表和账变表
        trans.transact(user_id, 11, bet_amt, 1, group_id, proj.issue, proj.id, '发红包', '')
        # 发消息
        content = f"[{myUsers[user_id].short_outname(6)}]发了个{bet_amt}U的红包，快来抢！"
        msg = await clients['bowin_redbagbot'].copy_message(
            group_id,
            params['my_channel'],
            2,
            caption=content,
            parse_mode=ParseMode.HTML,
            reply_markup=get_imp('send_redbag', proj))
        # 登记消息ID
        projRedbags[proj.id] = ('msg_id', msg.id)
        return msg.id
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发红包消息
@Client.on_message(~filters.bot & ~filters.chat([params['log_id'], params['studio_id']]) &
                   filters.group & filters.incoming & filters.regex('^\d{1,4}[/-]\d$', re.I))
async def send_redbag_message(client, message):
    try:
        user_id = message.from_user.id
        if myUsers[user_id].status in (3, 5):
            status = mean("users.status", myUsers[user_id].status)
            boss_chat = myUsers[params['boss_id']]
            await client.send_message(message.chat.id,
                                      f"{myUsers[user_id].outname}，目前您的状态是<b>{status}</b>，暂时不能游戏，请与<a href='"
                                      f"{link(boss_chat.chat_id)}'>{boss_chat.outname}</a>联系",
                                      parse_mode=ParseMode.HTML,
                                      disable_web_page_preview=True)
            return
        (amt, ray) = message.text.replace("/", "-").split('-')
        await send_redbag(message.chat.id, message.from_user.id, int(amt), int(ray))
        await message.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面
@Client.on_callback_query()
async def callback_interface(client, callback):
    try:
        # 新增/活跃成员
        user_id = callback.from_user.id
        game = games[callback.message.chat.id]
        user = myUsers[user_id, False, game.owner_id]
        myUsers.active(user_id)
        # 抢包
        if re.match('^redbag_\d+', callback.data, re.S) is not None:
            if user.status in (3, 5):
                status = mean("users.status", user.status)
                boss_chat = myUsers[params['boss_id']]
                await client.send_message(callback.message.chat.id,
                                          f"{user.outname}，目前您的状态是<b>{status}</b>，暂时不能游戏，请与<a href='"
                                          f"{link(boss_chat.chat_id)}'>{boss_chat.outname}</a>联系",
                                          parse_mode=ParseMode.HTML,
                                          disable_web_page_preview=True)
                return
            proj_id = int(callback.data.split('_')[1])
            feedback = getRedbags.new(proj_id, user_id)
            await callback.answer(feedback[1], show_alert=True)
            if feedback[0]:
                proj = projRedbags[proj_id]
                if proj.get_cnt == 6:
                    # 抢包人数到达后，结算抢包结果
                    await projRedbags.close(proj_id, get_imp('main', proj.owner_id))
                else:
                    # 否则，只修改抢包数量
                    await callback.edit_message_reply_markup(
                        get_imp('send_redbag', proj))
        # 充值
        elif callback.data == 'deposit':
            content = f"请联系助理(@bowin_asstbot)进行充值操作"
            await callback.answer(content, show_alert=True)
            await send_to_ws(f"发包员|助理|发起充值|{user_id}")
        # 介绍
        elif callback.data == 'introduce':
            content = f"请留意助理(@bowin_asstbot)介绍本群组"
            await callback.answer(content, show_alert=True)
            await send_to_ws(f"发包员|助理|群组介绍|{user_id}|{game.group_id}")
        # 余额
        elif callback.data == 'avail':
            content = f"尊敬的 {myUsers[user_id].outname}\n\nID号： {user_id}\n余额： {accs[user_id].avail:,.2f}U "
            await callback.answer(content, show_alert=True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 处理 WebSocket 消息
@websocket_handler(receiver="发包员", module_name="bowin_redbagbot")
async def get_websocket_message(sender, receiver, content):
    try:
        print_both(f"发包员接收到WebSocket信息 {sender}, {receiver}, {content}")
        command = content.split('|')
        if command[0] == "远程发包":
            # 远程发包|user_id|amt-ray|group_id    如：远程发包|1914064729|200-8|-1002093645173
            (user_id, bet, group_id) = command[1:]
            (amt, ray) = bet.split('-')
            msg_rst = await send_redbag(int(group_id), int(user_id), int(amt), int(ray))
            if not isinstance(msg_rst, int):
                content = msg_rst
            elif myUsers[group_id].username != '':
                msg_link = f"https://t.me/{myUsers[group_id].username}/{msg_rst}"
                content = f"恭喜！红包已发出 <a href='{msg_link}'>详情</a>"
            else:
                msg_link = f"https://t.me/c/{group_id}/{msg_rst}"
                content = f"恭喜！红包已发出 <a href='{msg_link}'>详情</a>"
            await send_to_ws(f"发包员|助理|发消息|{user_id}|{content}")
        elif command[0] == "更新params":
            # 更新params|id|name    如：更新params|sent_freq|"
            (id, name) = command[1:]
            params.refresh(id, name)
        elif command[0] == '更新myUsers':
            # 更新myUsers|chat_id
            _ = myUsers[int(command[1]), True]
        elif command[0] == '更新games':
            # 更新games|group_id
            _ = games[int(command[1]), True]


    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')




# test收到信息
@Client.on_message(filters.text)
async def other_message(client, message):
    try:
        print(f"redbag_bot 收到 {myUsers[message.from_user.id].outname} 的消息")
        # msg = await client.send_message(6985305888, message.text)
        # print(msg)
        # print(message)
        # # print(message.text)
        # msg = await message.reply("已收到其它信息： "+message.text.html, quote=False,
        #                           business_connection_id=message.business_connection_id,
        #                           reply_markup=get_imp('test'))
        # print(msg)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')












