# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'

import json
import random
from decimal import Decimal

import requests

from classes2 import *
from cryptography.fernet import Fernet
import hmac
import hashlib


class TTransTypes:
    def __init__(self, values):
        (self.id, self.name, self.sign_b, self.sign_f, self.sign_a, self.sign_w) = values

    def to_list(self) -> list:
        return [self.id, self.name, self.sign_b, self.sign_f, self.sign_a, self.sign_w]


class TransTypes:
    def __init__(self):
        self.__sql_str = "SELECT id,name,sign_b,sign_f,sign_a,sign_w from trans_types where id=%s"
        self.__dict = {}

    def __getitem__(self, id) -> TTransTypes:
        if id not in self.__dict:
            rst = read_connector.run(self.__sql_str, [id])
            if len(rst) > 0:
                self.__dict[id] = TTransTypes(rst[0])
        return self.__dict.get(id, None)


transTypes = TransTypes()


class TAccs:
    def __init__(self, values):
        (self.chat_id, self.balance, self.frozen, self.avail, self.wlimit, self.updated_at, self.created_at) = values


class Accs:
    def __init__(self):
        self.__sql_str = "select chat_id,balance,frozen,avail,wlimit,updated_at,created_at from accs where chat_id=%s"

    def __getitem__(self, chat_id) -> TAccs:
        rst = read_connector.run(self.__sql_str, [chat_id])
        if len(rst) == 0:
            sql_str = f"insert into accs(chat_id,updated_at,created_at) values({chat_id},current_timestamp()," \
                      f"current_timestamp())"
            exe_connector.run(sql_str)
            rst = read_connector.run(self.__sql_str, [chat_id])
        return TAccs(rst[0])

    def change(self, chat_id, amt, type_id):
        sql_str = "update accs set balance={2[2]:d}*{1:f}+balance, frozen={2[3]:d}*{1:f}+frozen, " \
                  "avail={2[4]:d}*{1:f}+avail, wlimit=case when {2[0]:d} in (1,3,7,16,17) and wlimit<0 then " \
                  "{2[5]:d}*{1:f} else {2[5]:d}*{1:f}+wlimit end, updated_at=current_timestamp() where chat_id={0:d}"\
            .format(chat_id, amt, transTypes[type_id].to_list())
        exe_connector.run(sql_str)


accs = Accs()


class TTrans:
    def __init__(self, values):
        (self.id, self.chat_id, self.role, self.parent_id, self.type_id, self.type_name, self.sign_income,
         self.game_type, self.group_id, self.issue, self.proj_id, self.tag, self.amt, self.pre_balance, self.pre_frozen,
         self.pre_avail, self.pre_wlimit, self.balance, self.frozen, self.avail, self.wlimit, self.note,
         self.created_at) = values


class Trans:
    def __init__(self):
        self.__sql_str = "select id,chat_id,role,parent_id,type_id,type_name,sign_income,game_type,group_id,issue," \
                         "proj_id,tag,amt,pre_balance,pre_frozen,pre_avail,pre_wlimit,balance,frozen,avail,wlimit," \
                         "note,created_at from trans "

    def __getitem__(self, id) -> TTrans:
        rst = read_connector.run(self.__sql_str + "where id=%s", [id])
        return TTrans(rst[0])

    def get_from_issue(self, chat_id, issue) -> TTrans | None:
        rst = read_connector.run(self.__sql_str + "where chat_id=%s and issue=%s", [chat_id, issue])
        if len(rst) > 0:
            return TTrans(rst[0])

    def get_from_type(self, chat_id, type_list, cnt) -> TTrans | None:
        sql_str = self.__sql_str + f"where chat_id={chat_id} and type_id in ({','.join(map(str, type_list))}) order " \
                                   f"by created_at desc limit {cnt}"
        rst = read_connector.run(sql_str)
        return rst

    def transact(self, chat_id, type_id, amt, game_type=0, group_id=0, issue='', proj_id=0, tag='', note=''):
        try:
            # 保存账户变更之前的信息，并锁定
            acc_pre = accs[chat_id]
            # 变更账户
            accs.change(chat_id, amt, type_id)
            # 保存账户变更之后的信息
            acc_aft = accs[chat_id]
            user = myUsers[chat_id]
            transType = transTypes[type_id]
            # 插入账变记录
            sql_str = f"insert into trans(chat_id,role,parent_id,type_id,type_name,sign_income,game_type,group_id," \
                      f"issue,proj_id,tag,amt,pre_balance,pre_frozen,pre_avail,pre_wlimit,balance,frozen,avail," \
                      f"wlimit,note,created_at) values({chat_id},{user.role},{user.parent_id},{type_id}," \
                      f"'{transType.name}',{transType.sign_a},{game_type},{group_id},'{issue}',{proj_id},'{tag}'," \
                      f"{amt},{acc_pre.balance},{acc_pre.frozen},{acc_pre.avail},{acc_pre.wlimit},{acc_aft.balance}," \
                      f"{acc_aft.frozen},{acc_aft.avail},{acc_aft.wlimit},'{note}',current_timestamp())"
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


trans = Trans()


class TGames:
    def __init__(self, values):
        (self.group_id, self.outname, self.owner_id, self.game_type, self.link, self.introduce, self.is_en,
         self.sys_rate, self.parent_rate, self.owner_rate, self.rent_amt, self.rent_date, self.status, self.updated_at,
         self.created_at) = values


class Games:
    def __init__(self):
        self.__sql_str = "select group_id,outname,owner_id,game_type,link,introduce,is_en,sys_rate,parent_rate," \
                         "owner_rate,rent_amt,rent_date,status,updated_at,created_at from games "
        self.__dict = {}

    def __new(self, group_id):
        sql_str = f"insert into games(group_id, outname, updated_at, created_at) values({group_id}, " \
                  f"'{myUsers[group_id].outname}', current_timestamp(), current_timestamp())"
        exe_connector.run(sql_str)

    def __getitem__(self, args) -> TGames:
        if isinstance(args, tuple):
            renew = args[1]
            group_id = args[0]
        else:
            renew = False
            group_id = args
        if group_id not in self.__dict or renew:
            sql_str = self.__sql_str + f"where group_id=%s"
            rst = read_connector.run(sql_str, [group_id])
            if len(rst) == 0:
                self.__new(group_id)
                rst = read_connector.run(sql_str, [group_id])
            self.__dict[group_id] = TGames(rst[0])
        return self.__dict[group_id]

    # games[group_id] = ('字段名', var)
    def __setitem__(self, group_id, value):
        properties = ["outname", "owner_id", "game_type", "link", "introduce", "is_en", "sys_rate", "parent_rate",
                      "owner_rate", "rent_amt", "rent_date", "status"]
        if len(value) == 2 and value[0] in properties:
            sql_str = self.__sql_str + f"where group_id=%s"
            # 检查记录是否已经存在
            rst = read_connector.run(sql_str, [group_id])
            if len(rst) == 0:
                self.__new(group_id)
            # 更新数据库
            update_sql = f"UPDATE games SET {value[0]}=%s, updated_at=current_timestamp() WHERE group_id=%s"
            exe_connector.run(update_sql, [value[1], group_id])
            # 更新字典
            rst = read_connector.run(sql_str, [group_id])
            self.__dict[group_id] = TGames(rst[0])
        else:
            raise ValueError("Games必须指定某个字段修改")

    # 获取有效游戏
    def avail_game(self):
        sql_str = "select group_id from games where status in (1, 2, 3) order by updated_at desc"
        rst = read_connector.run(sql_str)
        return [row[0] for row in rst]

    # 通过outname获取游戏信息
    def outname(self, outname) -> TGames | None:
        sql_str = self.__sql_str + f"where outname=%s"
        rst = read_connector.run(sql_str, [outname])
        if len(rst) > 0:
            return TGames(rst[0])
        else:
            return

    # 判断是否有效的游戏
    def is_effected(self, group_id) -> bool:
        sql_str = self.__sql_str + f"where status in (1, 2, 3) and group_id=%s"
        rst = read_connector.run(sql_str, [group_id])
        if len(rst) == 0:
            return False
        else:
            return True


games = Games()


class TCatfishRedbag:
    def __init__(self, values):
        (self.group_id, self.player_rate, self.bot_send_rate, self.bot_get_rate, self.updated_at) = values


class CatfishRedbags:
    def __init__(self):
        self.__sql_str = (f"select group_id, player_rate, bot_send_rate, bot_get_rate, updated_at from catfish_redbag "
                          f"where group_id=%s")
        self.__dict = {}

    def __new(self, group_id):
        sql_str = f"insert into catfish_redbag(group_id, updated_at) values({group_id}, current_timestamp())"
        exe_connector.run(sql_str)

    def __getitem__(self, group_id) -> TCatfishRedbag:
        # 这里可限制字典的长度
        if group_id not in self.__dict:
            rst = read_connector.run(self.__sql_str, [group_id])
            if len(rst) == 0:
                self.__new(group_id)
                rst = read_connector.run(self.__sql_str, [group_id])
            self.__dict.update({group_id: TCatfishRedbag(rst[0])})
        return self.__dict[group_id]

    # catfishRedbags[group_id] = ('字段名', var)
    def __setitem__(self, group_id, value):
        properties = ["player_rate", "bot_send_rate", "bot_get_rate"]
        if len(value) == 2 and value[0] in properties:
            # 检查记录是否已经存在
            rst = read_connector.run(self.__sql_str, [group_id])
            if len(rst) == 0:
                self.__new(group_id)
            # 更新数据库
            update_sql = f"UPDATE catfish_redbag SET {value[0]}=%s, updated_at=current_timestamp() WHERE group_id=%s"
            exe_connector.run(update_sql, [value[1], group_id])
            # 更新字典
            rst = read_connector.run(self.__sql_str, [group_id])
            self.__dict.update({group_id: TCatfishRedbag(rst[0])})
        else:
            raise ValueError("CatfishRedbags必须指定某个字段修改")


catfishRedbags = CatfishRedbags()


class TUsers:
    def __init__(self, values):
        (self.chat_id, self.outname, self.username, self.id, self.role, self.is_remind, self.parent_id,
         self.parent, self.fund_pwd, self.email, self.usdt, self.status, self.note, self.active_at,
         self.created_at) = values

    def short_outname(self, length) -> str:
        return self.outname if len(self.outname) <= length else self.outname[:length] + '..'


class MyUsers:
    def __init__(self):
        self.__sql_str = "select chat_id, outname, username, id, role, is_remind, parent_id, parent, " \
                         "fund_pwd, email, usdt, status, note, active_at, created_at from users where chat_id=%s"
        self.__sql_str2 = "select chat_id, outname, username, id, role, is_remind, parent_id, parent, " \
                          "fund_pwd, email, usdt, status, note, active_at, created_at from users where "
        self.__dict = {}

    # 参数形式 A:myUsers[chat_id] 或者 B:myUsers[chat_id, renew, parent_chat_id]
    # B: 若not renew and parent_chat_id!=0 只有在没有记录的时候，才把上级设定为parent_chat_id，其余情况parent_chat_id无效果
    # B: 若renew and parent_chat_id!=0 修改上级关系
    # B: 若renew and parent_chat_id==0 纯粹重新从数据库获取数据
    def __getitem__(self, args) -> TUsers:
        try:
            if isinstance(args, tuple):
                chat_id = args[0]  # 第一个元素是 chat_id
                renew = args[1] if len(args) > 1 else False  # 第二个元素是 renew，如果存在
                parent_chat_id = args[2] if len(args) > 2 else 0  # 第三个元素是 parent_chat_id，如果存在
            else:
                chat_id = args
                renew = False
                parent_chat_id = 0
            # 这里限制字典的长度
            if len(self.__dict) > 1000:
                self.__dict.clear()
            # 如果字典中有，并且不需要更新，直接返回
            if chat_id in self.__dict and not renew:
                return self.__dict[chat_id]
            chat = myChats[chat_id]
            parent = self.query(parent_chat_id)
            rst = read_connector.run(self.__sql_str, [chat_id])
            # 表中有记录，直接更新到内存中 因为bowin_boss账号会识别修改后的outname，所以，这里不再更新outname，在客户私聊助理时再统一更新
            if len(rst) > 0:
                # 修改代理线
                if renew and parent_chat_id != 0 and parent is not None:
                    sql_str_update = f"update users set username='{chat.username}',parent_id={parent_chat_id}," \
                                     f"parent='{parent.outname}' where chat_id={chat_id}"
                    exe_connector.run(sql_str_update)
                    rst = read_connector.run(self.__sql_str, [chat_id])
                # 纯更新信息
                elif renew:
                    sql_str_update = f"update users set username='{chat.username}' where chat_id={chat_id}"
                    exe_connector.run(sql_str_update)
                    rst = read_connector.run(self.__sql_str, [chat_id])
            else:
                v_id = get_users_id(chat_id)
                # 若没有指定上级，或者上级id不在客户列表中，则默认在 params['boss_id'] 名下
                if parent is None:
                    parent_chat_id = params['boss_id']
                    parent = self.query(parent_chat_id)
                sql_str_insert = f"insert into users(chat_id,outname,username,id,parent_id,parent,created_at) values " \
                                 f"({chat_id},'{chat.outname}','{chat.username}',{v_id},{parent_chat_id}," \
                                 f"'{parent.outname}',current_timestamp())"
                exe_connector.run(sql_str_insert)
                rst = read_connector.run(self.__sql_str, [chat_id])
            self.__dict[chat_id] = TUsers(rst[0])
            return TUsers(rst[0])
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # users[chat_id] = ('字段名', var)
    def __setitem__(self, chat_id, value):
        properties = ["role", "is_remind", "fund_pwd", "email", "usdt", "status", "note"]
        if len(value) == 2 and value[0] in properties:
            # 更新数据库
            update_sql = f"UPDATE users SET {value[0]}=%s WHERE chat_id=%s"
            exe_connector.run(update_sql, [value[1], chat_id])
            # 更新字典
            rst = read_connector.run(self.__sql_str, [chat_id])
            self.__dict.update({chat_id: TUsers(rst[0])})
        else:
            raise ValueError(
                "Users赋值参数必须是2个参数，且只能修改role、is_remind、fund_pwd、email、usdt、status和note")

    # 查询用户资料
    def query(self, any) -> TUsers | None:
        any = str(any)
        if re.match('^[0-9]{1,5}$', any, re.S) is not None:
            sub_sql_str = " id={0:d}".format(int(any))
        elif re.match('^[0-9]{6,14}$', any, re.S) is not None:
            sub_sql_str = " chat_id={0:d}".format(int(any))
        else:
            sub_sql_str = " username='{0:s}'".format(any.strip())
        rst = read_connector.run(self.__sql_str2 + sub_sql_str)
        if len(rst) > 0:
            self.__dict[rst[0][0]] = TUsers(rst[0])
            return TUsers(rst[0])

    # 更新users表的 outname username parent，并清空内存信息
    def refresh(self, chat_id):
        chat = myChats[chat_id]
        sql_str = "update users set outname=%s, username=%s where chat_id=%s"
        exe_connector.run(sql_str, [chat.outname, chat.username, chat.id])
        sql_str = "update users set parent=%s where parent_id=%s"
        exe_connector.run(sql_str, [chat.outname, chat.id])

    # 用户活跃标记
    # 注意：即使没有录入该user，也不需要新增
    def active(self, chat_id):
        sql_str = f"update users set active_at=current_timestamp() where chat_id={chat_id}"
        exe_connector.run(sql_str)

    # 有效推广成员
    def members(self, chat_id) -> int:
        sql_str = f"select count(1) from users where parent_id={chat_id}"
        rst = read_connector.run(sql_str)
        return rst[0][0]

    # 设置用户角色
    def set_role(self, chat_id, role) -> TUsers:
        # 角色为”机器人时，需要特殊处理
        if role == 3:
            sql_str = "update users set role=%s, status=5, is_remind=0 where chat_id=%s"
        else:
            sql_str = "update users set role=%s, status=0, is_remind=1 where chat_id=%s"
        exe_connector.run(sql_str, [role, chat_id])
        rst = read_connector.run(self.__sql_str, [chat_id])
        self.__dict.update({chat_id: TUsers(rst[0])})
        return TUsers(rst[0])

    # 随机机器人
    def random_bot(self, amt):
        sql_str = f"select a.chat_id from users a join accs b on a.chat_id=b.chat_id where a.role=3 and " \
                  f"b.avail>=%s ORDER BY RAND()"
        rst = read_connector.run(sql_str, [amt])
        if len(rst) < 500:
            sql_str2 = f"update accs a join users b on a.chat_id=b.chat_id set a.balance=a.balance+2000, " \
                       f"a.avail=a.avail+2000 where a.avail<%s and b.role=3"
            exe_connector.run(sql_str2, [amt])
            rst = read_connector.run(sql_str, [amt])
        return rst[0][0]


myUsers = MyUsers()


class TProjRedbag:
    def __init__(self, values):
        (self.id, self.chat_id, self.role, self.group_id, self.msg_id, self.issue, self.bet_amt, self.ray,
         self.get_cnt, self.get_amt, self.back_amt, self.ray_amt, self.sys_rate, self.amt, self.parent_id,
         self.parent_rate, self.parent_amt, self.owner_id, self.owner_rate, self.owner_amt, self.status,
         self.updated_at, self.created_at) = values


class ProjRedbags:
    def __init__(self):
        self.__sql_str = "select id,chat_id,role,group_id,msg_id,issue,bet_amt,ray,get_cnt,get_amt,back_amt," \
                         "ray_amt,sys_rate,amt,parent_id,parent_rate,parent_amt,owner_id,owner_rate,owner_amt,status," \
                         "updated_at,created_at from proj_redbag "

    # 提取注单详情
    def __getitem__(self, id) -> TProjRedbag | None:
        rst = read_connector.run(self.__sql_str + "where id=%s", [id])
        if len(rst) == 0:
            return None
        else:
            return TProjRedbag(rst[0])

    # 修改注单字段 projReadbags[id] = ('字段名', var)
    def __setitem__(self, id, value):
        properties = ["msg_id"]
        if len(value) == 2 and value[0] in properties:
            # 更新数据库
            update_sql = f"UPDATE proj_redbag SET {value[0]}=%s, updated_at=current_timestamp() WHERE id=%s"
            exe_connector.run(update_sql, [value[1], id])
        else:
            raise ValueError("ProjRedbags赋值参数必须是2个参数，且只能修改msg_id")

    # 新增注单
    def new(self, group_id, user_id, bet_amt, ray) -> TProjRedbag:
        user = myUsers[user_id]
        issue = str(round(time.time() * 10000))
        sql_str = f"insert into proj_redbag(chat_id,role,group_id,issue,bet_amt,ray,sys_rate,parent_id,parent_rate," \
                  f"owner_id,owner_rate,updated_at,created_at) values({user.chat_id},{user.role},{group_id}," \
                  f"'{issue}',{bet_amt},{ray},{games[group_id].sys_rate},{user.parent_id}," \
                  f"{games[group_id].parent_rate},{games[group_id].owner_id},{games[group_id].owner_rate}," \
                  f"current_timestamp(),current_timestamp())"
        exe_connector.run(sql_str)
        sql_str = self.__sql_str + f"where chat_id={user_id} and group_id={group_id} and issue='{issue}'"
        rst = read_connector.run(sql_str)
        return TProjRedbag(rst[0])

    # 抢红包
    def get_amt(self, id, amt, is_ray=0):
        if is_ray == 1:
            str_ray_amt = "ray_amt=ray_amt+bet_amt*cast(1.8 as Decimal(14,4)),"
        else:
            str_ray_amt = ''
        sql_str = (f"update proj_redbag set get_cnt=get_cnt+1, get_amt=get_amt+{amt}, {str_ray_amt} "
                   f"updated_at=current_timestamp() where id={id}")
        exe_connector.run(sql_str)

    # 关闭注单
    async def close(self, id, markup=None):
        sql_str = (f"update proj_redbag set back_amt=bet_amt-get_amt, amt=ray_amt*(1-sys_rate), parent_amt=ray_amt*"
                   f"parent_rate, owner_amt=ray_amt*owner_rate, status=1, updated_at=current_timestamp() where id={id}")
        exe_connector.run(sql_str)
        proj = self.__getitem__(id)
        if proj.back_amt > 0:
            # 变更账户表和账变表 - 红包退回
            trans.transact(proj.chat_id, 13, proj.back_amt, 1, proj.group_id, proj.issue, proj.id, '红包退回', '')
        if proj.amt > 0:
            # 变更账户表和账变表 - 包主收益
            trans.transact(proj.chat_id, 12, proj.amt, 1, proj.group_id, proj.issue, proj.id, '包主收益', '')
        if proj.parent_amt > 0:
            # 变更账户表和账变表 - 上级佣金
            trans.transact(proj.parent_id, 14, proj.parent_amt, 1, proj.group_id, proj.issue, proj.id, '上级佣金',
                           str(proj.chat_id))
        if proj.owner_amt > 0:
            # 变更账户表和账变表 - 群主佣金
            trans.transact(proj.owner_id, 15, proj.owner_amt, 1, proj.group_id, proj.issue, proj.id, '群主佣金',
                           str(proj.chat_id))
        # 展示发包结果
        content = self.__show(proj)
        await clients['bowin_redbagbot'].edit_message_text(
            proj.group_id,
            proj.msg_id,
            content,
            parse_mode=ParseMode.HTML,
            disable_web_page_preview=True,
            reply_markup=markup
            )
        # 若发包者允许提醒，则把结果转发给他
        if myUsers[proj.chat_id, True].is_remind:
            content = re.sub(r"\[.*?]的红包", "您的红包", content)
            await send_to_ws(f"发包员|助理|发消息|{proj.chat_id}|{content}")

    # 展示抢包结果
    def __show(self, proj: TProjRedbag) -> str:
        get_detail = ''
        n = 1
        for redbag in getRedbags.all(proj.id):
            emo = e_money2 if redbag.is_ray == 0 else e_ray
            get_detail += f"{n}.[{emo}]<code>{redbag.amt:>7.2f}</code>U {myUsers[redbag.chat_id].short_outname(8)}\n"
            n += 1
        if proj.get_cnt == 6:
            end_desc = "已被抢完！"
        else:
            end_desc = "已截单！"
        content = f"""
[{myUsers[proj.chat_id].short_outname(6)}]的红包{end_desc}

{e_loudou}期号：<code>{proj.issue}</code>
{e_redbag}红包金额：{proj.bet_amt:.0f}U
{e_shandian}中雷倍数：1.8
{e_bomb}中雷数字：{proj.ray}

--------领取详情--------
{get_detail}
{e_market}中雷盈利：<code>{proj.amt:>8.2f}</code>U
{e_market}发包成本：<code>{(proj.back_amt - proj.bet_amt):>8.2f}</code>U
{e_market}包主盈亏：<code>{(proj.amt + proj.back_amt - proj.bet_amt):>8.2f}</code>U"""
        return content

    # 提取已经多长时间没发包
    def last_send_time(self, group_id=0, id=0, issue=0):
        sql_str = f"select TIMESTAMPDIFF(SECOND, ifnull(max(created_at),'2024-01-01 00:00:00'), NOW()) from " \
                  f"proj_redbag where 1=1"
        if group_id != 0:
            sql_str += f" and group_id={group_id}"
        elif id != 0:
            sql_str += f" and id={id}"
        elif issue != 0:
            sql_str += f" and issue={issue}"
        rst = read_connector.run(sql_str)
        return rst[0][0]

    # 提取多长时间没有抢包
    def last_get_time(self, group_id=0, id=0, issue=0):
        sql_str = f"select TIMESTAMPDIFF(SECOND, ifnull(max(updated_at),'2024-01-01 00:00:00'), NOW()) from " \
                  f"proj_redbag where 1=1"
        if group_id != 0:
            sql_str += f" and group_id={group_id}"
        elif id != 0:
            sql_str += f" and id={id}"
        elif issue != 0:
            sql_str += f" and issue={issue}"
        rst = read_connector.run(sql_str)
        return rst[0][0]

    # 提取未抢完的包 msg_id<>0 排除没有发出去的异常单
    def unfinish(self, group_id=0):
        if group_id == 0:
            rst = read_connector.run(self.__sql_str + " where status=0 and msg_id<>0")
        else:
            rst = read_connector.run(self.__sql_str + " where status=0 and msg_id<>0 and group_id=%s", [group_id])
        return [TProjRedbag(row) for row in rst]


projRedbags = ProjRedbags()


class TGetRedbag:
    def __init__(self, values):
        (self.proj_id, self.chat_id, self.role, self.amt, self.is_ray, self.ray_amt, self.created_at) = values


class GetRedbags:
    def __init__(self):
        self.__sql_str = ("select proj_id, chat_id, role, amt, is_ray, ray_amt, created_at from get_redbag "
                          "where proj_id=%s")

    # 提取注单领包者详情 参数要求 (proj_id, chat_id)
    def __getitem__(self, args) -> TGetRedbag | None:
        (proj_id, chat_id) = args
        rst = read_connector.run(self.__sql_str + " and chat_id=%s", [proj_id, chat_id])
        if len(rst) == 0:
            return None
        else:
            return TGetRedbag(rst[0])

    # 提取同一注单所有领包者的详情
    def all(self, proj_id) -> list:
        rst = read_connector.run(self.__sql_str + " order by created_at", [proj_id])
        return [TGetRedbag(row) for row in rst]

    # 加工抽取的金额
    def __handle_amt(self, amt, rate, ray):
        rand_nums = [i for i in range(100)]
        for _ in range(random.randint(1, 5)):
            random.shuffle(rand_nums)
        obj_num = random.choice(rand_nums)
        if obj_num <= int(rate * 100):
            amt1 = amt // 10 * 10 + ray
        else:
            last_num = [num for num in range(10) if num != ray]
            random.shuffle(last_num)
            no_ray = random.choice(last_num)
            amt1 = amt // 10 * 10 + no_ray
        return Decimal(str(round(amt1 / 100, 2)))

    # 领取红包操作
    def new(self, proj_id, chat_id) -> list:
        if myUsers[chat_id].status == 2:
            return [False,
                    f"您现在无法游戏，请与 <a href='{link(params['boss_id'])}'>{myUsers[params['admin_id']].outname}</a> 联系"]
        redbag = self.__getitem__((proj_id, chat_id))
        if redbag is not None:
            return [False, f"您已经领取过红包，金额为{redbag.amt:,.2f}U，不能再领取了"]
        proj = projRedbags[proj_id]
        if proj.get_cnt >= 6:
            return [False, f"抱歉，此红包已抢完"]
        required_amt = proj.bet_amt * Decimal('1.8')
        if required_amt > accs[chat_id].avail:
            return [False, f"您的余额不够，领这个红包至少需要 {required_amt:,.2f}U 的余额"]
        # 获取抢包金额
        num = 6 - proj.get_cnt
        all_amt = proj.bet_amt - proj.get_amt
        if num == 1:
            amt = all_amt
        else:
            remaining = int(all_amt * 100)
            min = int(remaining / 12)
            amt_list = []
            for i in range(num):
                if i == num - 1:
                    next_amt = remaining
                else:
                    max_amt = int((remaining - (num - i - 1) * min) / (num - i) * 2)
                    max_amt = max_amt if max_amt > min else int(remaining - (num - i - 1) * min)
                    next_amt = random.randint(min, max_amt)
                remaining -= next_amt
                amt_list.append(next_amt)
            random.shuffle(amt_list)
            obj_amt = random.choice(amt_list)
            # 鲶鱼操作
            # 机器人抢包情况
            if myUsers[chat_id].role == 3:
                amt = self.__handle_amt(obj_amt, catfishRedbags[proj.group_id].bot_get_rate, proj.ray)
            # 机器人发红包
            elif proj.role == 3:
                amt = self.__handle_amt(obj_amt, catfishRedbags[proj.group_id].bot_send_rate, proj.ray)
            # 普通人抢红包
            else:
                amt = self.__handle_amt(obj_amt, catfishRedbags[proj.group_id].player_rate, proj.ray)
        # 判断是否中雷
        if str(proj.ray) == str(int(amt * 100))[-1]:
            is_ray = 1
            ray_amt = proj.bet_amt * Decimal('1.8')
            reply_text = f"您已领取红包 {amt:,.2f}U\n中雷赔付 {ray_amt:.2f}U"
        else:
            is_ray = 0
            ray_amt = 0
            reply_text = f"您已领取红包 {amt:,.2f}U\n恭喜！没中雷！"
        # 生成抢包者注单
        sql_str = (f"insert into get_redbag(proj_id,chat_id,role,amt,is_ray,ray_amt,created_at) values({proj_id},"
                   f"{chat_id},{myUsers[chat_id].role},{amt},{is_ray},{ray_amt},current_timestamp())")
        exe_connector.run(sql_str)
        # 在发包者注单中更新信息
        projRedbags.get_amt(proj_id, amt, is_ray)
        if is_ray == 1:
            # 变更账户表和账变表 - 抢包收益
            trans.transact(chat_id, 12, amt, 1, proj.group_id, proj.issue, proj.id, '抢包收益', '中雷')
            # 变更账户表和账变表 - 中雷赔付
            trans.transact(chat_id, 11, ray_amt, 1, proj.group_id, proj.issue, proj.id, '中雷赔付', '')
        else:
            # 变更账户表和账变表 - 抢包收益
            trans.transact(chat_id, 12, amt, 1, proj.group_id, proj.issue, proj.id, '抢包收益', '未中雷')
        return [True, reply_text]


getRedbags = GetRedbags()


class MyPwd:
    def __init__(self):
        self.cipher = Fernet(params['mykey'])

    # 加密
    def __encrypt(self, password):
        return self.cipher.encrypt(password.encode('utf-8'))

    # 解密
    def decrypt(self, ciphertext):
        return self.cipher.decrypt(ciphertext).decode('utf-8')

    # 提取用户密码
    def __getitem__(self, chat_id) -> str:
        ciphertext = myUsers[chat_id].fund_pwd
        if ciphertext != '':
            return self.decrypt(ciphertext)
        else:
            return '无'

    # 设置用户密码
    def __setitem__(self, chat_id, value):
        ciphertext = self.__encrypt(value)
        myUsers[chat_id] = ('fund_pwd', ciphertext)

    # 验证密码是否正确
    def check(self, chat_id, password) -> bool:
        real_ciphertext = myUsers[chat_id].fund_pwd
        real_password = self.decrypt(real_ciphertext)
        return password == real_password


myPwd = MyPwd()


class TLinks:
    def __init__(self, values):
        (self.chat_id, self.group_id, self.url, self.cnt, self.created_at) = values


class MyLinks:
    def __init__(self):
        self.__sql_str = "select chat_id, group_id, url, cnt, created_at from links where chat_id=%s and group_id=%s"

    # 获取链接
    def get_link(self, user_id, group_id) -> TLinks | None:
        rst = read_connector.run(self.__sql_str, [user_id, group_id])
        if len(rst) == 0:
            return None
        else:
            return TLinks(rst[0])

    # 新增推广链接
    def new(self, user_id, group_id, url):
        sql_str = f"insert into links(chat_id, group_id, url, created_at) values({user_id},{group_id},'{url}'," \
                  f"current_timestamp()) on duplicate key update url='{url}'"
        exe_connector.run(sql_str)

    # 新增推广成员，若新增成员之前已经是别人的下级，则不算有效成员
    def invite(self, chat_id, group_id, invited_id):
        user = myUsers[invited_id, False, chat_id]
        if user.parent_id == chat_id:
            # 更新推广日志
            sql_str = f"insert into links_log(chat_id,group_id,invited_id,created_at) values({chat_id},{group_id}," \
                      f"{invited_id},current_timestamp()) on duplicate key update created_at=created_at"
            exe_connector.run(sql_str)
            # 统计有效推广人数
            sql_str = f"select count(1) from links_log where chat_id={chat_id} and group_id={group_id}"
            rst = read_connector.run(sql_str)
            # 更新推广人数
            sql_str = f"insert into links(chat_id,group_id,cnt,created_at) values({chat_id},{group_id},{rst[0][0]}," \
                      f"current_timestamp()) on duplicate key update cnt={rst[0][0]}"
            exe_connector.run(sql_str)


myLinks = MyLinks()


class TProfits:
    def __init__(self, values):
        if len(values) == 9:
            (self.deposit, self.withdrawal, self.turnover, self.price, self.coupon, self.invited_comm, self.owner_comm,
             self.profit, self.invited_cnt) = values
        else:
            (self.data_date, self.chat_id, self.deposit, self.withdrawal, self.turnover, self.price, self.coupon,
             self.invited_comm, self.owner_comm, self.profit, self.invited_cnt, self.updated_at,  self.created_at) \
                = values


class Profits:
    def __init__(self):
        self.__sql_report = "select ifnull(sum(deposit),0), ifnull(sum(withdrawal),0), ifnull(sum(turnover),0), " \
                            "ifnull(sum(price),0), ifnull(sum(coupon),0), ifnull(sum(invited_comm),0), ifnull(sum(" \
                            "owner_comm),0), ifnull(sum(profit),0), ifnull(sum(invited_cnt),0) from profits "
        self.__sql_str = "select data_date, chat_id, deposit, withdrawal, turnover, price, coupon, invited_comm, " \
                         "owner_comm,profit, invited_cnt, updated_at, created_at from profits where data_date=%s and " \
                         "chat_id=%s"

    # 提取某个user某一天的盈亏
    def __getitem__(self, args) -> TProfits:
        rst = read_connector.run(self.__sql_str, args)
        return TProfits(rst[0])

    # 提取报表盈亏信息
    def report(self, chat_id, type='当天') -> TProfits:
        # 更新本chat_id在当天的盈亏情况
        exe_connector.pro('update_profits', [chat_id, 1])
        # 获取当前日期
        now_date = datetime.datetime.now().date()
        rst = []
        # 时间段
        if isinstance(type, list):
            rst = read_connector.run(self.__sql_report + "where chat_id=%s and data_date between %s and %s",
                                     [chat_id, type[0], type[1]])
        elif type == '当天':
            rst = read_connector.run(self.__sql_report+"where chat_id=%s and data_date=%s", [chat_id, now_date])
        elif type == '昨天':
            # 获取上1天的日期
            pre_date = now_date - timedelta(days=1)
            rst = read_connector.run(self.__sql_report + "where chat_id=%s and data_date=%s", [chat_id, pre_date])
        elif type == '前天':
            # 获取上2天的日期
            pre2_date = now_date - timedelta(days=2)
            rst = read_connector.run(self.__sql_report + "where chat_id=%s and data_date=%s", [chat_id, pre2_date])
        elif type == '本月':
            # 获取这个月月初的日期
            month_1 = now_date.replace(day=1)
            rst = read_connector.run(self.__sql_report + "where chat_id=%s and data_date>=%s", [chat_id, month_1])
        elif type == '上月':
            # 获取这个月月初的日期
            month_1 = now_date.replace(day=1)
            # 获取上个月月初的日期
            pre_month_1 = month_1 - relativedelta(months=1)
            # 获取上个月月末的日期
            pre_month_end = month_1 - timedelta(days=1)
            rst = read_connector.run(self.__sql_report + "where chat_id=%s and data_date between %s and %s",
                                     [chat_id, pre_month_1, pre_month_end])
        elif type == '上2月':
            # 获取这个月月初的日期
            month_1 = now_date.replace(day=1)
            # 获取上个月月初的日期
            pre_month2_1 = month_1 - relativedelta(months=2)
            # 获取上个月月末的日期
            pre_month2_end = month_1 - relativedelta(months=1) - timedelta(days=1)
            rst = read_connector.run(self.__sql_report + "where chat_id=%s and data_date between %s and %s",
                                     [chat_id, pre_month2_1, pre_month2_end])
        return TProfits([0]*9 if len(rst) == 0 else rst[0])


myProfit = Profits()


class TUaccs:
    def __init__(self, values):
        (self.name, self.addr, self.msg_id, self._balance, self._is_dp, self._is_wd, self.updated_at) = values

    @property
    def balance(self):
        return self._balance

    @balance.setter
    def balance(self, value):
        sql_str = f"UPDATE uaccs SET balance=%s WHERE name = %s"
        exe_connector.run(sql_str, [value, self.name])

    @property
    def is_dp(self):
        return self._is_dp

    @is_dp.setter
    def is_dp(self, value):
        sql_str = f"UPDATE uaccs SET is_dp = IF(name = %s, %s, 0), updated_at = IF(name = %s, CURRENT_TIMESTAMP(), " \
                  f"updated_at)"
        exe_connector.run(sql_str, [self.name, value, self.name])

    @property
    def is_wd(self):
        return self._is_wd

    @is_wd.setter
    def is_wd(self, value):
        sql_str = f"UPDATE uaccs SET is_wd = IF(name = %s, %s, 0), updated_at = IF(name = %s, CURRENT_TIMESTAMP(), " \
                  f"updated_at)"
        exe_connector.run(sql_str, [self.name, value, self.name])


class Uaccs:
    def __init__(self):
        self.__sql_str = "select name, addr, msg_id, balance, is_dp, is_wd, updated_at from uaccs "
        self.dp: TUaccs or None = None
        self.wd: TUaccs or None = None
        self.reload()

    def __getitem__(self, args) -> TUaccs:
        rst = read_connector.run(self.__sql_str + "where name=%s", [args])
        return TUaccs(rst[0])

    def reload(self):
        self.dp = None
        self.wd = None
        rst = read_connector.run(self.__sql_str + "where is_dp=1 or is_wd=1")
        for row in rst:
            if row[4] == 1:
                self.dp = TUaccs(row)
            if row[5] == 1:
                self.wd = TUaccs(row)

    def all(self):
        rst = read_connector.run(self.__sql_str + "order by name")
        return rst


uaccs = Uaccs()


class TDeposits:
    def __init__(self, values):
        (self.id, self.chat_id, self.uacc_name, self.addr, self.dep_id, self._msg_id, self.amt_apply, self.amt_require,
         self.amt_real, self._status, self.updated_at, self.created_at) = values

    @property
    def status(self):
        return self._status

    @status.setter
    def status(self, value):
        sql_str = "update deposits set status=%s, updated_at=current_timestamp() where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._status = value

    @property
    def msg_id(self):
        return self._msg_id

    @msg_id.setter
    def msg_id(self, value):
        sql_str = "update deposits set msg_id=%s where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._msg_id = value


class Deposits:
    def __init__(self):
        self.__sql_str = "select id, chat_id, uacc_name, addr, dep_id, msg_id, amt_apply, amt_require, amt_real, " \
                         "status, updated_at, created_at from deposits "

    def __getitem__(self, args) -> TDeposits or None:
        rst = read_connector.run(self.__sql_str + "where id=%s", [args])
        if len(rst) > 0:
            return TDeposits(rst[0])

    def last(self, chat_id) -> TDeposits or None:
        rst = read_connector.run(
            self.__sql_str + "where chat_id=%s and created_at>=date_add(CURRENT_TIMESTAMP(), interval -20 "
                             "minute)order by id desc",
            [chat_id])
        if len(rst) > 0:
            return TDeposits(rst[0])

    def apply(self, chat_id, amt_apply):
        rst = read_connector.run(
            self.__sql_str + "where chat_id=%s and created_at>=date_add(CURRENT_TIMESTAMP(), interval -20 minute)",
            [chat_id])
        if len(rst) > 5:
            return False, "您充值太频繁，请稍后再充"
        if uaccs.dp is None:
            return False, f"目前没有收款账号，请联系客服 @bowin_boss 进行充值"
        # 确认要求金额
        for i in range(1, 10):
            tail = 70 + ((params['dp_tail'] - 1) - 70) % 30
            amt_require = amt_apply - 1 + tail / 100
            sql_str = self.__sql_str + "where amt_require=%s and created_at>=date_add(CURRENT_TIMESTAMP(), " \
                                       "interval -20 minute)"
            rst = read_connector.run(sql_str, [amt_require])
            if len(rst) == 0:
                sql_str2 = f"insert into deposits(chat_id,uacc_name,addr,amt_apply,amt_require,updated_at,created_at) " \
                          f"values(%s, %s, %s, %s, %s, current_timestamp(),current_timestamp())"
                exe_connector.run(sql_str2, [chat_id, uaccs.dp.name, uaccs.dp.addr, amt_apply, amt_require])
                params['dp_tail'] = tail
                end_time = datetime.datetime.now() + timedelta(minutes=15)
                f_end_time = end_time.strftime("%m-%d %H:%M:%S")
                return (True, f"充值地址(TRC20)：{e_down3}点击复制\n<code>{uaccs.dp.addr}</code>\n\n"
                              f"要求到账金额：<b>{amt_require}</b>U\n"
                              f"截止充值时间：{f_end_time}\n"
                              f"支付完成后，请<b>截图</b>到本会话中，否则不会上分\n\n"
                              f"{e_warning} <b>请仔细核对地址，若与图片中地址不一致，请停止充值并反馈给客服</b>\n")
        else:
            return False, "目前充值用户较多，系统繁忙，请稍后再充"

    def obj_dep(self):
        return read_connector.run(
            "select distinct uacc_name from deposits where status=1 and created_at>=date_add(CURRENT_TIMESTAMP(), "
            "interval -20 minute)")

    async def check(self):
        # 匹配
        sql_str = "select a.id, a.chat_id, a.uacc_name, a.addr, b.dep_id, a.msg_id, a.amt_apply, a.amt_require, " \
                  "b.amt, a.status, a.updated_at, a.created_at from (select * from deposits where " \
                  "created_at>=date_add(CURRENT_TIMESTAMP(), interval -20 minute) and status=1) a join (select * from " \
                  "deposits_log where ts>=date_add(CURRENT_TIMESTAMP(), interval -20 minute) and id=0 and state='2') " \
                  "b on a.addr=b.toAcc and a.amt_require=b.amt where a.created_at<b.ts"
        rst = read_connector.run(sql_str)
        for row in rst:
            dep = TDeposits(row)
            # 更新deposits
            sql_str = "update deposits set dep_id=%s, amt_real=%s, status=2, updated_at=CURRENT_TIMESTAMP() where " \
                      "id=%s"
            exe_connector.run(sql_str, [dep.dep_id, dep.amt_real, dep.id])
            # 更新deposits_log
            sql_str = "update deposits_log set id=%s where dep_id=%s"
            exe_connector.run(sql_str, [dep.id, dep.dep_id])
            # 产生充值账变
            trans.transact(dep.chat_id, 1, dep.amt_apply, issue=str(dep.id), tag=dep.uacc_name, note=dep.dep_id)
            # 告诉客户充值成功
            await clients['bowin_asstbot'].send_message(dep.chat_id,
                                                        f"恭喜！您的账号已充值<b>{dep.amt_apply:,.2f}</b>U，祝您游戏愉快！",
                                                        parse_mode=ParseMode.HTML,
                                                        disable_web_page_preview=True
                                                        )
            # 变更充值截图信息
            prompt = line + e_gou + f" #<b>客户已充值成功</b>"
            await clients['bowin_asstbot'].edit_message_caption(
                params['bill_id'],
                dep.msg_id,
                caption=f"C{dep.id} <code>{myUsers[dep.chat_id].id:05}</code> {e_to} <code>{dep.uacc_name}</code>\n"
                        f"{dep.amt_require:,.2f}U {dep.created_at}\n" + prompt,
                parse_mode=ParseMode.HTML
                )
            # 防止频繁发出消息
            await asyncio.sleep(3)
        # 没有匹配上，而又超时的记录，取消，并只对发了图片的客户提醒
        sql_str = self.__sql_str + "where created_at<date_add(CURRENT_TIMESTAMP(), interval -20 minute) and status=1"
        rst = read_connector.run(sql_str)
        for row in rst:
            dep = TDeposits(row)
            await clients['bowin_asstbot'].send_message(
                dep.chat_id,
                f"您在<i>{dep.created_at}</i>的充值因为没找到充值记录而被取消，请重新核实",
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
                )
            prompt = line + e_remind + f"没有找到充值记录，取消"
            await clients['bowin_asstbot'].edit_message_caption(
                params['bill_id'],
                dep.msg_id,
                caption=f"C{dep.id} <code>{myUsers[dep.chat_id].id:05}</code> {e_to} <code>{dep.uacc_name}</code>\n"
                        f"{dep.amt_require:,.2f}U {dep.created_at}\n" + prompt,
                parse_mode=ParseMode.HTML
                )
        sql_str = "update deposits set status=3, updated_at=CURRENT_TIMESTAMP() where created_at<date_add(" \
                  "CURRENT_TIMESTAMP(), interval -20 minute) and status in (0,1)"
        exe_connector.run(sql_str)
        # 若20分钟内没有需要匹配的充值记录，则停止监控
        sql_str = "select * from deposits where created_at>=date_add(CURRENT_TIMESTAMP(), interval -20 minute) " \
                  "and status=1"
        rst = read_connector.run(sql_str)
        if len(rst) == 0:
            sql_str = "update monits set status='0' where id='DEPCHECK'"
            exe_connector.run(sql_str)
        else:
            # 检查超过20分钟，则停止监控
            sql_str = "update monits set status='0' where init_time<date_add(CURRENT_TIMESTAMP(), interval -20 " \
                      "minute) and id='DEPCHECK'"
            exe_connector.run(sql_str)


deposits = Deposits()


class TDepositsLog:
    def __init__(self, values):
        (self.dep_id, self.ccy, self.chain, self.amt, self.fromAcc, self.areaCodeFrom, self.toAcc, self.txid, self.ts,
         self.state, self.fromWdId, self.amtConfirm, self.id) = values


class DepositsLog:
    def __init__(self):
        self.__sql_str = "select dep_id, ccy, chain, amt, fromAcc, areaCodeFrom, toAcc, txid, ts, state, fromWdId, " \
                         "amtConfirm, id from deposits_log "


depositsLog = DepositsLog()


class TWithdraws:
    def __init__(self, values):
        (self.id, self.chat_id, self._from_name, self._from_acc, self.to_acc, self._wd_id, self._msg_id, self.amt_apply,
         self.amt_real, self._status, self.updated_at, self.created_at) = values

    @property
    def from_name(self):
        return self._from_name

    @from_name.setter
    def from_name(self, value):
        sql_str = "update withdraws set from_name=%s where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._from_name = value

    @property
    def from_acc(self):
        return self._from_acc

    @from_acc.setter
    def from_acc(self, value):
        sql_str = "update withdraws set from_acc=%s where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._from_acc = value

    @property
    def status(self):
        return self._status

    @status.setter
    def status(self, value):
        sql_str = "update withdraws set status=%s, updated_at=current_timestamp() where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._status = value

    @property
    def msg_id(self):
        return self._msg_id

    @msg_id.setter
    def msg_id(self, value):
        sql_str = "update withdraws set msg_id=%s where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._msg_id = value

    @property
    def wd_id(self):
        return self._wd_id

    @wd_id.setter
    def wd_id(self, value):
        sql_str = "update withdraws set wd_id=%s where id=%s"
        exe_connector.run(sql_str, [value, self.id])
        self._wd_id = value


class Withdraws:
    def __init__(self):
        self.__sql_str = "select id, chat_id, from_name, from_acc, to_acc, wd_id, msg_id, amt_apply, amt_real, " \
                         "status, updated_at, created_at from withdraws "

    def __getitem__(self, args) -> TWithdraws or None:
        rst = read_connector.run(self.__sql_str + "where id=%s", [args])
        if len(rst) > 0:
            return TWithdraws(rst[0])

    def apply(self, chat_id, amt_apply):
        if uaccs.wd is None:
            name = ''
            addr = ''
        else:
            name = uaccs.wd.name
            addr = uaccs.wd.addr
        # 产生提现记录
        sql_str = f"insert into withdraws(chat_id,from_name,from_acc,to_acc,amt_apply,updated_at,created_at) " \
                  f"values(%s, %s, %s, %s, %s, current_timestamp(),current_timestamp())"
        exe_connector.run(sql_str, [chat_id, name, addr, myUsers[chat_id].usdt, amt_apply])
        rst = read_connector.run(self.__sql_str + "where chat_id=%s and amt_apply=%s order by id desc limit 1",
                                 [chat_id, amt_apply])
        wd = TWithdraws(rst[0])
        # 产生冻结账变
        trans.transact(chat_id, 5, amt_apply, issue=str(wd.id), tag=wd.from_name)
        return wd

    def get_his(self, chat_id, rows):
        sql_str = self.__sql_str + "where chat_id=%s order by id desc limit %s"
        return read_connector.run(sql_str, [chat_id, rows])

    def obj_wd(self):
        return read_connector.run("select distinct from_name from withdraws where status=1")

    async def check(self):
        from plugins.bowin.common import content_withdraw_send
        # 匹配
        sql_str = "select a.id, a.chat_id, a.from_name, a.from_acc, a.to_acc, a.wd_id, a.msg_id, a.amt_apply, " \
                  "b.amt+b.fee, 2, CURRENT_TIMESTAMP(), a.created_at from (select * from withdraws where " \
                  "updated_at>=date_add(CURRENT_TIMESTAMP(), interval -20 minute) and status=1) a join (select * from " \
                  "withdraws_log where ts>=date_add(CURRENT_TIMESTAMP(), interval -20 minute) and state='2') b on " \
                  "a.to_acc=b.toAcc and a.amt_apply=b.amt"
        rst = read_connector.run(sql_str)
        for row in rst:
            wd = TWithdraws(row)
            # 更新withdraws
            sql_str = "update withdraws set amt_real=%s, status=2, updated_at=CURRENT_TIMESTAMP() where id=%s"
            exe_connector.run(sql_str, [wd.amt_real, wd.id])
            # 告诉客户充值成功
            await clients['bowin_asstbot'].send_message(wd.chat_id,
                                                        f"恭喜！您的账号已提现<b>{wd.amt_apply:,.2f}</b>U！",
                                                        parse_mode=ParseMode.HTML,
                                                        disable_web_page_preview=True
                                                        )
            # 变更充值截图信息
            prompt = line + e_gou + f" #<b>客户已提现成功</b>"
            await clients['bowin_asstbot'].edit_message_text(
                params['log_id'],
                wd.msg_id,
                content_withdraw_send(wd) + prompt,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
                )
            # 防止频繁发出消息
            await asyncio.sleep(3)
        # 没有匹配上，而又超时的记录，登记提现失败，并只对发了图片的客户提醒
        sql_str = "update withdraws set status=4, updated_at=CURRENT_TIMESTAMP() where updated_at<date_add(" \
                  "CURRENT_TIMESTAMP(), interval -20 minute) and status = 1"
        exe_connector.run(sql_str)
        sql_str = self.__sql_str + "where updated_at<date_add(CURRENT_TIMESTAMP(), interval -20 minute) and status=1"
        rst = read_connector.run(sql_str)
        for row in rst:
            wd = TWithdraws(row)
            prompt = line + e_gou + f" #<b>客户已提现失败</b>"
            await clients['bowin_asstbot'].edit_message_text(
                params['log_id'],
                wd.msg_id,
                content_withdraw_send(wd) + prompt,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
                )
        # 若20分钟内没有需要匹配的提现记录，则停止监控
        sql_str = "select * from withdraws where updated_at>=date_add(CURRENT_TIMESTAMP(), interval -20 minute) " \
                  "and status=1"
        rst = read_connector.run(sql_str)
        if len(rst) == 0:
            sql_str = "update monits set status='0' where id='WDCHECK'"
            exe_connector.run(sql_str)
        else:
            # 检查超过20分钟，则停止监控
            sql_str = "update monits set status='0' where init_time<date_add(CURRENT_TIMESTAMP(), interval -20 " \
                      "minute) and id='WDCHECK'"
            exe_connector.run(sql_str)


withdraws = Withdraws()


class TWithdrawsLog:
    def __init__(self, values):
        (self.wd_id, self.clientId, self.ccy, self.chain, self.nonAsset, self.amt, self.ts, self.fromAcc,
         self.areaCodeFrom, self.toAcc, self.areaCodeTo, self.txId, self.fee, self.feeCcy, self.state) = values


class WithdrawsLog:
    def __init__(self):
        self.__sql_str = "select wd_id, clientId, ccy, chain, nonAsset, amt, ts, fromAcc, areaCodeFrom, toAcc, " \
                         "areaCodeTo, txId, fee, feeCcy, state from withdraws_log "


withdrawsLog = WithdrawsLog()


from okx import Funding, Account


class OKX:
    def __init__(self, acc_name):
        self.fund = None
        self.account = None
        self.set(acc_name)

    def set(self, acc_name):
        api_key = sys_conf.read('api_key', acc_name)
        secret_key = sys_conf.read('secret_key', acc_name)
        passphrase = myPwd.decrypt(params['okx_pwd', acc_name])
        self.fund = Funding.FundingAPI(api_key, secret_key, passphrase, True, '0')
        self.account = Account.AccountAPI(api_key, secret_key, passphrase, True, '0')

    # 获取余额
    @property
    async def avail(self):
        await self.collect_funds()
        rst = await asyncio.to_thread(self.fund.get_balances, 'USDT')
        return Decimal(next((item['availBal'] for item in rst['data'] if item['ccy'] == 'USDT'), None))

    # 资金归集到资金账户
    async def collect_funds(self):
        try:
            rst = await asyncio.to_thread(self.account.get_account_balance, 'USDT')
            data = rst['data'][0]
            balance = next((item['cashBal'] for item in data['details'] if item['ccy'] == 'USDT'), None)
            if balance is not None and float(balance) > 1:
                await asyncio.to_thread(self.fund.funds_transfer, type='0', ccy='USDT', amt=balance, from_='18', to='6')
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # # 提现操作 此步骤做不了，因为需要认证过的地址才能转账
    # async def withdraw(self, wd: TWithdraws):
    #     try:
    #         # 检查余额是否足够
    #         balance = await self.avail
    #         if balance < wd.amt_apply + Decimal('1.5'):
    #             return False, f"余额不够，账户只有 <b>{balance:,.2f}</b> U"
    #         # 改变提现状态
    #         wd.status = 1
    #         # 进行提现操作
    #         rst = await asyncio.to_thread(self.fund.withdrawal, ccy='USDT', toAddr=wd.to_acc,
    #                                       amt=str(wd.amt_apply), dest='4', chain='USDT-TRC20', clientId=str(wd.id),
    #                                       fee='1.5')
    #         print(rst)
    #         data = rst['data']
    #         if len(data) == 0:
    #             return False, rst['msg']
    #         else:
    #             wd.wd_id = data[0]['wdId']
    #             return True, wd.wd_id
    #     except Exception as e:
    #         sys_log.write_log(traceback.format_exc(), 'a')

    # 同步充值记录到本地 pre_idx=0 当天数据 pre_idx=1上一天到现在的数据
    async def load_dep(self, pre_idx):
        today = datetime.datetime.now()
        # 当天凌晨
        midnight_today = datetime.datetime(today.year, today.month, today.day)
        # 目标日期凌晨
        start_time = midnight_today - timedelta(days=pre_idx)
        start_timestamp = str(int(start_time.timestamp()*1000))
        # 请求充值记录
        rst = await asyncio.to_thread(self.fund.get_deposit_history, before=start_timestamp)
        for row in rst['data']:
            amt = Decimal(row['amt']).quantize(Decimal('1.0000'))
            ts = datetime.datetime.fromtimestamp(int(row['ts']) / 1000).strftime('%Y-%m-%d %H:%M:%S')
            sql_str = f"insert into deposits_log(dep_id, ccy, chain, amt, fromAcc, areaCodeFrom, toAcc, txid, ts, " \
                      f"state, fromWdId, amtConfirm) values('{row['depId']}','{row['ccy']}','{row['chain']}',{amt}," \
                      f"'{row['from']}','{row['areaCodeFrom']}','{row['to']}','{row['txId']}','{ts}', '{row['state']}'," \
                      f"'{row['fromWdId']}','{row['actualDepBlkConfirm']}') ON DUPLICATE key update state='{row['state']}'"
            exe_connector.run(sql_str)

    # 同步提现记录到本地 pre_idx=0 当天数据 pre_idx=1上一天到现在的数据
    async def load_wd(self, pre_idx):
        today = datetime.datetime.now()
        # 当天凌晨
        midnight_today = datetime.datetime(today.year, today.month, today.day)
        # 目标日期凌晨
        start_time = midnight_today - timedelta(days=pre_idx)
        start_timestamp = str(int(start_time.timestamp()*1000))
        # 请求充值记录
        rst = await asyncio.to_thread(self.fund.get_withdrawal_history, before=start_timestamp)
        for row in rst['data']:
            amt = Decimal(row['amt']).quantize(Decimal('1.0000'))
            fee = Decimal(row['fee']).quantize(Decimal('1.0000'))
            ts = datetime.datetime.fromtimestamp(int(row['ts']) / 1000).strftime('%Y-%m-%d %H:%M:%S')
            nonAsset = 1 if row['nonTradableAsset'] else 0
            sql_str = f"insert into withdraws_log(wd_id, clientId, ccy, chain, nonAsset, amt, ts, fromAcc, " \
                      f"areaCodeFrom, toAcc, areaCodeTo, txId, fee, feeCcy, state) values('{row['wdId']}'," \
                      f"'{row['clientId']}','{row['ccy']}','{row['chain']}',{nonAsset},{amt},'{ts}','{row['from']}'," \
                      f"'{row['areaCodeFrom']}', '{row['to']}','{row['areaCodeTo']}','{row['txId']}',{fee}," \
                      f"'{row['feeCcy']}','{row['state']}') ON DUPLICATE key update state='{row['state']}'"
            exe_connector.run(sql_str)



