# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from plugins.bowin.common import *


# 内联按钮界面
def get_imp_en(index='-', param=None):
    try:
        # 参数[progress, bet_amt, ray, proj.id, proj.owner_id]
        if index == 'send_redbag':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(f"{e_redbag}抢红包[6/{param.get_cnt}]总 {param.bet_amt:.0f}U {e_ray}雷{param.ray}",
                                      callback_data=f"redbag_{param.id}")],
                [InlineKeyboardButton("充值", callback_data="deposit"),
                 InlineKeyboardButton("介绍", callback_data='introduce'),
                 InlineKeyboardButton("余额", callback_data='avail')],
                [InlineKeyboardButton("客服", url=link(param.owner_id)),
                 InlineKeyboardButton("助理", url="t.me/bowin_asstbot"),
                 InlineKeyboardButton("商务", url="t.me/bowin_boss")]
                ])
        # 参数[proj.owner_id]
        elif index == 'main':
            return InlineKeyboardMarkup([
                [InlineKeyboardButton("充值", callback_data='deposit'),
                 InlineKeyboardButton("介绍", callback_data='introduce'),
                 InlineKeyboardButton("余额", callback_data='avail')],
                [InlineKeyboardButton("客服", url=link(param)),
                 InlineKeyboardButton("常规操作", url="t.me/bowin_asstbot"),
                 InlineKeyboardButton("商务", url="t.me/bowin_boss")]
                ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发红包函数
async def send_redbag_en(group_id, user_id, bet_amt, ray):
    try:
        game = games[group_id]
        user = myUsers[user_id, False, game.owner_id]
        myUsers.active(user_id)
        # 检查群权限
        if not (game.game_type == 1 and game.status == 1):
            contect = (f"本群无法玩<b>抢红包</b>游戏，请群主与<a href='{link(params['boss_id'])}'>"
                       f"{myChats[params['boss_id']].outname}</a> 联系")
            await clients['bowin_redbagbot'].send_message(group_id, f"尊敬的 {myChats[user_id].outname}\n{contect}",
                                                          parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            return contect
        # 检查红包金额范围
        if not (5 <= bet_amt <= 2000):
            contect = "红包金额应该在 <b>[5, 2000]</b> 之间，请重新发"
            await clients['bowin_redbagbot'].send_message(group_id, f"尊敬的 {myChats[user_id].outname}\n{contect}",
                                                          parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            return contect
        # 检查avail是否有足够多钱
        if bet_amt > accs[user_id].avail:
            contect = f"{myChats[user_id].outname}，您余额不够，请联系 <a href='{link(game.owner_id)}'>" \
                      f"{myChats[game.owner_id].outname}</a> 进行充值"
            await clients['bowin_redbagbot'].send_message(group_id, f"尊敬的 {myChats[user_id].outname}\n{contect}",
                                                          parse_mode=ParseMode.HTML, disable_web_page_preview=True)
            return contect
        # 登记注单
        proj = projRedbags.new(group_id, user_id, bet_amt, ray)
        # 变更账户表和账变表
        trans.transact(user_id, 11, bet_amt, 1, group_id, proj.issue, proj.id, '发红包', '')
        # 发消息
        content = f"[{myChats[user_id].short_outname(6)}]发了个{bet_amt}U的红包，快来抢！"
        msg = await clients['bowin_redbagbot'].copy_message(
            group_id,
            params['my_channel'],
            2,
            caption=content,
            parse_mode=ParseMode.HTML,
            reply_markup=get_imp('send_redbag', proj))
        # 登记消息ID
        projRedbags[proj.id] = ('msg_id', msg.id)
        return msg.id
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 发红包消息
@Client.on_message(~filters.bot & ~filters.chat([params['log_id'], params['studio_id']]) &
                   filters.group & filters.incoming & filters.regex('^\d{1,4}[/-]\d$', re.I))
async def send_redbag_message_en(client, message):
    try:
        user_id = message.from_user.id
        if myUsers[user_id].status in (3, 5):
            status = mean("users.status", myUsers[user_id].status)
            boss_chat = myChats[params['boss_id']]
            await client.send_message(message.chat.id,
                                      f"{myUsers[user_id].outname}，目前您的状态是<b>{status}</b>，暂时不能游戏，请与<a href='{link(boss_chat.id)}'>"
                                      f"{boss_chat.outname}</a>联系",
                                      parse_mode=ParseMode.HTML,
                                      disable_web_page_preview=True)
            return
        (amt, ray) = message.text.replace("/", "-").split('-')
        await send_redbag(message.chat.id, message.from_user.id, int(amt), int(ray))
        await message.delete()
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 响应-页面
@Client.on_callback_query()
async def callback_interface_en(client, callback):
    try:
        # 新增/活跃成员
        user_id = callback.from_user.id
        game = games[callback.message.chat.id]
        user = myUsers[user_id, False, game.owner_id]
        myUsers.active(user_id)
        # 抢包
        if re.match('^redbag_\d+', callback.data, re.S) is not None:
            if user.status in (3, 5):
                status = mean("users.status", user.status)
                boss_chat = myChats[params['boss_id']]
                await client.send_message(callback.message.chat.id,
                                          f"{user.outname}，目前您的状态是<b>{status}</b>，暂时不能游戏，请与<a href='"
                                          f"{link(boss_chat.id)}'>{boss_chat.outname}</a>联系",
                                          parse_mode=ParseMode.HTML,
                                          disable_web_page_preview=True)
                return
            proj_id = int(callback.data.split('_')[1])
            feedback = getRedbags.new(proj_id, user_id)
            await callback.answer(feedback[1], show_alert=True)
            if feedback[0]:
                proj = projRedbags[proj_id]
                if proj.get_cnt == 6:
                    # 抢包人数到达后，结算抢包结果
                    await projRedbags.close(proj_id, get_imp('main', proj.owner_id))
                else:
                    # 否则，只修改抢包数量
                    await callback.edit_message_reply_markup(
                        get_imp('send_redbag', proj))
        # 充值
        elif callback.data == 'deposit':
            content = f"请联系助理(@bowin_asstbot)进行充值操作"
            await callback.answer(content, show_alert=True)
            await send_to_ws(f"发包员|助理|发起充值|{user_id}")
        # 介绍
        elif callback.data == 'introduce':
            content = f"请留意助理(@bowin_asstbot)介绍本群组"
            await callback.answer(content, show_alert=True)
            await send_to_ws(f"发包员|助理|群组介绍|{user_id}|{game.group_id}")
        # 余额
        elif callback.data == 'avail':
            content = f"尊敬的 {myChats[user_id].outname}\n\nID号： {user_id}\n余额： {accs[user_id].avail:,.2f}U "
            await callback.answer(content, show_alert=True)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# test收到信息
@Client.on_message(filters.text)
async def other_message_en(client, message):
    try:
        print(f"redbag_bot 收到 {myChats[message.from_user.id].outname} 的消息")
        # msg = await client.send_message(6985305888, message.text)
        # print(msg)
        # print(message)
        # # print(message.text)
        # msg = await message.reply("已收到其它信息： "+message.text.html, quote=False,
        #                           business_connection_id=message.business_connection_id,
        #                           reply_markup=get_imp('test'))
        # print(msg)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')












