# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'

import math

from plugins.bowin.class_sub import *


redbag_groups = [-1002093645173]


# 字段转义
def mean(type, text='', point='to'):
    to_dic = {}
    # 设定转义内容
    if type == 'monits.status':
        to_dic = {'0': '已停用', '1': '已启动'}
    elif type == 'users.role':
        to_dic = {0: '未充值客户', 1: '有效客户', 2: '群主', 3: '机器人'}
    elif type == 'users.status':
        to_dic = {0: '正常', 1: '禁止加群', 2: '禁止发言', 3: '禁止游戏', 4: '禁止出金', 5: '禁用全部'}
    elif type == 'users.is_remind':
        to_dic = {0: '拒绝提醒', 1: '接受提醒'}
    elif type == 'withdraws.status':
        to_dic = {0: '提现审核', 1: '放款中', 2: '已放款', 3: '审核不通过', 4: '提现失败'}
    elif type == 'games.status':
        to_dic = {0: '未启动', 1: '运营中', 2: '待收租', 3: '暂停', 4: '关闭'}
    elif type == 'chatUser.status':
        to_dic = {0: '创建者', 1: '管理员', 2: '成员', 3: '已离开', 4: '被限制', 5: '被禁止', 8: '未知'}
        # games.is_en
    elif type == 'games.is_en':
        to_dic = {0: '中文', 1: '英文'}
    # 确定转义方向
    if point == 'to':
        return to_dic.get(text, None)
    elif point == 'from':
        from_dict = {v: k for k, v in to_dic.items()}
        return from_dict.get(text, None)
    else:
        return None


# 输入验证
async def input_cert(str_input='', style='None', client=None, user_id=0):
    try:
        rst_finder = fuzzy_finder(
            ['.*delete.*', '.*drop.*', '.*alter.*', '.*add.*', '.*insert.*', '.*select.*', '.*update.*'],
            [str_input.lower()])
        if len(rst_finder) > 0:
            await client.send_message(params['boss_id'],
                                      text=f"用户 {myUsers[user_id].outname}(<code>{user_id}</code>) 在渗透入侵，"
                                           f"类型 {style}，内容 {str_input}，需尽快落实应对策略！",
                                      parse_mode=ParseMode.HTML)
            return [False, '请输入正确内容']
        if style == 'email':
            if len(str_input) > 50:
                return [False, '邮箱<b>长度太长</b>了，请输入正确邮箱']
            if re.match('^\w+@\w+.com$', str_input, re.S) is not None:
                return [True, str_input.lower()]
            else:
                return [False, '邮箱<b>格式</b>不正确，请重新输入']
        elif style == 'pwd':
            if not 3 < len(str_input) < 21:
                return [False, '密码长度应该在<b>4~20</b>之间，请重新输入']
            if re.match('^\S+', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '密码<b>不符合规范</b>，请重新输入']
        elif style == 'usdt':
            if re.match('^T[1-9A-HJ-NP-Za-km-z]{33}$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, '这不是合规的<b>USDT(trc20)</b>地址，请重新输入或联系管理员确认']
        elif style == 'receive_id':
            if str_input.isdigit():
                receive_id = int(str_input)
                if myChats[receive_id] is None:
                    return [False, '未收录到此ID，请联系转入方与平台交互后重试\n']
                else:
                    user = myUsers[receive_id, False, user_id]
                    return [True, user]
            else:
                return [False, '输入的ID不正确，请重新输入\n']
        elif style == 'withdraw_amt':
            if str_input.isdigit():
                amt = int(str_input)
                if accs[user_id].avail < amt:
                    return [False, '提现金额<b>不能大于</b>您的余额，请重新输入\n']
                else:
                    return [True, amt]
            else:
                return [False, '输入的金额必须为<b>整数</b>，请重新输入\n']
        elif style == 'transfer_amt':
            if str_input.isdigit():
                amt = int(str_input)
                if accs[user_id].avail < amt:
                    return [False, '转账金额<b>不能大于</b>您的余额，请重新输入\n']
                else:
                    return [True, amt]
            else:
                return [False, '输入的金额必须为<b>整数</b>，请重新输入\n']
        elif style == 'sent_freq':
            if str_input.isdigit():
                freq = int(str_input)
                if 60 <= freq <= 1200:
                    return [True, freq]
                else:
                    return [False, '最低发包频率应该在<b>[60,1200]</b>秒之间']
            else:
                return [False, '输入的频率必须为<b>整数</b>，请重新输入']
        elif style == 'sent_amt':
            if str_input.isdigit():
                amt = int(str_input)
                if 5 <= amt <= 200:
                    return [True, amt]
                else:
                    return [False, '最高发包金额应该在<b>[5,200]</b>之间']
            else:
                return [False, '输入的金额必须为<b>整数</b>，请重新输入']
        elif style == 'get_freq':
            if str_input.isdigit():
                freq = int(str_input)
                if 5 <= freq <= 120:
                    return [True, freq]
                else:
                    return [False, '最低抢包频率应该在<b>[5,120]</b>秒之间']
            else:
                return [False, '输入的频率必须为<b>整数</b>，请重新输入']
        elif style == 'get_end':
            if str_input.isdigit():
                time = int(str_input)
                if 60 <= time <= 600:
                    return [True, time]
                else:
                    return [False, '结束抢包时长应该在<b>[60,600]</b>之间']
            else:
                return [False, '输入的时长必须为<b>整数</b>，请重新输入']
        elif style == 'guest_report_自选':
            match = re.match('^(202\d-\d{2}-\d{2}) (202\d-\d{2}-\d{2})$', str_input, re.S)
            if match is not None:
                return [True, match.group(1), match.group(2)]
            else:
                return [False, "时间段格式不正确\n格式：YYYY-MM-DD YYYY-MM-DD"]
        elif style == 'dp_amt':
            if str_input.isdigit():
                amt = int(str_input)
                if amt >= 10:
                    return [True, amt]
                else:
                    return [False, '最小充值金额应大于10U，请重新输入']
            else:
                return [False, '输入的金额必须为<b>整数</b>，请重新输入']
        elif style == 'sysrate':
            try:
                num = float(str_input)
                if 0 < num < 0.1:
                    return [False, '比例单位为%，一般为0或者大于0.1的小数']
                elif num > 5:
                    return [False, '抽成比例超过5%，抽太多了']
                else:
                    return [True, num / 100]
            except ValueError:
                return [False, '输入的必须为小数，范围[0,5.0]']
        elif style == 'rentamt':
            if str_input.isdigit():
                amt = int(str_input)
                if 10 <= amt <= 1000:
                    return [True, amt]
                else:
                    return [False, '租金金额应该在<b>[10,1000]</b>之间']
            else:
                return [False, '输入的金额必须为<b>整数</b>，请重新输入']
        elif style == 'rentdate':
            if re.match('^20\d{2}-[01]\d-[0123]\d$', str_input, re.S) is not None:
                return [True, str_input]
            else:
                return [False, "时间段格式不正确\n格式：YYYY-MM-DD"]



        return [False, 'No this type']
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 内联按钮界面
def get_com_imp(index='-', param=None):
    try:
        # 客户
        if index == "guest":
            return InlineKeyboardMarkup([
                [InlineKeyboardButton(e_men + "团队", callback_data=f"guest_team_{param}"),
                 InlineKeyboardButton(e_market + "报表", callback_data=f"guest_report_{param}"),
                 InlineKeyboardButton(e_atm + "充提", callback_data=f"guest_counter_{param}")],
                [InlineKeyboardButton(e_page + "详细", callback_data=f"guest_detail_{param}"),
                 InlineKeyboardButton(e_traffic + "状态", callback_data=f"guest_status_{param}"),
                 InlineKeyboardButton(e_myOffer + "备注", callback_data=f"guest_note_{param}")]
                ])
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 助理 - 提现
def content_withdraw(user: TUsers):
    acc = accs[user.chat_id]
    limit = 0 if acc.wlimit < 0 else acc.wlimit
    content = f"""{e_atm}<b>提现</b>
{e_prize}ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail:,.2f}U
{e_climb}提现需打流水：{limit:,.2f}U
{e_warning}注意
{e_num[1]}需要配置资金密码
{e_num[2]}需要配置收款的USDT地址
{e_num[3]}需要<b>打够流水</b>后才能转出
"""
    return content


def content_withdraw_amt(user: TUsers, amt):
    acc = accs[user.chat_id]
    limit = 0 if acc.wlimit < 0 else acc.wlimit
    content = f"""{e_atm}<b>提现</b>
{e_prize}ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail:,.2f}U
{e_climb}提现需打流水：{limit:,.2f}U
{e_cheng}提现金额：{amt:,.0f}U
{e_money}提后余额：{acc.avail-amt:,.2f}U
{e_money2}USDT地址：<code>{user.usdt}</code>
    """
    return content


def content_withdraw_approve(wd: TWithdraws):
    user = myUsers[wd.chat_id]
    acc = accs[wd.chat_id]
    status = mean('withdraws.status', wd.status)
    content = f"""{e_atm}<b>提现</b>
{e_id}申请ID：<code>{wd.id}</code>
{e_prize}客户号：<code>{user.id:05}</code> / <code>{user.chat_id}</code>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail + wd.amt_apply:,.2f}U
{e_cheng}提现金额：<b>{wd.amt_apply:,.0f}U / {uaccs[wd.from_name].balance:,.0f}U</b>
{e_money}提后余额：{acc.avail:,.2f}U
{e_outbox}出款账号：{wd.from_name}
{e_inbox}收款地址：<code>{wd.to_acc}</code>
{e_traffic}流程状态：{status}
{e_time3}申请时间：{wd.created_at}
"""
    return content


def content_withdraw_send(wd: TWithdraws):
    user = myUsers[wd.chat_id]
    acc = accs[wd.chat_id]
    status = mean('withdraws.status', wd.status)
    content = f"""{e_atm}<b>提现</b>
{e_id}申请ID：<code>{wd.id}</code> / <code>{wd.wd_id}</code>
{e_prize}客户号：<code>{user.id:05}</code> / <code>{user.chat_id}</code>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail + wd.amt_apply:,.2f}U
{e_cheng}提现金额：<b>{wd.amt_apply:,.0f}U / {uaccs[wd.from_name].balance:,.0f}U</b>
{e_money}提后余额：{acc.avail:,.2f}U
{e_outbox}出款账号：{wd.from_name}
{e_inbox}收款地址：<code>{wd.to_acc}</code>
{e_traffic}流程状态：{status}
{e_time3}申请时间：{wd.created_at}
{e_time2}更新时间：{wd.updated_at}
"""
    return content


def content_withdraw_his(chat_id):
    rst = withdraws.get_his(chat_id, 10)
    contect = f"{e_log}<b>提现历史</b>\n"
    if len(rst) == 0:
        contect += "您暂无提现历史记录\n"
    else:
        contect += "编号 时间 金额 状态\n"
    for n in range(len(rst)):
        wd = TWithdraws(rst[n])
        status = mean('withdraws.status', wd.status)
        contect += f"{e_num[n + 1]}{wd.created_at:%m-%d %H:%M} {wd.amt_apply:.0f} {status}\n"
    return contect


    user = myUsers[wd.chat_id]
    acc = accs[wd.chat_id]
    status = mean('withdraws.status', wd.status)
    content = f"""{e_atm}<b>提现</b>
{e_id}申请ID：<code>{wd.id}</code> / <code>{wd.wd_id}</code>
{e_prize}客户号：<code>{user.id:05}</code> / <code>{user.chat_id}</code>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail + wd.amt_apply:,.2f}U
{e_cheng}提现金额：<b>{wd.amt_apply:,.0f}U / {uaccs[wd.from_name].balance:,.0f}U</b>
{e_money}提后余额：{acc.avail:,.2f}U
{e_outbox}出款账号：{wd.from_name}
{e_inbox}收款地址：<code>{wd.to_acc}</code>
{e_traffic}流程状态：{status}
{e_time3}申请时间：{wd.created_at}
{e_time2}更新时间：{wd.updated_at}
"""
    return content


# 助理-转账
def content_transfer(user: TUsers):
    acc = accs[user.chat_id]
    limit = 0 if acc.wlimit < 0 else acc.wlimit
    content = f"""{e_money3}<b>转账</b>
{e_prize}ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail:,.2f}U
{e_climb}转账需打流水：{limit:,.2f}U
{e_warning}注意
{e_num[1]}转账前需要明确转入方的<b>ID号</b>
{e_num[2]}转出方需要<b>打够流水</b>后才能转出
{e_num[3]}转入方需要<b>一倍流水</b>后才能提现
"""
    return content


# 助理-转账-ID
def content_transfer_id(user: TUsers, receive_user: TUsers):
    acc = accs[user.chat_id]
    limit = 0 if acc.wlimit < 0 else acc.wlimit
    content = f"""{e_outbox}<b>转出方</b>
{e_prize}ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail:,.2f}U
{e_climb}提现需打流水：{limit:,.2f}U
{e_inbox}<b>转入方</b>
{e_prize}ID号：<code>{receive_user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：<a href='{link(receive_user.chat_id)}'>{receive_user.outname}</a>
"""
    return content


# 助理-转账-金额
def content_transfer_amt(user: TUsers, receive_user: TUsers, amt):
    acc = accs[user.chat_id]
    limit = 0 if acc.wlimit < 0 else acc.wlimit
    content = f"""{e_outbox}<b>转出方</b>
{e_prize}ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail:,.2f}U
{e_climb}提现需打流水：{limit:,.2f}U
{e_inbox}<b>转入方</b>
{e_prize}ID号：<code>{receive_user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：<a href='{link(receive_user.chat_id)}'>{receive_user.outname}</a>
{e_money3}<b>转账</b>
{e_cheng}转出金额：{amt:,.0f}U
{e_money}转后余额：{acc.avail-amt:,.2f}U
"""
    return content


# 助理-转账-完成
def content_transfer_finish(user_id, receive_id, amt, issue):
    tran = trans.get_from_issue(user_id, issue)
    if tran is None:
        content = ''
    else:
        content = f"""{e_outbox}<b>转出方</b>
{e_prize}ID号：<code>{user_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：<a href='{link(user_id)}'>{myUsers[user_id].outname}</a>
{e_inbox}<b>转入方</b>
{e_prize}ID号：<code>{receive_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：<a href='{link(receive_id)}'>{myUsers[receive_id].outname}</a>
{e_goal}<b>转账结果</b>
{e_loudou}流水号：<code>{issue}</code>
{e_cheng}金额：{amt:.0f}U
{e_traffic}状态：<b>已成功</b>
{e_time3}时间：{tran.created_at}
"""
    return content


# 助理-转账-未完成
def content_transfer_not_finish(user_id, receive_id, amt):
    content = f"""{e_outbox}<b>转出方</b>
{e_prize}ID号：<code>{user_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：<a href='{link(user_id)}'>{myUsers[user_id].outname}</a>
{e_inbox}<b>转入方</b>
{e_prize}ID号：<code>{receive_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：<a href='{link(receive_id)}'>{myUsers[receive_id].outname}</a>
{e_goal}<b>转账结果</b>
{e_cheng}金额：{amt:.0f}U
{e_traffic}状态：<b>已失败</b>
"""
    return content


# 助理-转账-明细
def content_transfer_detail(user_id, issue):
    tran = trans.get_from_issue(user_id, issue)
    if tran is None:
        return ''
    content = f"""{e_money3}类型：<b>{tran.type_name}</b>
{e_loudou}流水号：<code>{issue}</code>
{e_money}转前余额：{tran.pre_avail:,.2f}U
{e_climb}转前需打流水：{tran.pre_wlimit:,.2f}U
{e_cheng}金额：{tran.amt:,.0f}U
{e_money}转后余额：{tran.avail:,.2f}U
{e_climb}转后需打流水：{tran.wlimit:,.2f}U
"""
    return content


# 助理-转账历史
def content_transfer_his(rst):
    contect = f"{e_log}<b>转账历史</b>\n"
    if len(rst) == 0:
        contect += "您暂无转账历史记录\n"
    else:
        contect += "编号 时间 交易类型 金额 交易对手\n"
    for n in range(len(rst)):
        tran = TTrans(rst[n])
        contect += f"{e_num[n + 1]}{tran.created_at:%m-%d %H:%M} {tran.type_name} {tran.amt*tran.sign_income:.0f} " \
                   f"<a href='{link(int(tran.tag))}'>{myUsers[int(tran.tag)].outname}</a>\n"
    return contect


# 助理-我的
def content_mine(user: TUsers):
    acc = accs[user.chat_id]
    limit = 0 if acc.wlimit < 0 else acc.wlimit
    if user.email == '':
        email = '无'
    else:
        email1, email2 = user.email.split('@')
        email = email1[0] + '...' + email1[-1] + '@' + email2
    usdt = '无' if user.usdt == '' else user.usdt[0:5] + '...' + user.usdt[-5:]
    content = f"""{e_me}<b>我的</b>
{e_prize}ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
{e_man}名字：{user.outname}
{e_money}余额：{acc.avail:,.2f}U
{e_climb}提现需打流水：{limit:,.2f}U
{e_men}有效推广人数：{myUsers.members(user.chat_id)}
{e_msg}邮箱： {email}
{e_money2}USDT地址：{usdt}
"""
    return content


# 助理-我的
def content_report(user, type='当天'):
    profit = myProfit.report(user.chat_id, type)
    if isinstance(type, list):
        content = f"{e_market}<b>报表 {type[0]} 至 {type[1]}</b>"
    else:
        content = f"{e_market}<b>{type}报表</b>"
    content += f"""
ID号：<code>{user.chat_id}</code>{e_left3}<i>点击复制</i>
名字：{user.outname}

游戏流水：{abs(profit.turnover):>8.2f}U
游戏返奖：{profit.price:>8.2f}U
活动优惠：{profit.coupon:>8.2f}U
推广佣金：{profit.invited_comm:>8.2f}U
群主佣金：{profit.owner_comm:>8.2f}U
净盈亏：{profit.profit:>10.2f}U

入金：{profit.deposit:>12.2f}U
出金：{abs(profit.withdrawal):>12.2f}U
净入金：{profit.deposit+profit.withdrawal:>8.2f}U

推广人数：{profit.invited_cnt:>8.0f}个
"""
    return content


# 助理-抢红包
def content_redbag_join(user_id):
    game_list = games.avail_game()
    sql_str = f"select a.chat_id,ifnull(b.cnt,0) from (select distinct chat_id,case when chat_id=" \
              f"{params['redbag_group_id']} then 0 else 1 end as ind from chat_user where (ch" \
              f"at_id in ({','.join(map(str, game_list))}) and user_id={user_id}) or chat_id=" \
              f"{params['redbag_group_id']}) a left join links b on b.chat_id={user_id} and a.chat_id=b.group_id " \
              f"order by a.ind,2 desc"
    print(sql_str)
    rst = read_connector.run(sql_str)
    content = "您已加入的红包群如下：\n"
    ind = 1
    for row in rst:
        content += f"{ind}、{myUsers[row[0]].outname}{e_to}推广：{row[1]}\n"
        ind += 1
    return content, rst


# 秘书-配置
def content_set():
    c_uacc = f"{e_inbox}收款USDT地址："
    if uaccs.dp is None:
        c_uacc += '无'
    else:
        c_uacc += uaccs.dp.name
    c_uacc += f"\n{e_outbox}付款USDT地址："
    if uaccs.wd is None:
        c_uacc += '无'
    else:
        c_uacc += uaccs.wd.name
    content = f"{e_set}配置如下\n{e_bot}AI红包：{mean('monits.status', monits['AISENT'].status)}\n" \
              f"{e_pinlv}最低发包频率：{params['sent_freq']} 秒\n{e_money}最高发包金额：{params['sent_amt']} U\n" \
              f"{e_time4}最低抢包频率：{params['get_freq']} 秒\n{e_endtime}结束抢包时长：{params['get_end']} 秒\n" \
              f"{c_uacc}\n"
    return content


# 收款管理
def content_set_uacc():
    content = f"{e_bankcard}USDT账号情况\n"
    for row in uaccs.all():
        uacc = TUaccs(row)
        sub_content = f"{uacc.name} "
        if uacc.is_dp:
            sub_content += f"{e_inbox} "
        if uacc.is_wd:
            sub_content += f"{e_outbox} "
        content += sub_content + f"\n余额：<b>{uacc.balance:,.2f}</b>U\n时间：{uacc.updated_at}\n地址：<code>{uacc.addr}</code>\n"
    return content


# 客户
def content_guest(my_user: TUsers):
    content = f"""{e_guests * 3}<b>客户信息</b>{e_guests * 3}
{e_prize}ID：<code>{my_user.id:05}</code> / <code>{my_user.chat_id}</code>
{e_man}名字：<a href="{link(my_user.chat_id)}">{my_user.outname}</a>
{e_ace2}上级：<a href="{link(my_user.parent_id)}">{my_user.parent}</a>
{e_dna}角色：{mean('users.role', my_user.role)}
{e_money}余额：<b>{accs[my_user.chat_id].avail:,.2f}</b> U
{e_traffic}状态：{mean('users.status', my_user.status)}
{e_time2}活跃时间：{my_user.active_at}
{e_time3}加入时间：{my_user.created_at}
{e_myOffer}备注：{my_user.note}
"""
    return content


# 客户
def content_guest_detail(my_user: TUsers):
    content = f"""{e_guests * 3}<b>客户详细信息</b>{e_guests * 3}
{e_prize}ID：<code>{my_user.id:05}</code> / <code>{my_user.chat_id}</code>
{e_man}名字：<a href="{link(my_user.chat_id)}">{my_user.outname}</a>
{e_ace2}上级：<a href="{link(my_user.parent_id)}">{my_user.parent}</a>
{e_dna}角色：{mean('users.role', my_user.role)}
{e_alert}提醒：{mean('users.is_remind', my_user.is_remind)}
{e_key}密码：{'无' if my_user.fund_pwd == '' else '有'} 
{e_msg}邮箱： <code>{'无' if my_user.email == '' else my_user.email}</code>
{e_money2}USDT地址：<code>{'无' if my_user.usdt == '' else my_user.usdt}</code>
{e_money}可用余额：{accs[my_user.chat_id].avail:,.2f} U
{e_money3}冻结余额：{accs[my_user.chat_id].frozen:,.2f} U
{e_climb}需打流水：{0 if accs[my_user.chat_id].wlimit < 0 else accs[my_user.chat_id].wlimit:,.2f} U
{e_traffic}状态：{mean('users.status', my_user.status)}
{e_time2}活跃时间：{my_user.active_at}
{e_time3}加入时间：{my_user.created_at}
{e_myOffer}备注：{my_user.note}
"""
    return content


# 群组  First:第一页 PrePage：上一页 NextPage：下一页
def content_group_page(pre_content, page):
    now_time = datetime.datetime.now().strftime('%H:%M:%S')
    sql_str = "select * from games order by updated_at desc "
    if page == "First":
        rst = read_connector.run(sql_str)
        all_page = math.ceil(len(rst) / 10)
        new_page = 1
        sql_str += "limit 0, 10"
        prompt = line + e_remind + f"请选择你的操作 {now_time}"
    else:
        match = re.match('.*\n' + e_right2 + ' 页数:(.*?)-(.*?) ' + e_left2 + '.*', pre_content, re.S)
        all_page = int(match.group(1))
        new_page = int(match.group(2))
        if page == "NextPage" and all_page>new_page:
            new_page += 1
            prompt = line + e_remind + f"你已经选择了第{new_page}页 {now_time}"
        elif page == "PrePage" and new_page != 1:
            new_page -= 1
            prompt = line + e_remind + f"你已经选择了第{new_page}页 {now_time}"
        else:
            prompt = line + e_remind + f"你已超出查询范围 {now_time}"
        start_row = (new_page - 1) * 10
        sql_str += f"limit {start_row}, 10"
    rst = read_connector.run(sql_str)
    # 整合内容
    if len(rst) == 0:
        content = e_game + " <b>没有符合要求的群组!</b>\n"
        content += e_right2 + " 页数:0-0 " + e_left2 + '\n'
    else:
        content = e_game + " <b>群组如下:\n" + e_light + " 名称 - 群主 - 状态 - 更新时间</b>\n"
        n = 1
        for row in rst:
            game = TGames(row)
            status = mean("games.status", game.status)
            if game.owner_id == 0:
                str_owner = "无"
            else:
                str_owner = f"<a href='{link(game.owner_id)}'>{myUsers[game.owner_id].outname}</a>"
            content += e_num[n] + f": <a href='{link(game.group_id)}'>{game.outname[:8]}</a>-{str_owner}-{status}" \
                                  f"-{game.updated_at}\n"
            n += 1
        content += e_right2 + " 页数:{0:d}-{1:d} ".format(all_page, new_page) + e_left2 + '\n' + prompt
    return content, rst


# 群组 - 新增
def content_group_new(id_list):
    id_str = ','.join(list(map(str, id_list)))
    sql_str = f"select a.chat_id from (select * from chat_user where chat_id in ({id_str}) and user_id={params['boss_id']}) " \
              f"a left join games b on a.chat_id=b.group_id where b.group_id is null order by a.active_at desc limit 10"
    rst = read_connector.run(sql_str)
    if len(rst) == 0:
        return "目前没有待新增的群组\n", []
    content = e_game + " <b>待新增群组如下:\n" + e_light + " ID - 名称</b>\n"
    n = 1
    for row in rst:
        content += e_num[n] + f": <code>{row[0]}</code> - <a href='{link(row[0])}'>{myUsers[row[0]].outname}</a>\n"
        n += 1
    return content, rst


# 群组 -详细
def content_group_detail(group_id):
    game = games[group_id]
    user = myUsers[game.owner_id]
    if game.owner_id == 0:
        str_owner = f"{e_prize}群主ID：\n{e_ace1}群主名称："
    else:
        str_owner = f"{e_prize}群主ID：<code>{user.id:05}</code> / <code>{user.chat_id}</code>\n" \
                    f"{e_ace1}群主名称：{user.outname}"
    content = f"""{e_game * 3}<b>群组详细信息</b>{e_game * 3}
{e_id}群ID：<code>{game.group_id}</code>
{e_redbag}群名称：<a href="{game.link}">{game.outname}</a>
{str_owner}
{e_detail}玩法介绍：{'无' if game.introduce == '' else '有'}
{e_cty0}语言：{mean('games.is_en', game.is_en)}
{e_pinlv}系统抽佣比例：{game.sys_rate * 100:.2f}%
{e_pinlv}群主抽佣比例：{game.owner_rate * 100:.2f}%
{e_pinlv}上级抽佣比例：{game.parent_rate * 100:.2f}%
{e_traffic}状态：{mean('games.status', game.status)}
{e_bank}租金金额：{game.rent_amt:,.0f}U
{e_time1}租金时间：{game.rent_date}
{e_time2}更新时间：{game.updated_at}
{e_time3}创建时间：{game.created_at}
"""
    return content


# 群组 - 介绍
def content_group_introduce(group_id):
    game = games[group_id]
    content = f"群组<b>{game.outname}</b>的介绍如下\n{game.introduce}\n"
    return content


