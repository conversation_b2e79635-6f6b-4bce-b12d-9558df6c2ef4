# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from plugins.bowin.common import *


# 查看guest信息
@Client.on_message(filters.outgoing & filters.command("guest"))
async def guest(client, message):
    try:
        text_list = message.text.split(" ")
        chat_id = message.chat.id
        await message.delete()
        prompt = e_remind + "请选择您需要的操作\n"
        if len(text_list) == 1:
            # 要求必须是普通用户
            if myChats[chat_id].type != 'user':
                await clients['bowin_adminbot'].send_message(
                    params['studio_id'],
                    f"查询的客户必须是<b>普通用户</b>： <i>{myChats[chat_id].outname}</i>",
                    disable_web_page_preview=True,
                    parse_mode=ParseMode.HTML)
            else:
                # 展示客户信息
                my_user = myUsers[chat_id]
                content = content_guest(my_user)
                await clients['bowin_adminbot'].send_message(
                    params['studio_id'],
                    content + line + prompt,
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True,
                    reply_markup=get_com_imp("guest", my_user.chat_id))
        elif len(text_list) == 2:
            my_user = myUsers.query(text_list[1])
            if my_user is not None:
                content = content_guest(my_user)
                await clients['bowin_adminbot'].send_message(
                    params['studio_id'],
                    content + line + prompt,
                    parse_mode=ParseMode.HTML,
                    disable_web_page_preview=True,
                    reply_markup=get_com_imp("guest", my_user.chat_id))
            else:
                await clients['bowin_adminbot'].send_message(
                    params['studio_id'],
                    f"查不到客户 {text_list[1]} 的信息\n",
                    disable_web_page_preview=True,
                    parse_mode=ParseMode.HTML)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 通过转发获取客户资料
@Client.on_message(filters.forwarded & filters.chat(params['studio_id']))
async def guest_from_forward(client, message):
    try:
        if message.forward_from is not None:
            from_user_id = message.from_user.id
            forward_user_id = message.forward_from.id
            # 要求必须是普通用户
            try:
                if myChats[forward_user_id].type != 'user':
                    await clients['bowin_adminbot'].send_message(
                        params['studio_id'],
                        f"查询的必须是<b>普通用户</b>： <i>{message.forward_sender_name}</i>",
                        disable_web_page_preview=True,
                        parse_mode=ParseMode.HTML)
                    return
            except Exception as e:
                # 如果私人号转发，可能会出现id未收录的情况
                await clients['bowin_adminbot'].send_message(
                    params['studio_id'],
                    f"目前还没收录 <i>{message.forward_sender_name}</i> 的资料",
                    disable_web_page_preview=True,
                    parse_mode=ParseMode.HTML)
                return
            # 如果是私人账户，新客户不算客户
            if from_user_id == params['boss_id']:
                # bowin_boss 转发的消息
                user = myUsers[forward_user_id]
            else:
                # 私人账号发的消息
                user = myUsers.query(from_user_id)
                if user is None:
                    await clients['bowin_adminbot'].send_message(
                        params['studio_id'],
                        f"<i>{message.forward_sender_name}</i> 暂时不是我们的正式客户",
                        disable_web_page_preview=True,
                        parse_mode=ParseMode.HTML)
                    return
            content = content_guest(user)
            prompt = e_remind + "请选择您需要的操作\n"
            await clients['bowin_adminbot'].send_message(
                params['studio_id'],
                content + line + prompt,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True,
                reply_markup=get_com_imp("guest", user.chat_id))
        else:
            await clients['bowin_adminbot'].send_message(
                params['studio_id'],
                f"无法获取到客户的相关信息: {message.forward_sender_name}\n请在客户会话中通过 /guest 查询"
                )
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


# 所有收到的私发信息
@Client.on_message(~filters.bot & filters.incoming & filters.private, group=-1)
async def all_private_message(client, message):
    try:
        print('all_private_message')
        # print(f"我目前使用的代理是 {client.proxy}")
        # bowin_boss|发包员|远程发包|1914064729|200-8|-1002093645173
        # await send_to_ws(message.text)
        # 新增/活跃成员
        # user_id = message.from_user.id
        # myUsers[message.from_user.id]
        # myUsers.active(user_id)
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')


