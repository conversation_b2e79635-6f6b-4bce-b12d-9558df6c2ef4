import time
import re
import gettext
from classes import *


# 多语言支持 要求目录正确
def test1():
    # gettext.bindtextdomain('robot_park', 'trans')
    # gettext.textdomain('robot_park')
    # _ = gettext.gettext

    lang = 'zh_CN'
    translation = gettext.translation('test', localedir='trans', languages=[lang])
    translation.install()
    _ = translation.gettext

    print(_('Hello  world!'))
    print(_('Goodbye world!'))


# 测试数据库连接类
def test2():
    sql_str = "SELECT * FROM clients WHERE id = %s or 1=1"
    params = [6981052546]
    print(exe_connector.run(sql_str, params))
    read_connector.export_to_txt('clients', f"./log/export.txt")


if __name__ == "__main__":
    test2()
    time.sleep(3)
