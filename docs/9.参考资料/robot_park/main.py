# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from monit import *
from classes import *
# from func import *
import asyncio
from pyrogram import idle


async def main():
    try:
        sys_log.write_log('The main_admin is begining...', 'a')
        await asyncio.gather(*(clients[c].start() for c in client_list))
        await asyncio.sleep(3)
        monit = Monit()
        await asyncio.gather(monit.start())
        await idle()
        await asyncio.gather(*(clients[c].stop() for c in client_list))
        read_connector.close()
        exe_connector.close()
    except Exception:
        sys_log.write_log('main', 'a')
        sys_log.write_log(traceback.format_exc(), 'a')


if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())
