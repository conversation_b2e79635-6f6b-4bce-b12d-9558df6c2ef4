# -*- coding: utf-8 -*-
__author__ = 'Manco.li'
from plugins.bowin.asst import *
import re
import time
import datetime
import traceback
import os
import random


async def do_monit_asst(monit):
    try:
        now_time = time.localtime(time.time())
        sys_log.write_log('{0[0]:s}|{0[1]:s}:{0[2]:s}->{0[3]:s}'.format(monit), 'a')
        # 监控任务
        if monit[0] == 'DEPCHECK':
            try:
                for row in deposits.obj_dep():
                    okx = OKX(row[0])
                    await okx.load_dep(0)
                await deposits.check()
            except Exception as e:
                sys_log.write_log(traceback.format_exc(), 'a')
        elif monit[0] == 'WDCHECK':
            try:
                for row in withdraws.obj_wd():
                    okx = OKX(row[0])
                    await okx.load_wd(0)
                await withdraws.check()
            except Exception as e:
                sys_log.write_log(traceback.format_exc(), 'a')
        elif monit[0] == 'DEPDAY':
            try:
                for row in uaccs.all():
                    uacc = TUaccs(row)
                    okx = OKX(uacc.name)
                    await okx.load_dep(1)
            except Exception as e:
                sys_log.write_log(traceback.format_exc(), 'a')
        elif monit[0] == 'WDDAY':
            try:
                for row in uaccs.all():
                    uacc = TUaccs(row)
                    okx = OKX(uacc.name)
                    await okx.load_wd(1)
            except Exception as e:
                sys_log.write_log(traceback.format_exc(), 'a')
    except Exception as e:
        sys_log.write_log(traceback.format_exc(), 'a')
