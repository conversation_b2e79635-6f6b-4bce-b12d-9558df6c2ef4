# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from monit import *
from classes import *
# from func import *
import asyncio
from pyrogram import idle


async def main():
    try:
        sys_log.write_log('The main_asst is begining...', 'a')
        # 启动所有客户端、监控和 WebSocket 监听
        await asyncio.gather(
            *(clients[c].start() for c in client_list),  # 启动所有 bot
            listen_ws('bowin_asstbot'),  # 启动 WebSocket 监听
            Monit().start()  # 启动监控
            )
        # 等待所有任务完成
        await idle()
        # 停止所有客户端
        await asyncio.gather(*(clients[c].stop() for c in client_list))
        # 关闭连接器
        read_connector.close()
        exe_connector.close()
    except Exception:
        sys_log.write_log('main', 'a')
        sys_log.write_log(traceback.format_exc(), 'a')


if __name__ == "__main__":
    asyncio.get_event_loop().run_until_complete(main())