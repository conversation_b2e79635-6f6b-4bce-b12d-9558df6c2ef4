# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from func import *
import time
from pyrogram.types import InputMediaPhoto, InputMediaVideo, InputMediaDocument, InputMediaAudio
from typing import Union
from smsactivate.api import SMSActivateAPI
from pyrogram.types import TermsOfService, User
from pyrogram.errors import (ApiIdInvalid, PhoneNumberInvalid, PhoneCodeInvalid, PhoneCodeExpired, BadRequest,
                             SessionPasswordNeeded, PhoneNumberBanned, PhoneNumberUnoccupied, UserDeactivatedBan,
                             Forbidden)
import random
# from telethon import TelegramClient, connection


# 定义公共全局变量
(e_market, e_myOffer, e_trade, e_report, e_safe, e_complaint, e_adv, e_service, e_remind, e_money,
 e_return, e_home, e_warning, e_succ, e_to, e_left, e_right, e_up, e_down, e_fire,
 e_left2, e_right2, e_up2, e_down2, e_ok, e_no, e_heart_black, e_heart_bad, e_heart_green, e_heart_orange,
 e_heart_red, e_flower, e_lip, e_sex_wear, e_ruler, e_2girl, e_phone, e_girl, e_loudou, e_mail,
 e_id, e_photo, e_comfirm, e_search, e_girls, e_write, e_baby, e_eyes, e_zoushi, e_traffic,
 e_handup1, e_handup2, e_location, e_delete, e_light, e_manhat, e_girlhat, e_ice, e_prize, e_han,
 e_set, beg_girl, e_left3, e_right3, e_up3, e_down3, e_gou, e_hezuo, e_travel, e_he,
 e_hotheart, e_cycle, e_kong, e_gift, e_guests, e_sign, e_bell, e_man, e_man2, e_manhat2,
 e_bot, e_time1, e_time2, e_time3, e_dna, e_add, e_reduce, e_circle, e_pinglun, e_fenlei,
 e_shandian, e_video, e_cty, e_cty0, e_cty1, e_cty2, e_cty3, e_cty4, e_man3, e_star,
 e_group, e_talk, e_cha, e_b, e_sound, e_help, e_clap, e_girl2, e_smile, e_outbox,
 e_task, e_girl_, e_mai, e_change, e_inbox, e_msg, e_refuse, e_cty5, e_alert, e_noalert,
 e_smile2, e_weiqu, e_report2, e_unknow, e_file, e_page, e_replace, e_falang, e_jin18, e_phone2,
 e_bell2, e_redbag, e_ray, e_bomb, e_money2, e_atm, e_money3, e_me, e_ace1, e_mailbox,
 e_key, e_bankcard, e_link, e_ace2, e_king, e_game, e_climb, e_what, e_men, e_log,
 e_exchange, e_cheng, e_goal, e_suanpan, e_date, e_month, e_pinlv, e_endtime, e_time4, e_game,
 e_baobiao, e_date2, e_bankcard2, e_phone3, e_id, e_flag, e_bank, e_detail, e_cty6) = \
("💹", "📝", "⚖", "💯", "🛡", "🈲", "📢", "💁‍♀‍", "🎈", "💰",
 "🔙", "🏠", "⚠️", "🎉", "👉", "⬅", "➡", "⬆", "⬇", "🔥",
 "◀️", "▶️", "🔼", "🔽", "👌", "❌", "🖤", "💔", "💚", "🧡",
 "❤", "🌹", "💋", "👙", "📏", "👩‍👧", "📞", "👩 ", "⏳", "📮",
 "🎫", "📸", "⁉️", "🔎", "👩‍👩‍👧‍👧", "✍️", "🤱", "👀", "📈", "🚥",
 "🙋", "🙋‍♂️", "📍", "❌", "💡", "🎩", "👒", "❄️", "🏅", "💦",
 "⚙️", "🙇‍♀️", "👈", "👉", "👆", "👇", "✅", "🤝", "🏖", "🈴",
 "❤️‍🔥", "♻️", "🈳", "🎁", "🤵‍♂️", "📡", "🛎", "🙍🏻‍♂️", "💁🏻‍♂️", "🧢",
 "🤖", "⏱", "⏰", "🕰", "🧬", "➕", "➖", "⭕️", "💬", "🗂",
 "⚡️", "🎥", "🏳️‍⚧️", "🇺🇳", "🇵🇭", "🇨🇳", "🇻🇳", "🇷🇺", "🙋🏻‍♂️", "🌟",
 "👯‍♀️", "🗣", "❎", "🅱️", "🔊", "🙏", "👏", "🙋‍♀️", "😊", "📤",
 "🔰", "👩", "🎙", "🔄", "📥", "📧", "⛔️", "🇹🇭", "🔔", "🔕",
 "😉", "🥺", "🚨", "❓", "🔖", "📜", "🎭", "💈", "🔞", "☎️",
 "📣", "🧧", "💣", "💥", "💵", "🏧", "💸", "👤", "♠️", "📬",
 "🔑", "🪪", "🔗", "♣️", "🃏", "🎮", "🧗‍♂️", "💢", "👬", "📑",
 "💱", "⚖️", "🎯", "🧮", "📅", "🈷️", "🎚", "⌛️", "⏲️", "🎰",
 "📊", "🗓", "💳", "📲", "🆔", "🎏", "🏦", "🧾", "🇺🇸")
e_num = ["0️⃣", "1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣", "🔟"]
line = "----------------------\n"


# 媒体操作类
class Media:
    def __init__(self, to_chat='me', active_code='', passive_list=[]):
        # 图片发送渠道
        self.to_chat = to_chat
        # 主同步编码
        self.active_code = active_code
        # 从同步编码列表
        self.passive_list = passive_list

    # 获取媒体
    # username:记录则表示从media_online获取信息，无记录则表示从medias获取信息
    # is_resend:若media_online中没记录，最终是否要从本地文件重发
    async def get(self, id, username='', is_resend=True):
        try:
            if username != '':
                sql_str = "select media_id,username,type,from_chat,message_id,file_id from media_online where " \
                          "media_id={0:d} and username='{1:s}'".format(id, username)
                rst = read_connector.run(sql_str)
                if len(rst) == 0:
                    # 优先重新get消息来获取file_id
                    sys_log.write_log("优先重新get {0:d} 的消息来获取file_id".format(id), 'a')
                    rst_reget = await self.update_reget([str(id)], username)
                    if rst_reget:
                        rst = read_connector.run(sql_str)
                    # 处理是否要从本地文件重发消息来获取file_id
                    elif is_resend:
                        sys_log.write_log("从本地文件重发 {0:d} 的消息来获取file_id".format(id), 'a')
                        rst_resend = await self.update_resend(id, username)
                        return rst_resend
                    else:
                        sys_log.write_log("在media_online中没有 {0:d} 的媒体信息".format(id), 'a')
                        return ["在media_online中没有媒体信息：{0:d}|{1:s}".format(id, username)]
            else:
                sql_str = "select id,type,storage,file_name,file_ext from medias where id={0:d}".format(id)
                rst = read_connector.run(sql_str)
                if len(rst) == 0:
                    return ["在medias中没有媒体信息：{0:d}".format(id)]
            return rst[0]
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 删除本地文件操作
    # del_record 是否要删除2个媒体表的记录
    async def del_file(self, id, del_record=False):
        try:
            rst = await self.get(id)
            file_path = 'media/' + rst[1] + '/' + rst[3] + '.' + rst[4]
            if rst[2] != 'both':
                sys_log.write_log("媒体 ID={0:d} 没有要求保存到本地".format(id), 'a')
            elif os.path.isfile(file_path):
                os.unlink(file_path)
            else:
                sys_log.write_log("找不到文件：{0:d}|{1:s}".format(id, file_path), 'a')
            # 更新媒体表
            if del_record:
                # 删除表记录
                sql_str = "delete from media_online where media_id in " \
                          "(select id from medias where file_name='{0[3]:s}')".format(rst)
                exe_connector.run(sql_str)
                sql_str = "delete from medias where file_name='{0[3]:s}'".format(rst)
                exe_connector.run(sql_str)
            else:
                # 更新媒体表的信息
                sql_str = "update medias set storage='online', file_ext='' where file_name='{0[3]:s}'".format(rst)
                exe_connector.run(sql_str)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 重新get消息获取新file_id
    # id_list: 需要更新的id字符串列表 string
    async def update_reget(self, id_list=[], username=''):
        try:
            str_id = ",".join(id_list)
            sys_log.write_log("重新get消息获取新file_id：{0:s}|{1:s}".format(str_id, username), 'a')
            # 获取media_online中最合适的一条记录
            sql_str = "select * from (select media_id,username,type,from_chat,message_id,file_id,updated_at,rk, " \
                      "row_number() over(partition by media_id order by rk,updated_at desc) as rn from (select " \
                      "media_id,username,type,from_chat,message_id,file_id,updated_at, case when from_chat={2:d} " \
                      "then 1 when username='{1:s}' then 2 else 3 end as rk from media_online where media_id in " \
                      "({0:s})) a ) b where b.rn=1".format(str_id, username, self.to_chat)
            rst = read_connector.run(sql_str)
            # 若找不到媒体信息，则退出
            if len(rst) == 0:
                sys_log.write_log("media_online找不到 {0:s} 的相关信息\n{1:s}".format(str_id, sql_str), 'a')
                return False
            # disc_msg_media 以message_id为key的字典
            # disc_chat_meg 以from_chat为key的字典
            disc_msg_media = {}
            disc_chat_meg = {}
            for row in rst:
                disc_msg_media[row[4]] = row[0]
                if row[3] in disc_chat_meg:
                    disc_chat_meg[row[3]].append(row[4])
                else:
                    disc_chat_meg[row[3]] = [row[4]]
            # 获取原消息记录
            for key in list(disc_chat_meg.keys()):
                msg_list = await clients[username].get_messages(key, disc_chat_meg[key])
                # 更新记录
                for get_msg in msg_list:
                    if get_msg.photo is not None:
                        type = 'photo'
                        new_file_id = get_msg.photo.file_id
                    elif get_msg.video is not None:
                        type = 'video'
                        new_file_id = get_msg.video.file_id
                    elif get_msg.document is not None:
                        type = 'document'
                        new_file_id = get_msg.document.file_id
                    new_from_chat = get_msg.chat.id
                    new_message_id = get_msg.id
                    sql_str = "insert into media_online(media_id,username,type,from_chat,message_id,file_id,updated_at) " \
                              "values({0:d},'{1:s}','{2:s}', {3:d}, {4:d},'{5:s}',CURRENT_TIMESTAMP()) on duplicate key " \
                              "update type='{2:s}', from_chat={3:d}, message_id={4:d}, file_id='{5:s}'," \
                              "updated_at=CURRENT_TIMESTAMP()" \
                        .format(disc_msg_media[new_message_id], username, type, new_from_chat, new_message_id, new_file_id)
                    exe_connector.run(sql_str)
                # 若从多个会话中获取消息，稍作停顿
                # if len(disc_chat_meg) > 1:
                #     await asyncio.sleep(1)
            return True
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return ['update_reget 报错:{0:s}|{1:s}'.format(",".join(id_list), username)]

    # 重新发送本地文件获取新file_id
    async def update_resend(self, id, username):
        try:
            sys_log.write_log("重新发送本地文件获取新file_id：{0:d}|{1:s}".format(id, username), 'a')
            rst = await self.get(id)
            # 若本地没记录，直接返回说明
            if len(rst) == 1 or rst[2] == 'online' or rst[1] not in ['photo', 'video', 'document']:
                return ['没有符合resend的媒体记录:{0:d}|{1:s}'.format(id, username)]
            else:
                adds = 'media/' + rst[1] + '/' + rst[3] + '.' + rst[4]
                content = "media_sync|{0:d}|{1:s}".format(id, self.active_code)
                if rst[1] == 'photo':
                    send_msg = await clients[username].send_photo(self.to_chat, photo=adds, caption=content, disable_notification=True)
                elif rst[1] == 'video':
                    send_msg = await clients[username].send_video(self.to_chat, video=adds, caption=content, disable_notification=True)
                elif rst[1] == 'document':
                    send_msg = await clients[username].send_document(self.to_chat, document=adds, caption=content, disable_notification=True)
                else:
                    return ["暂无法处理{0:s}类型:{1:d}|{2:s}".format(rst[1], id, self.active_code)]
                # 定位媒体类型
                if send_msg.photo is not None:
                    msg_type = 'photo'
                    new_file_id = send_msg.photo.file_id
                elif send_msg.video is not None:
                    msg_type = 'video'
                    new_file_id = send_msg.video.file_id
                elif send_msg.document is not None:
                    msg_type = 'document'
                    new_file_id = send_msg.document.file_id
                new_from_chat = send_msg.chat.id
                new_message_id = send_msg.id
                sql_str = "update medias set used_at=CURRENT_TIMESTAMP() where id={0:d}".format(id)
                exe_connector.run(sql_str)
                sql_str = "insert into media_online(media_id,username,type,from_chat,message_id,file_id,updated_at) " \
                          "values({0:d},'{1:s}','{2:s}', {3:d}, {4:d},'{5:s}',CURRENT_TIMESTAMP()) on duplicate key " \
                          "update type='{2:s}', from_chat={3:d}, message_id={4:d}, file_id='{5:s}'," \
                          "updated_at=CURRENT_TIMESTAMP()" \
                    .format(id, username, msg_type, new_from_chat, new_message_id, new_file_id)
                exe_connector.run(sql_str)
                return [id, username, msg_type, new_from_chat, new_message_id, new_file_id]
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return ['update_resend 报错:{0:d}|{1:s}'.format(id, username)]

    # 转发已有消息获取新file_id
    # id 数字类型
    async def update_forward(self, id, username):
        try:
            sys_log.write_log("转发已有消息获取新file_id：{0:d}|{1:s}".format(id, username), 'a')
            rst = await self.get(id, username)
            try:
                # 优先forward在线媒体，获取新的file_id
                if len(rst) > 1:
                    fwd_msg = await clients[username].forward_messages(self.to_chat, rst[3], rst[4], True)
                    # 定位媒体类型
                    if fwd_msg.photo is not None:
                        type = 'photo'
                        new_file_id = fwd_msg.photo.file_id
                    elif fwd_msg.video is not None:
                        type = 'video'
                        new_file_id = fwd_msg.video.file_id
                    elif fwd_msg.document is not None:
                        type = 'document'
                        new_file_id = fwd_msg.document.file_id
                    new_from_chat = fwd_msg.chat.id
                    new_message_id = fwd_msg.id
                    sql_str = "update media_online set type='{2:s}',from_chat={3:d},message_id={4:d},file_id='{5:s}'," \
                              "updated_at=CURRENT_TIMESTAMP() where media_id={0:d} and username='{1:s}'" \
                        .format(id, username, type, new_from_chat, new_message_id, new_file_id)
                    exe_connector.run(sql_str)
                    return [id, username, type, new_from_chat, new_message_id, new_file_id]
                else:
                    sys_log.write_log("update_forward时在media_online没有找到媒体信息：{0:s}".format(rst[0]), 'a')
                    raise Exception
            except Exception:
                return await self.update_resend(id, username)
            return ['update_forward失败']
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 获取媒体列表
    # med_list: 字符串列表
    async def get_media(self, client, med_list, content='', entities=[]):
        try:
            clt = clients.get_username(client)
            media_list = []
            first_ind = 0
            for med in med_list:
                my_media = await self.get(int(med), clt, is_resend=True)
                if len(my_media) == 1:
                    sys_log.write_log("get_media->media.get失败：{0[0]:s}".format(my_media), 'a')
                    raise Exception
                if my_media[2] == 'photo' and first_ind == 0:
                    media_list.append(InputMediaPhoto(my_media[5],
                                                      caption=content,
                                                      parse_mode=ParseMode.HTML,
                                                      caption_entities=entities))
                    first_ind += 1
                elif my_media[2] == 'video' and first_ind == 0:
                    media_list.append(InputMediaVideo(my_media[5],
                                                      caption=content,
                                                      parse_mode=ParseMode.HTML,
                                                      caption_entities=entities))
                    first_ind += 1
                elif my_media[2] == 'document' and first_ind == 0:
                    media_list.append(InputMediaDocument(my_media[5],
                                                         caption=content,
                                                         parse_mode=ParseMode.HTML,
                                                         caption_entities=entities))
                    first_ind += 1
                elif my_media[2] == 'photo':
                    media_list.append(InputMediaPhoto(my_media[5]))
                elif my_media[2] == 'video':
                    media_list.append(InputMediaVideo(my_media[5]))
                elif my_media[2] == 'document':
                    media_list.append(InputMediaDocument(my_media[5]))
            return media_list
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 保存媒体  storage=[both|online]
    # backup 是否转发媒体到共享频道
    async def save(self, client, message, storage='both', backup=False):
        try:
            # 筛选接受的媒体类型
            if message.photo is None and message.video is None and message.document is None:
                return []
            # 定位媒体类型
            if message.photo is not None:
                type = 'photo'
                file_id = message.photo.file_id
                file_name = 'P' + file_id[-30:]
            elif message.video is not None:
                type = 'video'
                file_id = message.video.file_id
                file_name = 'V' + file_id[-30:]
            elif message.document is not None:
                type = 'document'
                file_id = message.document.file_id
                file_name = 'D' + file_id[-30:]
            # 操作媒体表
            sql_str = "select id from medias where file_name='{0:s}' and storage='{1:s}'".format(file_name, storage)
            rst = read_connector.run(sql_str)
            if len(rst) == 0:
                if storage == 'both':
                    adds = 'media/' + type + '/'
                    file_dir = (await client.download_media(message, adds))
                    file_ext = file_dir.split('.')[1]
                    new_file_dir = os.getcwd() + '/media/' + type + '/' + file_name + '.' + file_ext
                    os.rename(file_dir, new_file_dir)
                else:
                    file_ext = ''
                sql_str = "insert into medias(type,storage,file_name,file_ext,used_at) values('{0:s}','{1:s}'," \
                          "'{2:s}','{3:s}',CURRENT_TIMESTAMP())".format(type, storage, file_name, file_ext)
                exe_connector.run(sql_str)
                sql_str = "select id from medias where file_name='{0:s}' and storage='{1:s}'".format(file_name, storage)
                rst = read_connector.run(sql_str)
            else:
                sql_str = "update medias set type='{1:s}', storage='{2:s}',used_at=CURRENT_TIMESTAMP() where id={0:d}" \
                    .format(rst[0][0], type, storage)
                exe_connector.run(sql_str)
            # 操作媒体在线表
            username = clients.get_username(client)
            from_chat = message.chat.id
            message_id = message.id
            sql_str = "insert into media_online(media_id,username,type,from_chat,message_id,file_id,updated_at) " \
                      "values({0:d},'{1:s}','{2:s}', {3:d}, {4:d},'{5:s}',CURRENT_TIMESTAMP()) on duplicate key " \
                      "update type='{2:s}', from_chat={3:d}, message_id={4:d}, file_id='{5:s}'," \
                      "updated_at=CURRENT_TIMESTAMP()" \
                .format(rst[0][0], username, type, from_chat, message_id, file_id)
            exe_connector.run(sql_str)
            if backup:
                await asyncio.sleep(1)
                rst_forward = await self.update_forward(rst[0][0], username)
                return rst_forward
            else:
                return [rst[0][0], username, type, from_chat, message_id, file_id]
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')


# 群消息管理类
class ChatMessage:
    def __init__(self, chat_id, kind):
        self.chat_id = chat_id
        self.kind = kind

    # 插入库表记录
    def add_row(self, ind, messages, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            msg_list = []
            if not isinstance(messages, list):
                messages = [messages]
            for msg in messages:
                if self.chat_id != msg.chat.id:
                    sys_log.write_log("ChatMessage.chat({0:s})与实际群({1:s})不匹配，请确认！"
                                      .format(myChats[self.chat_id].name, myChats[msg.chat.id].name), 'a')
                    return False
                msg_list.append(msg.id)
            sql_str = "insert into chat_message(chat_id,chat_name,kind,ind,msg_list,updated_at,created_at) " \
                      "values({0:d},'{1:s}','{2:s}','{3:s}','{4:s}',CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP()) " \
                      "on duplicate key update chat_name='{1:s}',msg_list='{4:s}',updated_at=CURRENT_TIMESTAMP()"\
                .format(self.chat_id, myChats[self.chat_id].name, kind, ind, ',' + ','.join(list(map(str, msg_list))) + ',')
            exe_connector.run(sql_str)
            return True
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 插入库表记录(可重复记录）
    def add_rows(self, ind, messages, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            msg_list = []
            if not isinstance(messages, list):
                messages = [messages]
            for msg in messages:
                if self.chat_id != msg.chat.id:
                    sys_log.write_log("ChatMessage.chat({0:s})与实际群({1:s})不匹配，请确认！"
                                      .format(myChats[self.chat_id].name, myChats[msg.chat.id].name), 'a')
                    return False
                msg_list.append(msg.id)
            str_msg = ',' + ','.join(list(map(str, msg_list))) + ','
            sql_str = "insert into chat_message(chat_id,chat_name,kind,ind,msg_list,updated_at,created_at) " \
                      "values({0:d},'{1:s}','{2:s}','{3:s}','{4:s}',CURRENT_TIMESTAMP(),CURRENT_TIMESTAMP()) " \
                      "on duplicate key update chat_name='{1:s}',msg_list='{4:s}',updated_at=CURRENT_TIMESTAMP()"\
                .format(self.chat_id, myChats[self.chat_id].name, kind, ind + '|' + str_msg, str_msg)
            exe_connector.run(sql_str)
            return True
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 删除库表记录
    def del_row(self, ind, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            sql_str = "delete from chat_message where chat_id={0:d} and kind='{1:s}' and ind='{2:s}'"\
                .format(self.chat_id, kind, ind)
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 删除库表记录(可重复记录）
    def del_rows(self, ind, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            str_ind = ind + '|'
            sql_str = "delete from chat_message where chat_id={0:d} and kind='{1:s}' and ind like '{2:s}%'"\
                .format(self.chat_id, kind, str_ind)
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 查询库表记录
    def get_row(self, ind, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            sql_str = "select chat_id,chat_name,kind,ind,msg_list,updated_at,created_at " \
                      "from chat_message where chat_id={0:d} and kind='{1:s}' and ind='{2:s}'" \
                .format(self.chat_id, kind, ind)
            rst = read_connector.run(sql_str)
            return rst
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 查询库表记录(可重复记录）
    def get_rows(self, ind, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            str_ind = ind + '|'
            sql_str = "select chat_id,chat_name,kind,ind,msg_list,updated_at,created_at " \
                      "from chat_message where chat_id={0:d} and kind='{1:s}' and ind like '{2:s}%'" \
                .format(self.chat_id, kind, str_ind)
            rst = read_connector.run(sql_str)
            return rst
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 通过message_id字符串查库表记录
    def get_from_msg(self, msg_str):
        try:
            sql_str = "select chat_id,chat_name,kind,ind,msg_list,updated_at,created_at " \
                      "from chat_message where chat_id={0:d} and msg_list like '%{1:s}%'" \
                .format(self.chat_id, msg_str)
            rst = read_connector.run(sql_str)
            return rst
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 删除消息
    async def del_msg(self, ind, client, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            rows = self.get_row(ind, kind)
            for row in rows:
                try:
                    await client.delete_messages(self.chat_id, list(map(int, row[4].strip(',').split(','))))
                    await asyncio.sleep(1)
                except FloodWait as e:
                    await asyncio.sleep(e.value)
                    await client.delete_messages(self.chat_id, list(map(int, row[4].strip(',').split(','))))
                except Exception as e:
                    sys_log.write_log("删除群信息失败：{0[0]:d}({0[2]:s}) {1[2]:s} @ {1[3]:s} {1[4]:s}"
                                      .format(myChats[self.chat_id].to_list(), row), 'a')
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 删除消息(可重复记录）
    async def del_msgs(self, ind, client, kind=''):
        try:
            kind = self.kind if kind == '' else kind
            rows = self.get_rows(ind, kind)
            for row in rows:
                try:
                    await client.delete_messages(self.chat_id, list(map(int, row[4].strip(',').split(','))))
                    if len(rows) > 1:
                        await asyncio.sleep(2)
                except FloodWait as e:
                    await asyncio.sleep(e.value)
                    await client.delete_messages(self.chat_id, list(map(int, row[4].strip(',').split(','))))
                except Exception as e:
                    sys_log.write_log("删除群信息列表失败：{0[0]:d}({0[2]:s}) {1[2]:s} @ {1[3]:s} {1[4]:s}"
                                      .format(myChats[self.chat_id].to_list(), row), 'a')
                await asyncio.sleep(5)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 矫正群信息
    async def correct_msgs(self, client, except_regex=[]):
        try:
            #  登记群信息
            msg_list = []
            msg_dic = {}
            if len(except_regex) == 0:
                async for msg in client.get_chat_history(self.chat_id):
                    msg_list.append(msg.id)
            else:
                async for msg in client.get_chat_history(self.chat_id):
                    msg_list.append(msg.id)
                    msg_dic.update({msg.id: msg})
            #  提取引导表信息
            table_list = []
            sql_str = "select msg_list from chat_message where chat_id={0:d}".format(self.chat_id)
            rst = read_connector.run(sql_str)
            for row in rst:
                table_list.extend(list(map(int, row[0].strip(',').split(','))))
            #  检查差异
            msg_list_more = [x for x in msg_list if x not in table_list]
            table_list_more = [x for x in table_list if x not in msg_list]
            if 1 in msg_list_more:
                msg_list_more.remove(1)
            # 引导表->群组 删除引导表的多余项
            for tb in table_list_more:
                sys_log.write_log("矫正群信息| 删除引导表的多余项:{0:s} -> {1:d}"
                                  .format(myChats[self.chat_id].name, tb), 'a')
                sql_str = "delete from chat_message where chat_id={0:d} and msg_list like '%,{1:d},%'"\
                    .format(self.chat_id, tb)
                exe_connector.run(sql_str)
            # 引导表->群组 删除群组多余信息
            for msg in msg_list_more:
                try:
                    # 排除匹配上“例外”的信息
                    if len(except_regex) > 0:
                        content = ''
                        if msg_dic[msg].text is not None:
                            content = msg_dic[msg].text
                        elif msg_dic[msg].caption is not None:
                            content = msg_dic[msg].caption
                        if len(fuzzy_finder(except_regex, [content])) > 0:
                            continue
                    # 删除信息
                    sys_log.write_log("矫正群信息| 删除群组的多余信息:{0:s} -> {1:d}"
                                      .format(myChats[self.chat_id].name, msg), 'a')
                    await client.delete_messages(self.chat_id, msg)
                    await asyncio.sleep(2)
                except FloodWait as e:
                    await asyncio.sleep(e.value)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


# # 爬取消息类
# class GrabMsg:
#     def __init(self, type):
#         self.type = type
#         if self.type == 'news':
#             self.sour_client = clients['yusheng1999']
#             self.obj_client = clients['yusheng1999']

# 注册Tg客户类
class RegClient(Client):
    # normal = True
    def __init__(self):
        # 配置接码平台
        self.sa = SMSActivateAPI('b0457109006AAfe6dAf8cfcAA0f60cf8')
        self.sa.debug_mode = False

        # 初始化变量
        self.code_hash = None

        # 设置代理
        # self.proxy_addr = '**************'
        # self.proxy_port = 1085
        # self.proxy = dict(scheme="socks5", hostname=self.hostname, port=self.port)

    # API设置
    def set_api(self):
        app_api = get_random('app_api')
        (self.api_id, self.api_hash) = app_api[1].split(':')

    # 启动client
    async def start(self):
        print_both("start...")
        await super().start()
        print_both("client实例已启动：{0:s}".format(self.name))

    # 关闭client
    async def stop(self):
        await super().stop()
        print_both("client实例已关闭：{0:s}".format(self.name))

    # 连接服务器
    async def conn(self):
        try:
            step_desc = "连接服务器"
            print_both("{0:s} {1:s}...".format(self.name, step_desc))
            await self.connect()
        except ConnectionError as e:
            print_both("{0:s} {1:s}：失败，现断开重新连接...".format(self.name, step_desc))
            await self.disconnect()
            await self.connect()

    # 请求发送验证码
    async def require_send_code(self):
        try:
            step_desc = "请求发送验证码"
            print_both(step_desc)
            # code_hash = await self.invoke(
            #     raw.functions.account.SendVerifyPhoneCode(phone_number=self.full_phone,
            #                                               settings=raw.types.CodeSettings(allow_app_hash=True))
            # )
            code_hash = await self.send_code(self.full_phone)
            return True, code_hash
        except FloodWait as e:
            print_both("{0:s}：操作太频繁，等待 {1:d} 秒后重试...".format(step_desc, e.value))
            await asyncio.sleep(e.value + 5)
            return await self.require_send_code()
        except ApiIdInvalid as e:
            step_desc = "{0:s}：ApiId无效".format(step_desc)
            print_both(step_desc)
            return False, step_desc
        except PhoneNumberInvalid as e:
            step_desc = "{0:s}：电话号码无效".format(step_desc)
            print_both(step_desc)
            return False, step_desc
        except PhoneNumberBanned as e:
            step_desc = "{0:s}：号码被禁用".format(step_desc)
            print_both(step_desc)
            return False, step_desc
        except Exception as e:
            step_desc = "{0:s}：其它报错".format(step_desc)
            print_both(step_desc)
            sys_log.write_log(traceback.format_exc(), 'a')
            return False, code_hash

    # 检验验证码
    async def check_code(self, code):
        try:
            try:
                step_desc = "检验验证码"
                print_both(step_desc)
                print((self.full_phone, self.code_hash, code, len(code)))
                sign_in = await self.sign_in(self.full_phone, self.code_hash.phone_code_hash, ' '.join(code))
            except SessionPasswordNeeded:
                step_desc_2 = "检验二次验证码"
                print_both(step_desc_2)
                max_times = 4
                i = 0
                while i < max_times:
                    two_step_code = input("请输入二次验证码：")
                    try:
                        sign_in = await self.check_password(two_step_code)
                        sql_str = "update myclient set password='{1:s}' where name='{0:s}'"\
                            .format(self.name, two_step_code)
                        exe_connector.run(sql_str)
                        break
                    except BadRequest:
                        step_desc_err = "{0:s}：密码无效".format(step_desc_2)
                        print_both(step_desc_err)
                    except Exception as e:
                        step_desc_err = "{0:s}：其它报错".format(step_desc_2)
                        print_both(step_desc_err)
                        sys_log.write_log(traceback.format_exc(), 'a')
                    i = i + 1
                if i == 4:
                    return False, "{0:s}：多次输入错误".format(step_desc_2)
            except PhoneCodeInvalid:
                step_desc = "{0:s}：验证码无效".format(step_desc)
                print_both(step_desc)
                return False, step_desc
            except PhoneCodeExpired:
                step_desc = "{0:s}：验证码过期".format(step_desc)
                sys_log.write_log(traceback.format_exc(), 'a')
                print_both(step_desc)
                return False, step_desc
            except PhoneNumberBanned:
                step_desc = "{0:s}：电话号码被禁止".format(step_desc)
                print_both(step_desc)
                return False, step_desc
            except BadRequest:
                step_desc = "{0:s}：参数错误".format(step_desc)
                sys_log.write_log(traceback.format_exc(), 'a')
                print_both(step_desc)
                return False, step_desc
            except Exception as e:
                step_desc = "{0:s}：其它报错".format(step_desc)
                print_both(step_desc)
                sys_log.write_log(traceback.format_exc(), 'a')
                return False, step_desc
            # 需要接收服务条款
            if isinstance(sign_in, TermsOfService):
                try:
                    step_desc = "接受服务条款"
                    print_both(step_desc)
                    await self.accept_terms_of_service(sign_in.id)
                    await asyncio.sleep(1)
                    step_desc = "接受服务条款后，再次检查验证码"
                    return await self.check_code(code)
                except PhoneNumberUnoccupied:
                    step_desc = "{0:s}：电话号码未被占用".format(step_desc)
                    print_both(step_desc)
                    return False, step_desc
            # 需要先注册
            if not sign_in:
                try:
                    step_desc = "注册新账号"
                    print_both(step_desc)
                    first_name = "first_name_tmp"
                    last_name = "last_name_tmp"
                    sign_in = await self.sign_up(self.full_phone,
                                                 self.code_hash.phone_code_hash,
                                                 first_name=first_name,
                                                 last_name=last_name)
                except BadRequest:
                    step_desc = "{0:s}：参数无效".format(step_desc)
                    print_both(step_desc)
                    return False, step_desc
            # 能继续完成验证
            if isinstance(sign_in, User):
                step_desc = "{0:s}：检验成功！".format(step_desc)
                print_both(step_desc)
                return True, sign_in
            else:
                step_desc = "{0:s}：检验不成功".format(step_desc)
                print_both(step_desc)
                return False, sign_in
        except Exception as e:
                step_desc = "{0:s}：其它报错".format(step_desc)
                print_both(step_desc)
                sys_log.write_log(traceback.format_exc(), 'a')
                return False, step_desc




        return sign_in

    # # 注册新账号
    # async def new_client(self, no, sign_in, code_hash, code):
    #     try:
    #         step_desc = "注册新账号"
    #         print_both("账号 {0:d}|{1:s} {2:s}".format(no, self.name, step_desc))
    #         first_name = get_random('first_name')
    #         last_name = get_random('last_name')
    #         await self.sign_up(self.full_phone,
    #                            code_hash.phone_code_hash,
    #                            first_name=first_name,
    #                            last_name=last_name)
    #         # 需要先接受服务条款
    #         if isinstance(sign_in, TermsOfService):
    #             try:
    #                 step_desc = "接受服务条款"
    #                 print_both("账号 {0:d}|{1:s} {2:s}".format(no, self.name, step_desc))
    #                 await self.accept_terms_of_service(sign_in.id)
    #             except PhoneNumberUnoccupied:
    #                 step_desc = "电话号码未被占用"
    #                 print_both("账号 {0:d}|{1:s} {2:s}".format(no, self.name, step_desc))
    #                 sys_log.write_log(traceback.format_exc(), 'a')
    #                 return False
    #             await asyncio.sleep(1)
    #             step_desc = "接受服务条款后，再次检查验证码"
    #             print_both("账号 {0:d}|{1:s} {2:s}...".format(no, self.name, step_desc))
    #             sign_in = await self.sign_in(self.full_phone, code_hash.phone_code_hash, ' '.join(code))
    #         if not sign_in:
    #             step_desc = "电话号码需要注册"
    #             sys_log.write_log("{0:s} {1:s}".format(self.name, step_desc), 'a')
    #             return False
    #         step_desc = "恭喜，用户已注册成功！"
    #         print_both("账号 {0:d}|{1:s} {2:s}...".format(no, self.name, step_desc))
    #         return True
    #     except BadRequest:
    #         sys_log.write_log("{0:s} {1:s}：无效请求".format(self.name, step_desc), 'a')
    #         sys_log.write_log(traceback.format_exc(), 'a')
    #         return False

    # # 注册新账号
    # async def new_client_tele(self, no, sign_in, code_hash, code):
    #     try:
    #         step_desc = "注册新账号"
    #         print_both("账号 {0:d}|{1:s} {2:s}".format(no, self.name, step_desc))
    #         first_name = get_random('first_name')
    #         last_name = get_random('last_name')
    #         await self.client_tele.sign_up(code=code,
    #                                        first_name=first_name,
    #                                        last_name=last_name,
    #                                        phone_code_hash=code_hash)
    #         return True
    #     except BadRequest:
    #         sys_log.write_log("{0:s} {1:s}：无效请求".format(self.name, step_desc), 'a')
    #         sys_log.write_log(traceback.format_exc(), 'a')
    #         return False

    # 更新参数
    def update_var(self):
        try:
            head_sql = "select name,prefix,phone,password,chat_id,chat_name,username,type,api_id,api_hash,proxy_addr," \
                       "proxy_port,session_string,no_updates,plugins,status,status_curr,active_at,created_at,note " \
                       "from myclient "
            sql_str = head_sql + "where name='{0:s}'".format(self.name)
            rst = read_connector.run(sql_str)
            if len(rst) == 0:
                return False
            else:
                (self.name, self.prefix, self.phone, self.password, self.chat_id, self.chat_name, self.username,
                 self.type, self.api_id, self.api_hash,self.proxy_addr, self.proxy_port, self.session_str,
                 self.no_updates, self.plugins, self.status, self.status_curr, self.active_at, self.created_at,
                 self.note) = rst[0]
                self.full_phone = '+' + self.prefix + self.phone
                return True
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 实例化对象client
    def init_client(self, way='session'):
        try:
            # 更新参数
            print_both("更新client参数：{0:s}".format(self.name))
            self.update_var()
            # 设置代理
            if self.proxy_addr != '' and test_ip(self.proxy_addr, self.proxy_port):
                # 若历史代理可用，则继续使用
                self.proxy = dict(scheme="socks5", hostname=self.proxy_addr, port=self.proxy_port)
                print_both("使用原代理登录tg: {0:s}->{1:s}:{2:d}".format(self.name, self.proxy_addr, self.proxy_port))
                proxy_set_use_status(self.proxy_addr, '3')
            else:
                # 若历史代理不可用，则重新申请新代理
                new_proxy = proxy_get('3')
                if len(new_proxy) > 0:
                    (self.proxy_addr, self.proxy_port) = new_proxy
                    sql_str = "update myclient set proxy_addr='{1[0]:s}', proxy_port={1[1]:d} where name='{0:s}'" \
                        .format(self.name, new_proxy)
                    exe_connector.run(sql_str)
                    self.proxy = dict(scheme="socks5", hostname=self.proxy_addr, port=self.proxy_port)
                    print_both("使用新代理登录tg: {0:s}->{1:s}:{2:d}".format(self.name, self.proxy_addr, self.proxy_port))
                else:
                    sql_str = "update myclient set proxy_addr='', proxy_port=0 where name='{0:s}'".format(self.name)
                    exe_connector.run(sql_str)
                    print_both("无可用代理: {0:s}".format(self.name))
                    return False
            # 插件设置
            plus = self.plugins.split('|')
            if self.plugins == '':
                plugins = dict()
            elif len(plus) == 1:
                plugins = dict(root="plugins", include=plus[0].split(','))
            else:
                plugins = dict(root="plugins", include=plus[0].split(','), exclude=plus[1].split(','))
            # 实例化
            print_both("实例化client")
            if way == 'string':
                super().__init__(name=self.name,
                                 api_id=self.api_id,
                                 api_hash=self.api_hash,
                                 proxy=self.proxy,
                                 session_string=self.session_str,
                                 phone_number=self.full_phone,
                                 workdir="./sessions",
                                 plugins=plugins,
                                 no_updates=explain('bool', self.no_updates)
                                 )
            else:
                super().__init__(name=self.name,
                                 api_id=self.api_id,
                                 api_hash=self.api_hash,
                                 proxy=self.proxy,
                                 phone_number=self.full_phone,
                                 workdir="./sessions",
                                 plugins=plugins,
                                 no_updates=explain('bool', self.no_updates)
                                 )
            return True
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # # 实例化对象client Telethon模式
    # def init_client_tele(self):
    #     try:
    #         # 更新参数
    #         print_both("更新client参数")
    #         self.update_var()
    #         # 设置代理
    #         if test_ip(self.proxy_addr, self.proxy_port):
    #             # 代理设置
    #             self.proxy = dict(proxy_type="socks5", addr=self.proxy_addr, port=int(self.proxy_port))
    #             print_both("检查代理可用 {0:s}:{1:s}".format(self.proxy_addr, self.proxy_port))
    #         else:
    #             print_both("检查代理不可用 {0:s}:{1:s}".format(self.proxy_addr, self.proxy_port))
    #             return False
    #         # 实例化
    #         print_both("实例化client")
    #         self.client_tele = TelegramClient(
    #             session="./sessions/backup/{0:s}".format(self.name),
    #             api_id=self.api_id,
    #             api_hash=self.api_hash,
    #             proxy=self.proxy
    #         )
    #         return True
    #     except Exception as e:
    #         sys_log.write_log(traceback.format_exc(), 'a')

    # 更新session_string
    async def update_session_string(self):
        try:
            if self.session_str == '':
                session_string = await self.export_session_string()
                sql_str = "update myclient set session_string='{1:s}' where name='{0:s}'" \
                    .format(self.name, session_string)
                exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


    # 通过客户端登录后台，第一步
    async def login_client_db_1(self, name):
        try:
            text = name.split('_')
            # 输入格式认证
            while len(text) != 2:
                return "输入格式不正确(国家区号_号码)"
            self.name = name
            # 验证db中号码情况，并做必要清理
            sql_str = "select name,status from myclient where name='{0:s}'".format(self.name)
            rst = read_connector.run(sql_str)
            if len(rst) > 0 and (rst[0][1])[0] not in ('0', '1'):
                return "账号无法登录：{0:s}".format(explain('MyclientStatus', rst[0][1]))
            elif len(rst) > 0 and rst[0][1] != '00':
                print_both("该账号可直接登录，无需重新接收验证码")
                ck_rst = self.init_client()
                if not ck_rst:
                    return "直接登录，但初始化失败"
                await self.start()
                now_time = time.localtime(time.time())
                time_to_str = time.strftime('%Y-%m-%d %H:%M:%S', now_time)
                msg = await self.send_message('me', "通过login_client_db直接登录 {0:s}".format(time_to_str))
                # await update_chats(self, msg)
                myChats.update_message(msg)
                await self.update_session_string()
                await self.stop()
                step_desc = "直接登录成功login_client_db"
                print_both(step_desc)
                sql_str = "update myclient a join (select * from chats where id={2:d}) b set " \
                          "a.active_at=CURRENT_TIMESTAMP(),a.note='{1:s}',a.chat_id=b.id,a.chat_name=b.name," \
                          "a.username=b.username where a.name='{0:s}'".format(self.name, step_desc, msg.from_user.id)
                exe_connector.run(sql_str)
                return step_desc
            else:
                if len(rst) > 0:
                    sql_str = "delete from myclient where name='{0:s}'".format(self.name)
                    exe_connector.run(sql_str)
                    print_both("库表中已有记录但未登录，重新接收验证码登录")
                else:
                    print_both("库表中未有记录，接收验证码登录")
                # 设置APP_API
                self.set_api()
                sql_str = "insert into myclient(name,prefix,phone,api_id,api_hash,created_at) " \
                          "values('{0[0]:s}_{0[1]:s}','{0[0]:s}','{0[1]:s}','{1:s}','{2:s}',CURRENT_TIMESTAMP())" \
                    .format(text, self.api_id, self.api_hash)
                exe_connector.run(sql_str)
                ck_rst = self.init_client()
                if not ck_rst:
                    return "验证登录，但初始化失败"
                await self.conn()
                require_rst = await self.require_send_code()
                if require_rst[0]:
                    self.code_hash = require_rst[1]
                    return "等待接收验证码"
                else:
                    return require_rst[1]
        except UserDeactivatedBan:
            step_desc = "账号被禁用"
            sql_str = "update myclient set status='30',active_at=CURRENT_TIMESTAMP(),note='{1:s}' where name='{0:s}'"\
                .format(self.name, step_desc)
            exe_connector.run(sql_str)
            print_both(step_desc)
            return step_desc
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 接收验证码
    async def login_client_db_2(self, code):
        try:
            check_rst = await self.check_code(code)
            if not check_rst[0]:
                return check_rst[1]
            now_time = time.localtime(time.time())
            time_to_str = time.strftime('%Y-%m-%d %H:%M:%S', now_time)
            msg = await self.send_message('me', "通过login_client_db验证登录 {0:s}".format(time_to_str))
            # await update_chats(self, msg)
            myChats.update_message(msg)
            await self.update_session_string()
            step_desc = "验证登录成功login_client_db"
            print_both(step_desc)
            sql_str = "update myclient a join (select * from chats where id={2:d}) b set a.status=(case when a.status" \
                      "='00' then '01' else a.status end),a.active_at=now(),a.note='{1:s}',a.chat_id=b.id,a.chat_name=" \
                      "b.name,a.username=b.username where a.name='{0:s}'".format(self.name, step_desc, msg.from_user.id)
            exe_connector.run(sql_str)
            return step_desc
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 通过后台登录客户端
    async def login_db_client(self):
        try:
            text = input("要登录client的号码(国家区号 号码)：").strip().split(' ')
            # 输入格式认证
            while len(text) != 2:
                text = input("输入格式不正确(国家区号 号码)：").strip().split(' ')
            self.name = "{0[0]:s}_{0[1]:s}".format(text)
            if not self.update_var():
                print_both("db中没有该号码信息：{0:s}".format(self.name))
                return
            self.init_client()
            await self.start()
            # 777000
            async for m in self.get_chat_history(777000, limit=1):
                msg = m
            print_both("Telegram 最后一条记录如下：\n" + msg.text)
            await self.stop()


        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 通过字符串登录后台
    async def login_via_str(self):
        try:
            text = input(self._show1 + "要通过String登录的号码(国家区号 号码)：").strip().split(' ')
            # 输入格式认证
            while len(text) != 2:
                text = input("输入格式不正确(国家区号 号码)：").strip().split(' ')
            self.name = "{0[0]:s}_{0[1]:s}".format(text)
            ck_rst = self.init_client("string")
            if not ck_rst:
                return
            await self.start()
            await self.send_message('me', "通过login_via_str登录")
            await self.stop()
            step_desc = "直接登录成功 login_via_str：{0:s}".format(self.name)
            print_both(step_desc)
            sql_str = "update myclient set active_at=now(),note='{1:s}' where name='{0:s}'" \
                .format(self.name, step_desc)
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 注册
    async def register(self, country_id: int, no):
        try:
            # 每次请求停留时间
            waiter = 1
            # 设定国家区号
            sql_str = "select name,value_str,value_int from params where type='国家区号编号' and value_int={0:d}".format(country_id)
            rst = read_connector.run(sql_str)
            if len(rst) == 0:
                print_both("账号 {0:d} 维表中没有{1:d}对应的国家区号，程序退出".format(no, country_id))
                RegClient.normal = False
                return
            else:
                self.prefix = rst[0][1]
            # 检查可用号码数
            total_phone = int((self.sa.getNumbersStatus(country_id))['tg_0'])
            print_both("账号 {0:d} 可用号码数：{1:d}".format(no, total_phone))
            if total_phone < 10:
                print_both("账号 {0:d} 可用号码数太少，程序退出".format(no))
                RegClient.normal = False
                return
            await asyncio.sleep(waiter)
            # 检查余额
            balance = float((self.sa.getBalance())['balance'])
            print_both("账号 {0:d} 可用余额：{1:.2f}".format(no, balance))
            if balance < 20:
                print_both("账号 {0:d} 账户余额太少，程序退出".format(no))
                RegClient.normal = False
                return
            await asyncio.sleep(waiter)
            # 获取可用的代理
            proxy = get_proxy()
            if len(proxy) > 0:
                (self.proxy_addr, self.proxy_port, self.proxy_name, self.proxy_pwd) = proxy
                print_both("账号 {0:d} 使用代理 {1:s}:{2:s}".format(no, self.proxy_addr, self.proxy_port))
            else:
                print_both("账号 {0:d} 无可用的代理，程序退出".format(no))
                RegClient.normal = False
                return
            # 请求号码
            number = self.sa.getNumber(service='tg', country=country_id)
            print(number)
            activation_id = number['activation_id']
            self.phone = str(int(str(number['phone']).lstrip(self.prefix)))
            self.name = "{0:s}_{1:s}".format(self.prefix, self.phone)
            print_both("账号 {0:d}|{1:s} 已获取到号码，激活id：{2:d}".format(no, self.name, activation_id))
            await asyncio.sleep(waiter)
            # 验证db中号码情况
            sql_str = "select name,status from myclient where name='{0:s}'".format(self.name)
            rst = read_connector.run(sql_str)
            if len(rst) != 0 and rst[0][1] != '00':
                # 取消激活
                print(self.sa.setStatus(id=activation_id, status=8))
                print_both("账号 {0:d}|{1:s} 库表中已经有该号码，取消激活".format(no, self.name))
                return
            else:
                if len(rst) != 0:
                    sql_str = "delete from myclient where name='{0:s}'".format(self.name)
                    exe_connector.run(sql_str)
                    print_both("账号 {0:d}|{1:s} 库表中已有记录但未登录，重新接收验证码登录".format(no, self.name))
                else:
                    print_both("账号 {0:d}|{1:s} 库表中未有记录，接收验证码登录".format(no, self.name))
                sql_str = "insert into myclient(name,prefix,phone,proxy_addr,proxy_port,proxy_name,proxy_pwd,created_at) " \
                          "values('{0:s}_{1:s}','{0:s}','{1:s}','{2:s}','{3:s}','{4:s}','{5:s}',now())" \
                    .format(self.prefix, self.phone, self.proxy_addr, self.proxy_port, self.proxy_name, self.proxy_pwd)
                exe_connector.run(sql_str)
                ck_rst = self.init_client()
                if not ck_rst:
                    print_both("账号 {0:d}|{1:s} 初始化client失败，退出".format(no, self.name))
                    return
                await self.conn()
                # 提醒接码平台接收短信验证码
                print(self.sa.setStatus(id=activation_id, status=1))
                print_both("账号 {0:d}|{1:s} 请求发送短信验证码".format(no, self.name))
                code_hash = await self.require_send_code()
                if code_hash is None:
                    print_both("账号 {0:d}|{1:s} 请求发送短信验证码失败".format(no, self.name))
                    return
                # 等待接收验证码
                code = ''
                for n in range(1, 30):
                    # 查询激活状态
                    result = self.sa.getStatus(id=activation_id)
                    print(result)
                    if len(result.split(':')) == 2:
                        code = result.split(':')[1]
                        break
                    await asyncio.sleep(5)
                if code == '':
                    print_both("账号 {0:d}|{1:s} 无法接收到验证码，退出".format(no, self.name))
                    print(self.sa.setStatus(id=activation_id, status=8))
                    return
                else:
                    print_both("账号 {0:d}|{1:s} 接收到验证码：{2:s}".format(no, self.name, code))
                # 检验验证码
                sign_in = await self.check_code(code_hash, code)
                if sign_in is None:
                    print_both("账号 {0:d}|{1:s} 检验验证码失败：{2:s}".format(no, self.name, code))
                    return
                else:
                    print_both("账号 {0:d}|{1:s} 检验验证码成功！".format(no, self.name))
                    print(self.sa.setStatus(id=activation_id, status=6))
                    await asyncio.sleep(waiter)
                # 注册新号码
                sign_up = await self.new_client(no, sign_in, code_hash, code)
                if not sign_up:
                    print_both("账号 {0:d}|{1:s} 注册新号码失败".format(no, self.name))
                    return
                await self.send_message('me', "通过register注册新号成功")
                await self.update_session_string()
                step_desc = "注册新号成功"
                print_both("账号 {0:d}|{1:s} {2:s}!".format(no, self.name, step_desc))
                sql_str = "update myclient set status=(case when status='00' then '01' else status end)," \
                          "active_at=now(),note='{1:s}' where name='{0:s}'".format(self.name, step_desc)
                exe_connector.run(sql_str)
        except Exception as e:
            RegClient.normal = False
            print_both(traceback.format_exc())


# Tg客户类
class MyClient(Client):
    __head_sql = "select name,prefix,phone,password,chat_id,chat_name,api_id,api_hash,is_bot,bot_token,proxy_addr," \
                 "proxy_port,proxy_name,proxy_pwd,session_string,no_updates,plugins,status,status_curr,active_at," \
                 "created_at,note from myclient "

    def __init__(self,
                 name: str = None,
                 type: str = 'workdir'
                 ):
        self.name = name
        sql_str = self.__head_sql + "where name='{0:s}'".format(name)
        rst = read_connector.run(sql_str)
        # 检查是否已有记录
        if len(rst) == 0:
            return
        # 赋值
        (self.prefix, self.phone, self.password, self.chat_id, self.chat_name, self.api_id, self.api_hash, self.is_bot,
         self.bot_token, self.proxy_addr, self.proxy_port, self.proxy_name, self.proxy_pwd, self.session_string,
         self.no_updates, self.plugins, self.status, self.status_curr, self.active_at, self.created_at, self.note) \
            = rst[0]
        print((self.prefix, self.phone, self.password, self.chat_id, self.chat_name, self.api_id, self.api_hash,
               self.is_bot, self.bot_token, self.proxy_addr, self.proxy_port, self.proxy_name, self.proxy_pwd,
               self.session_string, self.no_updates, self.plugins, self.status, self.status_curr, self.active_at,
               self.created_at, self.note))
        # 代理设置
        if self.proxy_name == '':
            proxy = dict(scheme="socks5", hostname=self.proxy_addr, port=int(self.proxy_port))
            print(proxy)
        else:
            proxy = dict(scheme="socks5", hostname=self.proxy_addr, port=int(self.proxy_port),
                         username=self.proxy_name, password=self.proxy_pwd)
       # 插件设置
        plus = self.plugins.split('|')
        if self.plugins == '':
            plugins = dict()
        elif len(plus) == 1:
            plugins = dict(root="plugins", include=plus[0].split(','))
        else:
            plugins = dict(root="plugins", include=plus[0].split(','), exclude=plus[1].split(','))
        # 实例化Client
        if type == 'workdir':
            super().__init__(name,
                             proxy=proxy,
                             workdir="./sessions",
                             plugins=plugins,
                             no_updates=explain('bool', self.no_updates))
        print("实例化完成")

    # 删除session文件
    def remove_session(self):
        os.remove(f"./sessions/{0:s}.session".format(self.name))

    # 启动client
    async def start(self):
        await super().start()
        print("client实例已启动")

    # 关闭client
    async def stop(self):
        await super().stop()

    # 更新session_string
    async def update_session_string(self):
        try:
            session_string = await self.export_session_string()
            print(session_string)
            if session_string is not None:
                sql_str = "update myclient set session_string='{1:s}' where name='{0:s}'" \
                    .format(self.name, session_string)
                exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')


# 群组供应商 由于加人功能无效，此类暂不用
class GroupSupply:
    def __init__(self, user_name, client_name, obj_chat_id):
        self.user_name = user_name
        self.client_name = client_name
        self.obj_chat_id = obj_chat_id
        self.user_ids = []
        self.users = []
        # 标记目标群中已经有的客户
        sql_str = "update group_supply a join (select user_id from chat_user where chat_id={1:d}) b on a.content_int=" \
                  "b.user_id set a.status='2', a.updated_at=CURRENT_TIMESTAMP() where a.type='{0:s}' and a.status='1'" \
            .format(user_name, obj_chat_id)
        exe_connector.run(sql_str)

    def get_user(self, cnt=1):
        try:
            sql_str = "select id,content_int from group_supply where status='1' and type='{0:s}' limit {1:d}"\
                .format(self.user_name, cnt)
            rst = read_connector.run(sql_str)
            self.user_ids.clear()
            self.users.clear()
            for col in rst:
                self.user_ids.append(col[0])
                self.users.append(col[1])
            return self.users
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    def update_user(self, status):
        try:
            ids_str = ",".join(list(map(str, self.user_ids)))
            sql_str = "update group_supply set cnt=cnt+1, status='{1:s}', updated_at=CURRENT_TIMESTAMP() where id " \
                      "in ({0:s})".format(ids_str, status)
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    def update_client(self, clt, status):
        try:
            sql_str = "update group_supply set cnt=cnt+1, status='{2:s}', updated_at=CURRENT_TIMESTAMP() where " \
                      "type='{0:s}' and content_str='{1:s}'".format(self.client_name, clt, status)
            exe_connector.run(sql_str)
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')

    async def add_member(self):
        try:
            sql_str = "select content_str from group_supply where type='{0:s}' and status='1'".format(self.client_name)
            rst = read_connector.run(sql_str)
            # 已经没有可执行的client
            if len(rst) == 0:
                return '2'
            for clt in rst:
                try:
                    # 若已经添加完成，则不需要继续操作
                    users_list = self.get_user(10)
                    if len(users_list) == 0:
                        return '2'
                    # await clients[clt[0]].add_chat_members(self.obj_chat_id, users_list)
                    await clients['lashou01'].add_chat_members(-1001526949053, 351390709)
                    self.update_user('3')
                    self.update_client(clt[0], '1')
                except Exception as e:
                    sys_log.write_log(traceback.format_exc(), 'a')
                    self.update_user('4')
                    self.update_client(clt[0], '0')
                await asyncio.sleep(5)
            # 本次操作成功完成
            return '1'
        except Exception as e:
            sys_log.write_log(traceback.format_exc(), 'a')



