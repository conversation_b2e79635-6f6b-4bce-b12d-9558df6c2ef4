# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
import pymysql
import mysql.connector
from mysql.connector import errorcode
from configparser import ConfigParser
from typing import Union
import pathlib
import re
import time
import shutil
import os
from pyrogram import Client
from pyrogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton, User, Chat, ChatMember, \
    MenuButton, MenuButtonCommands, ReplyKeyboardMarkup, KeyboardButton, ForceReply, ChatPermissions
from pyrogram.enums import ChatMembersFilter, ChatType, UserStatus, ChatMemberStatus, ParseMode, MessageMediaType, \
    MessageEntityType
import logging
import traceback
import sys
import datetime
from datetime import timedelta
from dateutil.relativedelta import relativedelta
import pandas as pd
import telnetlib
from decimal import Decimal
import asyncio
from itertools import chain


logging.basicConfig(level=logging.ERROR,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# # 获取并设置 'okx' 和 'werkzeug' 的日志级别为 ERROR，忽略调试信息
# logging.getLogger('okx').setLevel(logging.ERROR)
# logging.getLogger('werkzeug').setLevel(logging.ERROR)
# logging.disable(logging.CRITICAL)


# 重写ConfigParser类，取消自动转换成小写的功能
class MyConfigParser(ConfigParser):
    def __init__(self, defaults=None):
        ConfigParser.__init__(self, defaults=defaults)

    def optionxform(self, optionstr):
        return optionstr


# 文件操作类
class FileOpt:
    def __init__(self, file_dir):
        self.file_dir = file_dir

    # 从文件中读出匹配上的记录
    def read(self, pat):
        file = open(self.file_dir, 'r')
        content = file.read()
        rst_list = re.compile(pat, re.M).findall(content)
        file.close()
        return rst_list

    # 把内容写入文件
    def write(self, content, model):
        file = open(self.file_dir, model)
        file.write(content + '\n')
        file.flush()
        file.close()

    # 更新文件内容
    def replace(self, old_str, new_str):
        file_bak = '{0:s}.bak'.format(self.file_dir)
        with open(self.file_dir, "r") as rf, open(file_bak, "w") as wf:
            for line in rf:
                wf.write(line.replace(old_str, new_str))
        os.remove(self.file_dir)
        os.rename(file_bak, self.file_dir)

    # 为日志打上时间标签
    def write_log(self, content, model):
        tm = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
        self.write(tm + ' ' + content, model)

    # 在屏幕和日志中都显示
    def print_log(self, text: str):
        print(text)
        self.write_log(text, 'a')

    # 保存最新的keep_num条记录
    def clean_log(self, keep_num):
        read_file = open(self.file_dir, 'r')
        contents = read_file.readlines()
        if len(contents) > keep_num:
            str_line = len(contents) - keep_num
            end_line = len(contents)
            bak_file = self.file_dir + 'bak'
            write_file = open(bak_file, 'a')
            for i in range(str_line, end_line):
                write_file.write(contents[i])
            read_file.close()
            write_file.close()
            shutil.move(bak_file, self.file_dir)


# 定义全局变量
main_file = sys.argv[0]
if main_file == 'main_admin.py':  # 管理员
    client_list = ['bowin_boss', 'bowin_adminbot']
    # client_list = ['bowin']
    sys_log = FileOpt('./log/log_admin.txt')
elif main_file == 'main_redbag.py':  # 红包机器人
    client_list = ['bowin_redbagbot']
    sys_log = FileOpt('./log/log_redbag.txt')
elif main_file == 'main_asst.py':  # 红包机器人
    client_list = ['bowin_asstbot']
    sys_log = FileOpt('./log/log_asst.txt')
elif main_file == 'main_group.py':  # 群管理机器人
    client_list = ['bowin_groupbot']
    sys_log = FileOpt('./log/log_group.txt')
else:
    client_list = []
    sys_log = FileOpt('./log/log.txt')


# 配置文件操作类
class ConfFile:
    def __init__(self, file_dir):
        self.file_dir = file_dir
        # 若配置文件不存在，则创建新
        pathlib.Path(self.file_dir).touch()
        self.config = MyConfigParser()
        self.config.read(self.file_dir)

    # 写配置文件
    def write(self, section, option, value):
        try:
            self.config.set(section, str(option), str(value))
            self.config.write(open(self.file_dir, 'w'))
        except Exception as e:
            if re.match('^No section:.*', str(e)) is not None:
                self.config.add_section(section)
                self.write(section, option, value)
                return
            sys_log.write_log(traceback.format_exc(), 'a')

    # 读配置文件
    def read(self, section, option, r_type='string'):
        try:
            if r_type == 'string':
                return self.config.get(section, str(option))
            elif r_type == 'int':
                return self.config.getint(section, str(option))
            elif r_type == 'float':
                return self.config.getfloat(section, str(option))
            elif r_type == 'boolean':
                return self.config.getboolean(section, str(option))
            else:
                return
        except Exception as e:
            if re.match('^No option .*? in section.*', str(e)) is not None:
                return
            sys_log.write_log(traceback.format_exc(), 'a')
            return

    # 检查option是否存在
    def has_opt(self, section, option):
        try:
            return str(option) in self.config.options(section)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 删除option操作
    def del_opt(self, section, option):
        try:
            self.config.remove_option(section, option)
            self.config.write(open(self.file_dir, 'w'))
            return True
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False


# 改进型数据库操作类
class MySQLDB:
    def __init__(self, db_section):
        self.config = {
            'host': db_conf.read(db_section, 'host'),
            'user': db_conf.read(db_section, 'user'),
            'password': db_conf.read(db_section, 'pwd'),
            'database': db_conf.read(db_section, 'dbname'),
            'autocommit': False,
            'charset': 'utf8mb4',
            'use_pure': True
            }
        self.connection = ''
        self.cursor = ''
        self.last_conn = time.time()

    # 连接数据库
    def open(self):
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor()
            return True
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 当60秒内第一次异常，尝试重连数据库
    def reopen(self):
        try:
            if time.time() - self.last_conn > 60:
                sys_log.write_log("尝试重连数据库...", 'a')
                self.last_conn = time.time()
                return self.open()
            else:
                return False
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 连接数据库
    def close(self):
        try:
            self.cursor.close()
            self.connection.close()
            return True
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')
            return False

    # 开启事务
    def start_transaction(self):
        self.connection.start_transaction()

    # 提交
    def commit(self):
        self.connection.commit()

    # 回滚
    def rollback(self):
        self.connection.rollback()

    # 执行语句
    def run(self, sql_string, params_list=None):
        try:
            self.cursor.execute(sql_string, params_list)
            if sql_string.strip().upper().startswith("SELECT"):
                return self.cursor.fetchall()
            else:
                self.connection.commit()
                return self.cursor.rowcount
        except Exception:
            if self.reopen():
                return self.run(sql_string, params_list)
            else:
                sys_log.write_log(sql_string + ' 参数：' + str(params_list), 'a')
                sys_log.write_log(traceback.format_exc(), 'a')
                if sql_string.strip().upper().startswith("SELECT"):
                    return ()
                else:
                    self.connection.rollback()
                    return -1

    # 执行不执行commit的非SELECT语句
    def exe_without_commit(self, sql_string, params_list=None):
        try:
            self.cursor.execute(sql_string, params_list)
            return self.cursor.rowcount
        except Exception:
            if self.reopen():
                return self.exe_without_commit(sql_string, params_list)
            else:
                sys_log.write_log(sql_string + ' 参数：' + str(params_list), 'a')
                sys_log.write_log(traceback.format_exc(), 'a')
                return -1

    # 批量执行语句
    def run_many(self, sql_string, params_list):
        try:
            self.cursor.executemany(sql_string, params_list)
            return self.cursor.rowcount
        except Exception:
            if self.reopen():
                return self.run_many(sql_string, params_list)
            else:
                sys_log.write_log(sql_string + ' 参数：' + str(params_list), 'a')
                sys_log.write_log(traceback.format_exc(), 'a')
                return -1

    # 执行存储过程
    def pro(self, proc_name, params_list):
        try:
            self.cursor.callproc(proc_name, params_list)
            # 返回查询结果
            return [result.fetchall() for result in list(self.cursor.stored_results())]
        except Exception:
            if self.reopen():
                return self.pro(proc_name, params_list)
            else:
                sys_log.write_log(proc_name + ' 参数：' + str(params_list), 'a')
                sys_log.write_log(traceback.format_exc(), 'a')
                return ()

    # 从txt文件中导入数据到数据表
    def import_from_txt(self, file_path, table):
        try:
            # 入库前记录数
            sql_string = "select count(1) from {0:s}".format(table)
            pre_cnt = self.run(sql_string)[0][0]
            with open(file_path, 'r') as file:
                lines = file.readlines()
                params_list = [tuple(line.strip().split(',')) for line in lines]
                placeholders = ','.join(['%s'] * len(params_list[0]))
                sql_string = f"INSERT INTO {table} VALUES ({placeholders})"
                self.run_many(sql_string, params_list)
                file_cnt = sum(1 for line in file)
            # 入库后记录数
            aft_cnt = self.run(sql_string)[0][0]
            # 返回操作记录
            return file_cnt, pre_cnt, aft_cnt, aft_cnt - pre_cnt
        except Exception:
            if self.reopen():
                return self.import_from_txt(file_path, table)
            else:
                sys_log.write_log('import_from_txt：' + file_path + ' ' + table, 'a')
                sys_log.write_log(traceback.format_exc(), 'a')
                return -1, -1, -1, -1

    # 从数据表中导出数据到txt文件
    def export_to_txt(self, table, file_path):
        try:
            sql_string = f"SELECT * FROM {table}"
            results = self.run(sql_string)
            with open(file_path, 'w') as file:
                for row in results:
                    file.write(','.join(map(str, row)) + '\n')
        except Exception:
            if self.reopen():
                return self.export_to_txt(file_path, table)
            else:
                sys_log.write_log('export_to_txt：' + table + ' ' + file_path, 'a')
                sys_log.write_log(traceback.format_exc(), 'a')


# 定义全局方法
# 验证代理是否可用
def test_ip(ip: str, port=1085):
    try:
        telnetlib.Telnet(ip, port, timeout=5)
        return True
    except (telnetlib.socket.timeout, ConnectionRefusedError):
        return False
    else:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 把ip地址从txt文档入库，并合并到IP池
def proxy_load():
    # 入库前清空目标表
    sql_str = "delete from ip_load"
    exe_connector.run(sql_str)
    load_rst = exe_connector.import_from_txt('./txt/ip.txt', 'ip_load')
    sql_str = "delete from ip_load where id not in (select min(id) from ip_load group by ip)"
    exe_connector.run(sql_str)
    sql_str = "select count(1) from ip_load"
    rst = read_connector.run(sql_str)
    if load_rst[2] == rst[0][0]:
        print("恭喜！数据导入已完成\n文件行数 {0[0]:d}\n入库前记录数 {0[1]:d}\n入库后记录数 {0[2]:d}\n记录数变化 {0[3]:d}\n"
              "记录中无重复项\n".format(load_rst))
    else:
        print("恭喜！数据导入已完成\n文件行数 {0[0]:d}\n入库前记录数 {0[1]:d}\n入库后记录数 {0[2]:d}\n记录数变化 {0[3]:d}\n"
              "删除重复项后记录数 {1:d}\n".format(load_rst, rst[0][0]))
    str_input = input("是否继续合并到ip池？(y/n) ")
    if str_input.lower() == 'y':
        sql_str = "select count(1) from ips"
        pre_cnt = read_connector.run(sql_str)[0][0]
        sql_str_2 = "delete from ips"
        exe_connector.run(sql_str_2)
        sql_str_2 = "insert into ips(ip, created_at) select ip,CURRENT_TIMESTAMP() from ip_load"
        exe_connector.run(sql_str_2)
        aft_cnt = read_connector.run(sql_str)[0][0]
        print("恭喜！数据已合并到ip池\n入库前记录数 {0:d}\n入库后记录数 {1:d}\n记录数变化 {2:d}\n"
              .format(pre_cnt, aft_cnt, aft_cnt - pre_cnt))


# 代理IP池 检查ip状态 检查状态 0:未检查(默认)；1:有效；2:失效
def proxy_check(ip, port):
    try:
        if test_ip(ip, port):
            status = '1'
        else:
            status = '2'
        sql_str = "update ips set check_status='{1:s}',checked_at=CURRENT_TIMESTAMP() " \
                  "where ip='{0:s}'".format(ip, status)
        exe_connector.run(sql_str)
        return status
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 循环检查ip状态
def proxy_cycle_check():
    try:
        sql_str = ("select ip,port,check_status from ips where checked_at<date_add(current_timestamp(),interval - 24 "
                   "hour and check_status in ('0','1') order by created_at desc,checked_at")
        ck_list = read_connector.run(sql_str)
        for row in ck_list:
            check_status = proxy_check(row[0], row[1])
            print("代理 {0[0]:s}: {0[2]:s} -> {1:s}".format(row, check_status))
            time.sleep(3)
        print("批量检查已完成 {0:d}".format(len(ck_list)))
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 设定使用状态 使用状态 0:未使用(默认)；1:临时使用；2:长期使用；3：瞬间使用
def proxy_set_use_status(ip, use_status):
    try:
        if use_status in ('1', '2'):
            sql_str = "update ips set use_status='{1:s}', use_cnt=use_cnt+1, used_at=CURRENT_TIMESTAMP() where " \
                      "ip='{0:s}'".format(ip, use_status)
        elif use_status == '3':
            sql_str = "update ips set use_status='0', use_cnt=use_cnt+1, used_at=CURRENT_TIMESTAMP() where " \
                      "ip='{0:s}'".format(ip)
        else:
            sql_str = "update ips set use_status='{1:s}', used_at=CURRENT_TIMESTAMP() where " \
                      "ip='{0:s}'".format(ip, use_status)
        exe_connector.run(sql_str)
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 代理IP池 修复使用状态
# 1、如果不在clients中的ip不属于长期使用
# 2、使用时长超过1天的不属于临时使用
def proxy_repair_use_status():
    try:
        sql_str = "update ips set use_status='0' where use_status='2' and ip not in (select proxy_ip from clients)"
        exe_connector.run(sql_str)
        sql_str = "update ips set use_status='0' where use_status='1' and " \
                  "used_at<date_add(current_timestamp(),interval - 24 hour)"
        exe_connector.run(sql_str)
    except Exception:
        sys_log.write_log('proxy_repair_use_status', 'a')
        sys_log.write_log(traceback.format_exc(), 'a')


# 获取一个可用IP 使用状态 0:未使用(默认)；1:临时使用；2:长期使用
def proxy_get(use_status='1'):
    try:
        sql_str = "select ip,port from ips where check_status in ('0','1') order by use_status,used_at,use_cnt," \
                  "checked_at desc limit 5"
        rst = read_connector.run(sql_str)
        for row in rst:
            check_status = proxy_check(row[0], row[1])
            if check_status == '1':
                proxy_set_use_status(row[0], use_status)
                return row
            else:
                proxy_set_use_status(row[0], '0')
                time.sleep(2)
        return ()
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')


# 定义公共全局变量
db_conf = ConfFile('./config/db_info.ini')
sys_conf = ConfFile('./config/sys_config.ini')
exe_connector = MySQLDB('parkdb_w')
exe_connector.open()
read_connector = MySQLDB('parkdb_r')
read_connector.open()

# 公共参数池
# chat_user = {}
# 存储所有 WebSocket 消息处理函数的字典，按照模块module_name进行分类，单独加载
ws_handlers = {}


# 字段转义
def explain(type, text, point='to'):
    try:
        to_dic = {}
        # 设定转义内容
        if type == 'UserStatus':
            to_dic = {'1': UserStatus.ONLINE, '2': UserStatus.OFFLINE, '3': UserStatus.RECENTLY,
                      '4': UserStatus.LAST_WEEK, '5': UserStatus.LAST_MONTH, '6': UserStatus.LONG_AGO}
        elif type == 'ChatType':
            to_dic = {'user': ChatType.PRIVATE, 'bot': ChatType.BOT, 'group': ChatType.GROUP,
                      'supergroup': ChatType.SUPERGROUP, 'channel': ChatType.CHANNEL}
        elif type == 'ChatMemberStatus':
            to_dic = {0: ChatMemberStatus.OWNER, 1: ChatMemberStatus.ADMINISTRATOR, 2: ChatMemberStatus.MEMBER,
                      3: ChatMemberStatus.LEFT, 4: ChatMemberStatus.RESTRICTED, 5: ChatMemberStatus.BANNED}
        elif type == 'bool':
            to_dic = {'0': False, '1': True}
        elif type == 'MyclientStatus':
            to_dic = {'00': "待注册", '01': "待初始化", '02': "待养号", '10': "正常", '20': "限制", '30': "禁用",
                      '40': "销号"}
        elif type == 'JIAMI':
            to_dic = {'0': 'O', '1': 'I', '2': 'Z', '3': 'E', '4': 'X', '5': 'S', '6': 'G', '7': 'J', '8': 'B',
                      '9': 'P'}
        # 确定转义方向
        if point == 'to':
            return to_dic.get(text, None)
        elif point == 'from':
            from_dict = {v: k for k, v in to_dic.items()}
            return from_dict.get(text, None)
        else:
            return None
    except Exception:
        sys_log.write_log(traceback.format_exc(), 'a')
        return False


# 参数操作类
class Params:
    def __init__(self):
        self.__sql_str = "select id,name,type,value,note from params where id=%s and name = %s"
        self.__dict = {}

    # 获取参数值，key为(id, name)元组，返回实际参数值
    def __getitem__(self, key):
        if isinstance(key, tuple):
            id, name = key
        else:
            id, name = (key, '')
        v_key = id + '|' + name
        if v_key not in self.__dict:
            rst = read_connector.run(self.__sql_str, [id, name])
            if len(rst) > 0:
                self.__dict[v_key] = self.__get_value(rst[0][2], rst[0][3])
        return self.__dict.get(v_key, None)

    # 设置参数值，并更新数据库
    # key为元组(id, name)，对应数据库的对应字段元组，否则对应id
    # value为元组(type, value, note)，注意，type是字符类型，将会更新数据库，否则只更新字典
    def __setitem__(self, key, value):
        if isinstance(key, tuple):
            key_tup = key
        else:
            key_tup = (key, '')
        if isinstance(value, tuple):
            sql_str = "insert into params(id,name,type,value,note) values('{0[0]:s}','{0[1]:s}','{1[0]:s}','{2:s}'," \
                      "'{1[2]:s}') ON DUPLICATE KEY UPDATE type='{1[0]:s}',value='{2:s}',note='{1[2]:s}'".format(
                list(map(str, key_tup)), list(map(str, value)), self.__set_value(value[1]))
        else:
            sql_str = "update params set value='{1:s}' where id='{0[0]:s}' and name='{0[1]:s}'".format(
                list(map(str, key_tup)), self.__set_value(value))
        exe_connector.run(sql_str)
        rst = read_connector.run(self.__sql_str, key_tup)
        if len(rst) > 0:
            self.__dict[rst[0][0] + '|' + rst[0][1]] = self.__get_value(rst[0][2], rst[0][3])

    # 字符串转换成指定的类型
    def __get_value(self, type, value):
        try:
            if type == 'int':
                return int(value)
            elif type == 'float':
                return float(value)
            elif type == 'bool':
                return value.lower() in ('true', '1', 't')
            elif type == 'datetime':
                return datetime.datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
            elif type == 'decimal':
                return Decimal(value)
            else:
                return value
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 指定类型转换成字符串
    def __set_value(self, value):
        if isinstance(value, bool):
            return 'true' if value else 'false'
        else:
            return str(value)

    # 重新更新字典的值会返回
    def refresh(self, id, name=''):
        v_key = id + '|' + name
        rst = read_connector.run(self.__sql_str, [id, name])
        if len(rst) > 0:
            self.__dict[v_key] = self.__get_value(rst[0][2], rst[0][3])
        return self.__dict.get(v_key, None)


params = Params()


# 对应表chats
class TChats:
    def __init__(self, values):
        (self.id, self.type, self.name, self.outname, self.title, self.username, self.first_name, self.last_name,
         self.status, self.online_date) = values

    def short_outname(self, length) -> str:
        return self.outname if len(self.outname) <= length else self.outname[:length] + '..'

    def to_list(self) -> list:
        return [self.id, self.type, self.name, self.outname, self.title, self.username, self.first_name, self.last_name,
                self.status, self.online_date]


class MyChats:
    def __init__(self):
        self.__sql_id = ("select id,type,name,outname,title,username,first_name,last_name,status,online_date "
                         "from chats where id=%s")
        self.__sql_name = ("select id,type,name,outname,title,username,first_name,last_name,status,online_date "
                           "from chats where name='%s'")
        self.__dict = {}

    def __getitem__(self, id) -> TChats:
        if id not in self.__dict:
            rst = read_connector.run(self.__sql_id, [id])
            if len(rst) > 0:
                self.__dict[id] = TChats(rst[0])
        return self.__dict.get(id, None)

    # 通过name来获取MyChat的信息
    def name(self, name) -> TChats:
        if name not in self.__dict:
            rst = read_connector.run(self.__sql_name, [name])
            if len(rst) > 0:
                self.__dict[name] = TChats(rst[0])
        return self.__dict.get(name, None)

    # 剔除特殊字符
    def correct_char(self, sour_str):
        return ''.join(
            re.findall('[\u4e00-\u9fa5a-zA-Z0-9 （）_()&%#@!$^【】.:~\-/,，。！]+', sour_str, re.S))

    # 更新chat信息
    def update_chat(self, current_chat):
        try:
            # 清空字典
            if len(self.__dict) > 2000:
                self.__dict.clear()
            v_id = current_chat.id
            # 收集User类型的信息
            if type(current_chat) == User:
                v_title = ''
                # v_type
                if current_chat.is_bot is not None and current_chat.is_bot:
                    v_type = "bot"
                else:
                    v_type = "user"
                # v_status
                if current_chat.status is not None:
                    v_status = explain('UserStatus', current_chat.status, 'from')
                else:
                    v_status = '0'
                if current_chat.is_deleted is not None and current_chat.is_deleted:
                    v_status = '7'
                # v_online_date
                if current_chat.last_online_date is not None:
                    v_online_date = current_chat.last_online_date
                elif current_chat.next_offline_date is not None:
                    v_online_date = current_chat.next_offline_date
                else:
                    v_online_date = datetime.datetime.now()
            # 收集Chat类型的信息
            else:
                v_title = self.correct_char('' if current_chat.title is None else current_chat.title)
                v_type = explain('ChatType', current_chat.type, 'from')
                v_status = '0'
                v_online_date = datetime.datetime.strptime('2000-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')
            # 收集共有的变量
            v_username = self.correct_char('' if current_chat.username is None else current_chat.username)
            v_first_name = self.correct_char('' if current_chat.first_name is None else current_chat.first_name)
            v_last_name = self.correct_char('' if current_chat.last_name is None else current_chat.last_name)
            v_outname = v_title + v_first_name + v_last_name
            v_outname = '_' if v_outname == '' else v_outname
            # v_status
            if current_chat.is_restricted is not None and current_chat.is_restricted:
                v_status = '8'
            elif current_chat.is_scam is not None and current_chat.is_scam:
                v_status = '9'
            # 检查内存中id是否已存在
            if v_id in self.__dict:
                # 同步有更新的信息
                if ((v_type, v_outname, v_title, v_username, v_first_name, v_last_name, v_status,
                     v_online_date.strftime("%Y-%m-%d")) !=
                        (self.__dict[v_id].type, self.__dict[v_id].outname, self.__dict[v_id].title,
                         self.__dict[v_id].username, self.__dict[v_id].first_name, self.__dict[v_id].last_name,
                         self.__dict[v_id].status, self.__dict[v_id].online_date.strftime("%Y-%m-%d"))):
                    # chat不更新user信息
                    if type(current_chat) == Chat and self.__dict[v_id].type == 'user':
                        return
                    sql_str = "update chats set type='{1:s}', outname='{2:s}', title='{3:s}', username='{4:s}', " \
                              "first_name='{5:s}', last_name='{6:s}', status=if('{7:s}'<>'0','{7:s}',status), " \
                              "online_date=if(online_date<'{8:%Y-%m-%d %X}', '{8:%Y-%m-%d %X}', online_date) where " \
                              "id = {0:d}" \
                        .format(v_id, v_type, v_outname, v_title, v_username, v_first_name, v_last_name, v_status,
                                v_online_date)
                    exe_connector.run(sql_str)
                    self.__getitem__(v_id)
            else:
                sql_str = f"select id from chats where name = '{v_outname}'"
                rst = read_connector.run(sql_str)
                if len(rst) > 0 and rst[0][0] != v_id:
                    v_name = v_outname + '_' + str(v_id)
                else:
                    v_name = v_outname
                sql_str = "insert into chats(id,type,name,outname,title,username,first_name,last_name,status," \
                          "online_date) values({0:d},'{1:s}','{2:s}','{3:s}','{4:s}','{5:s}','{6:s}','{7:s}','{8:s}'," \
                          "'{9:%Y-%m-%d %X}') on duplicate key update type='{1:s}',outname='{3:s}',title='{4:s}'," \
                          "username='{5:s}',first_name='{6:s}',last_name='{7:s}',status=if('{8:s}'<>'0','{8:s}'," \
                          "status),online_date=if(online_date<'{9:%Y-%m-%d %X}', '{9:%Y-%m-%d %X}', " \
                          "online_date)".format(v_id, v_type, v_name, v_outname, v_title, v_username, v_first_name,
                                                v_last_name, v_status, v_online_date)
                exe_connector.run(sql_str)
                self.__getitem__(v_id)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 更新message中的chat信息
    def update_message(self, message):
        try:
            # 为了更新user的状态，User类型需要优先获取
            if message.from_user is not None:
                self.update_chat(message.from_user)
            if message.forward_from is not None:
                self.update_chat(message.forward_from)
            if message.new_chat_members is not None:
                for new_chat_member in message.new_chat_members:
                    self.update_chat(new_chat_member)
            if message.left_chat_member is not None:
                self.update_chat(message.left_chat_member)
            if message.sender_chat is not None:
                self.update_chat(message.sender_chat)
            if message.chat is not None:
                self.update_chat(message.chat)
            if message.forward_from_chat is not None:
                self.update_chat(message.forward_from_chat)
            if message.via_bot is not None:
                self.update_chat(message.via_bot)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 更新callback中的chat信息
    def update_callback(self, callback):
        try:
            # 为了更新user的状态，User类型需要优先获取
            if callback.from_user is not None:
                self.update_chat(callback.from_user)
            if callback.message is not None:
                self.update_message(callback.message)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 更新ChatMember中的chat信息
    def update_chatmember(self, chatmember):
        try:
            # 为了更新user的状态，User类型需要优先获取
            if chatmember.user is not None:
                self.update_chat(chatmember.user)
            if chatmember.chat is not None:
                self.update_chat(chatmember.chat)
            if chatmember.invited_by is not None:
                self.update_chat(chatmember.invited_by)
            if chatmember.promoted_by is not None:
                self.update_chat(chatmember.promoted_by)
            if chatmember.restricted_by is not None:
                self.update_chat(chatmember.restricted_by)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')


myChats = MyChats()


class TClients:
    def __init__(self, values):
        (self.username, self.is_bot, self.proxy_ip, self.token, self.api_id, self.api_hash, self.note) = values


class Clients:
    def __init__(self, username_list):
        try:
            client_str = "','".join(username_list)
            sql_str = f"select username,is_bot,proxy_ip,token,api_id,api_hash,note from clients where username in ('" \
                      f"{client_str}')"
            rst = read_connector.run(sql_str)
            self.__dict = {}
            proxy = {}
            for row in rst:
                tclient = TClients(row)
                # 确认代理信息
                if tclient.proxy_ip != '':
                    if test_ip(tclient.proxy_ip):
                        # 若历史代理可用，则继续使用
                        proxy = dict(scheme="socks5", hostname=tclient.proxy_ip, port=1085)
                        sys_log.print_log(f"使用原代理登录tg: {tclient.username}->{tclient.proxy_ip}")
                    else:
                        # 若历史代理不可用，则重新申请新代理
                        new_proxy = proxy_get('2')
                        if len(new_proxy) > 0:
                            sql_str = f"update clients set proxy_ip='{new_proxy[0]}' where username='" \
                                      f"{tclient.username}'"
                            exe_connector.run(sql_str)
                            proxy = dict(scheme="socks5", hostname=new_proxy[0], port=1085)
                            sys_log.print_log(f"使用新代理登录tg: {tclient.username}->{new_proxy[0]}")
                        else:
                            sql_str = f"update clients set proxy_ip='' where username='{tclient.username}'"
                            exe_connector.run(sql_str)
                            sys_log.print_log(f"无可用代理，直接使用服务器ip: {tclient.username}")
                else:
                    sys_log.print_log(f"没有设置代理，直接使用服务器ip: {tclient.username}")
                # 修复代理表中的使用状态
                proxy_repair_use_status()
                # 配置客户端构造函数
                plus_read = sys_conf.read('plugins', tclient.username)
                if plus_read is None:
                    # 无更新客户端
                    if tclient.is_bot == 1 and len(proxy) == 0:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               bot_token=tclient.token, parse_mode=ParseMode.HTML,
                                                               no_updates=True)
                    elif tclient.is_bot == 1 and len(proxy) > 0:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               proxy=proxy, bot_token=tclient.token,
                                                               parse_mode=ParseMode.HTML, no_updates=True)
                    elif tclient.is_bot == 0 and len(proxy) == 0:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               parse_mode=ParseMode.HTML, no_updates=True)
                    else:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               proxy=proxy, parse_mode=ParseMode.HTML,
                                                               no_updates=True)
                else:
                    # 更新客户端
                    plus = plus_read.split('|')
                    if len(plus) == 1:
                        plugins = dict(root="plugins", include=plus[0].split(','))
                    else:
                        plugins = dict(root="plugins", include=plus[0].split(','), exclude=plus[1].split(','))
                    if tclient.is_bot == 1 and len(proxy) == 0:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               bot_token=tclient.token, plugins=plugins,
                                                               parse_mode=ParseMode.HTML)
                    elif tclient.is_bot == 1 and len(proxy) > 0:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               proxy=proxy, bot_token=tclient.token,
                                                               plugins=plugins, parse_mode=ParseMode.HTML)
                    elif tclient.is_bot == 0 and len(proxy) == 0:
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               plugins=plugins, parse_mode=ParseMode.HTML)
                    else:
                        # self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                        #                                        tclient.api_hash, proxy=proxy, plugins=plugins,
                        #                                        parse_mode=ParseMode.HTML)
                        self.__dict[tclient.username] = Client(f"sessions/{tclient.username}", tclient.api_id,
                                                               tclient.api_hash, app_version='KobeePro',
                                                               device_model='PC', system_version='Linux',
                                                               proxy=proxy, plugins=plugins,
                                                               parse_mode=ParseMode.HTML)
                proxy.clear()
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    def __getitem__(self, username) -> Client:
        return self.__dict[username]

    def get_username(self, client: Client) -> str:
        for clt in self.__dict:
            if self.__dict[clt] == client:
                return clt
        return ''


clients = Clients(client_list)


class TChatUser:
    def __init__(self, values):
        (self.chat_id, self.chat_name, self.user_id, self.user_name, self.status, self.active_at, self.joined_at) \
            = values


class ChatUser:
    def __init__(self):
        self.__sql_str = "select chat_id,chat_name,user_id,user_name,status,active_at,joined_at from chat_user " \
                         "where chat_id=%s and user_id=%s"
        self.__dict = {}

    # 获取chat_user的信息
    def __getitem__(self, args) -> TChatUser | None:
        (chat_id, user_id) = args
        key = str(chat_id) + '|' + str(user_id)
        if key not in self.__dict:
            rst = read_connector.run(self.__sql_str, [chat_id, user_id])
            if len(rst) > 0:
                self.__dict[key] = TChatUser(rst[0])
        return self.__dict.get(key, None)

    # 把新的群成员关系入库
    def update_table(self, member: ChatMember, chat_id, record_user_status):
        try:
            myChats.update_chatmember(member)
            user_id = member.user.id
            status = explain('ChatMemberStatus', member.status, 'from')
            if str(user_id) + '|' + str(status) not in record_user_status:
                if member.joined_date is not None:
                    joined_at = member.joined_date
                else:
                    joined_at = datetime.datetime.now()
                sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,joined_at) values({0[" \
                          "0]:d},'{0[2]:s}',{1[0]:d},'{1[2]:s}',{2:d},'{3:%Y-%m-%d %X}') on duplicate key update " \
                          "chat_name='{0[2]:s}',user_name='{1[2]:s}',status={2:d},joined_at=least(joined_at," \
                          "'{3:%Y-%m-%d %X}')".format(myChats[chat_id].to_list(), myChats[user_id].to_list(), status,
                                                      joined_at)
                exe_connector.run(sql_str)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 更新群成员信息
    async def update_chat_user(self, client_name, chat_id, no_force=True):
        try:
            chat_name = myChats[chat_id].name
            sys_log.write_log(f"检查是否需要更新群: {client_name}->{chat_name}", 'a')
            # 对于非管理员的Channel，不更新
            if myChats[chat_id].type == "channel":
                me = await clients[client_name].get_chat_member(chat_id, "me")
                await asyncio.sleep(3)
                if me.status not in (ChatMemberStatus.OWNER, ChatMemberStatus.ADMINISTRATOR):
                    sys_log.write_log(f"非管理员的频道不更新: {client_name}->{chat_name}", 'a')
                    return
            # 判断是否到达更新时间
            sql_str = f"select all_cnt,record_cnt, (case when time_to_sec(timediff(now(), updated_at))>(" \
                      f"all_cnt/3000+2)*86400 then 1 else 0 end) from chat_user_info where chat_id = {chat_id}"
            rst = read_connector.run(sql_str)
            if len(rst) != 0 and rst[0][2] == 0 and no_force:
                sys_log.write_log(f"更新周期内不更新: {client_name}->{chat_name}", 'a')
                return
            v_all_cnt = await clients[client_name].get_chat_members_count(chat_id)
            await asyncio.sleep(3)
            if v_all_cnt == 0:
                sys_log.write_log(f"目标群无成员不更新: {client_name}->{chat_name}", 'a')
                return
            sql_str = f"select count(1) from chat_user where chat_id = {chat_id}"
            v_record_cnt = read_connector.run(sql_str)[0][0]
            if len(rst) != 0 and rst[0][1] == v_record_cnt and rst[0][0] == v_all_cnt and no_force:
                sys_log.write_log(f"成员数无变动不更新: {client_name}->{chat_name} 总人数：{v_all_cnt}", 'a')
                return
            sys_log.write_log(f"开始更新群: {client_name}->{chat_name} 官方总人数：{v_all_cnt}", 'a')
            # 提取已记录成员信息
            sql_str = f"select concat(user_id,'|',status) from chat_user where chat_id={chat_id}"
            rst = read_connector.run(sql_str)
            record_user_status = set(chain.from_iterable(rst))
            real_all_cnt = 0
            if len(record_user_status) > 9999 or (myChats[chat_id].type == 'channel' and len(record_user_status) > 199):
                # 已经超限的群，只取最近在线的成员
                async for member in clients[client_name].get_chat_members(chat_id, filter=ChatMembersFilter.RECENT):
                    self.update_table(member, chat_id, record_user_status)
                    real_all_cnt += 1
            else:
                # 未超限的群，取全部成员
                async for member in clients[client_name].get_chat_members(chat_id):
                    self.update_table(member, chat_id, record_user_status)
                    real_all_cnt += 1
            # 更新chat_user_info信息
            record_cnt = chatUserInfo.update(chat_id, real_all_cnt, v_all_cnt)
            sys_log.write_log(f"群信息更新完毕: {client_name}->{chat_name} 数据表有效成员数：{record_cnt} 本轮轮询成员数："
                              f"{real_all_cnt} 官方总人数：{v_all_cnt}", 'a')
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 轮询各个client的群户信息
    async def poll_client(self, ck_client):
        try:
            for client_name in ck_client:
                sys_log.write_log("开始更新用户 {0:s} 的所有群信息".format(client_name), 'a')
                # bot 没有 get_dialogs 方法
                if 'bot' in client_name:
                    rst = read_connector.run("select id from chats where username = %s", [client_name])
                    rst = read_connector.run("select distinct chat_id from chat_user where user_id=%s", [rst[0][0]])
                    for row in rst:
                        await self.update_chat_user(client_name, row[0])
                else:
                    async for dialog in clients[client_name].get_dialogs():
                        if dialog.chat.type in (ChatType.GROUP, ChatType.SUPERGROUP, ChatType.CHANNEL):
                            myChats.update_chat(dialog.chat)
                            await self.update_chat_user(client_name, dialog.chat.id)
                sys_log.write_log("已更新用户 {0:s} 的所有群信息".format(client_name), 'a')
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 更新chat_user的活跃成员 更新频率为6小时
    # status: 0：creator；1：administrator；2：member；3：left；4：restricted；5：banned；8：unKnow；
    def active(self, chat_id, user_id):
        try:
            if len(self.__dict) > 2000:
                self.__dict.clear()
            chat_user = self.__getitem__((chat_id, user_id))
            if chat_user is None or time.time() - chat_user.active_at.timestamp() > 21600:
                sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,active_at,joined_at) values({0[" \
                          "0]:d},'{0[2]:s}',{1[0]:d},'{1[2]:s}',now(),now()) on duplicate key update chat_name='{0[" \
                          "2]:s}',user_name='{1[2]:s}',active_at=now()".format(myChats[chat_id].to_list(),
                                                                               myChats[user_id].to_list())
                exe_connector.run(sql_str)
                rst = read_connector.run(self.__sql_str, [chat_id, user_id])
                self.__dict[str(chat_id) + '|' + str(user_id)] = TChatUser(rst[0])
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 加群动作
    def join(self, chat_id, user_id):
        try:
            sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,active_at,joined_at) " \
                      "values({0[0]:d},'{0[2]:s}',{1[0]:d},'{1[2]:s}',2,now(),now()) on duplicate key update " \
                      "chat_name='{0[2]:s}',user_name='{1[2]:s}',status=2,active_at=now(),joined_at=now()" \
                .format(myChats[chat_id].to_list(), myChats[user_id].to_list())
            exe_connector.run(sql_str)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')

    # 退群动作
    def leave(self, chat_id, user_id):
        try:
            sql_str = "insert into chat_user(chat_id,chat_name,user_id,user_name,status,active_at) values({0[" \
                      "0]:d},'{0[2]:s}',{1[0]:d},'{1[2]:s}',3,now()) on duplicate key update chat_name='{0[" \
                      "2]:s}',user_name='{1[2]:s}',status=3,active_at=now()".format(myChats[chat_id].to_list(),
                                                                                    myChats[user_id].to_list())
            exe_connector.run(sql_str)
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')


chatUser = ChatUser()


class TChatUserInfo:
    def __init__(self, values):
        (self.chat_id, self.chat_name, self.record_cnt, self.real_all_cnt, self.all_cnt, self.updated_at) = values


class ChatUserInfo:
    def __init__(self):
        self.__sql_str = "select chat_id,chat_name,record_cnt,real_all_cnt,all_cnt,updated_at from chat_user_info " \
                         "where chat_id=%s"

    def update(self, chat_id, real_all_cnt=0, all_cnt=0):
        try:
            sql_str = f"select count(1) from chat_user where chat_id={chat_id}"
            rst = read_connector.run(sql_str)
            sql_str = "insert into chat_user_info(chat_id,chat_name,record_cnt,real_all_cnt,all_cnt,updated_at) " \
                      "values({0[0]:d},'{0[2]:s}',{1:d},{2:d},{3:d},now()) on duplicate key update chat_name='{0[" \
                      "2]:s}',record_cnt={1:d},real_all_cnt={2:d},all_cnt={3:d},updated_at=now()".format(
                myChats[chat_id].to_list(), rst[0][0], real_all_cnt, all_cnt)
            exe_connector.run(sql_str)
            return rst[0][0]
        except Exception:
            sys_log.write_log(traceback.format_exc(), 'a')


chatUserInfo = ChatUserInfo()


class TMonits:
    def __init__(self, values):
        (self.id, self.name, self.bot_name, self.chat_list, self.freq, self._init_time, self._run_time,
         self._status) = values

    @property
    def init_time(self):
        return self._init_time

    @init_time.setter
    def init_time(self, value):
        update_sql = f"UPDATE monits SET init_time=%s WHERE id=%s"
        exe_connector.run(update_sql, [value, self.id])
        self._init_time = value

    @property
    def run_time(self):
        return self._run_time

    @run_time.setter
    def run_time(self, value):
        update_sql = f"UPDATE monits SET run_time=%s WHERE id=%s"
        exe_connector.run(update_sql, [value, self.id])
        self._run_time = value

    @property
    def status(self):
        return self._status

    @status.setter
    def status(self, value):
        update_sql = f"UPDATE monits SET status=%s WHERE id=%s"
        exe_connector.run(update_sql, [value, self.id])
        self._status = value



class Monits:
    def __init__(self):
        self.__sql_str = "SELECT id,name,bot_name,chat_list,freq,init_time,run_time,status from monits where id=%s"
        self.__dict = {}

    def __getitem__(self, id) -> TMonits:
        if id not in self.__dict:
            rst = read_connector.run(self.__sql_str, [id])
            if len(rst) > 0:
                self.__dict[id] = TMonits(rst[0])
        return self.__dict.get(id, None)

    # 修改单个字段 monits[id] = ('字段名', var)
    def __setitem__(self, id, value):
        properties = ["status"]
        if len(value) == 2 and value[0] in properties:
            # 更新数据库
            update_sql = f"UPDATE monits SET {value[0]}=%s WHERE id=%s"
            exe_connector.run(update_sql, [value[1], id])
            # 更新字典
            rst = read_connector.run(self.__sql_str, [id])
            self.__dict[id] = TMonits(rst[0])
        else:
            raise ValueError("monits赋值参数必须是2个参数，且只能修改status")


monits = Monits()

if __name__ == "__main__":
    sql_str = "SELECT * FROM clients WHERE id = %s or 1=1"
    params = [6981052546]
    print(read_connector.run(sql_str, params))
