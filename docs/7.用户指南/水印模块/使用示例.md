# 水印模块使用示例

## 🚀 基础使用示例

### 最简单的使用
```python
from shared.classes2 import Watermark

async def basic_example():
    watermark = Watermark()
    
    # 图片水印（默认覆盖原文件）
    result = await watermark.add_image_watermark("photo.jpg", "我的水印")
    if result:
        print(f"图片处理成功: {result}")
    
    # 视频水印（默认覆盖原文件）
    result = await watermark.add_video_watermark("video.mp4", "视频水印")
    if result:
        print(f"视频处理成功: {result}")
```

### 保存到新文件
```python
async def save_to_new_file():
    watermark = Watermark()
    
    # 保存图片到新文件
    result = await watermark.add_image_watermark(
        "original.jpg", 
        "水印文字", 
        "watermarked.jpg"
    )
    
    # 保存视频到新文件
    result = await watermark.add_video_watermark(
        "original.mp4", 
        "视频水印", 
        "watermarked.mp4"
    )
```

## 🎨 自定义配置示例

### 使用配置类
```python
from shared.classes2 import Watermark, WatermarkConfig

async def custom_config_example():
    # 创建自定义配置
    config = WatermarkConfig(
        pic_color="#FF6B35",      # 橙色水印
        pic_size=32,              # 较大字体
        pic_opacity=0.8,          # 较高透明度
        pic_angle=45,             # 45度角
        video_position="top-right", # 右上角
        video_color="#FFFFFF"     # 白色视频水印
    )
    
    watermark = Watermark(config=config)
    
    result = await watermark.add_image_watermark("photo.jpg", "自定义水印")
    print(f"自定义配置结果: {result}")
```

### 方法级参数覆盖
```python
async def method_override_example():
    watermark = Watermark()
    
    # 使用方法参数覆盖默认配置
    result = await watermark.add_image_watermark(
        "photo.jpg",
        "红色大水印",
        color="#FF0000",          # 红色
        size=40,                  # 大字体
        opacity=0.9,              # 高透明度
        angle=30,                 # 30度角
        spacing=300               # 大间距
    )
    
    # 视频水印参数覆盖
    result = await watermark.add_video_watermark(
        "video.mp4",
        "左上角水印",
        position="top-left",      # 左上角
        color="#FFFF00",          # 黄色
        margin=(50, 50)           # 大边距
    )
```

## 📱 Telegram 机器人集成示例

### 图片消息处理
```python
from pyrogram import Client, filters
from shared.classes2 import Watermark

app = Client("my_bot")
watermark = Watermark()

@app.on_message(filters.photo)
async def handle_photo(client, message):
    try:
        # 下载图片
        file_path = await message.download()
        
        # 添加水印
        result = await watermark.add_image_watermark(
            file_path,
            "Tg: @my_channel",
            color="#FF7256",
            size=18,
            opacity=0.6,
            angle=28
        )
        
        if result:
            # 发送处理后的图片
            await message.reply_photo(result)
            print(f"图片水印处理成功: {result}")
        else:
            await message.reply("图片处理失败")
            
    except Exception as e:
        print(f"处理图片时出错: {e}")
```

### 视频消息处理
```python
@app.on_message(filters.video)
async def handle_video(client, message):
    try:
        # 下载视频
        file_path = await message.download()
        
        # 添加水印
        result = await watermark.add_video_watermark(
            file_path,
            "欢乐园 tg: @happyzone168",
            position="bottom-right",
            color="#FFFFFF",
            margin=(20, 20)
        )
        
        if result:
            # 发送处理后的视频
            await message.reply_video(result)
            print(f"视频水印处理成功: {result}")
        else:
            await message.reply("视频处理失败")
            
    except Exception as e:
        print(f"处理视频时出错: {e}")
```

## 🔄 批量处理示例

### 批量处理文件夹
```python
import os
from pathlib import Path

async def batch_process_folder():
    watermark = Watermark()
    folder_path = "media_files"
    
    # 支持的文件扩展名
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.flv'}
    
    success_count = 0
    total_count = 0
    
    for file_path in Path(folder_path).iterdir():
        if file_path.is_file():
            total_count += 1
            file_ext = file_path.suffix.lower()
            
            try:
                if file_ext in image_extensions:
                    result = await watermark.add_image_watermark(
                        str(file_path), 
                        "批量水印"
                    )
                elif file_ext in video_extensions:
                    result = await watermark.add_video_watermark(
                        str(file_path), 
                        "批量水印"
                    )
                else:
                    print(f"跳过不支持的格式: {file_path}")
                    continue
                
                if result:
                    success_count += 1
                    print(f"✅ {file_path.name}")
                else:
                    print(f"❌ {file_path.name}")
                    
            except Exception as e:
                print(f"❌ {file_path.name}: {e}")
    
    print(f"批量处理完成: {success_count}/{total_count} 成功")
```

### 并发批量处理
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def concurrent_batch_process():
    watermark = Watermark()
    files = ["img1.jpg", "img2.png", "video1.mp4", "video2.avi"]
    
    async def process_single_file(file_path):
        try:
            if file_path.endswith(('.jpg', '.png', '.jpeg', '.bmp')):
                result = await watermark.add_image_watermark(file_path, "并发水印")
            else:
                result = await watermark.add_video_watermark(file_path, "并发水印")
            
            return f"✅ {file_path}" if result else f"❌ {file_path}"
        except Exception as e:
            return f"❌ {file_path}: {e}"
    
    # 并发处理
    tasks = [process_single_file(file_path) for file_path in files]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    for result in results:
        print(result)
```

## 🎯 不同场景的配置示例

### 高质量配置（适合重要图片）
```python
async def high_quality_example():
    config = WatermarkConfig(
        pic_size=36,              # 大字体
        pic_opacity=0.9,          # 高透明度
        pic_shadow=True,          # 启用阴影
        image_quality=98,         # 高图片质量
        video_crf=15,             # 高视频质量
        video_preset="slower"     # 慢速高质量编码
    )
    
    watermark = Watermark(config=config)
    
    result = await watermark.add_image_watermark(
        "important_photo.jpg", 
        "高质量水印"
    )
```

### 快速处理配置（适合批量处理）
```python
async def fast_processing_example():
    config = WatermarkConfig(
        pic_size=20,              # 小字体
        pic_opacity=0.5,          # 低透明度
        pic_shadow=False,         # 禁用阴影
        image_quality=80,         # 中等图片质量
        video_crf=28,             # 中等视频质量
        video_preset="ultrafast"  # 超快速编码
    )
    
    watermark = Watermark(config=config)
    
    result = await watermark.add_video_watermark(
        "batch_video.mp4", 
        "快速水印"
    )
```

### 社交媒体配置（适合分享）
```python
async def social_media_example():
    config = WatermarkConfig(
        pic_color="#FF6B35",      # 醒目的橙色
        pic_size=24,              # 中等字体
        pic_angle=0,              # 水平文字
        pic_opacity=0.7,          # 适中透明度
        video_position="bottom-right",
        video_color="#FFFFFF",    # 白色视频水印
        video_margin=(15, 15)     # 小边距
    )
    
    watermark = Watermark(config=config)
    
    # 为社交媒体添加品牌水印
    result = await watermark.add_image_watermark(
        "social_post.jpg", 
        "@my_brand"
    )
```

## 🔧 错误处理和调试示例

### 完整的错误处理
```python
async def robust_processing_example():
    watermark = Watermark()
    
    files_to_process = [
        ("existing_image.jpg", "图片水印"),
        ("nonexistent.jpg", "测试水印"),
        ("corrupted_video.mp4", "视频水印")
    ]
    
    for file_path, watermark_text in files_to_process:
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"⚠️  文件不存在: {file_path}")
                continue
            
            # 根据文件类型选择处理方法
            if file_path.lower().endswith(('.jpg', '.png', '.jpeg')):
                result = await watermark.add_image_watermark(file_path, watermark_text)
            else:
                result = await watermark.add_video_watermark(file_path, watermark_text)
            
            # 检查处理结果
            if result:
                print(f"✅ 处理成功: {file_path} -> {result}")
                
                # 验证输出文件
                if os.path.exists(result):
                    file_size = os.path.getsize(result)
                    print(f"   输出文件大小: {file_size / 1024 / 1024:.2f} MB")
                else:
                    print(f"⚠️  输出文件不存在: {result}")
            else:
                print(f"❌ 处理失败: {file_path}")
                print("   请检查 sys_log.error_log 获取详细错误信息")
                
        except Exception as e:
            print(f"❌ 处理 {file_path} 时发生异常: {e}")
```

### 性能监控示例
```python
import time

async def performance_monitoring_example():
    watermark = Watermark()
    
    files = ["test1.jpg", "test2.mp4", "test3.png"]
    
    for file_path in files:
        start_time = time.time()
        
        if file_path.endswith(('.jpg', '.png', '.jpeg')):
            result = await watermark.add_image_watermark(file_path, "性能测试")
        else:
            result = await watermark.add_video_watermark(file_path, "性能测试")
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result:
            print(f"✅ {file_path}: {duration:.2f}秒")
        else:
            print(f"❌ {file_path}: 处理失败")
```

## 📊 实际项目集成示例

### Flask Web 应用集成
```python
from flask import Flask, request, jsonify
from shared.classes2 import Watermark
import asyncio

app = Flask(__name__)
watermark = Watermark()

@app.route('/add_watermark', methods=['POST'])
def add_watermark_endpoint():
    try:
        file_path = request.json.get('file_path')
        watermark_text = request.json.get('watermark_text', '默认水印')
        
        # 在新的事件循环中运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            watermark.add_image_watermark(file_path, watermark_text)
        )
        
        loop.close()
        
        if result:
            return jsonify({
                'success': True, 
                'output_file': result
            })
        else:
            return jsonify({
                'success': False, 
                'error': '处理失败'
            }), 400
            
    except Exception as e:
        return jsonify({
            'success': False, 
            'error': str(e)
        }), 500
```

---

**提示：**
1. 所有示例都需要在异步环境中运行
2. 建议在生产环境中添加适当的错误处理和日志记录
3. 大文件处理时注意设置合适的超时时间
4. 批量处理时考虑内存使用和并发限制
