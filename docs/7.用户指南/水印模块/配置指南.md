# 水印模块配置指南

## 🎯 配置概述

水印模块提供了丰富的配置选项，支持三个层次的配置：
1. **默认配置** - 类定义中的默认值
2. **实例配置** - 创建 Watermark 实例时的配置
3. **方法配置** - 调用方法时的参数配置

**配置优先级**：方法配置 > 实例配置 > 默认配置

## 🖼️ 图片水印配置详解

### 基础外观配置

#### pic_color - 水印颜色
```python
# 支持的颜色格式
config = WatermarkConfig(
    pic_color="#FF0000"    # 十六进制红色
    # pic_color="#00FF00"  # 十六进制绿色
    # pic_color="#0000FF"  # 十六进制蓝色
    # pic_color="#FFFFFF"  # 白色
    # pic_color="#000000"  # 黑色
)

# 常用颜色推荐
COLORS = {
    "红色": "#FF0000",
    "绿色": "#00FF00", 
    "蓝色": "#0000FF",
    "黄色": "#FFFF00",
    "橙色": "#FF6B35",
    "紫色": "#8A2BE2",
    "白色": "#FFFFFF",
    "黑色": "#000000",
    "灰色": "#808080"
}
```

#### pic_size - 字体大小
```python
# 不同场景的字体大小建议
SIZE_PRESETS = {
    "小图标": 12,      # 适合小图片或不显眼的水印
    "标准": 24,        # 默认大小，适合大多数情况
    "醒目": 36,        # 适合需要突出的水印
    "标题": 48,        # 适合作为标题的水印
    "巨大": 72         # 适合大型图片或海报
}

config = WatermarkConfig(pic_size=24)
```

#### pic_opacity - 透明度
```python
# 透明度效果说明
OPACITY_LEVELS = {
    0.1: "几乎透明，仅作标记",
    0.3: "很透明，不影响观看",
    0.5: "半透明，平衡效果",
    0.7: "较明显，清晰可见",
    0.9: "很明显，强烈存在感",
    1.0: "完全不透明"
}

config = WatermarkConfig(pic_opacity=0.6)
```

### 布局和位置配置

#### pic_angle - 旋转角度
```python
# 常用角度效果
ANGLE_EFFECTS = {
    0: "水平文字，正常阅读",
    15: "轻微倾斜，动感效果",
    30: "明显倾斜，设计感强",
    45: "对角线，经典水印角度",
    90: "垂直文字，竖排效果",
    -30: "反向倾斜，特殊效果"
}

config = WatermarkConfig(pic_angle=30)
```

#### pic_spacing - 水印间距
```python
# 间距设置指南
SPACING_GUIDE = {
    "密集": 100,       # 水印密集分布
    "标准": 200,       # 默认间距
    "稀疏": 300,       # 水印稀疏分布
    "极稀": 500        # 很少的水印
}

# 根据图片大小动态调整
def calculate_spacing(image_width):
    return max(100, image_width // 6)

config = WatermarkConfig(pic_spacing=200)
```

### 高级效果配置

#### pic_shadow - 阴影效果
```python
# 阴影配置组合
SHADOW_PRESETS = {
    "无阴影": {
        "pic_shadow": False
    },
    "轻微阴影": {
        "pic_shadow": True,
        "pic_shadow_color": "#000000",
        "pic_shadow_offset": (1, 1)
    },
    "标准阴影": {
        "pic_shadow": True,
        "pic_shadow_color": "#000000", 
        "pic_shadow_offset": (2, 2)
    },
    "强烈阴影": {
        "pic_shadow": True,
        "pic_shadow_color": "#000000",
        "pic_shadow_offset": (4, 4)
    }
}

config = WatermarkConfig(
    pic_shadow=True,
    pic_shadow_color="#000000",
    pic_shadow_offset=(2, 2)
)
```

#### pic_font_path - 自定义字体
```python
# 系统字体路径示例
SYSTEM_FONTS = {
    "Linux": {
        "默认": "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        "中文": "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "等宽": "/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf"
    },
    "Windows": {
        "默认": "C:/Windows/Fonts/arial.ttf",
        "中文": "C:/Windows/Fonts/msyh.ttc",
        "等宽": "C:/Windows/Fonts/consola.ttf"
    }
}

config = WatermarkConfig(
    pic_font_path="/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
)
```

## 🎬 视频水印配置详解

### 位置配置

#### video_position - 水印位置
```python
# 位置效果图解
POSITION_GUIDE = {
    "top-left": "左上角 - 适合logo",
    "top-right": "右上角 - 适合频道标识", 
    "bottom-left": "左下角 - 适合版权信息",
    "bottom-right": "右下角 - 最常用位置",
    "center": "居中 - 适合重要声明"
}

# 位置选择建议
POSITION_RECOMMENDATIONS = {
    "品牌logo": "top-left",
    "频道标识": "top-right", 
    "版权信息": "bottom-left",
    "一般水印": "bottom-right",
    "重要声明": "center"
}

config = WatermarkConfig(video_position="bottom-right")
```

#### video_margin - 边距设置
```python
# 边距设置指南
MARGIN_PRESETS = {
    "贴边": (5, 5),        # 几乎贴边
    "紧凑": (10, 10),      # 紧凑布局
    "标准": (20, 20),      # 默认边距
    "宽松": (40, 40),      # 宽松布局
    "很宽": (60, 60)       # 很大边距
}

# 根据视频分辨率调整边距
def calculate_margin(video_width, video_height):
    base_margin = min(video_width, video_height) // 50
    return (base_margin, base_margin)

config = WatermarkConfig(video_margin=(20, 20))
```

### 外观配置

#### video_color 和 video_size
```python
# 视频水印颜色建议
VIDEO_COLOR_GUIDE = {
    "白色": "#FFFFFF",     # 适合深色背景
    "黑色": "#000000",     # 适合浅色背景
    "黄色": "#FFFF00",     # 醒目，适合警告
    "红色": "#FF0000",     # 强烈，适合重要信息
    "半透明白": "#FFFFFF@0.8"  # 带透明度的白色
}

# 视频字体大小建议
VIDEO_SIZE_GUIDE = {
    "720p": 16,    # 适合720p视频
    "1080p": 24,   # 适合1080p视频
    "1440p": 32,   # 适合1440p视频
    "4K": 48       # 适合4K视频
}

config = WatermarkConfig(
    video_color="#FFFFFF",
    video_size=24
)
```

## ⚙️ 输出质量配置

### 图片质量配置

#### image_quality - JPEG质量
```python
# 质量等级说明
QUALITY_LEVELS = {
    60: "低质量 - 文件小，适合网络传输",
    75: "中等质量 - 平衡大小和质量",
    85: "良好质量 - 适合一般使用",
    95: "高质量 - 默认设置，推荐",
    98: "极高质量 - 适合重要图片",
    100: "无损质量 - 文件最大"
}

# 使用场景建议
USAGE_QUALITY = {
    "网络分享": 75,
    "社交媒体": 85,
    "一般使用": 95,
    "专业用途": 98,
    "存档保存": 100
}

config = WatermarkConfig(image_quality=95)
```

### 视频质量配置

#### video_crf - 视频质量参数
```python
# CRF值说明（数值越小质量越高）
CRF_GUIDE = {
    18: "极高质量 - 几乎无损，文件很大",
    20: "很高质量 - 专业级别",
    23: "高质量 - 默认推荐",
    26: "良好质量 - 平衡选择", 
    28: "中等质量 - 适合网络",
    30: "较低质量 - 文件较小",
    35: "低质量 - 快速处理"
}

# 使用场景建议
CRF_USAGE = {
    "专业制作": 18,
    "高质量分享": 20,
    "一般使用": 23,
    "网络传输": 26,
    "快速处理": 28,
    "存储节省": 30
}

config = WatermarkConfig(video_crf=23)
```

#### video_preset - 编码预设
```python
# 预设说明
PRESET_GUIDE = {
    "ultrafast": "最快速度，文件最大",
    "superfast": "很快速度，文件很大", 
    "veryfast": "快速度，文件大",
    "faster": "较快速度，文件较大",
    "fast": "中等速度，平衡选择",
    "medium": "默认速度，推荐设置",
    "slow": "慢速度，文件小",
    "slower": "很慢速度，文件很小",
    "veryslow": "最慢速度，文件最小"
}

# 场景选择
PRESET_USAGE = {
    "实时处理": "ultrafast",
    "批量快速": "fast", 
    "一般使用": "medium",
    "高质量": "slow",
    "最佳压缩": "veryslow"
}

config = WatermarkConfig(video_preset="medium")
```

## 🚀 性能配置

### 超时和限制配置

#### timeout_seconds - 处理超时
```python
# 超时时间建议
TIMEOUT_GUIDE = {
    "小文件": 60,      # 1分钟，适合小图片/短视频
    "中等文件": 300,   # 5分钟，默认设置
    "大文件": 900,     # 15分钟，适合大视频
    "巨大文件": 1800   # 30分钟，适合4K长视频
}

# 根据文件大小动态设置
def calculate_timeout(file_size_mb):
    if file_size_mb < 10:
        return 60
    elif file_size_mb < 100:
        return 300
    elif file_size_mb < 500:
        return 900
    else:
        return 1800

config = WatermarkConfig(timeout_seconds=300)
```

#### use_hardware_acceleration - 硬件加速
```python
# 硬件加速配置
HARDWARE_CONFIG = {
    "启用": True,      # 自动检测并使用硬件加速
    "禁用": False      # 强制使用软件编码
}

# 硬件加速优势
ACCELERATION_BENEFITS = {
    "NVIDIA GPU": "速度提升2-5倍，支持h264_nvenc",
    "Intel GPU": "速度提升1.5-3倍，支持h264_qsv", 
    "软件编码": "兼容性最好，速度较慢"
}

config = WatermarkConfig(use_hardware_acceleration=True)
```

## 📋 预设配置模板

### 快速配置模板
```python
# 高性能配置（速度优先）
FAST_CONFIG = WatermarkConfig(
    pic_size=20,
    pic_opacity=0.5,
    pic_shadow=False,
    image_quality=80,
    video_crf=28,
    video_preset="fast",
    timeout_seconds=180
)

# 高质量配置（质量优先）
QUALITY_CONFIG = WatermarkConfig(
    pic_size=32,
    pic_opacity=0.8,
    pic_shadow=True,
    image_quality=98,
    video_crf=18,
    video_preset="slow",
    timeout_seconds=900
)

# 平衡配置（推荐）
BALANCED_CONFIG = WatermarkConfig(
    pic_size=24,
    pic_opacity=0.6,
    pic_shadow=True,
    image_quality=95,
    video_crf=23,
    video_preset="medium",
    timeout_seconds=300
)

# 社交媒体配置
SOCIAL_CONFIG = WatermarkConfig(
    pic_color="#FF6B35",
    pic_size=24,
    pic_angle=0,
    pic_opacity=0.7,
    video_position="bottom-right",
    video_color="#FFFFFF",
    image_quality=85,
    video_crf=26
)
```

### 场景化配置
```python
# Telegram机器人配置
TELEGRAM_CONFIG = WatermarkConfig(
    pic_color="#FF7256",
    pic_size=18,
    pic_opacity=0.6,
    pic_angle=28,
    video_position="bottom-right",
    video_color="#FFFFFF",
    image_quality=90,
    video_crf=25
)

# 批量处理配置
BATCH_CONFIG = WatermarkConfig(
    pic_size=20,
    pic_opacity=0.5,
    pic_shadow=False,
    image_quality=85,
    video_crf=28,
    video_preset="fast",
    timeout_seconds=120,
    use_hardware_acceleration=True
)

# 专业制作配置
PROFESSIONAL_CONFIG = WatermarkConfig(
    pic_size=36,
    pic_opacity=0.9,
    pic_shadow=True,
    pic_shadow_offset=(3, 3),
    image_quality=100,
    video_crf=15,
    video_preset="slower",
    timeout_seconds=1800
)
```

## 🔧 配置最佳实践

### 1. 根据用途选择配置
```python
# 不同用途的配置建议
def get_config_by_purpose(purpose):
    configs = {
        "telegram_bot": TELEGRAM_CONFIG,
        "social_media": SOCIAL_CONFIG,
        "batch_process": BATCH_CONFIG,
        "professional": PROFESSIONAL_CONFIG,
        "fast_preview": FAST_CONFIG,
        "high_quality": QUALITY_CONFIG
    }
    return configs.get(purpose, BALANCED_CONFIG)
```

### 2. 动态配置调整
```python
# 根据文件特征动态调整配置
def adjust_config_for_file(file_path, base_config):
    file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
    
    if file_size > 100:  # 大文件
        base_config.timeout_seconds = 900
        base_config.video_preset = "fast"
    elif file_size < 10:  # 小文件
        base_config.timeout_seconds = 60
        base_config.video_preset = "medium"
    
    return base_config
```

### 3. 配置验证
```python
# 配置参数验证
def validate_config(config):
    assert 0.0 <= config.pic_opacity <= 1.0, "透明度必须在0-1之间"
    assert 0.0 <= config.video_opacity <= 1.0, "透明度必须在0-1之间"
    assert 1 <= config.image_quality <= 100, "图片质量必须在1-100之间"
    assert 0 <= config.video_crf <= 51, "CRF值必须在0-51之间"
    assert config.timeout_seconds > 0, "超时时间必须大于0"
```

---

**配置建议总结：**
1. 新手建议使用默认配置或预设模板
2. 根据具体使用场景选择合适的配置
3. 大文件处理时适当增加超时时间
4. 批量处理时优先考虑速度配置
5. 重要文件使用高质量配置
