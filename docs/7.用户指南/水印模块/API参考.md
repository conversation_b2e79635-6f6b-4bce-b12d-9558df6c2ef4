# 水印模块 API 参考

## 📋 类和方法概览

### 主要类
- **`Watermark`** - 水印处理器主类
- **`WatermarkConfig`** - 水印配置类

### 主要方法
- **`add_image_watermark()`** - 添加图片水印
- **`add_video_watermark()`** - 添加视频水印

## 🏗️ WatermarkConfig 配置类

### 类定义
```python
@dataclass
class WatermarkConfig:
    """水印配置类"""
```

### 图片水印配置参数

| 参数 | 类型 | 默认值 | 说明 | 可选值 |
|------|------|--------|------|--------|
| `pic_color` | str | "#FF0000" | 水印文字颜色 | 十六进制颜色码，如 "#FF0000", "#00FF00" |
| `pic_size` | int | 24 | 字体大小（像素） | 正整数，建议 12-72 |
| `pic_opacity` | float | 0.6 | 透明度 | 0.0-1.0，0为完全透明，1为完全不透明 |
| `pic_angle` | int | 30 | 文字旋转角度（度） | 0-360，0为水平，90为垂直 |
| `pic_spacing` | int | 200 | 水印间距（像素） | 正整数，建议 100-500 |
| `pic_font_path` | Optional[str] | None | 自定义字体文件路径 | 字体文件绝对路径，如 "/path/to/font.ttf" |
| `pic_shadow` | bool | True | 是否添加阴影 | True/False |
| `pic_shadow_color` | str | "#000000" | 阴影颜色 | 十六进制颜色码 |
| `pic_shadow_offset` | Tuple[int, int] | (2, 2) | 阴影偏移量 | (x_offset, y_offset)，单位像素 |

### 视频水印配置参数

| 参数 | 类型 | 默认值 | 说明 | 可选值 |
|------|------|--------|------|--------|
| `video_color` | str | "#FFFFFF" | 水印文字颜色 | 十六进制颜色码 |
| `video_size` | int | 24 | 字体大小（像素） | 正整数，建议 16-48 |
| `video_opacity` | float | 0.7 | 透明度 | 0.0-1.0 |
| `video_position` | str | "bottom-right" | 水印位置 | 见下方位置选项 |
| `video_margin` | Tuple[int, int] | (20, 20) | 边距 | (x_margin, y_margin)，单位像素 |
| `video_font_path` | Optional[str] | None | 自定义字体文件路径 | 字体文件绝对路径 |

#### 视频水印位置选项
| 值 | 说明 | 位置描述 |
|---|------|----------|
| `"top-left"` | 左上角 | 距离左边和上边各 margin 像素 |
| `"top-right"` | 右上角 | 距离右边和上边各 margin 像素 |
| `"bottom-left"` | 左下角 | 距离左边和下边各 margin 像素 |
| `"bottom-right"` | 右下角 | 距离右边和下边各 margin 像素 |
| `"center"` | 居中 | 画面正中央 |

### 输出质量配置参数

| 参数 | 类型 | 默认值 | 说明 | 可选值 |
|------|------|--------|------|--------|
| `image_quality` | int | 95 | JPEG图片质量 | 1-100，数值越高质量越好 |
| `video_crf` | int | 23 | 视频质量参数 | 0-51，数值越小质量越高 |
| `video_preset` | str | "medium" | 编码速度预设 | 见下方预设选项 |

#### 视频编码预设选项
| 值 | 编码速度 | 文件大小 | 适用场景 |
|---|----------|----------|----------|
| `"ultrafast"` | 最快 | 最大 | 实时处理 |
| `"superfast"` | 很快 | 很大 | 快速处理 |
| `"veryfast"` | 快 | 大 | 一般处理 |
| `"faster"` | 较快 | 较大 | 平衡处理 |
| `"fast"` | 中等 | 中等 | 推荐使用 |
| `"medium"` | 中等 | 中等 | 默认选择 |
| `"slow"` | 慢 | 小 | 高质量 |
| `"slower"` | 很慢 | 很小 | 最高质量 |
| `"veryslow"` | 最慢 | 最小 | 极致质量 |

### 性能配置参数

| 参数 | 类型 | 默认值 | 说明 | 可选值 |
|------|------|--------|------|--------|
| `max_file_size_mb` | int | 500 | 最大文件大小限制（MB） | 正整数 |
| `timeout_seconds` | int | 300 | 处理超时时间（秒） | 正整数，建议 60-1800 |
| `use_hardware_acceleration` | bool | True | 是否使用硬件加速 | True/False |

### 配置示例

```python
# 默认配置
config = WatermarkConfig()

# 高质量配置
high_quality_config = WatermarkConfig(
    pic_size=32,
    pic_opacity=0.8,
    video_crf=18,
    video_preset="slow",
    image_quality=98
)

# 快速处理配置
fast_config = WatermarkConfig(
    pic_size=20,
    video_crf=28,
    video_preset="ultrafast",
    image_quality=85
)

# 自定义样式配置
custom_config = WatermarkConfig(
    pic_color="#FF6B35",
    pic_size=28,
    pic_angle=45,
    pic_spacing=150,
    video_position="top-right",
    video_color="#FFFFFF"
)
```

## 🎯 Watermark 主类

### 构造方法

```python
def __init__(self, config: Optional[WatermarkConfig] = None)
```

**参数：**
- `config` (Optional[WatermarkConfig]): 配置对象，如果为 None 则使用默认配置

**示例：**
```python
# 使用默认配置
watermark = Watermark()

# 使用自定义配置
config = WatermarkConfig(pic_size=30)
watermark = Watermark(config=config)
```

### add_image_watermark 方法

```python
async def add_image_watermark(
    self,
    input_file: str,
    watermark_text: str,
    output_file: Optional[str] = None,
    **kwargs
) -> str
```

**参数：**
- `input_file` (str): 输入图片文件路径
- `watermark_text` (str): 水印文字内容
- `output_file` (Optional[str]): 输出文件路径，为 None 时覆盖原文件
- `**kwargs`: 额外的配置参数，会覆盖实例配置

**返回值：**
- `str`: 成功时返回输出文件路径，失败时返回空字符串

**支持的 kwargs 参数：**
| 参数 | 类型 | 说明 |
|------|------|------|
| `color` | str | 覆盖 pic_color |
| `size` | int | 覆盖 pic_size |
| `opacity` | float | 覆盖 pic_opacity |
| `angle` | int | 覆盖 pic_angle |
| `spacing` | int | 覆盖 pic_spacing |
| `font_path` | str | 覆盖 pic_font_path |
| `shadow` | bool | 覆盖 pic_shadow |

**示例：**
```python
# 基础使用
result = await watermark.add_image_watermark("photo.jpg", "我的水印")

# 自定义输出文件
result = await watermark.add_image_watermark(
    "input.jpg", 
    "水印文字", 
    "output.jpg"
)

# 使用额外参数
result = await watermark.add_image_watermark(
    "photo.jpg", 
    "红色水印",
    color="#FF0000",
    size=30,
    opacity=0.8
)
```

### add_video_watermark 方法

```python
async def add_video_watermark(
    self,
    input_file: str,
    watermark_text: str,
    output_file: Optional[str] = None,
    progress_callback: Optional[Callable[[float], None]] = None,
    **kwargs
) -> str
```

**参数：**
- `input_file` (str): 输入视频文件路径
- `watermark_text` (str): 水印文字内容
- `output_file` (Optional[str]): 输出文件路径，为 None 时覆盖原文件
- `progress_callback` (Optional[Callable]): 进度回调函数（暂未实现）
- `**kwargs`: 额外的配置参数

**返回值：**
- `str`: 成功时返回输出文件路径，失败时返回空字符串

**支持的 kwargs 参数：**
| 参数 | 类型 | 说明 |
|------|------|------|
| `color` | str | 覆盖 video_color |
| `size` | int | 覆盖 video_size |
| `opacity` | float | 覆盖 video_opacity |
| `position` | str | 覆盖 video_position |
| `margin` | Tuple[int, int] | 覆盖 video_margin |

**示例：**
```python
# 基础使用
result = await watermark.add_video_watermark("video.mp4", "视频水印")

# 自定义位置和颜色
result = await watermark.add_video_watermark(
    "input.mp4",
    "右上角水印",
    position="top-right",
    color="#FFFF00",
    margin=(30, 30)
)
```

## 🔄 错误处理

### 返回值说明
- **成功**：返回输出文件的完整路径（字符串）
- **失败**：返回空字符串 `""`

### 错误日志
所有错误都会自动记录到 `sys_log.error_log`，包括：
- 文件不存在
- 格式不支持
- 处理超时
- 系统资源不足
- FFmpeg 执行失败

### 错误处理示例
```python
result = await watermark.add_image_watermark("test.jpg", "水印")

if result:
    print(f"处理成功: {result}")
    # 继续后续操作
else:
    print("处理失败，请检查日志")
    # 查看 sys_log.error_log 获取详细错误信息
```

## 📊 支持的文件格式

### 图片格式
- **输入支持**：JPEG, PNG, BMP, TIFF, WebP, GIF
- **输出支持**：JPEG, PNG, BMP, TIFF, WebP

### 视频格式
- **输入支持**：MP4, AVI, MOV, MKV, FLV, WebM
- **输出支持**：MP4, AVI, MOV, MKV, WebM

---

**注意事项：**
1. 所有方法都是异步的，需要使用 `await` 调用
2. 文件路径支持相对路径和绝对路径
3. 配置参数的优先级：方法参数 > 实例配置 > 默认配置
4. 处理大文件时建议适当增加 `timeout_seconds` 值
