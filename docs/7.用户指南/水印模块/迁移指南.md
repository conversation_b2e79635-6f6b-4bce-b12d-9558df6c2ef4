# 水印模块迁移指南

## 🎯 迁移概述

本指南帮助您从旧版水印系统（基于 watermarker.marker + moviepy）迁移到新版高性能水印系统（基于 PIL/Pillow + FFmpeg-python）。

### 迁移收益
- **性能提升**：图片处理快200-300%，视频处理快300-400%
- **内存优化**：内存使用减少50-80%
- **功能增强**：更丰富的水印效果和配置选项
- **错误处理**：统一的错误处理和日志记录

## 📋 迁移前准备

### 1. 环境检查
```bash
# 检查Python版本（需要3.7+）
python --version

# 检查FFmpeg安装
ffmpeg -version

# 检查系统字体
ls /usr/share/fonts/truetype/  # Linux
ls /System/Library/Fonts/      # macOS
dir C:\Windows\Fonts\          # Windows
```

### 2. 依赖安装
```bash
# 安装新的依赖库
pip install Pillow ffmpeg-python opencv-python numpy

# 可选：卸载旧的依赖（如果不再使用）
# pip uninstall watermarker moviepy
```

### 3. 备份现有代码
```bash
# 备份当前的水印相关代码
cp shared/func.py shared/func.py.backup
cp robot_telegram2/func.py robot_telegram2/func.py.backup
```

## 🔄 API迁移对照

### 图片水印迁移

#### 旧版API
```python
# 旧版：watermarker.marker
from watermarker.marker import add_mark

def old_add_pic_mark(input_file, output_dir, watermark_text, **kwargs):
    return add_mark(
        input_file, 
        output_dir, 
        watermark_text,
        color=kwargs.get('color', '#FF0000'),
        size=kwargs.get('size', 24),
        opacity=kwargs.get('opacity', 0.6),
        angle=kwargs.get('angle', 30),
        space=kwargs.get('space', 200)
    )
```

#### 新版API
```python
# 新版：PIL/Pillow
from shared.classes2 import Watermark

async def new_add_pic_mark(input_file, watermark_text, output_file=None, **kwargs):
    watermark = Watermark()
    return await watermark.add_image_watermark(
        input_file,
        watermark_text, 
        output_file,  # 可选，默认覆盖原文件
        color=kwargs.get('color', '#FF0000'),
        size=kwargs.get('size', 24),
        opacity=kwargs.get('opacity', 0.6),
        angle=kwargs.get('angle', 30),
        spacing=kwargs.get('spacing', 200)  # 注意：参数名从space改为spacing
    )
```

### 视频水印迁移

#### 旧版API
```python
# 旧版：moviepy
from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip

def old_add_video_mark(input_file, output_file, watermark_text):
    # 复杂的moviepy代码
    video = VideoFileClip(input_file)
    txt_clip = TextClip(watermark_text, fontsize=24, color='white')
    # ... 更多复杂处理
    final_video = CompositeVideoClip([video, txt_clip])
    final_video.write_videofile(output_file)
```

#### 新版API
```python
# 新版：FFmpeg-python
from shared.classes2 import Watermark

async def new_add_video_mark(input_file, watermark_text, output_file=None, **kwargs):
    watermark = Watermark()
    return await watermark.add_video_watermark(
        input_file,
        watermark_text,
        output_file,  # 可选，默认覆盖原文件
        position=kwargs.get('position', 'bottom-right'),
        color=kwargs.get('color', '#FFFFFF'),
        size=kwargs.get('size', 24),
        opacity=kwargs.get('opacity', 0.7)
    )
```

## 🔧 具体迁移步骤

### 步骤1：更新导入语句

#### 替换导入
```python
# 删除旧的导入
# from watermarker.marker import add_mark
# from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip

# 添加新的导入
from shared.classes2 import Watermark, WatermarkConfig
```

### 步骤2：迁移图片水印函数

#### 原有代码示例
```python
# robot_telegram2/func.py 中的原有代码
def add_pic_mark(adds, output_dir, watermark_text, **kwargs):
    try:
        return add_mark(
            adds, 
            output_dir, 
            watermark_text,
            color=kwargs.get('color', '#FF7256'),
            size=kwargs.get('size', 18),
            opacity=kwargs.get('opacity', 0.6),
            angle=kwargs.get('angle', 28),
            space=kwargs.get('space', 200)
        )
    except Exception as e:
        print(f"图片水印失败: {e}")
        return False
```

#### 迁移后代码
```python
# 新的异步版本
async def add_pic_mark(input_file, watermark_text, output_file=None, **kwargs):
    """
    添加图片水印（新版）
    
    Args:
        input_file: 输入文件路径
        watermark_text: 水印文字
        output_file: 输出文件路径，None时覆盖原文件
        **kwargs: 其他配置参数
    
    Returns:
        str: 成功返回输出文件路径，失败返回空字符串
    """
    try:
        watermark = Watermark()
        result = await watermark.add_image_watermark(
            input_file,
            watermark_text,
            output_file,
            color=kwargs.get('color', '#FF7256'),
            size=kwargs.get('size', 18),
            opacity=kwargs.get('opacity', 0.6),
            angle=kwargs.get('angle', 28),
            spacing=kwargs.get('spacing', 200)  # 注意参数名变化
        )
        return result
    except Exception as e:
        print(f"图片水印失败: {e}")
        return ""
```

### 步骤3：迁移视频水印函数

#### 原有代码示例
```python
# 原有的复杂视频处理代码
def add_video_mark(input_file, output_file, watermark_text):
    try:
        # 复杂的线程处理和moviepy代码
        loop = asyncio.get_running_loop()
        process_thread = threading.Thread(
            target=_process_video_with_moviepy, 
            args=(input_file, output_file, watermark_text)
        )
        process_thread.start()
        await asyncio.wait_for(
            loop.run_in_executor(None, process_thread.join), 
            timeout=60
        )
        return True
    except Exception as e:
        print(f"视频水印失败: {e}")
        return False
```

#### 迁移后代码
```python
# 新的简化异步版本
async def add_video_mark(input_file, watermark_text, output_file=None, **kwargs):
    """
    添加视频水印（新版）
    
    Args:
        input_file: 输入文件路径
        watermark_text: 水印文字
        output_file: 输出文件路径，None时覆盖原文件
        **kwargs: 其他配置参数
    
    Returns:
        str: 成功返回输出文件路径，失败返回空字符串
    """
    watermark = Watermark()
    result = await watermark.add_video_watermark(
        input_file,
        watermark_text,
        output_file,
        position=kwargs.get('position', 'bottom-right'),
        color=kwargs.get('color', '#FFFFFF'),
        size=kwargs.get('size', 24),
        opacity=kwargs.get('opacity', 0.7)
    )
    return result
```

### 步骤4：更新调用代码

#### Telegram机器人代码迁移

##### 原有代码
```python
@app.on_message(filters.photo)
async def handle_photo(client, message):
    file_path = await message.download()
    
    # 旧版调用（同步）
    success = add_pic_mark(
        file_path, 
        'media/', 
        "Tg: @my_channel",
        color="#FF7256",
        size=18,
        opacity=0.6,
        angle=28
    )
    
    if success:
        await message.reply_photo(file_path)
```

##### 迁移后代码
```python
@app.on_message(filters.photo)
async def handle_photo(client, message):
    file_path = await message.download()
    
    # 新版调用（异步）
    result = await add_pic_mark(
        file_path,
        "Tg: @my_channel",
        # output_file=None,  # 默认覆盖原文件
        color="#FF7256",
        size=18,
        opacity=0.6,
        angle=28
    )
    
    if result:  # 检查返回的文件路径
        await message.reply_photo(result)
    else:
        await message.reply("图片处理失败")
```

## 📝 参数映射表

### 图片水印参数映射

| 旧版参数 | 新版参数 | 说明 |
|----------|----------|------|
| `color` | `color` | 颜色，无变化 |
| `size` | `size` | 字体大小，无变化 |
| `opacity` | `opacity` | 透明度，无变化 |
| `angle` | `angle` | 角度，无变化 |
| `space` | `spacing` | **参数名变化** |
| - | `shadow` | **新增**：阴影效果 |
| - | `font_path` | **新增**：自定义字体 |

### 视频水印参数映射

| 旧版参数 | 新版参数 | 说明 |
|----------|----------|------|
| 复杂的位置计算 | `position` | **简化**：预设位置选项 |
| `fontsize` | `size` | 字体大小 |
| `color` | `color` | 颜色，无变化 |
| - | `opacity` | **新增**：透明度控制 |
| - | `margin` | **新增**：边距控制 |

## ⚠️ 注意事项和常见问题

### 1. 异步调用变化
```python
# 旧版：同步调用
result = add_pic_mark(file, "水印")

# 新版：异步调用（重要！）
result = await add_pic_mark(file, "水印")
```

### 2. 返回值变化
```python
# 旧版：返回布尔值
if add_pic_mark(file, "水印"):
    print("成功")

# 新版：返回文件路径或空字符串
result = await add_pic_mark(file, "水印")
if result:
    print(f"成功，输出文件: {result}")
else:
    print("失败")
```

### 3. 错误处理变化
```python
# 旧版：可能抛出异常
try:
    result = add_pic_mark(file, "水印")
except Exception as e:
    print(f"错误: {e}")

# 新版：不抛出异常，通过返回值判断
result = await add_pic_mark(file, "水印")
if not result:
    print("处理失败，详情见日志")
    # 错误详情已自动记录到 sys_log.error_log
```

### 4. 配置方式变化
```python
# 旧版：每次调用都传参数
add_pic_mark(file, "水印", color="#FF0000", size=24)

# 新版：支持配置类（推荐）
config = WatermarkConfig(
    pic_color="#FF0000",
    pic_size=24
)
watermark = Watermark(config)
result = await watermark.add_image_watermark(file, "水印")
```

## 🧪 迁移测试

### 测试脚本
```python
import asyncio
import os

async def test_migration():
    """测试迁移后的功能"""
    
    # 测试图片水印
    print("测试图片水印...")
    result = await add_pic_mark(
        "test_image.jpg",
        "迁移测试",
        color="#FF0000",
        size=24
    )
    
    if result:
        print(f"✅ 图片水印成功: {result}")
        # 验证文件存在
        if os.path.exists(result):
            print(f"✅ 输出文件存在: {os.path.getsize(result)} bytes")
        else:
            print("❌ 输出文件不存在")
    else:
        print("❌ 图片水印失败")
    
    # 测试视频水印
    print("\n测试视频水印...")
    result = await add_video_mark(
        "test_video.mp4",
        "迁移测试",
        position="bottom-right"
    )
    
    if result:
        print(f"✅ 视频水印成功: {result}")
        if os.path.exists(result):
            print(f"✅ 输出文件存在: {os.path.getsize(result)} bytes")
        else:
            print("❌ 输出文件不存在")
    else:
        print("❌ 视频水印失败")

# 运行测试
if __name__ == "__main__":
    asyncio.run(test_migration())
```

### 性能对比测试
```python
import time

async def performance_comparison():
    """对比新旧版本性能"""
    
    test_file = "performance_test.jpg"
    
    # 测试新版本
    start_time = time.time()
    result = await add_pic_mark(test_file, "性能测试")
    new_version_time = time.time() - start_time
    
    print(f"新版本处理时间: {new_version_time:.2f}秒")
    print(f"处理结果: {'成功' if result else '失败'}")
    
    # 如果还有旧版本可以对比
    # start_time = time.time()
    # old_result = old_add_pic_mark(test_file, "性能测试")
    # old_version_time = time.time() - start_time
    # 
    # speedup = old_version_time / new_version_time
    # print(f"性能提升: {speedup:.2f}倍")
```

## 📋 迁移检查清单

### 迁移前检查
- [ ] 备份现有代码
- [ ] 安装新的依赖库
- [ ] 检查系统环境（FFmpeg、字体等）
- [ ] 准备测试文件

### 迁移过程检查
- [ ] 更新导入语句
- [ ] 修改函数签名（添加async）
- [ ] 更新参数名称（space → spacing）
- [ ] 修改返回值处理逻辑
- [ ] 更新错误处理方式

### 迁移后检查
- [ ] 运行迁移测试脚本
- [ ] 验证功能正确性
- [ ] 检查性能提升效果
- [ ] 测试错误处理
- [ ] 更新相关文档

### 部署检查
- [ ] 在测试环境验证
- [ ] 检查日志记录
- [ ] 监控系统资源使用
- [ ] 准备回滚方案
- [ ] 部署到生产环境

---

**迁移建议：**
1. 建议分阶段迁移，先迁移图片处理，再迁移视频处理
2. 保留旧版本代码作为备份，直到新版本稳定运行
3. 充分测试各种文件格式和大小
4. 监控迁移后的性能和错误率
5. 及时更新相关文档和使用说明
