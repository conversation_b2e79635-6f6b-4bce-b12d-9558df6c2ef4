# 水印模块技术方案

## 🎯 技术架构

### 核心库选择

#### 图片处理：PIL/Pillow
**选择理由：**
- ✅ **性能优秀**：C扩展实现，处理速度快
- ✅ **功能全面**：支持所有主流图片格式和操作
- ✅ **稳定可靠**：Python官方推荐，维护活跃
- ✅ **精确控制**：像素级精确控制水印位置和效果
- ✅ **扩展性强**：支持自定义滤镜和效果

**替代方案对比：**
| 库 | 性能 | 功能 | 稳定性 | 维护状态 |
|---|---|---|---|---|
| PIL/Pillow | 优秀 | 全面 | 优秀 | 活跃 |
| watermarker.marker | 一般 | 有限 | 中等 | 不活跃 |
| OpenCV | 优秀 | 过剩 | 良好 | 活跃 |

#### 视频处理：FFmpeg-python
**选择理由：**
- ✅ **性能卓越**：直接调用FFmpeg，速度最快
- ✅ **功能强大**：支持所有FFmpeg功能
- ✅ **资源效率**：内存占用低，CPU利用率高
- ✅ **稳定性好**：基于成熟的FFmpeg，久经考验
- ✅ **硬件加速**：支持GPU加速（NVENC、Quick Sync等）

**替代方案对比：**
| 库 | 性能 | 功能 | 稳定性 | 资源占用 |
|---|---|---|---|---|
| FFmpeg-python | 优秀 | 全面 | 优秀 | 低 |
| moviepy | 一般 | 全面 | 中等 | 高 |
| OpenCV | 良好 | 中等 | 良好 | 中等 |

## 🏗️ 系统设计

### 类结构设计

```python
@dataclass
class WatermarkConfig:
    """水印配置类 - 集中管理所有配置参数"""
    # 图片水印配置
    pic_color: str = "#FF0000"
    pic_size: int = 24
    pic_opacity: float = 0.6
    # ... 更多配置

class Watermark:
    """水印处理器主类"""
    def __init__(self, config: Optional[WatermarkConfig] = None)
    
    async def add_image_watermark(self, ...) -> str
    async def add_video_watermark(self, ...) -> str
    
    def _get_font(self, ...) -> ImageFont.FreeTypeFont
    def _create_text_image(self, ...) -> Image.Image
    def _process_image_watermark(self, ...) -> bool
    def _process_video_watermark(self, ...) -> bool
```

### 设计原则

#### 1. 单一职责原则
- `WatermarkConfig`：专门负责配置管理
- `Watermark`：专门负责水印处理
- 每个私有方法都有明确的单一功能

#### 2. 开闭原则
- 通过配置类扩展功能，无需修改核心代码
- 支持通过 `**kwargs` 传递额外参数

#### 3. 依赖倒置原则
- 依赖抽象的配置接口，而非具体实现
- 支持配置注入，便于测试和扩展

#### 4. 接口隔离原则
- 图片和视频处理分离为不同方法
- 配置参数按功能分组

## 🔄 处理流程

### 图片水印处理流程

```mermaid
graph TD
    A[输入文件] --> B[参数验证]
    B --> C[生成输出路径]
    C --> D[异步执行处理]
    D --> E[打开原图]
    E --> F[转换RGBA模式]
    F --> G[获取字体]
    G --> H[创建文字图像]
    H --> I[应用效果]
    I --> J[创建水印层]
    J --> K[平铺水印]
    K --> L[合成图像]
    L --> M[保存文件]
    M --> N[返回结果]
```

### 视频水印处理流程

```mermaid
graph TD
    A[输入文件] --> B[参数验证]
    B --> C[生成输出路径]
    C --> D[异步执行处理]
    D --> E[构建FFmpeg命令]
    E --> F[设置水印滤镜]
    F --> G[配置编码参数]
    G --> H[硬件加速检测]
    H --> I[执行FFmpeg]
    I --> J[返回结果]
```

## ⚡ 性能优化策略

### 1. 异步处理
```python
# 使用线程池执行CPU密集型任务
loop = asyncio.get_event_loop()
success = await loop.run_in_executor(
    None, 
    self._process_image_watermark,
    # ... 参数
)
```

### 2. 内存管理
```python
# 使用 with 语句确保资源及时释放
with Image.open(input_file) as original:
    # 处理图像
    pass  # 自动释放内存
```

### 3. 硬件加速
```python
# 自动检测并使用硬件编码器
if self.config.use_hardware_acceleration:
    try:
        output_args['vcodec'] = 'h264_nvenc'  # NVIDIA GPU
    except:
        try:
            output_args['vcodec'] = 'h264_qsv'  # Intel Quick Sync
        except:
            output_args['vcodec'] = 'libx264'  # 软件编码
```

### 4. 配置优化
```python
# 针对不同场景的预设配置
fast_config = WatermarkConfig(
    video_preset="ultrafast",
    video_crf=28
)

quality_config = WatermarkConfig(
    video_preset="slow", 
    video_crf=18
)
```

## 🛡️ 错误处理设计

### 统一错误处理策略

#### 1. 无异常抛出设计
```python
# 不使用异常控制流程
try:
    # 处理逻辑
    return True
except Exception as e:
    sys_log.error_log(f"处理失败: {e}")
    return False  # 返回布尔值而非抛出异常
```

#### 2. 分层错误处理
```python
# 主方法层：处理返回值
async def add_image_watermark(self, ...) -> str:
    success = await self._process_image_watermark(...)
    if success:
        return output_file
    else:
        return ""

# 处理方法层：捕获异常并记录
def _process_image_watermark(self, ...) -> bool:
    try:
        # 具体处理逻辑
        return True
    except Exception as e:
        sys_log.error_log(f"图片处理失败: {e}")
        return False
```

#### 3. 统一日志记录
```python
# 成功信息
sys_log.write_log(f"图片水印添加成功: {input_file} -> {output_file}", 'i')

# 错误信息
sys_log.error_log(f"图片水印添加失败: {input_file}, 错误: {e}")
```

## 🔧 配置系统设计

### 配置层次结构

```python
# 1. 默认配置（类定义）
@dataclass
class WatermarkConfig:
    pic_color: str = "#FF0000"  # 默认值

# 2. 实例配置（构造时）
config = WatermarkConfig(pic_color="#00FF00")
watermark = Watermark(config=config)

# 3. 调用配置（方法参数）
result = await watermark.add_image_watermark(
    "test.jpg", "水印", 
    color="#0000FF"  # 覆盖实例配置
)
```

### 配置优先级
1. **方法参数** > **实例配置** > **默认配置**
2. 使用 `kwargs.get('param', self.config.param)` 实现

## 📊 性能监控

### 关键性能指标

#### 1. 处理时间
```python
start_time = time.time()
# 处理逻辑
duration = time.time() - start_time
sys_log.write_log(f"处理耗时: {duration:.2f}秒", 'i')
```

#### 2. 内存使用
```python
import psutil
process = psutil.Process()
memory_before = process.memory_info().rss / 1024 / 1024
# 处理逻辑
memory_after = process.memory_info().rss / 1024 / 1024
memory_increase = memory_after - memory_before
```

#### 3. 文件大小变化
```python
input_size = os.path.getsize(input_file)
output_size = os.path.getsize(output_file)
size_ratio = output_size / input_size
```

## 🔄 扩展性设计

### 1. 插件化水印效果
```python
# 未来可扩展的效果系统
class WatermarkEffect:
    def apply(self, image: Image.Image, text: str) -> Image.Image:
        pass

class ShadowEffect(WatermarkEffect):
    def apply(self, image, text):
        # 实现阴影效果
        pass
```

### 2. 多格式支持
```python
# 格式处理器注册系统
format_handlers = {
    '.jpg': JPEGHandler,
    '.png': PNGHandler,
    '.webp': WebPHandler,
}
```

### 3. 自定义字体管理
```python
# 字体管理系统
class FontManager:
    def __init__(self):
        self.font_cache = {}
    
    def get_font(self, path: str, size: int) -> ImageFont.FreeTypeFont:
        # 字体缓存和管理
        pass
```

## 📈 未来优化方向

### 1. 性能优化
- **GPU加速**：更广泛的GPU加速支持
- **并行处理**：批量文件的并行处理
- **缓存机制**：字体和配置缓存

### 2. 功能扩展
- **图片水印**：支持图片作为水印
- **动态效果**：支持动态水印效果
- **批量模板**：预设的水印模板

### 3. 易用性改进
- **配置向导**：图形化配置工具
- **预览功能**：水印效果预览
- **批量工具**：批量处理工具

---

**技术栈总结：**
- **核心库**：PIL/Pillow + FFmpeg-python
- **异步框架**：asyncio
- **配置管理**：dataclass
- **日志系统**：项目内置 sys_log
- **错误处理**：返回值 + 日志记录
