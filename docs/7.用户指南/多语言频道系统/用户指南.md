# 多语言频道系统用户指南

## 📋 系统概述

多语言频道系统实现了当客服点击"在线"按钮时，自动将女孩卡片展示到配置的多语言频道中。系统支持4种语言（中文、英文、俄语、哈萨克语）和7种频道类型的自动分类展示。

## 🎯 频道类型说明

| 频道类型 | 英文标识 | 分类编号 | 筛选规则 | 说明 |
|---------|---------|----------|----------|------|
| 主频道 | main | 0 | 所有在线女孩 | 所有女孩都会在这个频道中展示 |
| 空闲频道 | avail | 1 | 当前空闲的女孩 | 只有当前空闲的女孩才会展示 |
| 好评频道 | grade | 2 | grade ≥ 3分 | 只有girls.grade分数3分及以上的女孩 |
| 点评频道 | assess | 3 | 不展示女孩卡片 | 客户的点评内容展示频道 |
| 新女孩频道 | new | 4 | 前40个新女孩 | 刚上架的前40个女孩展示 |
| 丰满女孩频道 | boobs | 5 | 胸围≥C罩杯 | 胸围比较大的女孩展示 |
| 年轻女孩频道 | young | 6 | 年龄<20岁 | 年龄在20岁以下的女孩展示 |

## 🌍 多语言支持

系统支持4种语言的频道配置：
- **中文** (zh_CN)：🇨🇳 简体中文
- **英文** (en_US)：🇺🇸 English  
- **俄语** (ru_RU)：🇷🇺 Русский
- **哈萨克语** (kk_KZ)：🇰🇿 Қазақша

每种语言都支持上述7种频道类型，总计28个频道配置。

## 🔧 频道配置

### 配置格式
频道配置存储在 `params` 表中，格式为：
```
channel_config.{语言}.{频道类型} = 频道ID
```

### 配置示例
```
channel_config.zh_CN.main = -1001234567890     # 中文主频道
channel_config.zh_CN.avail = -1001234567891    # 中文空闲频道
channel_config.zh_CN.grade = -1001234567892    # 中文好评频道
channel_config.en_US.main = -1001234567900     # 英文主频道
channel_config.ru_RU.main = -1001234567910     # 俄语主频道
channel_config.kk_KZ.main = -1001234567920     # 哈萨克语主频道
```

## 📊 分类管理

### 自动分类
系统会根据女孩的属性自动进行分类：

1. **空闲频道 (1)**：所有在线女孩（临时实现，需要根据业务逻辑调整）
2. **好评频道 (2)**：`grade >= 3` 的女孩
3. **新女孩频道 (4)**：按ID倒序排列的前40个在线女孩
4. **丰满女孩频道 (5)**：胸围包含C/D/E/F/G/H罩杯的女孩
5. **年轻女孩频道 (6)**：`age < 20` 的女孩

### 手动分类管理
管理员可以通过以下方式进行手动分类管理：

```python
from plugins.hazoo.modules.girl_channel_manager import girl_channel_manager

# 添加女孩到好评频道
girl_channel_manager.set_classify(girl_id=123, classify_type=2, opt='add')

# 删除女孩的指定分类
girl_channel_manager.set_classify(girl_id=123, classify_type=2, opt='delete')

# 查看女孩的所有分类
classifications = girl_channel_manager.get_classify(girl_id=123)

# 查看好评频道的所有女孩
grade_girls = girl_channel_manager.get_classify(classify_type=2)
```

## 🚀 使用流程

### 1. 自动触发
当客服在女孩管理界面点击"在线"按钮时，系统会：
1. 更新女孩状态为在线
2. 自动判断女孩应该展示在哪些频道
3. 根据频道语言生成对应语言的卡片
4. 发送卡片到匹配的频道
5. 保存消息记录到ChatMessage系统

### 2. 手动管理
管理员可以：
- 运行自动分类更新所有女孩的分类
- 手动添加或删除女孩的分类
- 查看各频道的女孩列表
- 重新发送女孩卡片到频道

## 🌍 多语言卡片展示

### 中文卡片（默认）
使用原有的 `format_girl_card` 函数格式，显示中文特点字段。

### 英文卡片
```
👩 Girl Profile
─────────────────────────
👤 Name: 小美
🆔 ID: 1
🎂 Age: 22
📏 Height: 165cm
⚖️ Weight: 50kg
👙 Bust: 34B
✨ Features: Young and beautiful
📍 Location: 测试地址
📞 Phone: 123456789
💰 Price: 500
⭐ Grade: 7
👀 Views: 15
─────────────────────────
```

### 俄语卡片
```
👩 Профиль девушки
─────────────────────────
👤 Имя: 小美
🆔 ID: 1
🎂 Возраст: 22
📏 Рост: 165см
⚖️ Вес: 50кг
👙 Грудь: 34B
✨ Особенности: Молодая и красивая
📍 Местоположение: 测试地址
📞 Телефон: 123456789
💰 Цена: 500
⭐ Рейтинг: 7
👀 Просмотры: 15
─────────────────────────
```

### 哈萨克语卡片
```
👩 Қыз профилі
─────────────────────────
👤 Аты: 小美
🆔 ID: 1
🎂 Жасы: 22
📏 Бойы: 165см
⚖️ Салмағы: 50кг
👙 Кеуде: 34B
✨ Ерекшеліктері: Жас және әдемі
📍 Орналасқан жері: 测试地址
📞 Телефон: 123456789
💰 Бағасы: 500
⭐ Рейтинг: 7
👀 Көрулер: 15
─────────────────────────
```

## ⚠️ 注意事项

### 权限要求
- 机器人必须是所有配置频道的管理员
- 需要发送消息和删除消息权限

### 配置要求
- 频道 ID 必须是负数（Telegram 频道 ID 格式）
- 语言代码必须是支持的 4 种之一
- 频道类型必须是定义的 7 种之一

### 特殊说明
- **点评频道 (assess)**：主要用于展示客户点评内容，女孩卡片不会发送到此频道
- **空闲频道 (avail)**：需要根据实际业务逻辑判断女孩是否空闲，当前临时使用所有在线女孩
- **新女孩频道 (new)**：使用ID倒序排列的前40个在线女孩
- **分类表驱动**：系统基于 `girl_classify` 表进行判断，需要定期运行自动分类更新数据

## 📞 故障排除

### 常见问题

1. **女孩卡片没有发送到频道**
   - 检查女孩状态是否为在线(status=1)
   - 检查频道配置是否正确
   - 检查机器人是否有频道管理权限
   - 查看系统日志中的错误信息

2. **分类不正确**
   - 运行自动分类更新：`girl_channel_manager.auto_classify()`
   - 检查女孩的属性数据是否正确
   - 查看分类表中的数据是否准确

3. **多语言卡片显示异常**
   - 检查女孩的多语言特点字段是否已填写
   - 确认频道语言配置是否正确
   - 验证字体和编码支持

### 日志查看
系统会记录详细的操作日志，包括：
- 分类操作成功/失败
- 卡片发送成功/失败
- 配置加载成功/失败

查看日志文件获取详细的错误信息和调试信息。

## 🔄 维护建议

1. **定期自动分类**：建议设置定时任务运行自动分类更新
2. **监控发送结果**：定期检查卡片发送的成功率
3. **配置备份**：定期备份频道配置数据
4. **权限检查**：定期检查机器人在各频道的权限状态

---

**版本**: 5.0.0  
**更新时间**: 2025-01-19
