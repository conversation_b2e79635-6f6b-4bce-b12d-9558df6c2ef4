# 多语言频道系统技术文档

## 📋 系统架构

### 核心设计理念
多语言频道系统采用简化的单类架构设计，将所有功能整合到 `GirlChannelManager` 类中，避免了复杂的类继承和委托关系，提供直接、清晰的API接口。

### 技术栈
- **数据库**: MySQL (girl_classify 分类表)
- **消息系统**: Pyrogram (Telegram Bot API)
- **语言支持**: 4种语言 (中文、英文、俄语、哈萨克语)
- **异步处理**: asyncio (非阻塞频道发送)

## 🗄️ 数据库设计

### girl_classify 表结构
```sql
CREATE TABLE IF NOT EXISTS `girl_classify` (
  `girl_id` int NOT NULL COMMENT '女孩编号，同girls.id',
  `girl_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '女孩名字',
  `type` tinyint NOT NULL COMMENT '分组类型：1-avail；2-grade；3-assess；4-new；5-boobs；6-young',
  PRIMARY KEY (`girl_id`, `type`) USING BTREE,
  INDEX `idx_girl_id`(`girl_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE,
  INDEX `idx_girl_name`(`girl_name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '女孩分类表' ROW_FORMAT = Dynamic;
```

### 索引设计说明
- **主键**: (`girl_id`, `type`) 复合主键，确保一个女孩在同一分类中只有一条记录
- **idx_girl_id**: 支持按女孩ID快速查询该女孩的所有分类
- **idx_type**: 支持按分类类型快速查询该分类的所有女孩
- **idx_girl_name**: 支持按女孩姓名进行搜索和排序

### 数据类型选择
- `girl_id`: int 类型，与 girls 表的 id 字段保持一致
- `type`: tinyint 类型，节省存储空间，支持 1-255 的分类编号
- `girl_name`: varchar(50) 冗余存储，提高查询性能，避免频繁关联查询

## 🏗️ 类设计

### GirlChannelManager 类
```python
class GirlChannelManager:
    def __init__(self)                              # 初始化和加载配置
    def load_channels(self)                         # 加载频道配置
    def set_classify(girl_id, classify_type, opt)   # 设置分类
    def get_classify(girl_id, classify_type)        # 查询分类
    def auto_classify(classify_list)                # 自动分类
    def should_send_to_channel(girl_id, channel_type)  # 判断是否发送
    def format_girl_card(girl, language)            # 格式化卡片
    def _format_english_card(girl)                  # 英文卡片格式
    def _format_russian_card(girl)                  # 俄语卡片格式
    def _format_kazakh_card(girl)                   # 哈萨克语卡片格式
    def send_girl_to_channels(girl)                 # 发送到频道
```

### 设计优势
1. **单一职责**: 一个类管理所有频道相关功能
2. **数据类型统一**: 所有分类操作使用整数类型
3. **直接调用**: 避免复杂的方法链和委托
4. **全局实例**: 提供 `girl_channel_manager` 全局实例

## 🔧 核心算法

### 1. 自动分类算法
```python
def auto_classify(self, classify_list=[1, 2, 4, 5, 6]):
    # 1. 清空指定分类的现有数据
    # 2. 根据业务规则重新计算分类
    # 3. 批量插入新的分类数据
```

#### 分类规则
- **空闲频道 (1)**: `SELECT id, name, 1 FROM girls WHERE status = 1`
- **好评频道 (2)**: `SELECT id, name, 2 FROM girls WHERE status = 1 AND grade >= 3`
- **新女孩频道 (4)**: `SELECT id, name, 4 FROM girls WHERE status = 1 ORDER BY id DESC LIMIT 40`
- **丰满女孩频道 (5)**: 胸围包含 C/D/E/F/G/H 罩杯的女孩
- **年轻女孩频道 (6)**: `SELECT id, name, 6 FROM girls WHERE status = 1 AND age < 20`

### 2. 频道匹配算法
```python
def should_send_to_channel(self, girl_id, channel_type):
    if channel_type == "main":
        return True  # 主频道接受所有在线女孩
    elif channel_type == "assess":
        return False  # 点评频道不发送女孩卡片
    else:
        # 基于分类表判断
        type_mapping = {"avail": 1, "grade": 2, "new": 4, "boobs": 5, "young": 6}
        classify_type = type_mapping[channel_type]
        result = self.get_classify(girl_id=girl_id, classify_type=classify_type)
        return len(result) > 0
```

### 3. 多语言卡片生成
```python
def format_girl_card(self, girl, language):
    if language == "en_US":
        return self._format_english_card(girl)
    elif language == "ru_RU":
        return self._format_russian_card(girl)
    elif language == "kk_KZ":
        return self._format_kazakh_card(girl)
    else:
        return format_girl_card(girl)  # 默认中文格式
```

## 🔄 集成方案

### 最小侵入式集成
在现有的 `handle_status_confirmation` 方法中添加频道发送逻辑：

```python
# 在 plugins/hazoo/modules/girls_manage.py 中
from .girl_channel_manager import girl_channel_manager

async def handle_status_confirmation(callback_query: CallbackQuery, status: int):
    # 原有逻辑...
    
    # 新增：如果状态是在线，发送到频道
    if status == 1:
        try:
            results = await girl_channel_manager.send_girl_to_channels(girl)
            if results:
                sys_log.write_log(f"女孩卡片已发送到多语言频道: {girl.id}")
        except Exception as e:
            sys_log.error_log(f"发送女孩卡片到频道失败: {e}")
    
    # 原有逻辑继续...
```

### 部署文件结构
```
plugins/hazoo/modules/
├── girl_channel_manager.py    # 核心系统文件
├── girls_manage.py            # 修改：添加频道发送逻辑
└── ...

docs/7.用户指南/多语言频道系统/
├── 用户指南.md               # 用户操作指南
├── 技术文档.md               # 本技术文档
└── API参考.md                # API接口文档
```

## 📊 性能优化

### 1. 数据库优化
- **批量操作**: 自动分类使用批量 INSERT，避免逐条插入
- **索引优化**: 基于查询模式设计的复合索引
- **数据冗余**: girl_name 字段冗余存储，减少关联查询

### 2. 内存优化
- **全局实例**: 使用单例模式，避免重复初始化
- **配置缓存**: 频道配置加载后缓存在内存中
- **按需加载**: 只在需要时加载特定语言的格式化方法

### 3. 网络优化
- **异步发送**: 使用 asyncio 进行非阻塞的频道发送
- **错误隔离**: 单个频道发送失败不影响其他频道
- **批量处理**: 支持批量发送多个女孩到频道

## 🔒 安全考虑

### 1. 数据安全
- **参数化查询**: 所有 SQL 操作使用参数化查询，防止 SQL 注入
- **数据验证**: 输入参数类型和范围验证
- **错误处理**: 完善的异常处理，避免敏感信息泄露

### 2. 权限控制
- **频道权限**: 确保机器人具有频道发送权限
- **操作日志**: 记录所有分类和发送操作
- **失败重试**: 网络失败时的重试机制

## 📈 监控和日志

### 日志级别
- **INFO**: 正常操作记录（分类更新、卡片发送成功）
- **ERROR**: 错误操作记录（发送失败、配置错误）
- **DEBUG**: 调试信息（详细的执行流程）

### 关键指标
- 分类更新成功率
- 频道发送成功率
- 平均响应时间
- 错误频率统计

## 🔧 扩展性设计

### 1. 新增语言支持
只需要：
1. 在 `format_girl_card` 方法中添加新语言分支
2. 实现对应的 `_format_xxx_card` 方法
3. 在 girls 表中添加对应的特点字段

### 2. 新增频道类型
只需要：
1. 在 `should_send_to_channel` 方法中添加新的判断逻辑
2. 在 `auto_classify` 方法中添加新的分类规则
3. 更新分类编号映射

### 3. 新增分类规则
只需要：
1. 在 `auto_classify` 方法中添加新的 SQL 查询
2. 更新分类编号定义
3. 添加对应的业务逻辑

## ⚡ 最佳实践

### 1. 定期维护
- 每日自动运行分类更新
- 定期清理过期的分类数据
- 监控频道发送成功率

### 2. 错误处理
- 网络错误时的重试机制
- 频道权限问题的自动检测
- 详细的错误日志记录

### 3. 性能监控
- 监控分类更新的执行时间
- 监控频道发送的响应时间
- 定期分析数据库查询性能

---

**开发者**: Kobee.li  
**版本**: 5.0.0  
**架构**: 简化单类设计  
**更新时间**: 2025-01-19
