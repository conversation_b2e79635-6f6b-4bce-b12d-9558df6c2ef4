# 多语言频道系统 API 参考

## 📋 概述

本文档提供多语言频道系统的完整 API 参考，包括所有类、方法和参数的详细说明。

## 🏗️ 核心类

### GirlChannelManager

多语言频道管理器，整合所有频道和分类相关功能。

#### 初始化

```python
from plugins.hazoo.modules.girl_channel_manager import girl_channel_manager

# 使用全局实例（推荐）
manager = girl_channel_manager

# 或创建新实例
manager = GirlChannelManager()
```

## 📊 分类管理 API

### set_classify()

设置女孩分类。

```python
def set_classify(self, girl_id=0, classify_type=0, opt='add')
```

**参数:**
- `girl_id` (int): 女孩ID，0表示所有女孩
- `classify_type` (int): 分类编号 (1-6)，0表示所有分类
- `opt` (str): 操作类型，'add' 或 'delete'

**返回值:** 无

**示例:**
```python
# 添加女孩到好评频道
girl_channel_manager.set_classify(girl_id=123, classify_type=2, opt='add')

# 删除女孩的指定分类
girl_channel_manager.set_classify(girl_id=123, classify_type=2, opt='delete')

# 删除指定分类的所有女孩
girl_channel_manager.set_classify(girl_id=0, classify_type=2, opt='delete')

# 删除指定女孩的所有分类
girl_channel_manager.set_classify(girl_id=123, classify_type=0, opt='delete')
```

### get_classify()

查询女孩分类情况。

```python
def get_classify(self, girl_id=0, classify_type=0)
```

**参数:**
- `girl_id` (int): 女孩ID，0表示所有女孩
- `classify_type` (int): 分类编号 (1-6)，0表示所有分类

**返回值:** `List[Tuple]` - 查询结果列表，每个元组包含 (girl_id, girl_name, type)

**示例:**
```python
# 获取指定女孩的所有分类
result = girl_channel_manager.get_classify(girl_id=123)
# 返回: [(123, '小美', 2), (123, '小美', 5)]

# 获取指定分类的所有女孩
result = girl_channel_manager.get_classify(classify_type=2)
# 返回: [(123, '小美', 2), (124, '小丽', 2)]

# 获取指定女孩的指定分类
result = girl_channel_manager.get_classify(girl_id=123, classify_type=2)
# 返回: [(123, '小美', 2)] 或 []
```

### auto_classify()

自动更新女孩分类。

```python
def auto_classify(self, classify_list=[1, 2, 4, 5, 6])
```

**参数:**
- `classify_list` (List[int]): 要更新的分类编号列表

**返回值:** 无

**分类规则:**
- `1` - 空闲频道：所有在线女孩
- `2` - 好评频道：grade >= 3 的女孩
- `4` - 新女孩频道：前40个新女孩（按ID倒序）
- `5` - 丰满女孩频道：胸围≥C罩杯的女孩
- `6` - 年轻女孩频道：age < 20 的女孩

**示例:**
```python
# 更新所有分类
girl_channel_manager.auto_classify()

# 只更新好评和丰满分类
girl_channel_manager.auto_classify([2, 5])

# 只更新空闲分类
girl_channel_manager.auto_classify([1])
```

## 🎯 频道判断 API

### should_send_to_channel()

判断女孩是否应该发送到指定频道。

```python
def should_send_to_channel(self, girl_id, channel_type)
```

**参数:**
- `girl_id` (int): 女孩ID
- `channel_type` (str): 频道类型 ("main", "avail", "grade", "assess", "new", "boobs", "young")

**返回值:** `bool` - True表示应该发送，False表示不发送

**示例:**
```python
# 判断女孩是否应该发送到好评频道
should_send = girl_channel_manager.should_send_to_channel(123, "grade")

# 判断女孩是否应该发送到主频道
should_send = girl_channel_manager.should_send_to_channel(123, "main")  # 总是返回 True
```

## 🌍 多语言格式化 API

### format_girl_card()

根据语言格式化女孩卡片。

```python
def format_girl_card(self, girl, language)
```

**参数:**
- `girl` (TGirls): 女孩对象
- `language` (str): 语言代码 ("zh_CN", "en_US", "ru_RU", "kk_KZ")

**返回值:** `str` - 格式化后的卡片文本

**示例:**
```python
girl = girls[123]

# 生成中文卡片
card_zh = girl_channel_manager.format_girl_card(girl, "zh_CN")

# 生成英文卡片
card_en = girl_channel_manager.format_girl_card(girl, "en_US")

# 生成俄语卡片
card_ru = girl_channel_manager.format_girl_card(girl, "ru_RU")

# 生成哈萨克语卡片
card_kz = girl_channel_manager.format_girl_card(girl, "kk_KZ")
```

### 私有格式化方法

以下方法为内部使用，不建议直接调用：

- `_format_english_card(girl)` - 格式化英文卡片
- `_format_russian_card(girl)` - 格式化俄语卡片
- `_format_kazakh_card(girl)` - 格式化哈萨克语卡片

## 📤 频道发送 API

### send_girl_to_channels()

发送女孩卡片到匹配的频道。

```python
async def send_girl_to_channels(self, girl)
```

**参数:**
- `girl` (TGirls): 女孩对象

**返回值:** `Dict[str, List[int]]` - 按语言分组的发送结果，包含消息ID列表

**示例:**
```python
girl = girls[123]

# 发送女孩卡片到匹配的频道
results = await girl_channel_manager.send_girl_to_channels(girl)

# 返回示例:
# {
#     "zh_CN": [12345, 12346],  # 中文频道的消息ID
#     "en_US": [12347],         # 英文频道的消息ID
#     "ru_RU": [12348, 12349]   # 俄语频道的消息ID
# }
```

## 🔧 配置管理 API

### load_channels()

加载频道配置。

```python
def load_channels(self)
```

**参数:** 无

**返回值:** 无

**说明:** 从 params 表加载频道配置，格式为 `channel_config.{语言}.{频道类型} = 频道ID`

**示例:**
```python
# 重新加载频道配置
girl_channel_manager.load_channels()
```

## 📋 常量定义

### 分类编号映射

```python
CHANNEL_TYPE_MAPPING = {
    "main": 0,      # 主频道
    "avail": 1,     # 空闲频道
    "grade": 2,     # 好评频道
    "assess": 3,    # 点评频道
    "new": 4,       # 新女孩频道
    "boobs": 5,     # 丰满女孩频道
    "young": 6,     # 年轻女孩频道
}
```

### 支持的语言

```python
SUPPORTED_LANGUAGES = [
    "zh_CN",  # 简体中文
    "en_US",  # 英文
    "ru_RU",  # 俄语
    "kk_KZ"   # 哈萨克语
]
```

## 🔄 集成函数

### handle_status_confirmation_enhanced()

增强版状态确认处理函数，集成多语言频道功能。

```python
async def handle_status_confirmation_enhanced(callback_query: CallbackQuery, status: int)
```

**参数:**
- `callback_query` (CallbackQuery): Telegram 回调查询对象
- `status` (int): 女孩状态 (1=在线)

**返回值:** 无

**说明:** 当状态为在线时，自动发送女孩卡片到匹配的频道

**示例:**
```python
from plugins.hazoo.modules.girl_channel_manager import handle_status_confirmation_enhanced

# 在回调处理中使用
elif data.startswith("confirm_"):
    status = int(data.split("_")[1])
    await handle_status_confirmation_enhanced(callback_query, status)
```

## ⚠️ 错误处理

所有 API 方法都包含完善的错误处理：

- **数据库错误**: 自动记录到系统日志
- **网络错误**: 单个频道失败不影响其他频道
- **参数错误**: 返回空结果或默认值
- **权限错误**: 记录错误日志并继续执行

## 📊 返回值说明

### 分类查询结果格式

```python
# get_classify() 返回格式
[
    (girl_id, girl_name, classify_type),
    (123, '小美', 2),
    (124, '小丽', 2)
]
```

### 频道发送结果格式

```python
# send_girl_to_channels() 返回格式
{
    "language_code": [message_id1, message_id2, ...],
    "zh_CN": [12345, 12346],
    "en_US": [12347]
}
```

## 🔍 调试和日志

所有 API 操作都会记录到系统日志：

- **成功操作**: INFO 级别
- **失败操作**: ERROR 级别
- **详细调试**: DEBUG 级别

查看日志文件获取详细的执行信息和错误诊断。

---

**版本**: 5.0.0  
**API 版本**: 1.0  
**更新时间**: 2025-01-19
