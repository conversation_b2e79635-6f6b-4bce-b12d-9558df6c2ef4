# 调度框架配置迁移指南

## 🎯 迁移概述

本指南帮助您从旧的调度系统迁移到新的 APScheduler 任务级配置同步框架。

## 📋 迁移前准备

### 1. 备份现有配置

```bash
# 备份现有调度相关文件
cp shared/scheduler.py shared/scheduler.py.old
cp -r config/ config_backup/
```

### 2. 环境检查

确保以下环境变量已设置：
```bash
export DB_WRITE_SECTION="hadb_w"
export DB_READ_SECTION="hadb_r"
export SYS_LOG_FILE="./log/log.txt"
```

## 🔄 迁移步骤

### 步骤1：更新调度器代码

新的调度器已经集成任务级配置同步功能，无需额外配置。

### 步骤2：创建配置文件

#### 原有轮询任务映射

| 原任务类型 | 新配置handler | 说明 |
|-----------|---------------|------|
| PUBLIC01 | `update_members` | 更新群成员 |
| PUBLIC02 | `update_contacts` | 更新通讯录 |
| AISENT | `smart_redbag` | 智能红包 |
| DEPCHECK | `check_deposits` | 检查充值 |
| WDCHECK | `check_withdraws` | 检查提现 |
| ZOOHOME01 | `zoo_update` | 动物园更新 |

#### 配置文件示例

```json
{
  "plugin_name": "hazoo",
  "tasks": [
    {
      "id": "update_members_main",
      "name": "主群成员信息更新",
      "handler": "update_members",
      "trigger_type": "interval",
      "trigger_config": {
        "minutes": 30,
        "start_date": "2024-12-20 10:00:00"
      },
      "enabled": true,
      "max_retries": 3,
      "params": {
        "bot_name": "hazoo_admin_bot",
        "chat_list": "hazoo_main,hazoo_support"
      }
    },
    {
      "id": "check_deposits_frequent",
      "name": "充值状态检查",
      "handler": "check_deposits",
      "trigger_type": "interval",
      "trigger_config": {
        "minutes": 5,
        "start_date": "2024-12-20 08:00:00"
      },
      "enabled": true,
      "max_retries": 3,
      "params": {
        "bot_name": "hazoo_finance_bot"
      }
    }
  ]
}
```

### 步骤3：创建处理器文件

在 `plugins/handlers/{plugin_name}.py` 中实现处理器：

```python
# plugins/handlers/hazoo.py
import asyncio
from shared.classes import sys_log

async def update_members(**params):
    """更新群成员信息处理器"""
    bot_name = params.get('bot_name', '')
    chat_list = params.get('chat_list', '')
    
    sys_log.write_log(f"[{bot_name}] 开始更新群成员: {chat_list}")
    
    # 调用您现有的业务逻辑
    # from your_existing_module import poll_client
    # await poll_client([bot_name])
    
    return f"[{bot_name}] 群成员更新完成"

async def check_deposits(**params):
    """检查充值处理器"""
    bot_name = params.get('bot_name', '')
    
    # 调用您现有的业务逻辑
    # from your_existing_module import check_deposits_logic
    # await check_deposits_logic()
    
    return f"[{bot_name}] 充值检查完成"
```

### 步骤4：更新启动文件

```python
# hazoo_admin.py
import asyncio
from shared.classes import sys_log
from shared.scheduler import create_scheduler  # 新增

async def main():
    # 启动调度系统（新增）
    scheduler = create_scheduler("hazoo")
    scheduler.start()
    
    # 您原有的代码保持不变
    sys_log.write_log("启动Hazoo管理机器人")
    # ... 启动客户端、插件等 ...
    
    try:
        while True:
            await asyncio.sleep(60)
    except KeyboardInterrupt:
        scheduler.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔧 配置转换工具

### 自动转换脚本

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
旧配置转换工具
"""

import json
from datetime import datetime

def convert_old_config_to_new(old_tasks, plugin_name):
    """将旧的任务配置转换为新格式"""
    
    # 任务类型映射
    task_mapping = {
        'PUBLIC01': {
            'handler': 'update_members',
            'name': '更新群成员信息',
            'interval_minutes': 30
        },
        'PUBLIC02': {
            'handler': 'update_contacts', 
            'name': '更新通讯录',
            'interval_minutes': 60
        },
        'AISENT': {
            'handler': 'smart_redbag',
            'name': '智能红包发送',
            'interval_minutes': 5
        },
        'DEPCHECK': {
            'handler': 'check_deposits',
            'name': '检查充值',
            'interval_minutes': 5
        },
        'WDCHECK': {
            'handler': 'check_withdraws',
            'name': '检查提现',
            'interval_minutes': 10
        },
        'ZOOHOME01': {
            'handler': 'zoo_update',
            'name': '动物园更新',
            'interval_minutes': 15
        }
    }
    
    new_config = {
        "plugin_name": plugin_name,
        "tasks": []
    }
    
    for old_task in old_tasks:
        task_type = old_task.get('type', '')
        if task_type in task_mapping:
            mapping = task_mapping[task_type]
            
            new_task = {
                "id": f"{mapping['handler']}_{old_task.get('id', 'default')}",
                "name": mapping['name'],
                "handler": mapping['handler'],
                "trigger_type": "interval",
                "trigger_config": {
                    "minutes": mapping['interval_minutes'],
                    "start_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "enabled": old_task.get('enabled', True),
                "max_retries": 3,
                "params": old_task.get('params', {})
            }
            
            new_config['tasks'].append(new_task)
    
    return new_config

# 使用示例
old_tasks = [
    {
        'type': 'PUBLIC01',
        'id': 'main',
        'enabled': True,
        'params': {
            'bot_name': 'hazoo_admin_bot',
            'chat_list': 'hazoo_main,hazoo_support'
        }
    },
    {
        'type': 'DEPCHECK',
        'id': 'frequent',
        'enabled': True,
        'params': {
            'bot_name': 'hazoo_finance_bot'
        }
    }
]

new_config = convert_old_config_to_new(old_tasks, "hazoo")
print(json.dumps(new_config, ensure_ascii=False, indent=2))
```

## ✅ 迁移验证

### 1. 功能验证

```python
# 测试脚本
from shared.scheduler import create_scheduler

def test_scheduler():
    try:
        # 创建调度器
        scheduler = create_scheduler("hazoo")
        print("✅ 调度器创建成功")
        
        # 检查任务加载
        print(f"✅ 加载任务数量: {len(scheduler.tasks)}")
        
        # 检查处理器注册
        print(f"✅ 注册处理器数量: {len(scheduler.handlers)}")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_scheduler()
```

### 2. 配置验证

```bash
# 检查配置文件格式
python -m json.tool config/scheduler_hazoo.json

# 检查语法
python -m py_compile plugins/handlers/hazoo.py
```

### 3. 日志验证

启动应用后检查日志：

```
[hazoo] 开始任务级配置同步
[hazoo] 任务有变化，重新加载: update_members_main
[hazoo] 添加任务到调度器: 主群成员信息更新
[hazoo] 任务级配置同步完成
[hazoo] 调度器已启动
```

## 🚨 常见问题

### 问题1：任务未执行

**原因**：处理器函数未正确注册

**解决**：
1. 检查处理器文件路径：`plugins/handlers/{plugin_name}.py`
2. 确认函数名与配置中的 `handler` 一致
3. 检查函数是否为异步函数（`async def`）

### 问题2：配置同步失败

**原因**：数据库连接问题

**解决**：
1. 检查环境变量设置
2. 确认数据库连接正常
3. 检查 params 表权限

### 问题3：任务重复执行

**原因**：任务ID重复或配置错误

**解决**：
1. 确保任务ID唯一
2. 检查触发器配置
3. 查看调度器日志

## 🎯 迁移后优化

### 1. 性能优化

- 合理设置任务间隔时间
- 避免同时启动大量任务
- 监控系统资源使用

### 2. 监控设置

- 设置日志监控
- 配置任务执行状态监控
- 建立异常告警机制

### 3. 维护策略

- 定期备份配置文件
- 监控哈希值状态
- 建立配置变更流程

## 📋 迁移检查清单

- [ ] 备份现有配置
- [ ] 更新调度器代码
- [ ] 创建新配置文件
- [ ] 实现处理器函数
- [ ] 更新启动文件
- [ ] 功能测试验证
- [ ] 性能监控设置
- [ ] 文档更新完成

完成以上步骤后，您就成功迁移到了新的任务级配置同步框架！
