{"plugin_name": "hazoo", "tasks": [{"id": "update_members_main", "name": "主群成员信息更新", "handler": "update_members", "trigger_type": "interval", "trigger_config": {"minutes": 30, "start_date": "2024-12-20 10:00:00"}, "enabled": true, "max_retries": 3, "params": {"bot_name": "hazoo_admin_bot", "chat_list": "hazoo_main,hazoo_support,hazoo_announcement"}}, {"id": "update_contacts_hourly", "name": "每小时通讯录更新", "handler": "update_contacts", "trigger_type": "interval", "trigger_config": {"hours": 1, "start_date": "2024-12-20 09:00:00"}, "enabled": true, "max_retries": 2, "params": {"bot_name": "hazoo_admin_bot"}}, {"id": "check_deposits_frequent", "name": "充值状态检查", "handler": "check_deposits", "trigger_type": "interval", "trigger_config": {"minutes": 5, "start_date": "2024-12-20 08:00:00"}, "enabled": true, "max_retries": 3, "params": {"bot_name": "hazoo_finance_bot", "alert_threshold": 10}}, {"id": "check_withdraws_regular", "name": "提现状态检查", "handler": "check_withdraws", "trigger_type": "interval", "trigger_config": {"minutes": 10, "start_date": "2024-12-20 08:05:00"}, "enabled": true, "max_retries": 3, "params": {"bot_name": "hazoo_finance_bot"}}, {"id": "smart_redbag_main", "name": "主群智能红包发送", "handler": "smart_redbag", "trigger_type": "interval", "trigger_config": {"minutes": 5, "start_date": "2024-12-20 08:10:00"}, "enabled": true, "max_retries": 5, "params": {"bot_name": "hazoo_redbag_bot", "group_id": "hazoo_main_group", "min_amount": 5, "max_amount": 100, "min_freq": 23, "max_freq": 300}}, {"id": "smart_redbag_vip", "name": "VIP群智能红包发送", "handler": "smart_redbag", "trigger_type": "interval", "trigger_config": {"minutes": 3, "start_date": "2024-12-20 08:15:00"}, "enabled": true, "max_retries": 5, "params": {"bot_name": "hazoo_redbag_bot", "group_id": "hazoo_vip_group", "min_amount": 10, "max_amount": 200, "min_freq": 15, "max_freq": 180}}, {"id": "system_health_check", "name": "系统健康状态监控", "handler": "system_health_check", "trigger_type": "interval", "trigger_config": {"minutes": 15, "start_date": "2024-12-20 08:00:00"}, "enabled": true, "max_instances": 1, "params": {"check_items": ["database", "memory", "disk", "telegram_api"]}}, {"id": "daily_backup", "name": "每日增量备份", "handler": "data_backup", "trigger_type": "cron", "trigger_config": {"hour": 2, "minute": 0}, "enabled": true, "max_retries": 2, "params": {"backup_type": "incremental", "retention_days": 30}}, {"id": "weekly_full_backup", "name": "每周全量备份", "handler": "data_backup", "trigger_type": "cron", "trigger_config": {"day_of_week": "sun", "hour": 1, "minute": 0}, "enabled": true, "max_retries": 3, "params": {"backup_type": "full", "retention_weeks": 12}}, {"id": "daily_performance_report", "name": "每日性能报告", "handler": "performance_report", "trigger_type": "cron", "trigger_config": {"hour": 9, "minute": 0, "day_of_week": "mon-fri"}, "enabled": true, "params": {"report_type": "daily", "recipients": ["admin_channel", "performance_monitor_group"]}}, {"id": "business_hours_notification", "name": "工作时间通知", "handler": "custom_task", "trigger_type": "combining", "trigger_config": {"operator": "and", "triggers": [{"type": "cron", "config": {"day_of_week": "mon-fri"}}, {"type": "cron", "config": {"hour": "9-17", "minute": "0"}}]}, "enabled": true, "params": {"custom_type": "notification", "custom_params": {"message": "工作时间开始，系统正常运行", "channels": ["admin_channel"]}}}], "updated_at": "2024-12-20T10:00:00"}