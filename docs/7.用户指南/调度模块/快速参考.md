# APScheduler 调度框架快速参考

## 🚀 快速开始

### 1. 启动文件集成（2行代码）

```python
from shared.scheduler import create_scheduler

# 在 main() 函数中添加
scheduler = create_scheduler("插件名")
scheduler.start()
```

### 2. 配置文件格式

```json
{
  "plugin_name": "插件名",
  "tasks": [
    {
      "id": "任务ID",
      "name": "任务名称",
      "handler": "处理函数名",
      "trigger_type": "触发器类型",
      "trigger_config": { "触发器配置" },
      "enabled": true,
      "params": { "业务参数" }
    }
  ]
}
```

### 3. 处理器函数

```python
# plugins/handlers/插件名.py
async def 处理函数名(**params):
    """处理器函数"""
    # 获取参数
    param1 = params.get('param1', '默认值')
    
    # 业务逻辑
    # ...
    
    return "执行结果"
```

## 📋 触发器配置

### 间隔触发器（interval）

```json
{
  "trigger_type": "interval",
  "trigger_config": {
    "minutes": 30,                    // 每30分钟
    "start_date": "2024-12-20 10:00:00"  // 开始时间
  }
}
```

### 定时触发器（cron）

```json
{
  "trigger_type": "cron",
  "trigger_config": {
    "hour": 9,                        // 每天9点
    "minute": 0,
    "day_of_week": "mon-fri"          // 工作日
  }
}
```

### 单次触发器（date）

```json
{
  "trigger_type": "date",
  "trigger_config": {
    "run_date": "2024-12-25 10:00:00"  // 指定时间执行一次
  }
}
```

### 组合触发器（combining）

```json
{
  "trigger_type": "combining",
  "trigger_config": {
    "operator": "and",                // and 或 or
    "triggers": [
      {
        "type": "cron",
        "config": {"day_of_week": "mon-fri"}
      },
      {
        "type": "cron", 
        "config": {"hour": "9-17"}
      }
    ]
  }
}
```

## 🔧 常用配置示例

### 群成员更新（每30分钟）

```json
{
  "id": "update_members",
  "name": "更新群成员信息",
  "handler": "update_members",
  "trigger_type": "interval",
  "trigger_config": {
    "minutes": 30,
    "start_date": "2024-12-20 10:00:00"
  },
  "enabled": true,
  "params": {
    "bot_name": "admin_bot",
    "chat_list": "group1,group2"
  }
}
```

### 充值检查（每5分钟）

```json
{
  "id": "check_deposits",
  "name": "检查充值",
  "handler": "check_deposits", 
  "trigger_type": "interval",
  "trigger_config": {
    "minutes": 5,
    "start_date": "2024-12-20 08:00:00"
  },
  "enabled": true,
  "params": {
    "bot_name": "finance_bot"
  }
}
```

### 每日备份（凌晨2点）

```json
{
  "id": "daily_backup",
  "name": "每日数据备份",
  "handler": "data_backup",
  "trigger_type": "cron",
  "trigger_config": {
    "hour": 2,
    "minute": 0
  },
  "enabled": true,
  "params": {
    "backup_type": "incremental"
  }
}
```

## 🆕 任务级配置同步

### 哈希存储格式

```
scheduler_hash.插件名.任务ID = "MD5哈希值"

示例：
scheduler_hash.hazoo.update_members = "a1b2c3d4..."
```

### 同步场景

| 场景 | 处理方式 |
|------|----------|
| 新增任务 | 添加到调度器，存储哈希 |
| 修改任务 | 删除旧任务，重新加载，更新哈希 |
| 删除任务 | 从调度器删除，删除哈希记录 |
| 无变化 | 仅加载到内存，跳过调度器操作 |

### 监控日志

```
[插件名] 开始任务级配置同步
[插件名] 任务有变化，重新加载: 任务ID
[插件名] 删除已移除的任务: 任务ID
[插件名] 任务级配置同步完成
```

## 🔍 调试和监控

### 查看哈希状态

```sql
-- 查看所有调度器哈希
SELECT * FROM params WHERE id LIKE 'scheduler_hash.%';

-- 查看特定插件的哈希
SELECT * FROM params WHERE id LIKE 'scheduler_hash.插件名.%';
```

### 查看调度任务

```sql
-- 查看即将执行的任务
SELECT * FROM scheduler_jobs WHERE next_run_time > NOW();
```

### 常见问题排查

| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| 任务未执行 | 处理器未注册 | 检查文件路径和函数名 |
| 配置未同步 | 数据库连接问题 | 检查环境变量和权限 |
| 任务重复执行 | 任务ID重复 | 确保任务ID唯一 |

## 📁 文件结构

```
项目根目录/
├── shared/
│   └── scheduler.py                 # 调度框架核心
├── plugins/
│   └── handlers/
│       └── 插件名.py                # 任务处理器
├── config/
│   └── scheduler_插件名.json        # 调度配置
└── 启动文件.py                      # 主程序
```

## 🎯 最佳实践

### 任务设计

- ✅ 使用有意义的任务ID
- ✅ 设置合理的执行间隔
- ✅ 添加错误处理和重试
- ✅ 使用异步函数

### 配置管理

- ✅ 使用版本控制管理配置
- ✅ 在测试环境验证配置
- ✅ 避免频繁修改任务ID
- ✅ 设置不同的初始时间

### 监控运维

- ✅ 关注同步日志
- ✅ 监控任务执行状态
- ✅ 定期备份配置文件
- ✅ 建立异常告警

## 🔗 相关文档

- [APScheduler调度框架使用指南.md](./APScheduler调度框架使用指南.md) - 完整使用指南
- [任务级配置同步详解.md](./任务级配置同步详解.md) - 配置同步机制详解
- [配置迁移指南.md](./配置迁移指南.md) - 从旧系统迁移指南
- [handlers_样例.py](./handlers_样例.py) - 处理器函数示例
- [scheduler_hazoo_样例.json](./scheduler_hazoo_样例.json) - 配置文件示例
