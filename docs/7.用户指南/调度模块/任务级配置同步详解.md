# 任务级配置同步详解

## 🎯 功能概述

任务级配置同步是 APScheduler 调度框架的核心特性，实现了**以任务为维度**的精细化配置管理。每个任务都有独立的哈希值检测机制，确保只有真正变化的任务才会被重新加载。

## 🔧 工作原理

### 核心机制

```
启动时检查 → 逐个任务哈希比较 → 精确同步处理 → 哈希值更新
     ↓              ↓                ↓            ↓
加载配置文件    MD5哈希计算        增量更新      持久化存储
```

### 哈希存储格式

```
scheduler_hash.{plugin_name}.{task_id} = "MD5哈希值"

实际示例：
scheduler_hash.hazoo.update_members_main = "a1b2c3d4e5f6..."
scheduler_hash.hazoo.check_deposits_frequent = "f6e5d4c3b2a1..."
scheduler_hash.finance.daily_backup = "9e8d7c6b5a49..."
```

## 📋 同步场景详解

### 场景1：新增任务

**配置变化**：
```json
// 原配置
{
  "tasks": [
    {"id": "update_members", "handler": "update_members", ...}
  ]
}

// 新配置 - 新增任务
{
  "tasks": [
    {"id": "update_members", "handler": "update_members", ...},
    {"id": "check_deposits", "handler": "check_deposits", ...}  // 新增
  ]
}
```

**处理流程**：
1. 检测到新任务 `check_deposits`
2. 计算任务哈希值
3. 添加任务到调度器
4. 存储哈希值：`scheduler_hash.hazoo.check_deposits = "新哈希值"`

**日志输出**：
```
[hazoo] 任务有变化，重新加载: check_deposits
[hazoo] 添加任务到调度器: 检查充值
[hazoo] 任务哈希已更新: check_deposits -> f6e5d4c3...
```

### 场景2：修改任务配置

**配置变化**：
```json
// 原配置
{
  "id": "update_members",
  "trigger_config": {"minutes": 30}
}

// 新配置 - 修改间隔时间
{
  "id": "update_members",
  "trigger_config": {"minutes": 60}  // 从30分钟改为60分钟
}
```

**处理流程**：
1. 计算新的任务哈希值
2. 与存储的哈希值比较，发现不同
3. 从调度器删除旧任务
4. 重新加载新任务配置
5. 更新哈希值

**日志输出**：
```
[hazoo] 任务有变化，重新加载: update_members
[hazoo] 从调度器删除任务: update_members
[hazoo] 添加任务到调度器: 更新群成员信息
[hazoo] 任务哈希已更新: update_members -> a1b2c3d4...
```

### 场景3：删除任务

**配置变化**：
```json
// 原配置
{
  "tasks": [
    {"id": "update_members", ...},
    {"id": "old_task", ...}
  ]
}

// 新配置 - 删除任务
{
  "tasks": [
    {"id": "update_members", ...}
    // old_task 被删除
  ]
}
```

**处理流程**：
1. 检测到 `old_task` 不在新配置中
2. 从调度器删除该任务
3. 从内存中删除任务对象
4. 删除哈希记录

**日志输出**：
```
[hazoo] 删除已移除的任务: old_task
[hazoo] 从调度器删除任务: old_task
[hazoo] 任务哈希已删除: old_task
```

### 场景4：任务无变化

**配置变化**：
```json
// 配置完全相同，无任何变化
{
  "id": "update_members",
  "trigger_config": {"minutes": 30}
}
```

**处理流程**：
1. 计算任务哈希值
2. 与存储的哈希值比较，完全相同
3. 仅将任务加载到内存
4. 跳过调度器操作
5. 哈希值保持不变

**日志输出**：
```
[hazoo] 配置无变化，跳过同步
```

## 🔍 哈希计算规则

### 计算方法

```python
def _calculate_task_hash(self, task_data: dict) -> str:
    """计算单个任务的哈希值"""
    # 将任务数据转换为JSON字符串并计算哈希
    task_json = json.dumps(task_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(task_json.encode('utf-8')).hexdigest()
```

### 影响哈希值的字段

以下任何字段的变化都会导致哈希值改变：

- `id` - 任务ID
- `name` - 任务名称  
- `handler` - 处理函数名
- `trigger_type` - 触发器类型
- `trigger_config` - 触发器配置
- `enabled` - 是否启用
- `max_retries` - 最大重试次数
- `max_instances` - 最大并发实例
- `params` - 业务参数

### 哈希值示例

```json
// 任务配置
{
  "id": "update_members",
  "name": "更新群成员信息",
  "handler": "update_members",
  "trigger_type": "interval",
  "trigger_config": {"minutes": 30},
  "enabled": true,
  "params": {"bot_name": "hazoo_admin_bot"}
}

// 对应哈希值
MD5("上述JSON字符串") = "a1b2c3d4e5f6789..."
```

## 📊 监控和调试

### 查看哈希状态

```sql
-- 查看所有调度器哈希
SELECT * FROM params WHERE id LIKE 'scheduler_hash.%';

-- 查看特定插件的哈希
SELECT * FROM params WHERE id LIKE 'scheduler_hash.hazoo.%';

-- 查看特定任务的哈希
SELECT * FROM params WHERE id = 'scheduler_hash.hazoo.update_members';
```

### 日志监控

关注以下关键日志：

```
[插件名] 开始任务级配置同步
[插件名] 任务有变化，重新加载: 任务ID
[插件名] 删除已移除的任务: 任务ID
[插件名] 任务哈希已更新: 任务ID -> 哈希值前8位...
[插件名] 任务级配置同步完成
```

### 故障排除

**问题1：任务重复加载**
- 检查配置文件格式是否正确
- 确认任务ID唯一性
- 查看哈希值是否正常存储

**问题2：任务未同步**
- 检查配置文件路径是否正确
- 确认数据库连接正常
- 查看错误日志

**问题3：哈希值异常**
- 手动清理异常哈希记录
- 重启应用程序重新同步
- 检查 params 表权限

## 🎯 最佳实践

### 1. 配置管理

- **版本控制**：使用 Git 等工具管理配置文件变更
- **测试环境**：在测试环境验证配置变更
- **备份策略**：定期备份配置文件和哈希数据

### 2. 任务设计

- **稳定ID**：避免频繁修改任务ID
- **合理命名**：使用有意义的任务ID和名称
- **参数设计**：合理设计 params 参数结构

### 3. 监控运维

- **日志监控**：关注同步过程日志
- **性能监控**：监控同步耗时
- **异常处理**：建立异常恢复机制

## 🚀 优势总结

1. **精确控制**：每个任务独立管理，互不影响
2. **高效同步**：只处理真正变化的任务
3. **自动清理**：自动删除不再需要的任务和哈希记录
4. **零侵入**：现有代码完全不需要修改
5. **持久化**：哈希值存储在数据库中，程序重启后仍有效
6. **可监控**：完整的日志记录和数据库状态查询

通过任务级配置同步功能，您可以实现精细化的任务管理，提高系统的可维护性和运行效率。
