# APScheduler 调度模块文档

## 📚 文档目录

### 🚀 快速开始

- **[快速参考.md](./快速参考.md)** - 快速上手指南，包含常用配置和示例
- **[APScheduler调度框架使用指南.md](./APScheduler调度框架使用指南.md)** - 完整的框架使用指南

### 🆕 核心特性

- **[任务级配置同步详解.md](./任务级配置同步详解.md)** - 详细介绍任务级配置同步机制
- **[配置迁移指南.md](./配置迁移指南.md)** - 从旧调度系统迁移到新框架的指南

### 📋 示例文件

- **[handlers_样例.py](./handlers_样例.py)** - 任务处理器函数示例代码
- **[scheduler_hazoo_样例.json](./scheduler_hazoo_样例.json)** - 完整的配置文件示例

## 🎯 框架特点

### 核心优势

1. **🆕 任务级配置同步**
   - 每个任务独立的哈希检测
   - 只处理真正变化的任务
   - 自动清理过期任务

2. **极简集成**
   - 只需2行代码集成
   - 零学习成本
   - 完全向后兼容

3. **高性能**
   - 事件驱动调度
   - 异步执行
   - MySQL持久化

4. **易维护**
   - 清晰的文件结构
   - 完整的日志记录
   - 智能错误处理

### 技术架构

```
配置文件 → 任务级哈希检测 → 精确同步 → 异步执行 → MySQL持久化
    ↓           ↓              ↓         ↓           ↓
JSON配置    MD5哈希比较    只更新变化任务  异步函数    hadb_w数据库
           任务级检测      自动清理      高并发执行   任务持久化
```

## 🚀 快速开始

### 1. 启动文件集成

```python
# 在主程序中添加
from shared.scheduler import create_scheduler

async def main():
    scheduler = create_scheduler("插件名")  # 自动配置同步
    scheduler.start()
    
    # 您的原有代码...
```

### 2. 创建配置文件

```json
{
  "plugin_name": "hazoo",
  "tasks": [
    {
      "id": "update_members",
      "name": "更新群成员信息", 
      "handler": "update_members",
      "trigger_type": "interval",
      "trigger_config": {"minutes": 30},
      "enabled": true,
      "params": {"bot_name": "admin_bot"}
    }
  ]
}
```

### 3. 实现处理器

```python
# plugins/handlers/hazoo.py
async def update_members(**params):
    """更新群成员信息"""
    bot_name = params.get('bot_name', '')
    # 您的业务逻辑...
    return f"[{bot_name}] 更新完成"
```

## 📋 支持的触发器类型

| 触发器类型 | 说明 | 示例 |
|-----------|------|------|
| `interval` | 间隔执行 | 每30分钟执行一次 |
| `cron` | 定时执行 | 每天9点执行 |
| `date` | 单次执行 | 指定时间执行一次 |
| `calendarinterval` | 日历间隔 | 每月1号执行 |
| `combining` | 组合触发器 | 工作日的9-17点 |

## 🆕 任务级配置同步

### 同步机制

- **精确检测**：每个任务独立的 MD5 哈希值检测
- **增量更新**：只处理真正变化的任务
- **自动清理**：自动删除配置中已移除的任务
- **哈希持久化**：存储格式 `scheduler_hash.{plugin_name}.{task_id}`

### 同步场景

| 场景 | 处理方式 |
|------|----------|
| 新增任务 | 添加到调度器，存储哈希 |
| 修改任务 | 删除旧任务，重新加载，更新哈希 |
| 删除任务 | 从调度器删除，删除哈希记录 |
| 无变化 | 仅加载到内存，跳过调度器操作 |

## 🔧 常用配置示例

### 群成员更新

```json
{
  "id": "update_members_main",
  "name": "主群成员信息更新",
  "handler": "update_members",
  "trigger_type": "interval",
  "trigger_config": {
    "minutes": 30,
    "start_date": "2024-12-20 10:00:00"
  },
  "enabled": true,
  "params": {
    "bot_name": "hazoo_admin_bot",
    "chat_list": "group1,group2"
  }
}
```

### 充值检查

```json
{
  "id": "check_deposits_frequent",
  "name": "充值状态检查",
  "handler": "check_deposits",
  "trigger_type": "interval",
  "trigger_config": {
    "minutes": 5,
    "start_date": "2024-12-20 08:00:00"
  },
  "enabled": true,
  "params": {
    "bot_name": "finance_bot"
  }
}
```

### 每日备份

```json
{
  "id": "daily_backup",
  "name": "每日数据备份",
  "handler": "data_backup",
  "trigger_type": "cron",
  "trigger_config": {
    "hour": 2,
    "minute": 0
  },
  "enabled": true,
  "params": {
    "backup_type": "incremental"
  }
}
```

## 🔍 监控和调试

### 查看任务状态

```sql
-- 查看即将执行的任务
SELECT * FROM scheduler_jobs WHERE next_run_time > NOW();

-- 查看任务哈希状态
SELECT * FROM params WHERE id LIKE 'scheduler_hash.%';
```

### 关键日志

```
[插件名] 开始任务级配置同步
[插件名] 任务有变化，重新加载: 任务ID
[插件名] 删除已移除的任务: 任务ID
[插件名] 任务级配置同步完成
```

## 🎯 最佳实践

### 任务设计原则

- ✅ 使用有意义的任务ID
- ✅ 设置合理的执行间隔
- ✅ 添加错误处理和重试
- ✅ 使用异步函数

### 配置管理建议

- ✅ 使用版本控制管理配置文件
- ✅ 在测试环境验证配置变更
- ✅ 避免频繁修改任务ID
- ✅ 设置不同的初始时间避免冲突

### 监控运维要点

- ✅ 关注配置同步日志
- ✅ 监控任务执行状态
- ✅ 定期备份配置文件
- ✅ 建立异常告警机制

## 🚨 常见问题

| 问题 | 可能原因 | 解决方法 |
|------|----------|----------|
| 任务未执行 | 处理器未注册 | 检查文件路径和函数名 |
| 配置未同步 | 数据库连接问题 | 检查环境变量和权限 |
| 任务重复执行 | 任务ID重复 | 确保任务ID唯一 |
| 哈希值异常 | 配置格式错误 | 验证JSON格式 |

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看相关文档
2. 检查日志输出
3. 验证配置格式
4. 确认数据库连接

## 🔄 版本更新

### 最新版本特性

- ✅ 任务级配置同步
- ✅ 精确的哈希检测机制
- ✅ 自动清理过期任务
- ✅ 完整的日志记录
- ✅ 零侵入集成方式

---

**开始使用 APScheduler 调度框架，享受强大的任务级配置同步功能！** 🚀
