# 智能特点翻译器系统

## 📋 系统概述

智能特点翻译器是一个基于GoogleTranslator和Telegram翻译API的多语言翻译系统，专门为女孩特点翻译设计，支持中文、英文、俄语、哈萨克语四种语言的自动翻译。

### 🎯 核心特性

- **智能缓存**: 固定映射 + 临时映射 + 自动学习机制
- **数据安全**: 原始数据优先保存，翻译失败不影响主流程
- **高性能**: 固定映射<5ms，临时映射<10ms，实时翻译<5s
- **自动学习**: 高频短文本自动转为固定映射
- **极简集成**: 只需一行代码实例化，直接调用方法使用

### 🏗️ 系统架构

```
Translator (基础翻译服务)
    ├── trans_simple() - GoogleTranslator翻译
    ├── trans_telegram() - Telegram翻译
    └── translate() - 统一接口

FeatureTranslator (智能特点翻译器)
    ├── 数据库映射加载 → 固定映射缓存
    ├── 智能缓存管理 (200条记录限制)
    ├── 自动学习机制 (短文本≤10字，使用>5次转固定映射)
    └── translate_features() - 特点翻译

数据库表
    ├── trans_mapping - 翻译映射表
    └── girls - 女孩信息表(含多语言字段)
```

## 🚀 快速开始

### 1. 基础使用

```python
from plugins.hazoo.common import featureTranslator

# 翻译女孩特点
result = await featureTranslator.translate_features("白皮肤/大胸/性感")
print(result['english'])  # white skin/big breasts/sexy
print(result['russian'])  # белая кожа/большая грудь/сексуальная
print(result['kazakh'])   # ақ тері/үлкен кеуде/секси
```

### 2. 单个文本翻译

```python
# 使用GoogleTranslator
english = await featureTranslator.translate("你好", "en", method="simple")

# 使用Telegram翻译
russian = await featureTranslator.translate("你好", "ru", method="telegram")
```

### 3. 批量翻译

```python
# 批量翻译文本
texts = ["你好", "再见", "谢谢"]
results = await featureTranslator.batch_translate(texts, "en", method="simple")

# 批量翻译特点
features_list = ["白皮肤/性感", "温柔/可爱", "专业/贴心"]
results = await featureTranslator.batch_translate_features(features_list)
```

## 📚 详细文档

- [API参考](./API参考.md) - 完整的API文档
- [技术架构](./技术架构.md) - 系统架构和设计原理
- [部署指南](./部署指南.md) - 安装和配置说明
- [使用示例](./使用示例.md) - 详细的使用案例
- [故障排除](./故障排除.md) - 常见问题和解决方案

## 🔧 支持的语言

| 语言 | 代码 | GoogleTranslator | Telegram |
|------|------|------------------|----------|
| 中文 | zh   | zh-cn           | zh-CN    |
| 英文 | en   | en              | en       |
| 俄语 | ru   | ru              | ru       |
| 哈萨克语 | kz | kk              | kk       |

## 📊 性能指标

| 翻译类型 | 响应时间 | 说明 |
|----------|----------|------|
| 固定映射 | < 5ms    | 数据库预加载的常用特点 |
| 临时映射 | < 10ms   | 内存缓存的短文本 |
| 实时翻译 | < 5s     | GoogleTranslator或Telegram API |

## ⚠️ 注意事项

1. **数据安全**: 原始数据优先保存，翻译失败不影响主业务流程
2. **缓存策略**: 长文本不缓存，短文本智能学习
3. **网络依赖**: 需要稳定的网络连接访问翻译服务
4. **频率限制**: 注意GoogleTranslator的使用频率限制

## 🆕 版本历史

### v1.0.0 (2025-01-27)
- 初始版本发布
- 支持4种语言翻译
- 智能缓存和自动学习机制
- 集成到女孩管理系统
