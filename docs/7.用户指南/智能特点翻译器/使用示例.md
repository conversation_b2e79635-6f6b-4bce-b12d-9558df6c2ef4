# 使用示例

## 📋 基础使用示例

### 1. 导入翻译器

```python
from plugins.hazoo.common import featureTranslator
```

### 2. 翻译女孩特点

```python
async def translate_girl_features_example():
    """翻译女孩特点示例"""
    
    # 基础翻译
    result = await featureTranslator.translate_features("白皮肤/大胸/性感")
    print(f"英文: {result['english']}")  # white skin/big breasts/sexy
    print(f"俄语: {result['russian']}")  # белая кожа/большая грудь/сексуальная
    print(f"哈语: {result['kazakh']}")   # ақ тері/үлкен кеуде/секси
    
    # 空字符串处理
    empty_result = await featureTranslator.translate_features("")
    print(f"空字符串结果: {empty_result}")  # {'english': '', 'russian': '', 'kazakh': ''}
    
    # 单个特点
    single_result = await featureTranslator.translate_features("温柔")
    print(f"单个特点: {single_result}")
```

### 3. 单个文本翻译

```python
async def single_text_translation_example():
    """单个文本翻译示例"""
    
    # 使用GoogleTranslator
    english = await featureTranslator.translate("你好", "en", method="simple")
    print(f"Google翻译: {english}")  # Hello
    
    # 使用Telegram翻译
    russian = await featureTranslator.translate("你好", "ru", method="telegram")
    print(f"Telegram翻译: {russian}")  # Привет
    
    # 翻译到哈萨克语
    kazakh = await featureTranslator.translate("谢谢", "kz", method="simple")
    print(f"哈萨克语: {kazakh}")  # Рахмет
```

### 4. 批量翻译

```python
async def batch_translation_example():
    """批量翻译示例"""
    
    # 批量翻译文本
    texts = ["你好", "再见", "谢谢", "对不起"]
    results = await featureTranslator.batch_translate(texts, "en", method="simple")
    for i, result in enumerate(results):
        print(f"{texts[i]} -> {result}")
    
    # 批量翻译特点
    features_list = [
        "白皮肤/性感",
        "温柔/可爱", 
        "专业/贴心",
        "活泼/开朗"
    ]
    
    batch_results = await featureTranslator.batch_translate_features(features_list)
    for i, result in enumerate(batch_results):
        print(f"特点 {i+1}:")
        print(f"  中文: {features_list[i]}")
        print(f"  英文: {result['english']}")
        print(f"  俄语: {result['russian']}")
        print(f"  哈语: {result['kazakh']}")
```

### 5. 多语言翻译

```python
async def multi_language_translation_example():
    """多语言翻译示例"""
    
    # 翻译到多种语言
    text = "美丽"
    languages = ["en", "ru", "kz"]
    
    result = await featureTranslator.translate_to_multiple_languages(
        text, languages, method="simple"
    )
    
    print(f"'{text}' 的多语言翻译:")
    for lang, translation in result.items():
        print(f"  {lang}: {translation}")
```

## 🏢 业务场景示例

### 1. 女孩录入时自动翻译

```python
async def girl_registration_with_translation():
    """女孩录入时的翻译集成示例"""
    
    # 模拟女孩数据
    girl_data = {
        'name': '小美',
        'special': '白皮肤/大胸/性感/温柔',
        'age': 22,
        # ... 其他字段
    }
    
    try:
        # 1. 先保存原始数据
        girl = await create_girl_record(girl_data)
        print(f"女孩 {girl.name} 基础信息已保存")
        
        # 2. 翻译特点
        if girl.special:
            print("开始翻译特点...")
            translations = await featureTranslator.translate_features(girl.special)
            
            # 3. 更新多语言字段
            update_sql = """
            UPDATE girls 
            SET special_us = %s, special_ru = %s, special_kz = %s 
            WHERE id = %s
            """
            exe_connector.run(update_sql, [
                translations['english'],
                translations['russian'],
                translations['kazakh'],
                girl.id
            ])
            
            print("翻译完成并已保存到数据库")
            print(f"英文特点: {translations['english']}")
            print(f"俄语特点: {translations['russian']}")
            print(f"哈语特点: {translations['kazakh']}")
            
    except Exception as e:
        print(f"处理失败: {e}")
        # 原始数据已保存，翻译失败不影响主流程
```

### 2. 批量翻译现有数据

```python
async def batch_translate_existing_girls():
    """批量翻译现有女孩数据示例"""
    
    try:
        # 查询需要翻译的女孩
        sql = """
        SELECT id, special FROM girls 
        WHERE special IS NOT NULL 
        AND special != '' 
        AND (special_us IS NULL OR special_us = '')
        LIMIT 50
        """
        
        results = read_connector.run(sql)
        if not results:
            print("没有需要翻译的数据")
            return
        
        print(f"找到 {len(results)} 条需要翻译的记录")
        
        success_count = 0
        for row in results:
            girl_id, special = row
            
            try:
                # 翻译特点
                translations = await featureTranslator.translate_features(special)
                
                # 更新数据库
                update_sql = """
                UPDATE girls 
                SET special_us = %s, special_ru = %s, special_kz = %s 
                WHERE id = %s
                """
                exe_connector.run(update_sql, [
                    translations['english'],
                    translations['russian'],
                    translations['kazakh'],
                    girl_id
                ])
                
                success_count += 1
                print(f"女孩 {girl_id} 翻译完成")
                
                # 添加延迟避免频率限制
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"女孩 {girl_id} 翻译失败: {e}")
                continue
        
        print(f"批量翻译完成，成功 {success_count} 条")
        
    except Exception as e:
        print(f"批量翻译失败: {e}")
```

### 3. 实时翻译验证

```python
async def real_time_translation_validation():
    """实时翻译验证示例"""
    
    # 测试数据
    test_features = [
        "白皮肤/大胸",
        "温柔/可爱/专业",
        "性感/热情/活泼/开朗",
        "苗条/高挑/长腿/细腰",
        "这是一个很长的特点描述，用来测试长文本的处理"
    ]
    
    print("开始实时翻译验证...")
    
    for i, features in enumerate(test_features, 1):
        print(f"\n测试 {i}: {features}")
        
        start_time = time.time()
        result = await featureTranslator.translate_features(features)
        end_time = time.time()
        
        print(f"耗时: {(end_time - start_time)*1000:.2f}ms")
        print(f"英文: {result['english']}")
        print(f"俄语: {result['russian']}")
        print(f"哈语: {result['kazakh']}")
```

## 🔧 高级使用示例

### 1. 自定义翻译方法

```python
async def custom_translation_method():
    """自定义翻译方法示例"""
    
    # 创建自定义翻译器
    custom_translator = FeatureTranslator()
    
    # 设置不同的客户端
    if 'edenmanbot' in clients:
        custom_translator.set_client(clients['edenmanbot'])
    
    # 使用不同的翻译方法
    text = "你好世界"
    
    simple_result = await custom_translator.translate(text, "en", method="simple")
    telegram_result = await custom_translator.translate(text, "en", method="telegram")
    
    print(f"Simple方法: {simple_result}")
    print(f"Telegram方法: {telegram_result}")
```

### 2. 缓存状态监控

```python
def monitor_cache_status():
    """缓存状态监控示例"""
    
    cache = featureTranslator._cache
    
    # 统计缓存信息
    total_count = len(cache)
    fixed_count = sum(1 for item in cache.values() if item['type'] == 'fixed')
    temp_count = sum(1 for item in cache.values() if item['type'] == 'temp')
    
    print(f"缓存统计:")
    print(f"  总记录数: {total_count}")
    print(f"  固定映射: {fixed_count}")
    print(f"  临时映射: {temp_count}")
    print(f"  使用率: {total_count/featureTranslator._max_cache_size*100:.1f}%")
    
    # 显示高频临时映射
    temp_mappings = [(k, v) for k, v in cache.items() if v['type'] == 'temp']
    temp_mappings.sort(key=lambda x: x[1]['count'], reverse=True)
    
    print(f"\n高频临时映射 (前5个):")
    for i, (text, info) in enumerate(temp_mappings[:5], 1):
        print(f"  {i}. '{text}' - 使用次数: {info['count']}")
```

### 3. 错误处理示例

```python
async def error_handling_example():
    """错误处理示例"""
    
    # 测试各种错误情况
    test_cases = [
        "",  # 空字符串
        None,  # None值
        "a" * 1000,  # 超长文本
        "正常特点/测试",  # 正常情况
    ]
    
    for test_case in test_cases:
        try:
            print(f"\n测试输入: {repr(test_case)}")
            result = await featureTranslator.translate_features(test_case)
            print(f"翻译结果: {result}")
            
        except Exception as e:
            print(f"发生错误: {e}")
            # 错误处理逻辑
            continue
```

### 4. 性能测试示例

```python
async def performance_test():
    """性能测试示例"""
    
    import time
    
    # 测试数据
    test_features = ["白皮肤/大胸/性感"] * 100
    
    print("开始性能测试...")
    
    # 串行测试
    start_time = time.time()
    for features in test_features:
        await featureTranslator.translate_features(features)
    serial_time = time.time() - start_time
    
    print(f"串行处理100次翻译耗时: {serial_time:.2f}秒")
    
    # 并行测试
    start_time = time.time()
    tasks = [featureTranslator.translate_features(features) for features in test_features]
    await asyncio.gather(*tasks)
    parallel_time = time.time() - start_time
    
    print(f"并行处理100次翻译耗时: {parallel_time:.2f}秒")
    print(f"性能提升: {serial_time/parallel_time:.2f}倍")
```

## 🛠️ 调试和测试

### 1. 单元测试示例

```python
import unittest

class TestFeatureTranslator(unittest.TestCase):
    
    def setUp(self):
        self.translator = FeatureTranslator()
    
    async def test_basic_translation(self):
        """测试基础翻译功能"""
        result = await self.translator.translate_features("白皮肤/性感")
        
        self.assertIn('english', result)
        self.assertIn('russian', result)
        self.assertIn('kazakh', result)
        self.assertNotEqual(result['english'], '')
    
    async def test_empty_input(self):
        """测试空输入处理"""
        result = await self.translator.translate_features("")
        
        expected = {'english': '', 'russian': '', 'kazakh': ''}
        self.assertEqual(result, expected)
    
    async def test_cache_functionality(self):
        """测试缓存功能"""
        # 第一次翻译
        result1 = await self.translator.translate_features("测试")
        
        # 第二次翻译（应该使用缓存）
        result2 = await self.translator.translate_features("测试")
        
        self.assertEqual(result1, result2)

if __name__ == '__main__':
    unittest.main()
```

### 2. 集成测试示例

```python
async def integration_test():
    """集成测试示例"""
    
    print("开始集成测试...")
    
    # 测试数据库连接
    try:
        sql = "SELECT COUNT(*) FROM trans_mapping"
        result = read_connector.run(sql)
        print(f"✅ 数据库连接正常，映射表有 {result[0][0]} 条记录")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    # 测试翻译功能
    try:
        result = await featureTranslator.translate_features("白皮肤/性感")
        print(f"✅ 翻译功能正常: {result['english']}")
    except Exception as e:
        print(f"❌ 翻译功能失败: {e}")
        return
    
    # 测试缓存功能
    try:
        cache_size = len(featureTranslator._cache)
        print(f"✅ 缓存功能正常，当前缓存 {cache_size} 条记录")
    except Exception as e:
        print(f"❌ 缓存功能异常: {e}")
        return
    
    print("✅ 所有集成测试通过")
```

这些示例涵盖了智能特点翻译器的各种使用场景，从基础功能到高级特性，从业务集成到性能测试，为用户提供了全面的使用指导。
