# 技术架构文档

## 📋 系统架构概述

智能特点翻译器采用分层架构设计，包含基础翻译服务层、智能缓存层、业务逻辑层和数据持久化层。

## 🏗️ 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    业务应用层                                │
├─────────────────────────────────────────────────────────────┤
│  girls_manage.py  │  其他业务模块  │  批量翻译任务  │  API接口  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                │
├─────────────────────────────────────────────────────────────┤
│              FeatureTranslator (智能特点翻译器)              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 特点分割    │ │ 缓存管理    │ │ 自动学习    │ │ 批量处理 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    智能缓存层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ 固定映射    │ │ 临时映射    │ │ 缓存管理    │            │
│  │ (数据库)    │ │ (内存)      │ │ (LRU清理)   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│                    基础翻译层                                │
├─────────────────────────────────────────────────────────────┤
│                Translator (基础翻译服务)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ trans_simple│ │trans_telegram│ │ 统一接口    │            │
│  │(GoogleTrans)│ │ (Telegram)  │ │ (translate) │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│                    外部服务层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │GoogleTransla│ │ Telegram    │ │ 数据库      │            │
│  │tor API      │ │ API         │ │ MySQL       │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件

### 1. Translator (基础翻译服务)

**职责:**
- 提供统一的翻译接口
- 封装不同翻译服务的调用
- 处理语言代码转换
- 异步执行和错误处理

**关键特性:**
- GoogleTranslator实例复用
- 线程池异步执行
- 自动降级机制
- 语言代码映射

### 2. FeatureTranslator (智能特点翻译器)

**职责:**
- 继承基础翻译功能
- 实现智能缓存策略
- 提供自动学习机制
- 处理特点分割和组合

**关键特性:**
- 三级缓存策略
- 自动学习算法
- 批量处理优化
- 缓存大小管理

## 💾 数据存储设计

### 数据库表结构

#### trans_mapping (翻译映射表)
```sql
CREATE TABLE trans_mapping (
    cn VARCHAR(255) PRIMARY KEY COMMENT '中文',
    us VARCHAR(255) NOT NULL DEFAULT '' COMMENT '英文',
    ru VARCHAR(255) NOT NULL DEFAULT '' COMMENT '俄语',
    kz VARCHAR(255) NOT NULL DEFAULT '' COMMENT '哈萨克语',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);
```

#### girls (女孩信息表 - 多语言字段)
```sql
ALTER TABLE girls 
ADD COLUMN special_us VARCHAR(500) DEFAULT '' COMMENT '英文特点';
ADD COLUMN special_ru VARCHAR(500) DEFAULT '' COMMENT '俄语特点';
ADD COLUMN special_kz VARCHAR(500) DEFAULT '' COMMENT '哈萨克语特点';
```

### 缓存结构

```python
cache = {
    '白皮肤': {
        'en': 'white skin',
        'ru': 'белая кожа',
        'kz': 'ақ тері',
        'type': 'fixed',    # fixed/temp
        'count': 0          # 使用次数
    }
}
```

## 🧠 智能缓存策略

### 三级缓存架构

1. **固定映射缓存**
   - 来源: 数据库预加载
   - 特点: 永久缓存，优先级最高
   - 性能: < 5ms

2. **临时映射缓存**
   - 来源: 短文本翻译结果
   - 条件: 文本长度 ≤ 10字符
   - 特点: 有使用计数，可升级为固定映射

3. **实时翻译**
   - 来源: 外部翻译服务
   - 条件: 缓存未命中
   - 特点: 长文本不缓存

### 自动学习机制

```python
def auto_learning_algorithm():
    if text_length <= 10 and usage_count > 5:
        # 升级为固定映射
        save_to_database(text, translations)
        cache[text]['type'] = 'fixed'
        cache[text]['count'] = 0
```

### 缓存管理策略

```python
def cache_management():
    if cache_size > max_size:
        # 保留所有固定映射
        fixed_mappings = filter(type='fixed')
        
        # 保留2/3高频临时映射
        temp_mappings = filter(type='temp')
        sorted_temp = sort_by_usage_count(temp_mappings)
        keep_count = (max_size - len(fixed_mappings)) * 2 / 3
        
        # 重建缓存
        cache = fixed_mappings + sorted_temp[:keep_count]
```

## 🔄 数据流程

### 翻译请求流程

```
用户请求 → 特点分割 → 逐个翻译 → 结果组合 → 缓存管理 → 返回结果
    ↓
检查缓存 → 固定映射? → 直接返回
    ↓
临时映射? → 更新计数 → 检查升级 → 返回结果
    ↓
实时翻译 → 判断缓存 → 短文本缓存 → 返回结果
```

### 数据插入流程

```
女孩录入 → 原始数据入库 → 翻译特点 → 更新多语言字段
    ↓
翻译失败? → 记录日志 → 不影响主流程
    ↓
翻译成功 → 更新数据库 → 用户提示
```

## ⚡ 性能优化

### 1. 异步执行
- 使用asyncio.gather并行翻译
- 线程池执行同步翻译API
- 避免阻塞事件循环

### 2. 缓存优化
- GoogleTranslator实例复用
- 智能缓存大小管理
- LRU清理策略

### 3. 批量处理
- 批量数据库操作
- 并行翻译任务
- 频率限制控制

### 4. 错误处理
- 优雅降级机制
- 详细错误日志
- 自动重试策略

## 🔒 安全设计

### 1. 数据安全
- 原始数据优先保存
- 翻译失败不影响主流程
- 参数化SQL查询防注入

### 2. 服务可用性
- 多翻译服务支持
- 自动降级机制
- 错误隔离处理

### 3. 资源控制
- 缓存大小限制
- 翻译频率控制
- 内存使用监控

## 📈 扩展性设计

### 1. 新语言支持
- 语言代码映射扩展
- 数据库字段扩展
- 缓存结构兼容

### 2. 新翻译服务
- 翻译方法扩展
- 统一接口兼容
- 配置化管理

### 3. 业务场景扩展
- 继承基础翻译类
- 自定义缓存策略
- 业务逻辑封装

## 🔍 监控和调试

### 1. 日志系统
- 翻译请求日志
- 缓存命中率统计
- 错误详情记录

### 2. 性能监控
- 响应时间统计
- 缓存使用率监控
- 翻译服务状态

### 3. 调试工具
- 缓存状态查看
- 翻译结果验证
- 系统健康检查

## 🎯 设计原则

### 1. 简单性
- 极简集成方式
- 直观的API设计
- 清晰的代码结构

### 2. 可靠性
- 数据安全优先
- 完善错误处理
- 优雅降级机制

### 3. 高性能
- 智能缓存策略
- 异步并行处理
- 资源优化使用

### 4. 可扩展性
- 模块化设计
- 接口标准化
- 配置化管理
