# API参考文档

## Translator 基础翻译服务类

### 构造函数

```python
Translator(client=None)
```

**参数:**
- `client` (可选): Telegram Client实例，用于telegram翻译方法

### 方法

#### trans_simple(text, target_lang)

使用GoogleTranslator进行异步翻译

**参数:**
- `text` (str): 要翻译的文本
- `target_lang` (str): 目标语言代码 ('zh', 'en', 'ru', 'kz')

**返回:** str - 翻译结果，失败时返回原文

**示例:**
```python
result = await translator.trans_simple("你好", "en")
# 返回: "Hello"
```

#### trans_telegram(text, target_lang)

使用Telegram翻译API进行翻译

**参数:**
- `text` (str): 要翻译的文本
- `target_lang` (str): 目标语言代码 ('zh', 'en', 'ru', 'kz')

**返回:** str - 翻译结果，失败时返回原文

**示例:**
```python
result = await translator.trans_telegram("你好", "ru")
# 返回: "Привет"
```

#### translate(text, target_lang, method='simple')

统一翻译接口

**参数:**
- `text` (str): 要翻译的文本
- `target_lang` (str): 目标语言代码
- `method` (str): 翻译方法 ('simple', 'telegram')

**返回:** str - 翻译结果

**示例:**
```python
result = await translator.translate("你好", "en", method="simple")
```

#### batch_translate(texts, target_lang, method='simple')

批量翻译文本列表

**参数:**
- `texts` (list): 要翻译的文本列表
- `target_lang` (str): 目标语言代码
- `method` (str): 翻译方法

**返回:** list - 翻译结果列表

**示例:**
```python
texts = ["你好", "再见", "谢谢"]
results = await translator.batch_translate(texts, "en")
# 返回: ["Hello", "Goodbye", "Thank you"]
```

#### translate_to_multiple_languages(text, target_languages, method='simple')

将文本翻译为多种语言

**参数:**
- `text` (str): 要翻译的文本
- `target_languages` (list): 目标语言代码列表
- `method` (str): 翻译方法

**返回:** dict - 翻译结果字典

**示例:**
```python
result = await translator.translate_to_multiple_languages("你好", ["en", "ru", "kz"])
# 返回: {'en': 'Hello', 'ru': 'Привет', 'kz': 'Сәлем'}
```

## FeatureTranslator 智能特点翻译器

继承自Translator类，提供智能缓存和自动学习功能。

### 构造函数

```python
FeatureTranslator(client=None)
```

**参数:**
- `client` (可选): Telegram Client实例

### 核心方法

#### translate_features(chinese_features)

翻译女孩特点到多语言

**参数:**
- `chinese_features` (str): 中文特点字符串，用"/"分隔

**返回:** dict - 翻译结果字典

```python
{
    'english': '英文特点',
    'russian': '俄语特点', 
    'kazakh': '哈语特点'
}
```

**示例:**
```python
result = await feature_translator.translate_features("白皮肤/大胸/性感")
# 返回: {
#     'english': 'white skin/big breasts/sexy',
#     'russian': 'белая кожа/большая грудь/сексуальная',
#     'kazakh': 'ақ тері/үлкен кеуде/секси'
# }
```

#### batch_translate_features(features_list)

批量翻译特点列表

**参数:**
- `features_list` (list): 中文特点字符串列表

**返回:** list - 翻译结果列表

**示例:**
```python
features = ["白皮肤/性感", "温柔/可爱", "专业/贴心"]
results = await feature_translator.batch_translate_features(features)
```

### 工具方法

#### get_supported_languages(method='simple')

获取支持的语言列表

**返回:** list - 支持的语言代码列表

#### validate_language_code(lang_code, method='simple')

验证语言代码是否支持

**返回:** bool - 是否支持该语言代码

#### set_client(client)

设置Telegram Client

**参数:**
- `client`: Telegram Client实例

#### detect_language(text)

检测文本语言（基础实现）

**返回:** str - 检测到的语言代码

## 错误处理

所有翻译方法都包含完善的错误处理机制：

1. **网络错误**: 自动重试或降级处理
2. **API限制**: 返回原文，记录错误日志
3. **格式错误**: 参数验证，返回默认值
4. **系统异常**: 详细日志记录，优雅降级

## 语言代码映射

### GoogleTranslator (simple方法)
- 中文: `zh-cn`
- 英文: `en`
- 俄语: `ru`
- 哈萨克语: `kk`

### Telegram (telegram方法)
- 中文: `zh-CN`
- 英文: `en`
- 俄语: `ru`
- 哈萨克语: `kk`

## 缓存机制

### 固定映射
- 来源: 数据库trans_mapping表
- 特点: 永久缓存，优先级最高
- 性能: < 5ms

### 临时映射
- 来源: 短文本翻译结果
- 条件: 文本长度 ≤ 10字符
- 学习: 使用次数 > 5次自动转为固定映射
- 性能: < 10ms

### 缓存限制
- 最大记录数: 200条
- 清理策略: 保留2/3临时映射
- 管理时机: 每次translate_features调用后
