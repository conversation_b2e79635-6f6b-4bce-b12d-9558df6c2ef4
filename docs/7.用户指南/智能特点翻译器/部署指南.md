# 部署指南

## 📋 系统要求

### 环境要求
- Python 3.8+
- MySQL 5.7+
- 稳定的网络连接

### 依赖包
- `googletrans==4.0.0rc1` - Google翻译服务
- `pyrogram` - Telegram客户端
- `asyncio` - 异步支持
- `mysql-connector-python` - MySQL连接器

## 🚀 安装步骤

### 1. 安装依赖包

```bash
# 安装Google翻译包
pip install googletrans==4.0.0rc1

# 其他依赖包通常已在项目中安装
pip install pyrogram mysql-connector-python
```

### 2. 数据库准备

#### 创建翻译映射表
```sql
CREATE TABLE IF NOT EXISTS trans_mapping (
    cn VARCHAR(255) PRIMARY KEY COMMENT '中文',
    us VARCHAR(255) NOT NULL DEFAULT '' COMMENT '英文',
    ru VARCHAR(255) NOT NULL DEFAULT '' COMMENT '俄语',
    kz VARCHAR(255) NOT NULL DEFAULT '' COMMENT '哈萨克语',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译映射表';
```

#### 为girls表添加多语言字段
```sql
-- 添加英文特点字段
ALTER TABLE girls 
ADD COLUMN IF NOT EXISTS special_us VARCHAR(500) DEFAULT '' COMMENT '英文特点，用"/"分隔' 
AFTER special;

-- 添加俄语特点字段  
ALTER TABLE girls 
ADD COLUMN IF NOT EXISTS special_ru VARCHAR(500) DEFAULT '' COMMENT '俄语特点，用"/"分隔'
AFTER special_us;

-- 添加哈萨克语特点字段
ALTER TABLE girls 
ADD COLUMN IF NOT EXISTS special_kz VARCHAR(500) DEFAULT '' COMMENT '哈萨克语特点，用"/"分隔'
AFTER special_ru;

-- 添加索引（可选，用于搜索优化）
CREATE INDEX IF NOT EXISTS idx_girls_special_us ON girls(special_us);
CREATE INDEX IF NOT EXISTS idx_girls_special_ru ON girls(special_ru);  
CREATE INDEX IF NOT EXISTS idx_girls_special_kz ON girls(special_kz);
```

#### 初始化常用翻译映射
```sql
INSERT INTO trans_mapping (cn, us, ru, kz) VALUES
-- 外貌特征
('白皮肤', 'white skin', 'белая кожа', 'ақ тері'),
('黑皮肤', 'dark skin', 'темная кожа', 'қара тері'),
('大胸', 'big breasts', 'большая грудь', 'үлкен кеуде'),
('小胸', 'small breasts', 'маленькая грудь', 'кіші кеуде'),
('苗条', 'slim', 'стройная', 'арық'),
('丰满', 'plump', 'пышная', 'толық'),

-- 性格特征
('性感', 'sexy', 'сексуальная', 'секси'),
('可爱', 'cute', 'милая', 'сүйкімді'),
('温柔', 'gentle', 'нежная', 'жұмсақ'),
('热情', 'passionate', 'страстная', 'ыстық'),
('活泼', 'lively', 'живая', 'белсенді'),
('成熟', 'mature', 'зрелая', 'жетілген'),

-- 服务特征
('专业', 'professional', 'профессиональная', 'кәсіби'),
('贴心', 'caring', 'заботливая', 'қамқорлы'),
('耐心', 'patient', 'терпеливая', 'шыдамды'),
('主动', 'proactive', 'активная', 'белсенді')

ON DUPLICATE KEY UPDATE
us = VALUES(us),
ru = VALUES(ru),
kz = VALUES(kz);
```

### 3. 代码部署

#### 3.1 修改 shared/classes2.py
在文件末尾添加Translator类：

```python
# 添加导入
from googletrans import Translator as GoogleTranslator

# 添加Translator类（完整代码见temp/shared_classes2_Translator_最终版.py）
class Translator:
    # ... 完整实现
```

#### 3.2 创建 plugins/hazoo/modules/feature_translator.py
创建新文件，包含FeatureTranslator类：

```python
# 完整代码见temp/hazoo_modules_feature_translator_最终版.py
from shared.classes2 import Translator, sys_log, read_connector, exe_connector

class FeatureTranslator(Translator):
    # ... 完整实现
```

#### 3.3 修改 plugins/hazoo/common.py
在文件末尾添加实例化代码：

```python
# 导入翻译器类
from .modules.feature_translator import FeatureTranslator

# 实例化FeatureTranslator（使用ayanroom客户端）
try:
    featureTranslator = FeatureTranslator(clients['ayanroom'])
except (KeyError, NameError):
    # 在测试环境或clients未初始化时，使用None作为client
    featureTranslator = FeatureTranslator(None)
    sys_log.warning_log("clients['ayanroom']不可用，翻译器将使用simple方法")
```

#### 3.4 修改 plugins/hazoo/modules/girls_manage.py
集成翻译功能到女孩管理：

```python
# 添加导入
from ..common import featureTranslator

# 修改handle_complete_input函数
# 在创建女孩记录成功后，添加翻译逻辑
if result[0]:
    girl = result[1]
    
    # 原始数据插入成功后，再翻译多语言字段
    if girl.special and girl.special.strip():
        try:
            # 翻译特点
            translations = await featureTranslator.translate_features(girl.special)
            
            # 更新数据库多语言字段
            update_sql = """
            UPDATE girls 
            SET special_us = %s, special_ru = %s, special_kz = %s 
            WHERE id = %s
            """
            exe_connector.run(update_sql, [
                translations.get('english', ''),
                translations.get('russian', ''),
                translations.get('kazakh', ''),
                girl.id
            ])
        except Exception as e:
            sys_log.error_log(f"翻译失败，但原始数据已保存: {e}")
```

## 🔧 配置说明

### 翻译服务配置

#### GoogleTranslator配置
- 无需额外配置，使用默认设置
- 注意频率限制，建议添加延迟控制

#### Telegram翻译配置
- 需要有效的Telegram客户端
- 确保客户端有翻译权限

### 缓存配置

```python
# 在FeatureTranslator.__init__中可调整
self._max_cache_size = 200  # 最大缓存记录数
self._temp_mapping_threshold = 5  # 临时映射转固定映射的阈值
self._short_text_length = 10  # 短文本长度阈值
```

## ✅ 部署验证

### 1. 基础功能测试

```python
# 创建测试脚本
import asyncio
from plugins.hazoo.common import featureTranslator

async def test_translation():
    # 测试特点翻译
    result = await featureTranslator.translate_features("白皮肤/大胸/性感")
    print(f"翻译结果: {result}")
    
    # 测试单个翻译
    english = await featureTranslator.translate("你好", "en", method="simple")
    print(f"单个翻译: {english}")

# 运行测试
asyncio.run(test_translation())
```

### 2. 数据库连接测试

```sql
-- 检查表结构
DESCRIBE trans_mapping;
DESCRIBE girls;

-- 检查翻译映射数据
SELECT COUNT(*) FROM trans_mapping;
SELECT * FROM trans_mapping LIMIT 5;
```

### 3. 缓存功能测试

```python
# 测试缓存加载
print(f"缓存大小: {len(featureTranslator._cache)}")

# 测试自动学习
for i in range(10):
    await featureTranslator.translate("测试", "en")
```

## 🚨 故障排除

### 常见问题

1. **ImportError: No module named 'googletrans'**
   ```bash
   pip install googletrans==4.0.0rc1
   ```

2. **翻译服务连接失败**
   - 检查网络连接
   - 确认防火墙设置
   - 尝试使用代理

3. **数据库连接错误**
   - 检查数据库配置
   - 确认表结构正确
   - 验证权限设置

4. **缓存加载失败**
   - 检查trans_mapping表是否存在
   - 确认数据库连接正常
   - 查看错误日志

### 日志查看

```python
# 查看翻译相关日志
grep "翻译" /path/to/log/file
grep "translation" /path/to/log/file
grep "FeatureTranslator" /path/to/log/file
```

## 📊 性能优化

### 1. 数据库优化
- 为常用查询添加索引
- 定期清理过期数据
- 优化查询语句

### 2. 缓存优化
- 根据使用情况调整缓存大小
- 监控缓存命中率
- 定期清理无效缓存

### 3. 网络优化
- 使用连接池
- 添加重试机制
- 实现请求限流

## 🔄 维护指南

### 定期维护任务

1. **数据库维护**
   - 备份翻译映射数据
   - 清理临时数据
   - 优化表结构

2. **缓存维护**
   - 监控缓存使用情况
   - 清理过期缓存
   - 更新常用映射

3. **日志维护**
   - 定期清理日志文件
   - 分析错误模式
   - 优化系统性能

### 升级指南

1. **备份数据**
   - 备份数据库
   - 备份配置文件
   - 记录当前版本

2. **测试新版本**
   - 在测试环境验证
   - 检查兼容性
   - 验证功能完整性

3. **生产部署**
   - 停止相关服务
   - 更新代码
   - 重启服务
   - 验证功能正常
