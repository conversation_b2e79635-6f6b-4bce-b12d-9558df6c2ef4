# 故障排除指南

## 🚨 常见问题及解决方案

### 1. 安装和依赖问题

#### 问题：ImportError: No module named 'googletrans'
**症状：**
```
ImportError: No module named 'googletrans'
```

**解决方案：**
```bash
# 安装正确版本的googletrans
pip install googletrans==4.0.0rc1

# 如果仍有问题，尝试卸载后重新安装
pip uninstall googletrans
pip install googletrans==4.0.0rc1
```

#### 问题：googletrans版本冲突
**症状：**
```
AttributeError: 'NoneType' object has no attribute 'group'
```

**解决方案：**
```bash
# 确保使用正确版本
pip install googletrans==4.0.0rc1

# 检查版本
python -c "import googletrans; print(googletrans.__version__)"
```

### 2. 网络连接问题

#### 问题：Google翻译服务连接失败
**症状：**
```
翻译错误：HTTPSConnectionPool(host='translate.googleapis.com', port=443)
```

**解决方案：**
1. **检查网络连接**
   ```bash
   ping translate.googleapis.com
   ```

2. **配置代理（如需要）**
   ```python
   import os
   os.environ['HTTP_PROXY'] = 'http://proxy.example.com:8080'
   os.environ['HTTPS_PROXY'] = 'http://proxy.example.com:8080'
   ```

3. **使用备用翻译方法**
   ```python
   # 如果Google翻译不可用，使用Telegram翻译
   result = await featureTranslator.translate(text, "en", method="telegram")
   ```

#### 问题：请求频率限制
**症状：**
```
翻译错误：429 Too Many Requests
```

**解决方案：**
```python
import asyncio

# 在批量翻译中添加延迟
async def batch_translate_with_delay():
    for item in items:
        result = await featureTranslator.translate_features(item)
        await asyncio.sleep(1)  # 添加1秒延迟
```

### 3. 数据库相关问题

#### 问题：trans_mapping表不存在
**症状：**
```
Table 'database.trans_mapping' doesn't exist
```

**解决方案：**
```sql
-- 创建翻译映射表
CREATE TABLE IF NOT EXISTS trans_mapping (
    cn VARCHAR(255) PRIMARY KEY COMMENT '中文',
    us VARCHAR(255) NOT NULL DEFAULT '' COMMENT '英文',
    ru VARCHAR(255) NOT NULL DEFAULT '' COMMENT '俄语',
    kz VARCHAR(255) NOT NULL DEFAULT '' COMMENT '哈萨克语',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 问题：girls表缺少多语言字段
**症状：**
```
Unknown column 'special_us' in 'field list'
```

**解决方案：**
```sql
-- 添加多语言字段
ALTER TABLE girls 
ADD COLUMN IF NOT EXISTS special_us VARCHAR(500) DEFAULT '' COMMENT '英文特点' AFTER special;

ALTER TABLE girls 
ADD COLUMN IF NOT EXISTS special_ru VARCHAR(500) DEFAULT '' COMMENT '俄语特点' AFTER special_us;

ALTER TABLE girls 
ADD COLUMN IF NOT EXISTS special_kz VARCHAR(500) DEFAULT '' COMMENT '哈萨克语特点' AFTER special_ru;
```

#### 问题：数据库连接失败
**症状：**
```
Can't connect to MySQL server
```

**解决方案：**
1. **检查数据库服务状态**
   ```bash
   systemctl status mysql
   ```

2. **检查连接配置**
   ```python
   # 验证数据库连接参数
   try:
       result = read_connector.run("SELECT 1")
       print("数据库连接正常")
   except Exception as e:
       print(f"数据库连接失败: {e}")
   ```

### 4. 翻译功能问题

#### 问题：翻译结果为空或错误
**症状：**
```
翻译结果: {'english': '', 'russian': '', 'kazakh': ''}
```

**解决方案：**
1. **检查输入格式**
   ```python
   # 确保输入不为空
   if not chinese_features or not chinese_features.strip():
       return {'english': '', 'russian': '', 'kazakh': ''}
   ```

2. **检查网络连接**
   ```python
   # 测试单个翻译
   result = await featureTranslator.translate("测试", "en", method="simple")
   print(f"测试翻译: {result}")
   ```

3. **查看详细错误日志**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

#### 问题：缓存加载失败
**症状：**
```
加载数据库映射失败: Table 'trans_mapping' doesn't exist
```

**解决方案：**
1. **检查表是否存在**
   ```sql
   SHOW TABLES LIKE 'trans_mapping';
   ```

2. **重新创建表并初始化数据**
   ```sql
   -- 参考部署指南中的建表语句
   ```

3. **手动重新加载缓存**
   ```python
   featureTranslator._load_database_mappings()
   ```

### 5. 性能问题

#### 问题：翻译速度过慢
**症状：**
翻译单个特点需要超过10秒

**解决方案：**
1. **检查缓存命中率**
   ```python
   def check_cache_hit_rate():
       cache = featureTranslator._cache
       print(f"缓存大小: {len(cache)}")
       
       # 测试常用特点是否在缓存中
       common_features = ["白皮肤", "性感", "温柔", "可爱"]
       for feature in common_features:
           if feature in cache:
               print(f"✅ {feature} 在缓存中")
           else:
               print(f"❌ {feature} 不在缓存中")
   ```

2. **优化批量翻译**
   ```python
   # 使用并行翻译
   tasks = [featureTranslator.translate_features(f) for f in features_list]
   results = await asyncio.gather(*tasks)
   ```

3. **增加常用映射**
   ```sql
   -- 添加更多常用特点到数据库
   INSERT INTO trans_mapping (cn, us, ru, kz) VALUES
   ('常用特点1', 'translation1', 'перевод1', 'аударма1'),
   ('常用特点2', 'translation2', 'перевод2', 'аударма2');
   ```

#### 问题：内存使用过高
**症状：**
系统内存占用持续增长

**解决方案：**
1. **检查缓存大小**
   ```python
   print(f"当前缓存大小: {len(featureTranslator._cache)}")
   print(f"最大缓存大小: {featureTranslator._max_cache_size}")
   ```

2. **调整缓存配置**
   ```python
   # 减少缓存大小
   featureTranslator._max_cache_size = 100
   ```

3. **手动清理缓存**
   ```python
   # 清理临时映射
   featureTranslator._cache = {
       k: v for k, v in featureTranslator._cache.items() 
       if v['type'] == 'fixed'
   }
   ```

### 6. 集成问题

#### 问题：clients['ayanroom']不存在
**症状：**
```
KeyError: 'ayanroom'
```

**解决方案：**
1. **检查客户端初始化**
   ```python
   # 在common.py中添加错误处理
   try:
       featureTranslator = FeatureTranslator(clients['ayanroom'])
   except (KeyError, NameError):
       featureTranslator = FeatureTranslator(None)
       sys_log.warning_log("clients['ayanroom']不可用，使用simple方法")
   ```

2. **使用其他可用客户端**
   ```python
   # 尝试其他客户端
   available_clients = ['ayanroom', 'edenmanbot', 'edenguestbot']
   client = None
   for client_name in available_clients:
       if client_name in clients:
           client = clients[client_name]
           break
   
   featureTranslator = FeatureTranslator(client)
   ```

#### 问题：在girls_manage.py中导入失败
**症状：**
```
ImportError: cannot import name 'featureTranslator'
```

**解决方案：**
1. **检查导入路径**
   ```python
   # 确保路径正确
   from ..common import featureTranslator
   ```

2. **检查common.py中的实例化**
   ```python
   # 确保featureTranslator已正确实例化
   print(f"翻译器类型: {type(featureTranslator)}")
   ```

## 🔍 调试工具和方法

### 1. 日志调试

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看翻译相关日志
def debug_translation(text):
    print(f"调试翻译: {text}")
    result = await featureTranslator.translate_features(text)
    print(f"翻译结果: {result}")
    return result
```

### 2. 缓存状态检查

```python
def debug_cache_status():
    """调试缓存状态"""
    cache = featureTranslator._cache
    
    print(f"缓存统计:")
    print(f"  总记录: {len(cache)}")
    print(f"  固定映射: {sum(1 for v in cache.values() if v['type'] == 'fixed')}")
    print(f"  临时映射: {sum(1 for v in cache.values() if v['type'] == 'temp')}")
    
    # 显示部分缓存内容
    print(f"\n缓存内容示例:")
    for i, (key, value) in enumerate(list(cache.items())[:5]):
        print(f"  {key}: {value}")
```

### 3. 网络连接测试

```python
async def test_network_connectivity():
    """测试网络连接"""
    
    # 测试Google翻译
    try:
        result = await featureTranslator.translate("测试", "en", method="simple")
        print(f"✅ Google翻译正常: {result}")
    except Exception as e:
        print(f"❌ Google翻译失败: {e}")
    
    # 测试Telegram翻译
    try:
        result = await featureTranslator.translate("测试", "en", method="telegram")
        print(f"✅ Telegram翻译正常: {result}")
    except Exception as e:
        print(f"❌ Telegram翻译失败: {e}")
```

### 4. 数据库连接测试

```python
def test_database_connection():
    """测试数据库连接"""
    
    try:
        # 测试读连接
        result = read_connector.run("SELECT COUNT(*) FROM trans_mapping")
        print(f"✅ 数据库读取正常，映射表有 {result[0][0]} 条记录")
        
        # 测试写连接
        test_sql = "SELECT 1"
        exe_connector.run(test_sql)
        print(f"✅ 数据库写入连接正常")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
```

## 📞 获取帮助

### 1. 查看系统状态

```python
async def system_health_check():
    """系统健康检查"""
    
    print("🔍 智能翻译系统健康检查")
    print("=" * 40)
    
    # 检查翻译器实例
    try:
        print(f"✅ 翻译器实例: {type(featureTranslator)}")
    except:
        print("❌ 翻译器实例不存在")
    
    # 检查数据库连接
    test_database_connection()
    
    # 检查网络连接
    await test_network_connectivity()
    
    # 检查缓存状态
    debug_cache_status()
    
    print("=" * 40)
    print("健康检查完成")
```

### 2. 收集诊断信息

```python
def collect_diagnostic_info():
    """收集诊断信息"""
    
    import sys
    import platform
    
    info = {
        'python_version': sys.version,
        'platform': platform.platform(),
        'cache_size': len(featureTranslator._cache),
        'max_cache_size': featureTranslator._max_cache_size,
        'translator_type': type(featureTranslator).__name__,
    }
    
    try:
        import googletrans
        info['googletrans_version'] = googletrans.__version__
    except:
        info['googletrans_version'] = 'Not installed'
    
    print("📋 诊断信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    return info
```

### 3. 联系支持

如果以上解决方案都无法解决问题，请：

1. **收集错误信息**
   - 完整的错误堆栈
   - 系统诊断信息
   - 复现步骤

2. **检查日志文件**
   - 查看相关错误日志
   - 记录发生时间

3. **提供环境信息**
   - Python版本
   - 依赖包版本
   - 操作系统信息

通过这些故障排除指南，用户应该能够解决大部分常见问题，确保智能特点翻译器的正常运行。
