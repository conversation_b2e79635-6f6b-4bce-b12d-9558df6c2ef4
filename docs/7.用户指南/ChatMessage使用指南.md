# ChatMessage 使用指南

## 概述

ChatMessage 是一个优化的群消息管理类，用于管理 Telegram 群聊中的消息记录。该类已集成到 `shared/classes2.py` 中，提供了完整的消息增删改查功能。

## 主要特性

- ✅ **参数化查询**：防止 SQL 注入攻击
- ✅ **精确匹配**：避免查询结果的误匹配
- ✅ **类型安全**：使用数据类提供属性访问
- ✅ **统一日志**：集成项目日志系统
- ✅ **向后兼容**：与原有代码完全兼容

## 快速开始

### 导入和初始化

```python
from shared.classes2 import ChatMessage, TChatMessage

# 创建消息管理器
chat_id = -1001234567890  # 群聊ID
chat_msg = ChatMessage(chat_id, "admin")  # 消息类型为 "admin"
```

### 基础操作

#### 1. 添加消息记录

```python
# 添加单条记录（唯一约束）
success = chat_msg.add_row("welcome", welcome_messages)
if success:
    print("添加成功")

# 添加可重复记录
success = chat_msg.add_rows("rules", rule_messages)
```

#### 2. 查询消息记录

```python
# 查询单条记录
rows = chat_msg.get_row("welcome")
if rows:
    msg_obj = rows[0]  # TChatMessage 实例
    print(f"聊天名称: {msg_obj.chat_name}")
    print(f"消息数量: {len(msg_obj.get_msg_ids())}")
    print(f"消息ID列表: {msg_obj.get_msg_ids()}")

# 查询重复记录
rows = chat_msg.get_rows("rules")
for msg_obj in rows:
    print(f"规则记录: {msg_obj.ind}")
```

#### 3. 通过消息ID查询

```python
# 精确匹配消息ID
rows = chat_msg.get_from_msg("123456")
for msg_obj in rows:
    print(f"包含消息ID 123456 的记录: {msg_obj.ind}")
```

#### 4. 删除记录

```python
# 删除数据库记录
chat_msg.del_row("welcome")
chat_msg.del_rows("rules")  # 删除所有 "rules" 相关的重复记录
```

#### 5. 删除 Telegram 消息

```python
# 删除单条消息
await chat_msg.del_msg("welcome", client)

# 删除多条消息
await chat_msg.del_msgs("rules", client)
```

#### 6. 矫正群信息

```python
# 同步数据库与实际群消息
except_patterns = ["重要公告", "置顶消息"]
await chat_msg.correct_msgs(client, except_patterns)
```

## 数据类使用

### TChatMessage 属性

```python
msg_obj = rows[0]  # 从查询结果获取

# 直接访问属性
print(f"聊天ID: {msg_obj.chat_id}")
print(f"聊天名称: {msg_obj.chat_name}")
print(f"消息类型: {msg_obj.kind}")
print(f"索引标识: {msg_obj.ind}")
print(f"原始消息列表: {msg_obj.msg_list}")
print(f"创建时间: {msg_obj.created_at}")
print(f"更新时间: {msg_obj.updated_at}")

# 获取解析后的消息ID列表
msg_ids = msg_obj.get_msg_ids()
print(f"消息ID列表: {msg_ids}")
```

## 高级用法

### 1. 不同消息类型管理

```python
# 管理不同类型的消息
admin_msg = ChatMessage(chat_id, "admin")
notification_msg = ChatMessage(chat_id, "notification")
rules_msg = ChatMessage(chat_id, "rules")

# 各自独立管理
admin_msg.add_row("welcome", welcome_messages)
notification_msg.add_row("announcement", announcement_messages)
rules_msg.add_row("group_rules", rule_messages)
```

### 2. 批量处理

```python
# 批量查询和处理
message_types = ["admin", "notification", "rules"]
for msg_type in message_types:
    chat_msg = ChatMessage(chat_id, msg_type)
    rows = chat_msg.get_row("latest")
    
    for msg_obj in rows:
        print(f"{msg_type}: {len(msg_obj.get_msg_ids())} 条消息")
```

### 3. 错误处理

```python
try:
    success = chat_msg.add_row("test", messages)
    if not success:
        print("添加失败，请检查日志")
        
    rows = chat_msg.get_row("test")
    if not rows:
        print("未找到记录")
        
except Exception as e:
    print(f"操作异常: {e}")
    # 详细错误信息会自动记录到 sys_log
```

## 精确匹配说明

### 重复记录匹配

```python
# 假设数据库中有以下记录：
# ind = "rule|,123,456,"
# ind = "rule_v2|,789,012,"

# 查询 "rule" 的重复记录
rows = chat_msg.get_rows("rule")  # 只匹配 "rule|%" 格式
# 结果：只返回第一条记录，不会误匹配 "rule_v2"
```

### 消息ID匹配

```python
# 精确匹配消息ID "123"
rows = chat_msg.get_from_msg("123")  # 使用 "%,123,%" 格式
# 避免匹配到 "1234" 或 "0123" 等不准确的结果
```

## 性能优化

### SQL 优化

- 使用参数化查询防止 SQL 注入
- 使用 `VALUES()` 函数减少重复参数
- 精确匹配提高查询效率

### 代码优化

```python
# 优化前：手动解析
rows = chat_msg.get_row("test")
if rows:
    row = rows[0]
    msg_list_str = row.msg_list
    if msg_list_str and msg_list_str != ',,':
        msg_ids = [int(x) for x in msg_list_str.strip(',').split(',') if x]

# 优化后：一行搞定
rows = chat_msg.get_row("test")
if rows:
    msg_ids = rows[0].get_msg_ids()
```

## 迁移指南

### 从原版迁移

如果您之前使用的是原版 ChatMessage，无需修改任何代码：

```python
# 原有代码完全兼容
chat_msg = ChatMessage(chat_id, "admin")
chat_msg.add_row("test", messages)
rows = chat_msg.get_row("test")
await chat_msg.del_msg("test", client)
```

### 享受新特性

```python
# 新特性：类型安全的数据访问
rows = chat_msg.get_row("test")
if rows:
    msg_obj = rows[0]  # 现在是 TChatMessage 实例
    # 直接访问属性，IDE 提供自动补全
    print(f"消息数量: {len(msg_obj.get_msg_ids())}")
```

## 注意事项

1. **消息格式**：`msg_list` 字段保持字符串格式 `",123,456,789,"`
2. **精确匹配**：重复记录使用 `|` 分隔符，消息ID查询使用逗号包围
3. **错误日志**：所有错误都会自动记录到 `sys_log.error_log`
4. **异步操作**：删除 Telegram 消息的方法需要使用 `await`

## 常见问题

### Q: 如何区分单条记录和重复记录？

A: 使用不同的方法：
- `add_row()` / `get_row()` - 单条记录
- `add_rows()` / `get_rows()` - 重复记录

### Q: 查询结果为空怎么办？

A: 所有查询方法都返回列表，空结果返回空列表 `[]`，不会抛出异常。

### Q: 如何处理大量消息？

A: 建议分批处理，并在删除操作间添加适当的延时避免触发 FloodWait。

## 示例项目

完整的使用示例请参考 `temp/ChatMessage最终版使用示例.py` 文件。
