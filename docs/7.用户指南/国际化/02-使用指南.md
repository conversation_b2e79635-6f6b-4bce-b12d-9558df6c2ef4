# 国际化使用指南

## 🚀 快速开始

### 1. 环境准备

在机器人启动文件开头设置环境变量：

```python
import os
os.environ['PLUGIN_NAME'] = 'your_bot_name'  # 替换为您的机器人名称
```

### 2. 导入模块

```python
from shared.language import Language

# 创建语言管理器实例
language = Language()
```

### 3. 基础使用

```python
# 设置用户语言
language[user_id] = "zh_CN"

# 获取用户语言
user_lang = language[user_id]

# 翻译文本
text = language.t(user_id, "通用.欢迎", bot_name="MyBot")

# 获取语言菜单
menu = language.get_menu("zh_CN")
```

## 📝 详细使用说明

### 语言设置

#### 设置用户语言

```python
try:
    language[user_id] = "en_US"
    print("语言设置成功")
except ValueError as e:
    print(f"不支持的语言: {e}")
```

#### 获取用户语言

```python
user_lang = language[user_id]  # 返回语言代码，如 "zh_CN"
```

### 文本翻译

#### 基础翻译

```python
# 简单翻译
text = language.t(user_id, "通用.欢迎")

# 带参数翻译
text = language.t(user_id, "通用.欢迎", bot_name="EdenBot")
```

#### 多参数翻译

```python
# JSON中的文本: "用户 {user_name} 有 {count} 条消息，状态：{status}"
text = language.t(
    user_id, 
    "通用.用户状态", 
    user_name="张三", 
    count=5, 
    status="在线"
)
```

#### 嵌套键访问

```python
# 访问 JSON 中的嵌套结构
text = language.t(user_id, "菜单.设置.语言设置")
text = language.t(user_id, "内容.帮助.常见问题")
```

### 语言菜单

#### 获取本地化菜单

```python
# 获取中文版本的语言选择菜单
menu_zh = language.get_menu("zh_CN")
# 结果: {"zh_CN": "🇨🇳 简体中文", "en_US": "🇺🇸 英文", ...}

# 获取英文版本的语言选择菜单
menu_en = language.get_menu("en_US")
# 结果: {"zh_CN": "🇨🇳 Chinese", "en_US": "🇺🇸 English", ...}
```

#### 创建内联键盘

```python
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton

def create_language_keyboard(user_id: int):
    """创建语言选择键盘"""
    current_lang = language[user_id]
    lang_menu = language.get_menu(current_lang)
    
    keyboard = []
    row = []
    
    for lang_code, lang_name in lang_menu.items():
        row.append(InlineKeyboardButton(
            lang_name,
            callback_data=f"lang_{lang_code}"
        ))
        
        if len(row) == 2:  # 每行2个按钮
            keyboard.append(row)
            row = []
    
    if row:  # 添加剩余按钮
        keyboard.append(row)
    
    return InlineKeyboardMarkup(keyboard)
```

## 🤖 机器人集成示例

### 完整的机器人实现

```python
import os
os.environ['PLUGIN_NAME'] = 'mybot'

from pyrogram import Client, filters
from pyrogram.types import Message, CallbackQuery
from shared.language import Language

# 创建语言管理器
language = Language()

@Client.on_message(filters.command("start"))
async def start_command(client, message: Message):
    """处理开始命令"""
    user_id = message.from_user.id
    
    # 检查是否为新用户
    user_lang = language[user_id]
    if user_lang == "zh_CN":  # 默认语言，可能是新用户
        # 显示语言选择
        welcome_text = language.t(user_id, "通用.首次欢迎", bot_name="MyBot")
        keyboard = create_language_keyboard(user_id)
        await message.reply_text(welcome_text, reply_markup=keyboard)
    else:
        # 显示主菜单
        await show_main_menu(message, user_id)

@Client.on_callback_query(filters.regex(r"^lang_"))
async def language_callback(client, callback_query: CallbackQuery):
    """处理语言选择"""
    user_id = callback_query.from_user.id
    lang_code = callback_query.data.replace("lang_", "")
    
    try:
        language[user_id] = lang_code
        success_text = language.t(user_id, "通用.语言已设置")
        await callback_query.answer(success_text, show_alert=True)
        
        # 显示主菜单
        await show_main_menu(callback_query.message, user_id, edit=True)
    except ValueError:
        await callback_query.answer("❌ 不支持的语言", show_alert=True)

async def show_main_menu(message: Message, user_id: int, edit: bool = False):
    """显示主菜单"""
    welcome_text = language.t(user_id, "通用.欢迎", bot_name="MyBot")
    
    keyboard = [
        [
            InlineKeyboardButton(
                language.t(user_id, "菜单.功能1"),
                callback_data="menu_feature1"
            )
        ],
        [
            InlineKeyboardButton(
                "🌍 " + language.t(user_id, "通用.选择语言"),
                callback_data="choose_language"
            )
        ]
    ]
    
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    if edit:
        await message.edit_text(welcome_text, reply_markup=reply_markup)
    else:
        await message.reply_text(welcome_text, reply_markup=reply_markup)
```

## 📁 语言包管理

### 创建新语言包

1. **创建目录结构**

```bash
mkdir -p language/your_bot_name/
```

2. **创建语言文件**

```bash
# 复制现有语言包作为模板
cp language/edenguestbot/zh_CN.json language/your_bot_name/zh_CN.json
```

3. **编辑语言内容**

```json
{
  "通用": {
    "欢迎": "🎉 欢迎使用 {bot_name}！",
    "语言已设置": "✅ 语言已设置为简体中文"
  },
  "菜单": {
    "主菜单": "📋 主菜单",
    "设置": "⚙️ 设置"
  }
}
```

### 语言包最佳实践

#### 键名规范

- **使用中文键名**：直观易懂
- **层级结构**：`"通用.欢迎"` 而不是 `"general_welcome"`
- **一致性**：相同功能使用相同键名

#### 内容规范

- **表情符号**：适当使用表情增强用户体验
- **参数化**：使用 `{参数名}` 实现动态内容
- **简洁明了**：避免过长的文本

#### 文件组织

```
language/your_bot_name/
├── zh_CN.json    # 简体中文（主要语言）
├── en_US.json    # 英文
├── ru_RU.json    # 俄语
└── kk_KZ.json    # 哈萨克语
```

## 🔧 高级功能

### 缓存管理

系统自动管理缓存，当缓存达到500条记录时自动清理：

```python
# 缓存会自动管理，无需手动操作
language[user_id] = "en_US"  # 自动缓存
user_lang = language[user_id]  # 从缓存读取
```

### 错误处理

```python
try:
    language[user_id] = "invalid_lang"
except ValueError as e:
    print(f"语言设置失败: {e}")

try:
    text = language.t(user_id, "不存在的键")
    # 如果键不存在，返回键名本身
    print(text)  # 输出: "不存在的键"
except Exception as e:
    print(f"翻译失败: {e}")
```

### 性能优化

- **批量操作**：避免在循环中频繁设置语言
- **缓存预热**：在机器人启动时预加载常用用户的语言设置
- **异步操作**：所有数据库操作都是异步的

## 🐛 常见问题

### Q: 如何添加新语言？

A: 
1. 在 `SupportedLanguage` 枚举中添加新语言代码
2. 在 `language_info` 中添加语言信息
3. 创建对应的 JSON 语言包文件

### Q: 语言包文件放在哪里？

A: 放在 `language/{PLUGIN_NAME}/` 目录下，其中 `PLUGIN_NAME` 是环境变量中设置的插件名称。

### Q: 如何处理缺失的翻译？

A: 如果翻译键不存在，系统会返回键名本身，并记录错误日志。建议在开发时检查日志确保所有键都有对应翻译。

### Q: 缓存什么时候清理？

A: 当缓存中的用户记录达到500条时，系统会自动清空所有缓存。这是为了防止内存溢出。

### Q: 支持实时语言切换吗？

A: 是的，用户可以随时切换语言，新设置会立即生效并更新缓存。
