#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EdenGuestBot - 支持国际化的示例机器人
"""

import os

# 设置插件名称（必须在导入 Language 之前）
os.environ['PLUGIN_NAME'] = 'edenguestbot'

from plugins.hazoo.common import *
from shared.unix_broker import unix_broker, unix_handler
from shared.language import Language
from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery

# 创建语言管理器实例
language = Language()

# 机器人配置
BOT_NAME = "EdenGuestBot"

# 处理来自edenmanbot的转发消息（保留原有功能）
@unix_handler("forward_message")
async def handle_forward_message(message):
    """处理转发消息并发送给指定用户"""
    try:
        content = message['content']
        text = content['text']
        from_user_id = content['from_user_id']
        from_user_name = content['from_user_name']

        # 构造转发消息
        forward_text = f"来自 {from_user_name} (ID: {from_user_id}) 的消息:\n\n{text}"

        sys_log.write_log(f"edenguestbot收到转发消息: {text}")

        # 发送给指定用户 1914064729
        await clients['edenguestbot'].send_message(
            1914064729,
            forward_text,
            parse_mode=ParseMode.HTML
        )

        sys_log.write_log(f"消息已转发给用户1914064729: {forward_text}")

    except Exception as e:
        sys_log.error_log("处理转发消息异常")

# 国际化功能处理器
@Client.on_message(filters.command("start") & filters.private)
async def start_command(client: Client, message: Message):
    """处理 /start 命令"""
    await handle_start(message)

@Client.on_callback_query(filters.regex(r"^lang_"))
async def language_callback(client: Client, callback_query: CallbackQuery):
    """处理语言选择回调"""
    await handle_language_selection(callback_query)

@Client.on_callback_query(filters.regex(r"^menu_"))
async def menu_callback(client: Client, callback_query: CallbackQuery):
    """处理菜单选择回调"""
    await handle_menu_selection(callback_query)

@Client.on_callback_query(filters.regex("back_to_main"))
async def back_to_main_callback(client: Client, callback_query: CallbackQuery):
    """返回主菜单"""
    await show_main_menu(callback_query.message, callback_query.from_user.id, edit=True)

@Client.on_callback_query(filters.regex("choose_language"))
async def choose_language_callback(client: Client, callback_query: CallbackQuery):
    """重新选择语言"""
    await show_language_menu(callback_query.message, callback_query.from_user.id, edit=True)

# 国际化功能函数
async def handle_start(message: Message):
    """处理开始命令"""
    user_id = message.from_user.id
    user_name = message.from_user.first_name or "用户"

    sys_log.write_log(f"用户 {user_id} ({user_name}) 开始使用机器人")

    try:
        # 尝试获取用户语言，如果是新用户会返回默认语言
        user_lang = language[user_id]

        # 检查用户是否是第一次使用（通过检查数据库中是否有记录）
        # 这里简化处理：如果用户语言是默认语言，显示语言选择
        if user_lang == "zh_CN":
            # 显示首次欢迎和语言选择
            welcome_text = language.t(user_id, "通用.首次欢迎", bot_name=BOT_NAME)
            await show_language_menu(message, user_id, welcome_text=welcome_text)
        else:
            # 用户已设置过语言，直接显示主菜单
            await show_main_menu(message, user_id)

    except Exception as e:
        sys_log.error_log(f"处理开始命令失败: {e}")
        await message.reply_text("❌ 系统错误，请稍后重试")

async def show_language_menu(message: Message, user_id: int, edit: bool = False, welcome_text: str = None):
    """显示语言选择菜单"""
    try:
        # 如果没有提供欢迎文本，使用默认的
        if not welcome_text:
            welcome_text = language.t(user_id, "通用.选择语言")

        # 获取语言菜单（使用用户当前语言显示）
        current_lang = language[user_id]
        lang_menu = language.get_menu(current_lang)

        # 创建内联键盘
        keyboard = []
        row = []

        for lang_code, lang_name in lang_menu.items():
            row.append(InlineKeyboardButton(
                lang_name,
                callback_data=f"lang_{lang_code}"
            ))

            # 每行显示2个语言选项
            if len(row) == 2:
                keyboard.append(row)
                row = []

        # 添加剩余的按钮
        if row:
            keyboard.append(row)

        reply_markup = InlineKeyboardMarkup(keyboard)

        if edit:
            await message.edit_text(welcome_text, reply_markup=reply_markup)
        else:
            await message.reply_text(welcome_text, reply_markup=reply_markup)

    except Exception as e:
        sys_log.error_log(f"显示语言菜单失败: {e}")

async def handle_language_selection(callback_query: CallbackQuery):
    """处理语言选择"""
    user_id = callback_query.from_user.id
    lang_code = callback_query.data.replace("lang_", "")

    try:
        # 设置用户语言
        language[user_id] = lang_code

        # 显示语言设置成功消息
        success_text = language.t(user_id, "通用.语言已设置")
        await callback_query.answer(success_text, show_alert=True)

        sys_log.write_log(f"用户 {user_id} 设置语言为: {lang_code}")

        # 显示主菜单
        await show_main_menu(callback_query.message, user_id, edit=True)

    except ValueError as e:
        await callback_query.answer(f"❌ 不支持的语言: {lang_code}", show_alert=True)
        sys_log.error_log(f"设置语言失败: {e}")
    except Exception as e:
        await callback_query.answer("❌ 设置语言失败", show_alert=True)
        sys_log.error_log(f"处理语言选择失败: {e}")

async def show_main_menu(message: Message, user_id: int, edit: bool = False):
    """显示主菜单"""
    try:
        # 获取欢迎消息
        welcome_text = language.t(user_id, "通用.欢迎", bot_name=BOT_NAME)

        # 创建主菜单键盘
        keyboard = [
            [
                InlineKeyboardButton(
                    language.t(user_id, "菜单.功能1"),
                    callback_data="menu_feature1"
                ),
                InlineKeyboardButton(
                    language.t(user_id, "菜单.功能2"),
                    callback_data="menu_feature2"
                )
            ],
            [
                InlineKeyboardButton(
                    language.t(user_id, "菜单.功能3"),
                    callback_data="menu_feature3"
                )
            ],
            [
                InlineKeyboardButton(
                    language.t(user_id, "菜单.设置"),
                    callback_data="menu_settings"
                ),
                InlineKeyboardButton(
                    language.t(user_id, "菜单.帮助"),
                    callback_data="menu_help"
                )
            ],
            [
                InlineKeyboardButton(
                    language.t(user_id, "菜单.关于"),
                    callback_data="menu_about"
                )
            ],
            [
                InlineKeyboardButton(
                    "🌍 " + language.t(user_id, "通用.选择语言"),
                    callback_data="choose_language"
                )
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        if edit:
            await message.edit_text(welcome_text, reply_markup=reply_markup)
        else:
            await message.reply_text(welcome_text, reply_markup=reply_markup)

    except Exception as e:
        sys_log.error_log(f"显示主菜单失败: {e}")

async def handle_menu_selection(callback_query: CallbackQuery):
    """处理菜单选择"""
    user_id = callback_query.from_user.id
    menu_action = callback_query.data.replace("menu_", "")

    try:
        # 根据菜单选择显示对应内容
        content_map = {
            "feature1": "内容.功能1描述",
            "feature2": "内容.功能2描述",
            "feature3": "内容.功能3描述",
            "settings": "内容.设置描述",
            "help": "内容.帮助描述",
            "about": "内容.关于描述"
        }

        if menu_action in content_map:
            content_key = content_map[menu_action]
            content_text = language.t(user_id, content_key, bot_name=BOT_NAME)

            # 创建返回按钮
            keyboard = [
                [
                    InlineKeyboardButton(
                        language.t(user_id, "通用.返回"),
                        callback_data="back_to_main"
                    )
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await callback_query.message.edit_text(content_text, reply_markup=reply_markup)

            sys_log.write_log(f"用户 {user_id} 查看了 {menu_action}")
        else:
            await callback_query.answer("❌ 未知的菜单选项", show_alert=True)

    except Exception as e:
        sys_log.error_log(f"处理菜单选择失败: {e}")
        await callback_query.answer("❌ 处理失败", show_alert=True)

# # 保留原有的消息处理功能（用于测试）
# @Client.on_message(filters.private & filters.incoming, group=3)
# async def f_on_message(client, message):
#     """原有的消息处理功能"""
#     try:
#         # 添加详细日志
#         sys_log.write_log(f"edenguestbot f_on_message 被触发！")
#         sys_log.write_log(f"消息来源: {message.from_user.id if message.from_user else 'Unknown'}")
#         sys_log.write_log(f"消息内容: {message.text or message.caption or 'No text'}")

#         # 原有的媒体处理逻辑
#         # media = await medias.get_media(client, "11,13")
#         # await client.send_media_group(1914064729, media)
#         # sys_log.write_log(f"edenguestbot 媒体发送完成")

#         std_id = user_to_std(message.from_user.id)
#         await message.reply_text(f"你的标准ID是: {std_id}", quote=False)

#     except Exception as e:
#         sys_log.error_log(f"f_on_message 收到信息异常: {e}")

# # 添加一个更简单的测试处理器（保留原有功能）
# @Client.on_message(group=1)
# async def test_all_messages(client, message):
#     """测试：捕获所有消息"""
#     try:
#         sys_log.write_log(f"🔍 edenguestbot 收到任何消息！类型: {message.chat.type}")
#         sys_log.write_log(f"🔍 消息来源: {message.from_user.id if message.from_user else 'Unknown'}")
#         sys_log.write_log(f"🔍 消息内容: {message.text or message.caption or 'No text'}")
#     except Exception as e:
#         sys_log.error_log(f"test_all_messages 异常: {e}")



