# 国际化 API 参考

## 📚 核心类

### Language 类

主语言管理器，提供完整的国际化功能。

#### 构造函数

```python
language = Language()
```

自动根据环境变量 `PLUGIN_NAME` 加载对应的语言包。

#### 魔术方法

##### `__setitem__(user_id: int, language_code: str)`

设置用户语言。

**参数：**
- `user_id` (int): 用户ID
- `language_code` (str): 语言代码 (zh_CN, en_US, ru_RU, kk_KZ)

**异常：**
- `ValueError`: 不支持的语言代码

**示例：**
```python
language[12345] = "zh_CN"
language[12345] = "en_US"
```

##### `__getitem__(user_id: int) -> str`

获取用户语言。

**参数：**
- `user_id` (int): 用户ID

**返回：**
- `str`: 语言代码

**示例：**
```python
user_lang = language[12345]  # 返回 "zh_CN"
```

#### 实例方法

##### `t(user_id: int, key: str, **kwargs) -> str`

翻译文本。

**参数：**
- `user_id` (int): 用户ID
- `key` (str): 翻译键，支持嵌套（如 "通用.欢迎"）
- `**kwargs`: 格式化参数

**返回：**
- `str`: 翻译后的文本

**示例：**
```python
# 基础翻译
text = language.t(12345, "通用.欢迎")

# 带参数翻译
text = language.t(12345, "通用.欢迎", bot_name="MyBot")

# 多参数翻译
text = language.t(12345, "通用.状态", user="张三", count=5)
```

##### `get_menu(language_code: str = "zh_CN") -> Dict[str, str]`

获取本地化语言选择菜单。

**参数：**
- `language_code` (str): 菜单显示的语言版本，默认 "zh_CN"

**返回：**
- `Dict[str, str]`: 语言代码到本地化显示名称的映射

**示例：**
```python
# 中文版本菜单
menu_zh = language.get_menu("zh_CN")
# 返回: {"zh_CN": "🇨🇳 简体中文", "en_US": "🇺🇸 英文", ...}

# 英文版本菜单
menu_en = language.get_menu("en_US")
# 返回: {"zh_CN": "🇨🇳 Chinese", "en_US": "🇺🇸 English", ...}
```

##### `get_supported_languages() -> list`

获取支持的语言列表。

**返回：**
- `list`: 支持的语言代码列表

**示例：**
```python
langs = language.get_supported_languages()
# 返回: ["zh_CN", "en_US", "ru_RU", "kk_KZ"]
```

### TLanguage 类

数据库语言管理器，处理用户语言的持久化存储。

#### 魔术方法

##### `__setitem__(user_id: int, language_code: str)`

将用户语言设置保存到数据库。

**参数：**
- `user_id` (int): 用户ID
- `language_code` (str): 语言代码

**异常：**
- `Exception`: 数据库操作失败时抛出异常

##### `__getitem__(user_id: int) -> str`

从数据库获取用户语言设置。

**参数：**
- `user_id` (int): 用户ID

**返回：**
- `str`: 语言代码，如果用户不存在则返回默认语言 "zh_CN"

## 🏷️ 枚举和数据类

### SupportedLanguage 枚举

定义支持的语言类型。

```python
class SupportedLanguage(Enum):
    ZH_CN = "zh_CN"  # 简体中文
    EN_US = "en_US"  # 英文
    RU_RU = "ru_RU"  # 俄语
    KK_KZ = "kk_KZ"  # 哈萨克语
```

### LanguageInfo 数据类

语言信息数据结构。

```python
@dataclass
class LanguageInfo:
    code: str    # 语言代码
    name: str    # 语言显示名称
    flag: str    # 旗帜表情符号
```

## 🌐 常量

### SUPPORTED_LANGUAGES

支持的语言常量映射。

```python
SUPPORTED_LANGUAGES = {
    "zh_CN": "🇨🇳 简体中文",
    "en_US": "🇺🇸 English", 
    "ru_RU": "🇷🇺 Русский",
    "kk_KZ": "🇰🇿 Қазақша"
}
```

## 📝 使用模式

### 基础使用模式

```python
import os
os.environ['PLUGIN_NAME'] = 'mybot'

from shared.language import Language

# 创建实例
language = Language()

# 设置语言
language[user_id] = "zh_CN"

# 获取翻译
text = language.t(user_id, "通用.欢迎", bot_name="MyBot")
```

### 机器人集成模式

```python
from pyrogram import Client, filters
from shared.language import Language

language = Language()

@Client.on_message(filters.command("start"))
async def start_handler(client, message):
    user_id = message.from_user.id
    welcome = language.t(user_id, "通用.欢迎")
    await message.reply_text(welcome)
```

### 语言选择模式

```python
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton

def create_language_menu(user_id: int):
    current_lang = language[user_id]
    menu = language.get_menu(current_lang)
    
    keyboard = []
    for code, name in menu.items():
        keyboard.append([InlineKeyboardButton(name, callback_data=f"lang_{code}")])
    
    return InlineKeyboardMarkup(keyboard)
```

## ⚠️ 异常处理

### ValueError

当设置不支持的语言代码时抛出。

```python
try:
    language[user_id] = "invalid_code"
except ValueError as e:
    print(f"不支持的语言: {e}")
```

### 数据库异常

数据库操作失败时的处理。

```python
try:
    language[user_id] = "zh_CN"
except Exception as e:
    print(f"数据库操作失败: {e}")
```

## 🔧 配置要求

### 环境变量

必须在导入模块前设置：

```python
import os
os.environ['PLUGIN_NAME'] = 'your_bot_name'
```

### 数据库表

需要创建 `user_languages` 表：

```sql
CREATE TABLE user_languages (
    user_id BIGINT PRIMARY KEY,
    language_code VARCHAR(10) NOT NULL DEFAULT 'zh_CN',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_language_code (language_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 语言包文件

需要在 `language/{PLUGIN_NAME}/` 目录下创建对应的 JSON 文件：

- `zh_CN.json` - 简体中文
- `en_US.json` - 英文
- `ru_RU.json` - 俄语
- `kk_KZ.json` - 哈萨克语

## 📊 性能特性

### 缓存机制

- **内存缓存**：用户语言设置缓存在内存中
- **自动清理**：达到500条记录时自动清空缓存
- **即时更新**：语言设置变更时立即更新缓存

### 数据库优化

- **索引优化**：language_code 字段建立索引
- **批量操作**：支持高效的批量查询
- **连接复用**：使用现有的数据库连接池

## 🔍 调试和日志

### 日志输出

系统会自动记录以下日志：

- 语言设置操作
- 翻译失败的键名
- 数据库操作异常
- 缓存清理事件

### 调试技巧

```python
# 检查支持的语言
print(language.get_supported_languages())

# 检查用户当前语言
print(f"用户语言: {language[user_id]}")

# 测试翻译
text = language.t(user_id, "测试键名")
print(f"翻译结果: {text}")
```
