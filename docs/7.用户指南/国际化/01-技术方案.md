# 国际化(i18n)技术方案

## 📋 方案概述

本方案实现了一套完整的多语言国际化系统，支持机器人在不同语言环境下为用户提供本地化服务。

### 🎯 设计目标

- **高效性**：内存缓存 + 数据库持久化，减少重复查询
- **无冗余**：统一API设计，避免重复代码
- **易扩展**：添加新语言只需新增JSON文件
- **易维护**：中文键名直观，便于管理和更新

## 🏗️ 架构设计

### 核心组件

```
shared/language.py          # 统一国际化模块
├── TLanguage               # 数据库语言管理器
├── Language                # 主语言管理器
├── SupportedLanguage       # 支持的语言枚举
└── LanguageInfo           # 语言信息数据类

language/{PLUGIN_NAME}/     # 语言包目录
├── zh_CN.json             # 简体中文
├── en_US.json             # 英文
├── ru_RU.json             # 俄语
└── kk_KZ.json             # 哈萨克语
```

### 数据库设计

```sql
CREATE TABLE user_languages (
    user_id BIGINT PRIMARY KEY COMMENT '用户ID',
    language_code VARCHAR(10) NOT NULL DEFAULT 'zh_CN' COMMENT '语言代码',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_language_code (language_code) COMMENT '语言代码索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 🔧 核心类设计

### TLanguage 类

数据库语言管理器，使用魔术方法简化操作：

```python
class TLanguage:
    def __setitem__(self, user_id: int, language_code: str):
        """设置用户语言到数据库"""
        
    def __getitem__(self, user_id: int) -> str:
        """从数据库获取用户语言"""
```

### Language 类

主语言管理器，提供完整的国际化功能：

```python
class Language:
    def __setitem__(self, user_id: int, language_code: str):
        """设置用户语言（带验证和缓存）"""
        
    def __getitem__(self, user_id: int) -> str:
        """获取用户语言（带缓存）"""
        
    def t(self, user_id: int, key: str, **kwargs) -> str:
        """翻译文本"""
        
    def get_menu(self, language_code: str = "zh_CN") -> Dict[str, str]:
        """获取本地化语言菜单"""
```

## 🌍 支持的语言

| 语言代码 | 语言名称 | 旗帜 | 状态 |
|---------|---------|------|------|
| zh_CN   | 简体中文 | 🇨🇳 | ✅ 已实现 |
| en_US   | English | 🇺🇸 | ✅ 已实现 |
| ru_RU   | Русский | 🇷🇺 | ✅ 已实现 |
| kk_KZ   | Қазақша | 🇰🇿 | ✅ 已实现 |

## 📁 语言包结构

### JSON文件格式

```json
{
  "通用": {
    "欢迎": "🎉 欢迎使用 {bot_name}！",
    "语言已设置": "✅ 语言已设置为简体中文",
    "选择语言": "🌍 请选择您的语言：",
    "确认": "✅ 确认",
    "取消": "❌ 取消"
  },
  "菜单": {
    "主菜单": "📋 主菜单",
    "设置": "⚙️ 设置",
    "帮助": "❓ 帮助"
  },
  "内容": {
    "功能描述": "这是功能的详细说明"
  }
}
```

### 键名设计原则

1. **使用中文键名**：直观易懂，便于维护
2. **层级结构**：通过 `.` 分隔实现嵌套访问
3. **参数化支持**：使用 `{参数名}` 实现动态内容
4. **一致性**：相同功能在不同语言中使用相同键名

## ⚡ 性能优化

### 缓存机制

- **内存缓存**：用户语言设置缓存在内存中
- **缓存限制**：500条记录自动清理，防止内存溢出
- **缓存策略**：LRU（最近最少使用）自动清理

### 数据库优化

- **索引优化**：language_code 字段建立索引
- **连接复用**：使用连接池减少数据库连接开销
- **批量操作**：支持批量语言设置（如需要）

## 🔒 安全设计

### 输入验证

- **语言代码验证**：只允许预定义的语言代码
- **SQL注入防护**：使用参数化查询
- **异常处理**：完整的错误处理和日志记录

### 权限控制

- **用户隔离**：每个用户只能设置自己的语言
- **数据完整性**：数据库约束确保数据一致性

## 🚀 扩展性设计

### 添加新语言

1. 在 `SupportedLanguage` 枚举中添加新语言代码
2. 在 `language_info` 中添加语言信息
3. 创建对应的 JSON 语言包文件
4. 在本地化语言名称映射中添加翻译

### 添加新功能

1. 在各语言包的 JSON 文件中添加新的键值对
2. 在代码中使用 `language.t(user_id, "新键名")` 调用
3. 支持参数化：`language.t(user_id, "键名", 参数1="值1")`

## 📊 监控和日志

### 日志记录

- **操作日志**：记录语言设置操作
- **错误日志**：记录异常和错误信息
- **性能日志**：记录缓存命中率和响应时间

### 统计信息

- **语言使用统计**：各语言的用户数量分布
- **缓存性能**：缓存命中率和清理频率
- **错误统计**：错误类型和频率分析

## 🔄 版本兼容性

### 向前兼容

- **键名稳定性**：已有键名不轻易修改
- **默认值处理**：新增键提供合理默认值
- **渐进式升级**：支持逐步迁移到新版本

### 向后兼容

- **数据库结构**：新增字段使用默认值
- **API稳定性**：核心API保持稳定
- **配置兼容**：支持旧版本配置格式
