# 国际化(i18n)文档中心

## 📚 文档概览

本目录包含了完整的国际化系统文档，涵盖技术方案、使用指南、API参考和实施案例。

## 📖 文档结构

### [01-技术方案.md](./01-技术方案.md)
**技术架构和设计方案**

- 🏗️ 架构设计
- 🔧 核心类设计  
- 🌍 支持的语言
- 📁 语言包结构
- ⚡ 性能优化
- 🔒 安全设计
- 🚀 扩展性设计

### [02-使用指南.md](./02-使用指南.md)
**详细的使用说明和示例**

- 🚀 快速开始
- 📝 详细使用说明
- 🤖 机器人集成示例
- 📁 语言包管理
- 🔧 高级功能
- 🐛 常见问题

### [03-API参考.md](./03-API参考.md)
**完整的API文档**

- 📚 核心类
- 🏷️ 枚举和数据类
- 🌐 常量
- 📝 使用模式
- ⚠️ 异常处理
- 🔧 配置要求

### [04-实施案例.md](./04-实施案例.md)
**EdenGuestBot完整实施案例**

- 📋 案例概述
- 🏗️ 项目结构
- 🚀 实施步骤
- 📱 用户交互流程
- 🌍 多语言内容示例
- 📊 实施效果

## 🎯 快速导航

### 🆕 新手入门
1. 阅读 [技术方案](./01-技术方案.md) 了解整体架构
2. 跟随 [使用指南](./02-使用指南.md) 快速开始
3. 参考 [实施案例](./04-实施案例.md) 学习最佳实践

### 👨‍💻 开发者
1. 查看 [API参考](./03-API参考.md) 了解详细接口
2. 参考 [使用指南](./02-使用指南.md) 的高级功能
3. 学习 [实施案例](./04-实施案例.md) 的完整实现

### 🔧 运维人员
1. 了解 [技术方案](./01-技术方案.md) 的性能优化
2. 掌握 [使用指南](./02-使用指南.md) 的语言包管理
3. 参考 [API参考](./03-API参考.md) 的配置要求

## 🌟 核心特性

### ✨ 主要功能
- **多语言支持**：中文、英文、俄语、哈萨克语
- **魔术方法**：`language[user_id] = "zh_CN"` 简化操作
- **本地化菜单**：根据用户语言显示对应菜单
- **缓存优化**：500条记录自动管理
- **实时切换**：支持动态语言切换

### 🎯 设计优势
- **高效性**：内存缓存 + 数据库持久化
- **无冗余**：统一API设计，避免重复代码
- **易扩展**：添加新语言只需新增JSON文件
- **易维护**：中文键名直观，便于管理

## 🚀 快速开始

### 1. 基础使用

```python
import os
os.environ['PLUGIN_NAME'] = 'mybot'

from shared.language import Language

# 创建实例
language = Language()

# 设置语言
language[user_id] = "zh_CN"

# 获取翻译
text = language.t(user_id, "通用.欢迎", bot_name="MyBot")

# 获取菜单
menu = language.get_menu("zh_CN")
```

### 2. 机器人集成

```python
@Client.on_message(filters.command("start"))
async def start_handler(client, message):
    user_id = message.from_user.id
    welcome = language.t(user_id, "通用.欢迎")
    await message.reply_text(welcome)
```

## 📋 支持的语言

| 语言代码 | 语言名称 | 旗帜 | 文件名 |
|---------|---------|------|--------|
| zh_CN   | 简体中文 | 🇨🇳 | zh_CN.json |
| en_US   | English | 🇺🇸 | en_US.json |
| ru_RU   | Русский | 🇷🇺 | ru_RU.json |
| kk_KZ   | Қазақша | 🇰🇿 | kk_KZ.json |

## 🏗️ 项目结构

```
/home/<USER>/Robots/
├── shared/
│   └── language.py                    # 国际化核心模块
├── language/
│   └── {PLUGIN_NAME}/                 # 语言包目录
│       ├── zh_CN.json                 # 简体中文
│       ├── en_US.json                 # 英文
│       ├── ru_RU.json                 # 俄语
│       └── kk_KZ.json                 # 哈萨克语
└── docs/7.用户指南/国际化/            # 文档目录
    ├── README.md                      # 本文档
    ├── 01-技术方案.md                 # 技术方案
    ├── 02-使用指南.md                 # 使用指南
    ├── 03-API参考.md                  # API参考
    └── 04-实施案例.md                 # 实施案例
```

## 🔧 环境要求

### 必需组件
- Python 3.7+
- MySQL 数据库
- Pyrogram 库

### 数据库表
```sql
CREATE TABLE user_languages (
    user_id BIGINT PRIMARY KEY,
    language_code VARCHAR(10) NOT NULL DEFAULT 'zh_CN',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_language_code (language_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 📞 技术支持

### 常见问题
- 查看 [使用指南](./02-使用指南.md) 的常见问题章节
- 参考 [API参考](./03-API参考.md) 的异常处理

### 最佳实践
- 学习 [实施案例](./04-实施案例.md) 的完整实现
- 遵循 [技术方案](./01-技术方案.md) 的设计原则

## 📈 版本历史

### v1.0.0 (当前版本)
- ✅ 完整的多语言支持
- ✅ 魔术方法API设计
- ✅ 缓存优化机制
- ✅ 完整的文档体系
- ✅ EdenGuestBot实施案例

## 🎉 总结

国际化系统为机器人项目提供了完整的多语言解决方案，具有高效、易用、可扩展的特点。通过本文档体系，您可以：

- **快速上手**：跟随使用指南快速集成
- **深入理解**：通过技术方案了解设计思路
- **准确开发**：使用API参考确保正确实现
- **学习最佳实践**：参考实施案例避免常见问题

开始您的国际化之旅吧！🚀
