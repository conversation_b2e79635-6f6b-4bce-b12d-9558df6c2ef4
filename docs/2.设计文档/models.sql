/*
 Navicat Premium Dump SQL

 Source Server         : LA_server
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42-0ubuntu0.24.04.1)
 Source Host           : 127.0.0.1:3306
 Source Schema         : hadb

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42-0ubuntu0.24.04.1)
 File Encoding         : 65001

 Date: 22/07/2025 19:54:08
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for chat_message
-- ----------------------------
DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message`  (
  `chat_id` bigint NOT NULL COMMENT '群组ID',
  `chat_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '群组名称',
  `kind` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '种类',
  `ind` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标识符',
  `msg_list` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '消息列表，格式如 ,1,2,3,',
  `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `created_at` datetime NULL DEFAULT NULL COMMENT '建立时间',
  PRIMARY KEY (`chat_id`, `kind`, `ind`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '群消息管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for chat_user
-- ----------------------------
DROP TABLE IF EXISTS `chat_user`;
CREATE TABLE `chat_user`  (
  `chat_id` bigint NOT NULL COMMENT '群组ID',
  `chat_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '群组名称',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `status` tinyint NOT NULL DEFAULT 8 COMMENT '角色状态：0：creator；1：administrator；2：member；3：left；4：restricted；5：banned；8：unKnow',
  `active_at` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '更新时间',
  `joined_at` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '建立/加入时间',
  PRIMARY KEY (`chat_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '群户关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for chat_user_info
-- ----------------------------
DROP TABLE IF EXISTS `chat_user_info`;
CREATE TABLE `chat_user_info`  (
  `chat_id` bigint NOT NULL COMMENT '群组ID',
  `chat_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '群组名称',
  `record_cnt` int NOT NULL DEFAULT 0 COMMENT '数据表有效成员数',
  `real_all_cnt` int NOT NULL DEFAULT 0 COMMENT '实际总人数，本轮轮询成员数',
  `all_cnt` int NOT NULL DEFAULT 0 COMMENT '群总人数，官方总人数',
  `updated_at` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '最近全更新时间',
  PRIMARY KEY (`chat_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '群户关系信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for chats
-- ----------------------------
DROP TABLE IF EXISTS `chats`;
CREATE TABLE `chats`  (
  `id` bigint NOT NULL COMMENT 'id',
  `type` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型：user/group/supergroup/channel/bot/forum/direct',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内部名称，保证唯一性，原则上使用第一获取的outname，若重复则加id后缀',
  `outname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部名称',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '主题，group/supergroup/channel/forum才有',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字',
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '姓',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '状态：0：no_status(默认)；1：ONLINE；2：OFFLINE；3：RECENTLY；4：LAST_WEEK；5：LAST_MONTH；6：LONG_AGO；7：is_deleted；8：is_restricted；9：is_deactivated',
  `online_date` datetime NOT NULL DEFAULT '2000-01-01 00:00:00' COMMENT '在线时间，最近上线时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '会话表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for clients
-- ----------------------------
DROP TABLE IF EXISTS `clients`;
CREATE TABLE `clients`  (
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '默认使用username，若没有，则使用name等',
  `is_bot` tinyint NOT NULL DEFAULT 1,
  `proxy_ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `proxy_port` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `proxy_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `proxy_pwd` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `api_id` int NOT NULL DEFAULT 0,
  `api_hash` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '默认0,https://my.telegram.org/ 登录注册用户id，当为bot时使用所属机器人信息',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`username`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for girl_classify
-- ----------------------------
DROP TABLE IF EXISTS `girl_classify`;
CREATE TABLE `girl_classify`  (
  `girl_id` int NOT NULL COMMENT '女孩编号，同girls.id',
  `type` tinyint NOT NULL COMMENT '分组类型：1-avail；2-grade；3-assess；4-new；5-boobs；6-young',
  PRIMARY KEY (`girl_id`, `type`) USING BTREE,
  INDEX `idx_girl_id`(`girl_id` ASC) USING BTREE,
  INDEX `idx_type`(`type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '女孩分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for girlc
-- ----------------------------
DROP TABLE IF EXISTS `girlc`;
CREATE TABLE `girlc`  (
  `user_id` bigint NOT NULL COMMENT '用户id',
  `outname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部名称，记录主账号备注的名字',
  `girl_id` int NOT NULL DEFAULT 0 COMMENT '女孩编号，如果取不到则默认0',
  `girl_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '女孩名字，来源于girls.name',
  `status_chat` tinyint NOT NULL DEFAULT 0 COMMENT '私聊状态，0：未聊天；1：待聊；2：待上架：3：已上架；4：不上架',
  `status_bot` tinyint NOT NULL DEFAULT 0 COMMENT '机器人状态，0:未聊天；1:正常可提醒；2:正常不提醒；3：禁止使用',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注动物情况，显示时，girls.note+girlc.note，更新时更新girls.note,本字段为空字符串',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `idx_girl_id`(`girl_id` ASC) USING BTREE,
  INDEX `idx_girl_name`(`girl_name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '女孩中心表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for girls
-- ----------------------------
DROP TABLE IF EXISTS `girls`;
CREATE TABLE `girls`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '编号，自增长',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名字，不能重复，如果输入时重复，则保存为 name_id',
  `country` tinyint NOT NULL DEFAULT 1 COMMENT '国籍，0：混血；1：哈萨克斯坦；2：俄罗斯；3：乌克兰；4：乌兹别克斯坦；5：吉尔吉斯斯坦；6：土库曼斯坦；7：塔吉克斯坦；8：中国',
  `age` int NOT NULL DEFAULT 0 COMMENT '年龄',
  `height` int NOT NULL DEFAULT 0 COMMENT '身高，单位：cm',
  `weight` int NOT NULL DEFAULT 0 COMMENT '重量，单位：kg',
  `boobs` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '胸围，如32A 34B 34C等',
  `baby` tinyint NOT NULL DEFAULT 2 COMMENT '孩子，0：没有；1：有；2：未知',
  `location` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '住址',
  `special` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '特点',
  `special_us` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '英文特点',
  `special_ru` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '俄语特点',
  `special_kz` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '哈萨克斯坦语特点',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '电话',
  `price_in` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '内部价格',
  `price_out` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部价格',
  `picture` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片列表',
  `grade` decimal(4, 2) NOT NULL DEFAULT 0.00 COMMENT '0：无等级(默认)；(0,1.1)：不满意；[1.1,2)：普通；[2,3)：还不错；[3,4)：优质；[4,5]：极品；',
  `assess_cnt` int NOT NULL DEFAULT 0 COMMENT '评价次数',
  `viewed` int NOT NULL DEFAULT 0 COMMENT '查看次数',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态，0：暂缓；1：在线；2：私家推荐；3：下架',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '女孩表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for guests
-- ----------------------------
DROP TABLE IF EXISTS `guests`;
CREATE TABLE `guests`  (
  `chat_id` bigint NOT NULL COMMENT '客户ID',
  `std_id` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标准ID，来源于user_std.std_id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '客户名字',
  `outname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '外部名称，记录主账号备注的名字',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '地址',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '电话',
  `orders` int NOT NULL DEFAULT 0 COMMENT '下单次数',
  `order_girls` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '下单动物列表',
  `status_chat` tinyint NOT NULL DEFAULT 0 COMMENT '聊天状态，0:未聊天；1:正常客户；2:非客户；3:女孩中心',
  `status_bot` tinyint NOT NULL DEFAULT 0 COMMENT '机器人状态，0:未聊天；1:正常可提醒；2:正常不提醒；3：禁止使用',
  `active_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近活跃时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注客人信息',
  PRIMARY KEY (`chat_id`) USING BTREE,
  INDEX `idx_std_id`(`std_id` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ips
-- ----------------------------
DROP TABLE IF EXISTS `ips`;
CREATE TABLE `ips`  (
  `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1085',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `pwd` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT '2000-01-01 00:00:00',
  `checked_at` datetime NOT NULL DEFAULT '2000-01-01 00:00:00',
  `check_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '0:未检查(默认)；1:有效；2:失效',
  `used_at` datetime NOT NULL DEFAULT '2000-01-01 00:00:00',
  `use_cnt` int NOT NULL DEFAULT 0,
  `use_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '0:未使用(默认)；1:临时使用；2:长期使用；3：瞬间使用',
  PRIMARY KEY (`ip`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for medias
-- ----------------------------
DROP TABLE IF EXISTS `medias`;
CREATE TABLE `medias`  (
  `chat_id` bigint NOT NULL COMMENT '频道ID',
  `file_unique_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件唯一ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `id` int NOT NULL COMMENT '标准ID，关联medias_std.id',
  `type` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'photo' COMMENT '媒体类型：photo/video/document',
  `message_id` bigint NOT NULL COMMENT '消息ID',
  `file_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件ID',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`chat_id`, `file_unique_id`, `username`) USING BTREE,
  INDEX `idx_id`(`id` ASC) USING BTREE COMMENT '标准ID索引',
  CONSTRAINT `fk_medias_std_id` FOREIGN KEY (`id`) REFERENCES `medias_std` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '媒体表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for medias_std
-- ----------------------------
DROP TABLE IF EXISTS `medias_std`;
CREATE TABLE `medias_std`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '标准ID，自增长主键',
  `file_unique_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件唯一ID，来自media.file_unique_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_file_unique_id`(`file_unique_id` ASC) USING BTREE COMMENT '文件唯一ID唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '媒体标准表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for params
-- ----------------------------
DROP TABLE IF EXISTS `params`;
CREATE TABLE `params`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string' COMMENT '类型，可选值：string,int,float,bool,datetime,decimal',
  `value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '值',
  `note` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '参数表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for scheduler_jobs
-- ----------------------------
DROP TABLE IF EXISTS `scheduler_jobs`;
CREATE TABLE `scheduler_jobs`  (
  `id` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `next_run_time` double NULL DEFAULT NULL,
  `job_state` blob NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_languages
-- ----------------------------
DROP TABLE IF EXISTS `user_languages`;
CREATE TABLE `user_languages`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `language_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'zh_CN' COMMENT '语言代码',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  INDEX `idx_language_code`(`language_code` ASC) USING BTREE COMMENT '语言代码索引'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户语言设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_std
-- ----------------------------
DROP TABLE IF EXISTS `user_std`;
CREATE TABLE `user_std`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户ID',
  `std_id` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标准ID，格式如0001A',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_std_id`(`std_id` ASC) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_std_id`(`std_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '编号转换表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for scheduler_jobs_view
-- ----------------------------
DROP VIEW IF EXISTS `scheduler_jobs_view`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `scheduler_jobs_view` AS select `scheduler_jobs`.`id` AS `id`,from_unixtime(`scheduler_jobs`.`next_run_time`) AS `next_run_time` from `scheduler_jobs`;

SET FOREIGN_KEY_CHECKS = 1;
