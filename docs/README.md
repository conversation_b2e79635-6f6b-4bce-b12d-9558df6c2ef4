# 文档库

## 概述

本文档库按照功能和用途进行分类组织，便于查找和维护。所有文档优先使用简体中文，采用飞书文档格式和良好的层级结构。

## 文档分类

### [1.需求与规划](./1.需求与规划/README.md)
项目背景、需求分析和规划文档

### [2.设计文档](./2.设计文档/README.md)
系统架构、数据库和UI设计文档

### [3.接口文档](./3.接口文档/README.md)
API接口规范和定义文档

### [4.开发文档](./4.开发文档/README.md)
开发环境、编码规范和实现说明

### [5.测试文档](./5.测试文档/README.md)
测试计划、用例和报告

### [6.部署与运维](./6.部署与运维/README.md)
部署指南和运维手册

### [7.用户指南](./7.用户指南/README.md)
用户手册和操作指南

### [8.经验总结](./8.经验总结/README.md)
项目经验和Bug解决方案记录

### [9.参考资料](./9.参考资料/README.md)
技术参考资料和外部链接

## 文档命名规范

1. **文件名使用简体中文**，应简洁明了，能反映文档内容
2. **根据文档数量**，可以灵活创建子文件夹进行分类
3. **示例**：`用户认证功能.md`、`数据库设计.md`

## 文档格式

1. **所有文档优先使用 Markdown 格式**
2. **图表可使用 PlantUML、Mermaid 等工具生成**
3. **大型设计文档可使用 PDF 格式**
4. **复杂表格可使用 Excel 文件**，但应提供 Markdown 格式的摘要

## 文档更新流程

1. **新增文档**：自动分类新文档，并根据分类规则将其放入相应的文件夹中
2. **每次变更文件**，都要同步本文件夹中的README.md，确保README.md能反映该文件夹的当前状况

## 维护说明

- 本文档库遵循MECE原则（相互独立，完全穷尽）
- 每个分类文件夹都有独立的README.md文件
- 文档更新时需同步更新相关的README.md文件
- 临时文件统一放到项目根目录的temp文件夹下

---

**最后更新时间**：$(date +"%Y-%m-%d")
