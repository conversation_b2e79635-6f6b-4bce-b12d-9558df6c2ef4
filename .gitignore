# Python虚拟环境
.venv/
venv/
env/
ENV/

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
temp/
log/
logs/
*.log
sessions/
media/temp/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
config/secrets.yaml
config/production.yaml
*.key
*.pem
*.crt

# Telegram会话文件
*.session
*.session-journal

# 备份文件
*.bak
*.backup
*.old
