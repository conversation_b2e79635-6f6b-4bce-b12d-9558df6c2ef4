# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'

import os
import sys
import asyncio
import uvloop


# 设置环境变量
# 记得设置 {main_file_name}.mychannel
os.environ['DB_WRITE_SECTION'] = 'hadb_w'
os.environ['DB_READ_SECTION'] = 'hadb_r'
os.environ['CLIENT_LIST'] = 'ayanroom,edenmanbot'
os.environ['SYS_LOG_FILE'] = './log/log_hazoo_admin.txt'
os.environ['PLUGIN_NAME'] = 'hazooadmin'           # 插件名称


async def main():
    from shared.classes2 import sys_log, client_list, clients, read_connector, exe_connector
    from shared.scheduler import create_scheduler
    from shared.unix_broker import unix_broker
    from pyrogram import idle

    scheduler = None
    try:
        sys_log.write_log(f'The {sys.argv[0].split('.')[0]} is begining...')

        # 初始化Unix Socket客户端
        await unix_broker.init_client(os.environ['PLUGIN_NAME'])

        # 先启动所有客户端
        await asyncio.gather(
            *(clients[c].start() for c in client_list),  # 启动所有客户端（这会加载插件）
        )

        # 客户端启动完成后再启动调度系统
        scheduler = create_scheduler(os.environ['PLUGIN_NAME'])
        scheduler.start()

        # 启动Unix Socket监听
        await unix_broker.start_listening()

        await idle() # Telegram事件处理，这个是必须的

        # 停止所有客户端
        await asyncio.gather(*(clients[c].stop() for c in client_list))

        # 关闭连接器
        read_connector.close()
        exe_connector.close()
        
    except KeyboardInterrupt:
        sys_log.write_log("收到停止信号")
    except Exception:
        sys_log.error_log('main异常')
    finally:
        # 确保调度器被正确关闭
        if scheduler:
            scheduler.stop()


if __name__ == "__main__":
    uvloop.run(main())
