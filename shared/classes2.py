# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from shared.func import *
import time
from pyrogram.types import InputMediaPhoto, InputMediaVideo, InputMediaDocument, InputMediaAudio, Message
from typing import Union, List, Optional, Tuple, Dict, Any, Callable
import asyncio
import os
import tempfile
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import math
from pyrogram.types import TermsOfService, User
from pyrogram.errors import (ApiIdInvalid, PhoneNumberInvalid, PhoneCodeInvalid, PhoneCodeExpired, BadRequest,
                             SessionPasswordNeeded, PhoneNumberBanned, PhoneNumberUnoccupied, UserDeactivatedBan,
                             Forbidden, FloodWait)
import random
from googletrans import Translator as GoogleTranslator



@dataclass
class TMedias:
    chat_id: int
    file_unique_id: str
    username: str
    id: int
    type: str
    message_id: int
    file_id: str
    updated_at: datetime.datetime


# 媒体操作类
class Medias:
    def __init__(self, chat_id=None):
        # 图片发送渠道
        if not chat_id:
            main_file_name = os.path.splitext(sys.argv[0])[0]
            self.chat_id = params[f"{main_file_name}.mychannel"]
        else:
            self.chat_id = chat_id

    # 获取或创建媒体标准ID
    def get_id(self, file_unique_id):
        """获取或创建媒体标准ID"""
        try:
            # 尝试插入（如果已存在则忽略）
            sql_insert = "INSERT IGNORE INTO medias_std (file_unique_id) VALUES (%s)"
            exe_connector.run(sql_insert, [file_unique_id])
            # 查询获取ID（无论是新插入的还是已存在的）
            sql_select = "SELECT id FROM medias_std WHERE file_unique_id = %s"
            rst = read_connector.run(sql_select, [file_unique_id])
            return rst[0][0] 
        except Exception:
            sys_log.error_log(f"获取媒体标准ID异常\nsql_insert: {sql_insert}\nsql_select: {sql_select}")
            return 0
    
    # 保存媒体
    async def save(self, client, message):
        try:
            # 筛选接受的媒体类型
            if message.photo is None and message.video is None and message.document is None:
                return None
            # 获取file_unique_id
            if message.photo is not None:
                file_unique_id = message.photo.file_unique_id
            elif message.video is not None:
                file_unique_id = message.video.file_unique_id
            elif message.document is not None:
                file_unique_id = message.document.file_unique_id
            # 基础信息
            username = clients.get_username(client)
            # 已有保存记录
            sql_check = "select * from medias where chat_id=%s and file_unique_id=%s and username=%s"
            rst = read_connector.run(sql_check, [self.chat_id, file_unique_id, username])
            if len(rst) > 0:
                return TMedias(*rst[0])
            # 其他username已有保存记录
            sql_str = "select * from medias where chat_id=%s and file_unique_id=%s order by updated_at desc"
            rst = read_connector.run(sql_str, [self.chat_id, file_unique_id])
            sql_insert = "insert into medias(chat_id,file_unique_id,username,id,type,message_id,file_id,updated_at) " \
            "values(%s,%s,%s,%s,%s,%s,%s,CURRENT_TIMESTAMP())"
            if len(rst) > 0:
                media = TMedias(*rst[0])
                msg = await clients[username].get_messages(media.chat_id, media.message_id)
                # 获取最新file_id
                if msg.photo is not None:
                    file_id = msg.photo.file_id
                elif msg.video is not None:
                    file_id = msg.video.file_id
                elif msg.document is not None:
                    file_id = msg.document.file_id
                # 保存记录
                exe_connector.run(sql_insert, [self.chat_id,file_unique_id,username,media.id,media.type,media.message_id,file_id])
                rst = read_connector.run(sql_check, [self.chat_id, file_unique_id, username])
                return TMedias(*rst[0])
            # 无记录
            message = await message.forward(self.chat_id)
            # 定位媒体类型
            if message.photo is not None:
                type = 'photo'
                file_id = message.photo.file_id
            elif message.video is not None:
                type = 'video'
                file_id = message.video.file_id
            elif message.document is not None:
                type = 'document'
                file_id = message.document.file_id
            id = self.get_id(file_unique_id)
            # 保存记录
            exe_connector.run(sql_insert, [self.chat_id,file_unique_id,username,id,type,message.id,file_id])
            rst = read_connector.run(sql_check, [self.chat_id, file_unique_id, username])
            return TMedias(*rst[0])
        except Exception:
            sys_log.error_log(f"Medias.save 执行报错")

    # 保存媒体组
    async def save_media(self, client, message):
        try:
            # 确认是媒体组
            if message.media_group_id is None:
                sys_log.error_log(f"Medias.save_media 不是媒体组消息: {message.id}")
                return None
            # 无记录
            messages = await message.copy_media_group(self.chat_id)
            medias_ids = []
            username = clients.get_username(client)
            for message in messages:
                # 定位媒体类型
                if message.photo is not None:
                    file_unique_id = message.photo.file_unique_id
                    type = 'photo'
                    file_id = message.photo.file_id
                elif message.video is not None:
                    file_unique_id = message.video.file_unique_id
                    type = 'video'
                    file_id = message.video.file_id
                elif message.document is not None:
                    file_unique_id = message.document.file_unique_id
                    type = 'document'
                    file_id = message.document.file_id
                else:
                    continue
                # 保存记录
                id = self.get_id(file_unique_id)
                sql_str = "insert into medias(chat_id,file_unique_id,username,id,type,message_id,file_id,updated_at) " \
                "values(%s,%s,%s,%s,%s,%s,%s,CURRENT_TIMESTAMP()) on duplicate key update file_id=values(file_id), updated_at=CURRENT_TIMESTAMP()"
                exe_connector.run(sql_str, [self.chat_id,file_unique_id,username,id,type,message.id,file_id])
                sql_str = "select * from medias where chat_id=%s and file_unique_id=%s and username=%s"
                rst = read_connector.run(sql_str, [self.chat_id, file_unique_id, username])
                medias_ids.append(rst[0][3])
            return medias_ids
        except Exception:
            sys_log.error_log(f"Medias.save_media 执行报错")

    # 下载媒体
    async def download(self, client, id):
        try:
            username = clients.get_username(client)
            sql_select = "select * from medias where chat_id=%s and username=%s and id=%s"
            rst = read_connector.run(sql_select, [self.chat_id, username, id])
            if len(rst) == 0:
                if not await self.regets(client, str(id)):
                    return
                rst = read_connector.run(sql_select, [self.chat_id, username, id])
            media = TMedias(*rst[0])
            message = await client.get_messages(media.chat_id, media.message_id)
            # 先下载到临时位置，让 Pyrogram 自动处理扩展名
            temp_dir = f"media/{media.type}/"
            temp_file = await client.download_media(message, temp_dir)
            # 提取真实扩展名
            file_ext = temp_file.split('.')[-1]
            # 重命名为期望的文件名
            final_file = f"media/{media.type}/{media.id}.{file_ext}"
            os.rename(temp_file, final_file)
            return final_file
        except Exception:
            sys_log.error_log(f"下载媒体报错: {id}")

    # 上传媒体
    async def upload(self, client, file_dir):
        try:
            type = file_dir.split('/')[-2]
            if type == 'photo':
                send_msg = await client.send_photo(self.chat_id, photo=file_dir, disable_notification=True)
                file_unique_id = send_msg.photo.file_unique_id
                file_id = send_msg.photo.file_id
            elif type == 'video':
                send_msg = await client.send_video(self.chat_id, video=file_dir, disable_notification=True)
                file_unique_id = send_msg.video.file_unique_id
                file_id = send_msg.video.file_id
            elif type == 'document':
                send_msg = await client.send_document(self.chat_id, document=file_dir, disable_notification=True)
                file_unique_id = send_msg.document.file_unique_id
                file_id = send_msg.document.file_id
            else:
                sys_log.error_log(f"路径不正确，无法上传媒体: {file_dir}")
                return
            username = clients.get_username(client)
            id = self.get_id(file_unique_id)
            sql_insert = "insert into medias(chat_id, file_unique_id, username, id, type, message_id, file_id, updated_at) " \
            "values(%s,%s,%s,%s,%s,%s,%s,CURRENT_TIMESTAMP()) on duplicate key update message_id=values(message_id), " \
            "file_id=values(file_id), updated_at=CURRENT_TIMESTAMP()"
            exe_connector.run(sql_insert, [self.chat_id, file_unique_id, username, id, type, send_msg.id, file_id])
            return id
        except Exception:
            sys_log.error_log(f"上传媒体报错: {file_dir}")

    # 批量重新获取新file_id
    # id_str: 媒体ID字符串列表
    async def regets(self, client, id_str=''):
        try:
            # sys_log.write_log("获取新file_id：{0:s}".format(id_str))
            # 获取media_online中最合适的一条记录
            sql_str = f"""
            SELECT * FROM (
                SELECT chat_id, file_unique_id, username, id, type, message_id, file_id, updated_at,
                    row_number() OVER (PARTITION BY id ORDER BY updated_at DESC) AS rn
                FROM medias
                WHERE id IN ({id_str}) and chat_id = %s
            ) b
            WHERE b.rn = 1
            """
            rst = read_connector.run(sql_str, [self.chat_id])
            # 若找不到足够媒体信息，则退出
            id_list = id_str.split(',')
            if len(rst) != len(id_list):
                sys_log.error_log("medias中找不到全部id_str信息\nid: {0:s}\nchat_id: {1:d}\n{2:s}".format(id_str, self.chat_id, sql_str))
                return False
            # 构建message_id到media_id的映射和消息ID列表
            msg_media = {}
            message_ids = []
            for row in rst:
                msg_media[row[5]] = TMedias(*row[:8])
                message_ids.append(row[5])
            msg_list = await client.get_messages(self.chat_id, message_ids)
            username = clients.get_username(client)
            # 更新记录
            for msg in msg_list:
                if msg.photo is not None:
                    new_file_id = msg.photo.file_id
                elif msg.video is not None:
                    new_file_id = msg.video.file_id
                elif msg.document is not None:
                    new_file_id = msg.document.file_id
                sql_str = "insert into medias(chat_id, file_unique_id, username, id, type, message_id, file_id, updated_at) " \
                "values(%s,%s,%s,%s,%s,%s,%s,CURRENT_TIMESTAMP()) on duplicate key update file_id=values(file_id),updated_at=CURRENT_TIMESTAMP()"
                exe_connector.run(sql_str, [msg_media[msg.id].chat_id, msg_media[msg.id].file_unique_id, username, 
                                            msg_media[msg.id].id, msg_media[msg.id].type, msg.id, new_file_id])
            return True
        except Exception:
            sys_log.error_log(f"update_reget 报错: {id_str}")
            return False

    # 获取媒体列表
    # id_str: 媒体ID字符串列表
    async def get_media(self, client, id_str='', content=''):
        try:
            username = clients.get_username(client)
            sql_str = f"SELECT * FROM medias WHERE id IN ({id_str}) and chat_id = %s and username = %s"
            rst = read_connector.run(sql_str, [self.chat_id, username])
            id_list = id_str.split(',')
            if len(rst) != len(id_list):
                if not await self.regets(client, id_str):
                    return
                rst = read_connector.run(sql_str, [self.chat_id, username])
            media_list = []
            first_ind = 0
            # 媒体类型映射表
            media_classes = {
                'photo': InputMediaPhoto,
                'video': InputMediaVideo,
                'document': InputMediaDocument
            }
            for row in rst:
                media = TMedias(*row)
                media_class = media_classes.get(media.type)
                if first_ind == 0:
                    media_list.append(media_class(media.file_id, caption=content))
                else:
                    media_list.append(media_class(media.file_id))
                first_ind += 1
            return media_list
        except Exception:
            sys_log.error_log(f"获取媒体列表失败{id_str}")


@dataclass
class TChatMessage:
    """
    聊天消息数据类
    对应 chat_message 表结构
    """
    chat_id: int
    chat_name: str
    kind: str
    ind: str
    msg_list: str  # 保持字符串类型，格式：",123,456,789,"
    updated_at: Optional[datetime.datetime] = None
    created_at: Optional[datetime.datetime] = None

    def get_msg_ids(self) -> List[int]:
        """获取消息ID列表"""
        if not self.msg_list or self.msg_list == ',,':
            return []
        return [int(x) for x in self.msg_list.strip(',').split(',') if x]


class ChatMessage:
    """
    群消息管理类 - 最终优化版

    优化内容：
    1. 使用参数化查询防止SQL注入
    2. 集成项目的日志系统
    3. 使用myChats查询方式
    4. 改进错误处理
    5. 使用**解包创建数据类实例
    6. 优化SQL语句使用VALUES()
    7. 精确匹配查询条件
    8. 返回TChatMessage实例而非元组
    """

    def __init__(self, chat_id: int, kind: str):
        self.chat_id = chat_id
        self.kind = kind

    def _validate_messages(self, messages: Union[Message, List[Message]]) -> List[Message]:
        """验证消息列表"""
        if not isinstance(messages, list):
            messages = [messages]

        for msg in messages:
            if self.chat_id != msg.chat.id:
                chat_name = myChats[self.chat_id].name
                msg_chat_name = myChats[msg.chat.id].name
                sys_log.write_log(
                    f"ChatMessage.chat({chat_name})与实际群({msg_chat_name})不匹配，请确认！"
                )
                return []
        return messages

    def _format_msg_list(self, messages: List[Message]) -> str:
        """格式化消息ID列表为字符串"""
        msg_ids = [str(msg.id) for msg in messages]
        return ',' + ','.join(msg_ids) + ','

    # 插入库表记录
    def add_row(self, ind: str, messages: Union[Message, List[Message]], kind: str = '') -> bool:
        try:
            kind = self.kind if kind == '' else kind
            messages = self._validate_messages(messages)
            if not messages:
                return False

            msg_list_str = self._format_msg_list(messages)
            chat_name = myChats[self.chat_id].name

            sql_str = """
            INSERT INTO chat_message(chat_id, chat_name, kind, ind, msg_list, updated_at, created_at)
            VALUES(%s, %s, %s, %s, %s, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
            ON DUPLICATE KEY UPDATE
            chat_name=VALUES(chat_name), msg_list=VALUES(msg_list), updated_at=CURRENT_TIMESTAMP()
            """

            exe_connector.run(sql_str, [self.chat_id, chat_name, kind, ind, msg_list_str])
            return True

        except Exception:
            sys_log.error_log(f"添加消息记录失败: chat_id={self.chat_id}, ind={ind}")
            return False

    # 插入库表记录(可重复记录）
    def add_rows(self, ind: str, messages: Union[Message, List[Message]], kind: str = '') -> bool:
        try:
            kind = self.kind if kind == '' else kind
            messages = self._validate_messages(messages)
            if not messages:
                return False

            msg_list_str = self._format_msg_list(messages)
            chat_name = myChats[self.chat_id].name
            ind_with_msg = ind + '|' + msg_list_str

            sql_str = """
            INSERT INTO chat_message(chat_id, chat_name, kind, ind, msg_list, updated_at, created_at)
            VALUES(%s, %s, %s, %s, %s, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
            ON DUPLICATE KEY UPDATE
            chat_name=VALUES(chat_name), msg_list=VALUES(msg_list), updated_at=CURRENT_TIMESTAMP()
            """

            exe_connector.run(sql_str, [self.chat_id, chat_name, kind, ind_with_msg, msg_list_str])
            return True

        except Exception:
            sys_log.error_log(f"添加重复消息记录失败: chat_id={self.chat_id}, ind={ind}")
            return False

    # 删除库表记录
    def del_row(self, ind: str, kind: str = ''):
        try:
            kind = self.kind if kind == '' else kind
            sql_str = "DELETE FROM chat_message WHERE chat_id=%s AND kind=%s AND ind=%s"
            exe_connector.run(sql_str, [self.chat_id, kind, ind])

        except Exception:
            sys_log.error_log(f"删除消息记录失败: chat_id={self.chat_id}, ind={ind}")

    # 删除库表记录(可重复记录）
    def del_rows(self, ind: str, kind: str = ''):
        try:
            kind = self.kind if kind == '' else kind
            str_ind = ind + '|'
            sql_str = "DELETE FROM chat_message WHERE chat_id=%s AND kind=%s AND ind LIKE %s"
            exe_connector.run(sql_str, [self.chat_id, kind, f"{str_ind}%"])

        except Exception:
            sys_log.error_log(f"删除重复消息记录失败: chat_id={self.chat_id}, ind={ind}")

    # 查询库表记录
    def get_row(self, ind: str, kind: str = '') -> List[TChatMessage]:
        try:
            kind = self.kind if kind == '' else kind
            sql_str = """
            SELECT chat_id, chat_name, kind, ind, msg_list, updated_at, created_at
            FROM chat_message
            WHERE chat_id=%s AND kind=%s AND ind=%s
            """
            result = read_connector.run(sql_str, [self.chat_id, kind, ind])
            return [TChatMessage(*row) for row in result] if result else []

        except Exception:
            sys_log.error_log(f"查询消息记录失败: chat_id={self.chat_id}, ind={ind}")
            return []

    # 查询库表记录(可重复记录）
    def get_rows(self, ind: str, kind: str = '') -> List[TChatMessage]:
        try:
            kind = self.kind if kind == '' else kind
            str_ind = ind + '|'
            sql_str = """
            SELECT chat_id, chat_name, kind, ind, msg_list, updated_at, created_at
            FROM chat_message
            WHERE chat_id=%s AND kind=%s AND ind LIKE %s
            """
            result = read_connector.run(sql_str, [self.chat_id, kind, f"{str_ind}%"])
            return [TChatMessage(*row) for row in result] if result else []

        except Exception:
            sys_log.error_log(f"查询重复消息记录失败: chat_id={self.chat_id}, ind={ind}")
            return []

    # 通过message_id字符串查库表记录
    def get_from_msg(self, msg_str: str) -> List[TChatMessage]:
        try:
            sql_str = """
            SELECT chat_id, chat_name, kind, ind, msg_list, updated_at, created_at
            FROM chat_message
            WHERE chat_id=%s AND msg_list LIKE %s
            """
            result = read_connector.run(sql_str, [self.chat_id, f"%,{msg_str},%"])
            return [TChatMessage(*row) for row in result] if result else []

        except Exception:
            sys_log.error_log(f"通过消息ID查询记录失败: chat_id={self.chat_id}, msg_str={msg_str}")
            return []

    # 删除消息
    async def del_msg(self, ind: str, client: Client, kind: str = ''):
        try:
            kind = self.kind if kind == '' else kind
            rows = self.get_row(ind, kind)

            for row in rows:
                try:
                    # 解析消息ID列表
                    msg_ids = row.get_msg_ids()
                    if msg_ids:
                        await client.delete_messages(self.chat_id, msg_ids)
                        await asyncio.sleep(1)

                except FloodWait as e:
                    await asyncio.sleep(e.value)
                    await client.delete_messages(self.chat_id, msg_ids)

                except Exception:
                    chat_name = myChats[self.chat_id].name
                    sys_log.write_log(
                        f"删除群信息失败：{self.chat_id}({chat_name}) {row.kind} @ {row.ind} {row.msg_list}"
                    )

        except Exception:
            sys_log.error_log(f"删除消息失败: chat_id={self.chat_id}, ind={ind}")

    # 删除消息(可重复记录）
    async def del_msgs(self, ind: str, client: Client, kind: str = ''):
        try:
            kind = self.kind if kind == '' else kind
            rows = self.get_rows(ind, kind)

            for row in rows:
                try:
                    # 解析消息ID列表
                    msg_ids = row.get_msg_ids()
                    if msg_ids:
                        await client.delete_messages(self.chat_id, msg_ids)

                        if len(rows) > 1:
                            await asyncio.sleep(2)

                except FloodWait as e:
                    await asyncio.sleep(e.value)
                    await client.delete_messages(self.chat_id, msg_ids)

                except Exception:
                    chat_name = myChats[self.chat_id].name
                    sys_log.write_log(
                        f"删除群信息列表失败：{self.chat_id}({chat_name}) {row.kind} @ {row.ind} {row.msg_list}"
                    )

                await asyncio.sleep(5)

        except Exception:
            sys_log.error_log(f"删除多条消息失败: chat_id={self.chat_id}, ind={ind}")

    # 矫正群信息
    async def correct_msgs(self, client: Client, except_regex: List[str] = None):
        try:
            except_regex = except_regex or []

            # 登记群信息
            msg_list = []
            msg_dic = {}

            if len(except_regex) == 0:
                async for msg in client.get_chat_history(self.chat_id):
                    msg_list.append(msg.id)
            else:
                async for msg in client.get_chat_history(self.chat_id):
                    msg_list.append(msg.id)
                    msg_dic[msg.id] = msg

            # 提取引导表信息
            table_list = []
            sql_str = "SELECT msg_list FROM chat_message WHERE chat_id=%s"
            result = read_connector.run(sql_str, [self.chat_id])

            for row in result:
                if row[0] and row[0] != ',,':
                    msg_ids = [int(x) for x in row[0].strip(',').split(',') if x]
                    table_list.extend(msg_ids)

            # 检查差异
            msg_list_more = [x for x in msg_list if x not in table_list]
            table_list_more = [x for x in table_list if x not in msg_list]

            if 1 in msg_list_more:
                msg_list_more.remove(1)

            # 删除引导表的多余项
            for tb in table_list_more:
                chat_name = myChats[self.chat_id].name
                sys_log.write_log(f"矫正群信息| 删除引导表的多余项:{chat_name} -> {tb}")

                sql_str = "DELETE FROM chat_message WHERE chat_id=%s AND msg_list LIKE %s"
                exe_connector.run(sql_str, [self.chat_id, f"%,{tb},%"])

            # 删除群组多余信息
            for msg_id in msg_list_more:
                try:
                    # 排除匹配上"例外"的信息
                    if len(except_regex) > 0 and msg_id in msg_dic:
                        msg = msg_dic[msg_id]
                        content = msg.text or msg.caption or ''

                        # 简化的模糊匹配
                        if any(pattern in content for pattern in except_regex):
                            continue

                    # 删除信息
                    chat_name = myChats[self.chat_id].name
                    sys_log.write_log(f"矫正群信息| 删除群组的多余信息:{chat_name} -> {msg_id}")

                    await client.delete_messages(self.chat_id, msg_id)
                    await asyncio.sleep(2)

                except FloodWait as e:
                    await asyncio.sleep(e.value)

        except Exception:
            sys_log.error_log(f"矫正群信息失败: chat_id={self.chat_id}")


# ============================================
# 优化后的水印处理器
# 基于 PIL/Pillow + FFmpeg-python 实现高性能水印处理
# ============================================

# 推荐的高性能库
from PIL import Image, ImageDraw, ImageFont, ImageEnhance, ImageFilter
import ffmpeg
import cv2
import numpy as np


@dataclass
class WatermarkConfig:
    """水印配置类"""
    # 图片水印配置
    pic_color: str = "#EE1010"  # 保持原有颜色
    pic_size: int = 36  # 更大字体提高清晰度
    pic_opacity: float = 0.8  # 更高透明度
    pic_angle: int = 30  # 45度倾斜
    pic_spacing: int = 250  # 平铺模式间距
    pic_shadow: bool = True
    pic_shadow_color: str = "#161615"  # 黑色阴影提高对比度
    pic_shadow_offset: Tuple[int, int] = (2, 2)
    # 新增图片水印参数
    pic_positions: list = field(default_factory=lambda: ['top-left', 'bottom-right'])  # 默认左上角和右下角
    pic_margin: int = 30  # 边距
    pic_tile_mode: bool = False  # 默认使用指定位置模式而非平铺

    # 视频水印配置
    video_color: str = "#EE1010"  # 保持原有颜色
    video_size: int = 24  # 保持原有大小
    video_opacity: float = 0.8  # 保持原有透明度
    video_position: str = "golden-ratio"  # 保持黄金分割位置
    video_margin: Tuple[int, int] = (20, 20)  # 保持原有边距

    # 输出质量配置
    image_quality: int = 95  # JPEG质量
    video_crf: int = 23      # 视频质量 (0-51, 越小质量越高)
    video_preset: str = "medium"  # ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow

    # 性能配置
    max_file_size_mb: int = 500
    timeout_seconds: int = 300
    use_hardware_acceleration: bool = False  # 默认禁用硬件加速，避免 CUDA/NVENC 错误


class Watermark:
    """基于推荐库的水印处理器"""

    def __init__(self, config: Optional[WatermarkConfig] = None):
        self.config = config or WatermarkConfig()

    def _generate_output_path(self, input_file: str, output_file: Optional[str] = None) -> str:
        """生成输出文件路径"""
        if output_file:
            return output_file

        # 自动生成输出路径：原文件名 + "_wm" + 扩展名
        input_path = Path(input_file)
        output_path = input_path.parent / f"{input_path.stem}_wm{input_path.suffix}"
        return str(output_path)



    def _create_text_image(self, text: str, font: ImageFont.FreeTypeFont, color: str,
                          shadow: bool = False, shadow_color: str = "#000000",
                          shadow_offset: Tuple[int, int] = (2, 2)) -> Image.Image:
        """创建文字图像"""
        # 获取文字尺寸
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        # 创建透明背景图像
        padding = max(shadow_offset) + 5 if shadow else 5
        img_width = text_width + padding * 2
        img_height = text_height + padding * 2

        text_img = Image.new('RGBA', (img_width, img_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(text_img)

        # 绘制阴影
        if shadow:
            shadow_x = padding + shadow_offset[0]
            shadow_y = padding + shadow_offset[1]
            draw.text((shadow_x, shadow_y), text, font=font, fill=shadow_color)

        # 绘制文字
        text_x = padding
        text_y = padding
        draw.text((text_x, text_y), text, font=font, fill=color)

        return text_img

    def _add_positioned_watermarks(self, original: Image.Image, text_img: Image.Image,
                                 positions: list = None, margin: int = 20) -> Image.Image:
        """在指定位置添加水印"""
        if positions is None:
            positions = ['top-left', 'bottom-right']  # 默认左上角和右下角

        # 创建水印层
        watermark_layer = Image.new('RGBA', original.size, (0, 0, 0, 0))
        text_width, text_height = text_img.size

        for position in positions:
            if position == 'top-left':
                x, y = margin, margin
            elif position == 'top-right':
                x, y = original.width - text_width - margin, margin
            elif position == 'bottom-left':
                x, y = margin, original.height - text_height - margin
            elif position == 'bottom-right':
                x, y = original.width - text_width - margin, original.height - text_height - margin
            elif position == 'center':
                x, y = (original.width - text_width) // 2, (original.height - text_height) // 2
            else:
                continue  # 跳过不支持的位置

            # 确保水印在图片范围内
            x = max(0, min(x, original.width - text_width))
            y = max(0, min(y, original.height - text_height))

            watermark_layer.paste(text_img, (x, y), text_img)

        # 合成最终图像
        return Image.alpha_composite(original, watermark_layer)



    async def add_image_watermark(
        self,
        input_file: str,
        watermark_text: str,
        output_file: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        使用PIL/Pillow添加图片水印

        Args:
            input_file: 输入图片文件路径
            watermark_text: 水印文字
            output_file: 输出文件路径，如果为None则默认覆盖输入文件
            **kwargs: 其他配置参数

        Returns:
            str: 处理后的输出文件路径，失败返回空字符串
        """
        try:
            # 生成输出文件路径
            output_file = self._generate_output_path(input_file, output_file)

            # 合并配置参数
            color = kwargs.get('color', self.config.pic_color)
            size = kwargs.get('size', self.config.pic_size)
            opacity = kwargs.get('opacity', self.config.pic_opacity)
            angle = kwargs.get('angle', self.config.pic_angle)
            spacing = kwargs.get('spacing', self.config.pic_spacing)
            shadow = kwargs.get('shadow', self.config.pic_shadow)
            positions = kwargs.get('positions', self.config.pic_positions)  # 使用配置默认值
            margin = kwargs.get('margin', self.config.pic_margin)  # 使用配置默认值
            tile_mode = kwargs.get('tile_mode', self.config.pic_tile_mode)  # 使用配置默认值

            # 在线程池中执行图片处理
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                None,
                self._process_image_watermark,
                input_file, output_file, watermark_text,
                color, size, opacity, angle, spacing, shadow,
                positions, margin, tile_mode
            )

            if success:
                # sys_log.write_log(f"图片水印添加成功: {input_file} -> {output_file}")
                return output_file
            else:
                return ""

        except Exception as e:
            sys_log.error_log(f"图片水印添加失败: {input_file}, 错误: {e}")
            return ""

    def _process_image_watermark(self, input_file: str, output_file: str, watermark_text: str,
                               color: str, size: int, opacity: float, angle: int,
                               spacing: int, shadow: bool,
                               positions: Optional[list] = None, margin: int = 20,
                               tile_mode: bool = True) -> bool:
        """在线程中处理图片水印，返回处理是否成功"""
        try:
            # 打开原图
            with Image.open(input_file) as original:
                # 转换为RGBA模式以支持透明度
                if original.mode != 'RGBA':
                    original = original.convert('RGBA')

                # 获取字体 - 直接使用中文字体逻辑
                # 获取字体 - 直接使用中文字体逻辑
                try:
                    chinese_font_path = self._get_chinese_font_path()
                    if chinese_font_path:
                        font = ImageFont.truetype(chinese_font_path, size)
                    else:
                        font = ImageFont.load_default()
                except Exception:
                    font = ImageFont.load_default()

                # 创建文字图像
                text_img = self._create_text_image(
                    watermark_text, font, color, shadow,
                    self.config.pic_shadow_color, self.config.pic_shadow_offset
                )

                # 旋转文字
                if angle != 0:
                    text_img = text_img.rotate(angle, expand=True)

                # 设置透明度
                if opacity < 1.0:
                    alpha = text_img.split()[-1]
                    alpha = ImageEnhance.Brightness(alpha).enhance(opacity)
                    text_img.putalpha(alpha)

                # 根据模式选择水印添加方式
                if tile_mode and positions is None:
                    # 传统平铺模式
                    watermark_layer = Image.new('RGBA', original.size, (0, 0, 0, 0))
                    text_width, text_height = text_img.size
                    for y in range(0, original.height, text_height + spacing):
                        for x in range(0, original.width, text_width + spacing):
                            watermark_layer.paste(text_img, (x, y), text_img)
                    watermarked = Image.alpha_composite(original, watermark_layer)
                else:
                    # 指定位置模式
                    if positions is None:
                        positions = ['top-left', 'bottom-right']  # 默认左上角和右下角
                    watermarked = self._add_positioned_watermarks(original, text_img, positions, margin)

                # 保存图像
                if output_file.lower().endswith('.jpg') or output_file.lower().endswith('.jpeg'):
                    # JPEG不支持透明度，转换为RGB
                    watermarked = watermarked.convert('RGB')
                    watermarked.save(output_file, 'JPEG', quality=self.config.image_quality, optimize=True)
                else:
                    watermarked.save(output_file, optimize=True)

            return True

        except Exception as e:
            sys_log.error_log(f"图片水印处理失败: {input_file}, 错误: {e}")
            return False

    async def add_video_watermark(
        self,
        input_file: str,
        watermark_text: str,
        output_file: Optional[str] = None,
        progress_callback: Optional[Callable[[float], None]] = None,
        **kwargs
    ) -> str:
        """
        使用FFmpeg-python添加视频水印

        Args:
            input_file: 输入视频文件路径
            watermark_text: 水印文字
            output_file: 输出文件路径，如果为None则默认覆盖输入文件
            progress_callback: 进度回调函数
            **kwargs: 其他配置参数

        Returns:
            str: 处理后的输出文件路径，失败返回空字符串
        """
        try:
            # 生成输出文件路径
            output_file = self._generate_output_path(input_file, output_file)

            # 合并配置参数
            color = kwargs.get('color', self.config.video_color)
            size = kwargs.get('size', self.config.video_size)
            opacity = kwargs.get('opacity', self.config.video_opacity)
            position = kwargs.get('position', self.config.video_position)
            margin = kwargs.get('margin', self.config.video_margin)

            # 在线程池中执行视频处理
            loop = asyncio.get_event_loop()
            success = await asyncio.wait_for(
                loop.run_in_executor(
                    None,
                    self._process_video_watermark,
                    input_file, output_file, watermark_text,
                    color, size, opacity, position, margin, progress_callback
                ),
                timeout=self.config.timeout_seconds
            )

            if success:
                # sys_log.write_log(f"视频水印添加成功: {input_file} -> {output_file}")
                return output_file
            else:
                return ""

        except asyncio.TimeoutError:
            sys_log.error_log(f"视频处理超时: {input_file}")
            return ""
        except Exception as e:
            sys_log.error_log(f"视频水印添加失败: {input_file}, 错误: {e}")
            return ""

    def _get_video_dimensions(self, input_file: str) -> tuple[int, int]:
        """获取视频尺寸"""
        try:
            probe = ffmpeg.probe(input_file)
            video_stream = next((stream for stream in probe['streams']
                               if stream['codec_type'] == 'video'), None)
            if video_stream:
                width = int(video_stream['width'])
                height = int(video_stream['height'])
                # sys_log.write_log(f"检测到视频尺寸: {width}x{height}")
                return width, height
        except Exception as e:
            sys_log.error_log(f"获取视频尺寸失败: {e}")

        # 如果获取失败，返回常见的默认值
        # sys_log.write_log("使用默认视频尺寸: 540x720")
        return 540, 720

    def _estimate_text_dimensions(self, text: str, fontsize: int) -> tuple[int, int]:
        """估算文字尺寸"""
        # 计算字符数（中文字符按1.5倍宽度计算）
        char_count = 0
        for char in text:
            if ord(char) > 127:  # 非ASCII字符（中文等）
                char_count += 1.5
            else:  # ASCII字符
                char_count += 1

        # 基于实际测试的估算公式
        estimated_width = char_count * fontsize * 0.6
        estimated_height = fontsize * 1.2

        return int(estimated_width), int(estimated_height)

    def _calculate_adaptive_font_size(self, video_width: int, video_height: int,
                                     base_font_size: int = 24) -> int:
        """计算自适应字体大小"""
        # 基准尺寸（540x720）
        BASE_WIDTH = 540
        BASE_HEIGHT = 720

        # 基于最小边进行缩放，确保在小屏幕上也能清晰显示
        min_dimension = min(video_width, video_height)
        base_min_dimension = min(BASE_WIDTH, BASE_HEIGHT)

        # 计算缩放比例
        scale_ratio = min_dimension / base_min_dimension

        # 计算自适应字体大小，限制在合理范围内
        adaptive_size = int(base_font_size * scale_ratio)

        # 限制字体大小范围：最小12px，最大60px
        adaptive_size = max(12, min(60, adaptive_size))

        # sys_log.write_log(f"自适应字体大小: {base_font_size}px -> {adaptive_size}px (缩放比例: {scale_ratio:.2f})")

        return adaptive_size

    def _calculate_golden_ratio_position(self, video_width: int, video_height: int,
                                       text_width: int, text_height: int) -> tuple[int, int]:
        """计算黄金分割位置"""
        GOLDEN_RATIO = 0.618

        # 计算黄金分割点（文字中心点）
        center_x = video_width * GOLDEN_RATIO
        center_y = video_height * GOLDEN_RATIO

        # 计算文字左上角位置
        x_pos = int(center_x - text_width / 2)
        y_pos = int(center_y - text_height / 2)

        # 确保文字完全在视频范围内
        x_pos = max(0, min(x_pos, video_width - text_width))
        y_pos = max(0, min(y_pos, video_height - text_height))

        # sys_log.write_log(f"黄金分割位置: 中心点({center_x:.0f}, {center_y:.0f}) -> 左上角({x_pos}, {y_pos})")

        return x_pos, y_pos

    def _ensure_text_fits_in_video(self, text: str, video_width: int, video_height: int,
                                  initial_font_size: int) -> tuple[int, int, int]:
        """确保文字能完全显示在视频中"""
        max_attempts = 10
        font_size = initial_font_size

        for attempt in range(max_attempts):
            # 估算当前字体大小的文字尺寸
            text_width, text_height = self._estimate_text_dimensions(text, font_size)

            # 检查是否超出视频边界（留10%边距）
            max_width = video_width * 0.9
            max_height = video_height * 0.9

            if text_width <= max_width and text_height <= max_height:
                # sys_log.write_log(f"文字适配成功: {font_size}px, 尺寸{text_width}x{text_height}")
                return font_size, int(text_width), int(text_height)

            # 如果超出边界，减小字体
            font_size = int(font_size * 0.9)

            if font_size < 8:  # 最小字体限制
                break

        # 如果仍然超出，使用最小字体
        text_width, text_height = self._estimate_text_dimensions(text, 8)
        # sys_log.write_log(f"文字适配到最小字体: 8px, 尺寸{text_width}x{text_height}")

        return 8, int(text_width), int(text_height)

    def _calculate_position_coordinates(self, position: str, video_width: int, video_height: int,
                                      text_width: int, text_height: int, margin: Tuple[int, int]) -> tuple[int, int]:
        """计算位置坐标"""
        # 添加黄金分割位置支持
        if position == 'golden-ratio':
            return self._calculate_golden_ratio_position(video_width, video_height, text_width, text_height)

        x_margin, y_margin = margin

        position_map = {
            'top-left': (x_margin, y_margin),
            'top-center': ((video_width - text_width) // 2, y_margin),
            'top-right': (video_width - text_width - x_margin, y_margin),
            'center-left': (x_margin, (video_height - text_height) // 2),
            'center': ((video_width - text_width) // 2, (video_height - text_height) // 2),
            'center-right': (video_width - text_width - x_margin, (video_height - text_height) // 2),
            'bottom-left': (x_margin, video_height - text_height - y_margin),
            'bottom-center': ((video_width - text_width) // 2, video_height - text_height - y_margin),
            'bottom-right': (video_width - text_width - x_margin, video_height - text_height - y_margin)
        }

        x_pos, y_pos = position_map.get(position, position_map['bottom-right'])
        # sys_log.write_log(f"位置计算: {position} -> ({x_pos}, {y_pos})")

        return x_pos, y_pos

    def _get_chinese_font_path(self) -> Optional[str]:
        """获取支持中文的字体路径"""
        # 常见的中文字体路径
        chinese_fonts = [
            # Linux 系统字体
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
            "/usr/share/fonts/truetype/arphic/uming.ttc",
            "/usr/share/fonts/truetype/arphic/ukai.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # 备用字体
            
            # Windows 字体（如果在 WSL 中）
            "/mnt/c/Windows/Fonts/msyh.ttc",
            "/mnt/c/Windows/Fonts/simsun.ttc",

            # macOS 字体
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/STHeiti Light.ttc"
        ]

        for font_path in chinese_fonts:
            if os.path.exists(font_path):
                return font_path

        return None

    def _process_video_watermark(self, input_file: str, output_file: str, watermark_text: str,
                               color: str, size: int, opacity: float, position: str,
                               margin: Tuple[int, int], progress_callback: Optional[Callable] = None) -> bool:
        """在线程中处理视频水印，返回处理是否成功"""
        try:
            # 1. 获取视频尺寸
            video_width, video_height = self._get_video_dimensions(input_file)

            # 2. 如果是黄金分割位置，使用自适应字体大小和边界检查
            if position == 'golden-ratio':
                adaptive_font_size = self._calculate_adaptive_font_size(video_width, video_height, size)
                # 确保文字能完全显示
                final_font_size, text_width, text_height = self._ensure_text_fits_in_video(
                    watermark_text, video_width, video_height, adaptive_font_size
                )
            else:
                # 使用指定的字体大小
                final_font_size = size
                text_width, text_height = self._estimate_text_dimensions(watermark_text, final_font_size)

            # 3. 计算精确位置
            x_pos, y_pos = self._calculate_position_coordinates(
                position, video_width, video_height, text_width, text_height, margin
            )

            # 4. 构建FFmpeg命令
            input_stream = ffmpeg.input(input_file)

            # 5. 构建水印滤镜参数 - 使用动态计算的坐标
            filter_params = {
                'text': watermark_text,
                'fontsize': final_font_size,
                'fontcolor': f"{color}@{opacity}",
                'x': str(x_pos),
                'y': str(y_pos)
            }

            # 添加字体路径以支持中文显示
            filter_params['fontfile'] = self._get_chinese_font_path()

            # 应用滤镜
            output_stream = input_stream.video.filter('drawtext', **filter_params)

            # 构建输出参数 - 强制使用软件编码
            output_args = {
                'vcodec': 'libx264',  # 强制使用软件编码，避免硬件加速问题
                'crf': self.config.video_crf,
                'preset': self.config.video_preset,
                'acodec': 'copy'  # 复制音频流，不重新编码
            }

            # 使用软件编码，避免硬件加速相关问题
            # sys_log.write_log(f"使用软件编码处理视频: {input_file}")

            # 执行FFmpeg命令
            out = ffmpeg.output(output_stream, input_stream.audio, output_file, **output_args)
            ffmpeg.run(out, overwrite_output=True, quiet=True)
            # 以下命令是测试用，显示报错信息
            # ffmpeg.run(out, overwrite_output=True)
            return True

        except Exception as e:
            sys_log.error_log(f"FFmpeg处理失败: {input_file}, 错误: {e}")
            return False


class Translator:
    """最终版基础翻译服务类"""

    def __init__(self, client=None):
        """
        初始化翻译器

        Args:
            client: 可选的Telegram Client，用于trans_telegram方法
        """
        self.client = client
        self.translator = GoogleTranslator()  # 初始化时创建GoogleTranslator实例

        # 语言代码映射 - 支持4种语言
        self.lang_codes = {
            'simple': {
                'zh': 'zh-cn',  # 中文
                'en': 'en',     # 英文
                'ru': 'ru',     # 俄语
                'kz': 'kk',     # 哈萨克语（GoogleTranslator使用kk）
            },
            'telegram': {
                'zh': 'zh-CN',  # 中文（Telegram格式）
                'en': 'en',     # 英文
                'ru': 'ru',     # 俄语
                'kz': 'kk',     # 哈萨克语
            }
        }

    def _sync_translate(self, text: str, trans_lang_code: str) -> str:
        """
        同步翻译方法（简化版）

        Args:
            text: 要翻译的文本
            trans_lang_code: GoogleTranslator语言代码

        Returns:
            翻译结果
        """
        return self.translator.translate(text, dest=trans_lang_code).text

    async def trans_simple(self, text: str, target_lang: str) -> str:
        """
        简化的GoogleTranslator异步翻译方法

        Args:
            text: 要翻译的文本
            target_lang: 目标语言代码 ('zh', 'en', 'ru', 'kz')

        Returns:
            翻译结果，失败时返回原文
        """
        try:
            # 转换语言代码
            trans_lang_code = self.lang_codes['simple'].get(target_lang, target_lang)

            # 在线程池中运行同步翻译，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._sync_translate, text, trans_lang_code)

            return result.strip() if result else text
        except Exception as e:
            sys_log.error_log(f"trans_simple翻译失败: {text} -> {target_lang}, {e}")
            return text

    async def trans_telegram(self, text: str, target_lang: str) -> str:
        """
        Telegram的translate_text方法

        Args:
            text: 要翻译的文本
            target_lang: 目标语言代码 ('zh', 'en', 'ru', 'kz')

        Returns:
            翻译结果，失败时返回原文
        """
        if not self.client:
            sys_log.error_log("trans_telegram方法需要Client参数，回退到trans_simple方法")
            return await self.trans_simple(text, target_lang)

        try:
            # 转换语言代码
            telegram_lang_code = self.lang_codes['telegram'].get(target_lang, target_lang)

            # 调用Telegram的translate_text方法
            result = await self.client.translate_text(text, telegram_lang_code)

            return result.text

        except Exception as e:
            sys_log.error_log(f"Telegram翻译失败: {text} -> {target_lang}, {e}")
            # 回退到trans_simple方法
            return await self.trans_simple(text, target_lang)

    async def translate(self, text: str, target_lang: str, method: str = 'simple') -> str:
        """
        统一翻译接口，可选择翻译方法

        Args:
            text: 要翻译的文本
            target_lang: 目标语言代码 ('zh', 'en', 'ru', 'kz')
            method: 翻译方法 ('simple', 'telegram')

        Returns:
            翻译结果，失败时返回原文
        """
        if not text or not text.strip():
            return text

        try:
            if method == 'telegram':
                return await self.trans_telegram(text, target_lang)
            else:
                return await self.trans_simple(text, target_lang)

        except Exception as e:
            sys_log.error_log(f"统一翻译接口失败: {text} -> {target_lang}, method={method}, {e}")
            return text

    async def batch_translate(self, texts: list, target_lang: str, method: str = 'simple') -> list:
        """
        批量翻译文本列表

        Args:
            texts: 要翻译的文本列表
            target_lang: 目标语言代码
            method: 翻译方法 ('simple', 'telegram')

        Returns:
            翻译结果列表
        """
        if not texts:
            return []

        try:
            # 并行翻译所有文本
            tasks = []
            for text in texts:
                task = self.translate(text, target_lang, method)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            translations = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    sys_log.error_log(f"批量翻译第{i}项失败: {result}")
                    translations.append(texts[i])  # 失败时使用原文
                else:
                    translations.append(result)

            return translations

        except Exception as e:
            sys_log.error_log(f"批量翻译失败: {e}")
            return texts  # 失败时返回原文列表

    async def translate_to_multiple_languages(self, text: str, target_languages: list, method: str = 'simple') -> dict:
        """
        将文本翻译为多种语言

        Args:
            text: 要翻译的文本
            target_languages: 目标语言代码列表 ['en', 'ru', 'kz']
            method: 翻译方法 ('simple', 'telegram')

        Returns:
            翻译结果字典 {'en': '英文翻译', 'ru': '俄语翻译', 'kz': '哈语翻译'}
        """
        try:
            # 并行翻译到各种语言
            tasks = []
            for lang in target_languages:
                task = self.translate(text, lang, method)
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 组装结果
            translations = {}
            for i, lang in enumerate(target_languages):
                if isinstance(results[i], Exception):
                    sys_log.error_log(f"翻译到{lang}失败: {results[i]}")
                    translations[lang] = text  # 失败时使用原文
                else:
                    translations[lang] = results[i]

            return translations

        except Exception as e:
            sys_log.error_log(f"多语言翻译失败: {e}")
            return {lang: text for lang in target_languages}

    def set_client(self, client):
        """
        设置Telegram Client

        Args:
            client: Telegram Client实例
        """
        self.client = client
        sys_log.debug_log("已设置Telegram Client")

    async def detect_language(self, text: str) -> Optional[str]:
        """
        检测文本语言（基础实现）

        Args:
            text: 要检测的文本

        Returns:
            检测到的语言代码，失败时返回None
        """
        if not text or not text.strip():
            return None

        try:
            # 简单的语言检测逻辑
            # 检查是否包含中文字符
            for char in text:
                if '\u4e00' <= char <= '\u9fff':
                    return 'zh'

            # 检查是否包含俄语字符
            for char in text:
                if '\u0400' <= char <= '\u04ff':
                    return 'ru'

            # 检查是否包含哈萨克语特有字符
            # 哈萨克语使用西里尔字母，但有一些特有的字符
            kazakh_specific_chars = {
                'ә', 'ғ', 'қ', 'ң', 'ө', 'ұ', 'ү', 'һ', 'і',  # 小写
                'Ә', 'Ғ', 'Қ', 'Ң', 'Ө', 'Ұ', 'Ү', 'Һ', 'І'   # 大写
            }

            for char in text:
                if char in kazakh_specific_chars:
                    return 'kz'

            # 默认认为是英文
            return 'en'

        except Exception as e:
            sys_log.error_log(f"语言检测失败: {e}")
            return None

