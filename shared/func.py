# -*- coding: utf-8 -*-
__author__ = 'Kobee.li'
from shared.classes import *
# from main import *
from pyrogram import filters
from pyrogram.errors import ChatAdminRequired
from pyrogram.errors import Flood<PERSON>ait
from pyrogram import raw
import asyncio
import json
import traceback
from functools import wraps


# 获取 media_group 中的消息列表（含过滤后续message作用）
# ['1',  media_group列表] media_group的第一条消息，获取全部消息列表
# ['2',  原消息]           media_group的非第一条消息，返回原消息
# ['0',  原消息]           不是media_group消息，返回原消息
async def check_media_group(client, message):
    try:
        # 处理media_group的第一条记录
        if message.media_group_id is not None and message.media_group_id != params_pool['media_group_id']:
            params_pool['media_group_id'] = message.media_group_id
            rec_msg = await client.get_media_group(message.chat.id, message.id)
            return ['1', rec_msg]
        elif message.media_group_id is not None and message.media_group_id == params_pool['media_group_id']:
            return ['2', [message]]
        else:
            params_pool['media_group_id'] = 0
            return ['0', [message]]
    except Exception:
        sys_log.error_log(f"check_media_group异常: {message.chat.id}-{message.id}")


# ============================================
# 用户ID和标准ID转换函数
# ============================================

def user_to_std(user_id: int) -> str:
    """
    将用户ID转换为标准ID

    Args:
        user_id (int): 用户ID

    Returns:
        str: 标准ID，格式如 "0001A"，失败返回空字符串

    算法：基于自增ID的数学计算
    - 数字部分：auto_id % 10000
    - 字母部分：(auto_id // 10000) % 26，转换为A-Z
    """
    try:
        # 检查是否已存在映射
        sql_check = "SELECT std_id FROM user_std WHERE user_id = %s"
        result = read_connector.run(sql_check, [user_id])

        if result:
            return result[0][0]

        # 插入新记录，获取自增ID
        sql_insert = "INSERT INTO user_std (user_id) VALUES (%s)"
        exe_connector.run(sql_insert, [user_id])

        # 获取刚插入的自增ID
        sql_get_id = "SELECT id FROM user_std WHERE user_id = %s"
        id_result = read_connector.run(sql_get_id, [user_id])

        if not id_result:
            sys_log.error_log(f"获取自增ID失败: user_id={user_id}")
            return ""

        # 计算标准ID
        auto_id = id_result[0][0]
        std_id = _calculate_std_id(auto_id)

        # 更新标准ID
        sql_update = "UPDATE user_std SET std_id = %s WHERE user_id = %s"
        exe_connector.run(sql_update, [std_id, user_id])

        return std_id

    except Exception:
        sys_log.error_log(f"user_to_std转换失败: user_id={user_id}")
        return ""


def std_to_user(std_id: str) -> int:
    """
    将标准ID转换为用户ID

    Args:
        std_id (str): 标准ID，格式如 "0001A"

    Returns:
        int: 用户ID，如果不存在返回0
    """
    try:
        sql_query = "SELECT user_id FROM user_std WHERE std_id = %s"
        result = read_connector.run(sql_query, [std_id])

        if result:
            return int(result[0][0])
        else:
            return 0

    except Exception:
        sys_log.error_log(f"std_to_user转换失败: std_id={std_id}")
        return 0


def user_std_exist(id_value) -> bool:
    """
    判断用户ID或标准ID是否存在

    Args:
        id_value: 用户ID(int)或标准ID(str)

    Returns:
        bool: 存在返回True，不存在返回False
    """
    try:
        import re
        id_str = str(id_value)

        # 判断是user_id还是std_id
        if re.match(r'^\d{6,}$', id_str):
            # 6位及以上数字，认为是user_id
            sql_query = "SELECT COUNT(1) FROM user_std WHERE user_id = %s"
            result = read_connector.run(sql_query, [int(id_value)])
        elif re.match(r'^[0-9]{4}[A-Z]$', id_str):
            # 标准ID格式：4位数字+1位大写字母
            sql_query = "SELECT COUNT(1) FROM user_std WHERE std_id = %s"
            result = read_connector.run(sql_query, [id_str])
        else:
            return False

        return result[0][0] == 1

    except Exception:
        sys_log.error_log(f"user_std_exist检查失败: id_value={id_value}")
        return False


def _calculate_std_id(auto_id: int) -> str:
    """
    根据自增ID计算标准ID

    Args:
        auto_id (int): 数据库自增ID

    Returns:
        str: 标准ID

    算法：
    - 数字部分：auto_id % 10000
    - 字母部分：(auto_id // 10000) % 26，转换为A-Z (支持循环)
    """
    # 数字部分：取模10000
    num_part = auto_id % 10000

    # 字母部分：整除10000后再取模26，支持A-Z循环
    letter_index = (auto_id // 10000) % 26
    letter = chr(ord('A') + letter_index)

    # 组合成标准ID
    return f"{num_part:04d}{letter}"


# 定义公共全局变量
params_pool = {'media_group_id': 0
              }
line = "----------------------"
e_num = ["0️⃣", "1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣", "🔟"]
(e_market, e_myOffer, e_trade, e_report, e_safe, e_complaint, e_adv, e_service, e_remind, e_money,
 e_return, e_home, e_warning, e_succ, e_to, e_left, e_right, e_up, e_down, e_fire,
 e_left2, e_right2, e_up2, e_down2, e_ok, e_no, e_heart_black, e_heart_bad, e_heart_green, e_heart_orange,
 e_heart_red, e_flower, e_lip, e_sex_wear, e_ruler, e_2girl, e_phone, e_girl, e_loudou, e_mail,
 e_id, e_photo, e_comfirm, e_search, e_girls, e_write, e_baby, e_eyes, e_zoushi, e_traffic,
 e_handup1, e_handup2, e_location, e_delete, e_light, e_manhat, e_girlhat, e_ice, e_prize, e_han,
 e_set, beg_girl, e_left3, e_right3, e_up3, e_down3, e_gou, e_hezuo, e_travel, e_he,
 e_hotheart, e_cycle, e_kong, e_gift, e_guests, e_sign, e_bell, e_man, e_man2, e_manhat2,
 e_bot, e_time1, e_time2, e_time3, e_dna, e_add, e_reduce, e_circle, e_pinglun, e_fenlei,
 e_shandian, e_video, e_cty, e_cty0, e_cty1, e_cty2, e_cty3, e_cty4, e_man3, e_star,
 e_group, e_talk, e_cha, e_b, e_sound, e_help, e_clap, e_girl2, e_smile, e_outbox,
 e_task, e_girl_, e_mai, e_change, e_inbox, e_msg, e_refuse, e_cty5, e_alert, e_noalert,
 e_smile2, e_weiqu, e_report2, e_unknow, e_file, e_page, e_replace, e_falang, e_jin18, e_phone2,
 e_bell2, e_redbag, e_ray, e_bomb, e_money2, e_atm, e_money3, e_me, e_ace1, e_mailbox,
 e_key, e_bankcard, e_link, e_ace2, e_king, e_game, e_climb, e_what, e_men, e_log,
 e_exchange, e_cheng, e_goal, e_suanpan, e_date, e_month, e_pinlv, e_endtime, e_time4, e_game,
 e_baobiao, e_date2, e_bankcard2, e_phone3, e_id, e_flag, e_bank, e_detail, e_cty6) = \
("💹", "📝", "⚖", "💯", "🛡", "🈲", "📢", "💁‍♀‍", "🎈", "💰",
 "🔙", "🏠", "⚠️", "🎉", "👉", "⬅", "➡", "⬆", "⬇", "🔥",
 "◀️", "▶️", "🔼", "🔽", "👌", "❌", "🖤", "💔", "💚", "🧡",
 "❤", "🌹", "💋", "👙", "📏", "👩‍👧", "📞", "👩 ", "⏳", "📮",
 "🎫", "📸", "⁉️", "🔎", "👩‍👩‍👧‍👧", "✍️", "🤱", "👀", "📈", "🚥",
 "🙋", "🙋‍♂️", "📍", "❌", "💡", "🎩", "👒", "❄️", "🏅", "💦",
 "⚙️", "🙇‍♀️", "👈", "👉", "👆", "👇", "✅", "🤝", "🏖", "🈴",
 "❤️‍🔥", "♻️", "🈳", "🎁", "🤵‍♂️", "📡", "🛎", "🙍🏻‍♂️", "💁🏻‍♂️", "🧢",
 "🤖", "⏱", "⏰", "🕰", "🧬", "➕", "➖", "⭕️", "💬", "🗂",
 "⚡️", "🎥", "🏳️‍⚧️", "🇺🇳", "🇵🇭", "🇨🇳", "🇻🇳", "🇷🇺", "🙋🏻‍♂️", "🌟",
 "👯‍♀️", "🗣", "❎", "🅱️", "🔊", "🙏", "👏", "🙋‍♀️", "😊", "📤",
 "🔰", "👩", "🎙", "🔄", "📥", "📧", "⛔️", "🇹🇭", "🔔", "🔕",
 "😉", "🥺", "🚨", "❓", "🔖", "📜", "🎭", "💈", "🔞", "☎️",
 "📣", "🧧", "💣", "💥", "💵", "🏧", "💸", "👤", "♠️", "📬",
 "🔑", "🪪", "🔗", "♣️", "🃏", "🎮", "🧗‍♂️", "💢", "👬", "📑",
 "💱", "⚖️", "🎯", "🧮", "📅", "🈷️", "🎚", "⌛️", "⏲️", "🎰",
 "📊", "🗓", "💳", "📲", "🆔", "🎏", "🏦", "🧾", "🇺🇸")


