# -*- coding: utf-8 -*-
__author__ = "Kobee.li"
import os
import concurrent
import pymysql
import mysql.connector
from mysql.connector import errorcode
from configparser import ConfigParser
from typing import Any, Optional, Union, Dict, List, Tuple
from dataclasses import dataclass
import pathlib
import re
import time
import shutil
import os
from pyrogram import Client
from pyrogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    User,
    Chat,
    ChatMember,
    MenuButton,
    MenuButtonCommands,
    ReplyKeyboardMarkup,
    KeyboardButton,
    ForceReply,
    ChatPermissions,
    LinkPreviewOptions,
)
from pyrogram.enums import (
    ChatMembersFilter,
    ChatType,
    UserStatus,
    ChatMemberStatus,
    ParseMode,
    MessageMediaType,
    MessageEntityType,
)
import logging
import traceback
import sys
import inspect
import datetime
from datetime import timedelta
from dateutil.relativedelta import relativedelta
import pandas as pd
import telnetlib
from decimal import Decimal
import asyncio
from itertools import chain
import socks

logging.basicConfig(
    level=logging.ERROR,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# # 获取并设置 'okx' 和 'werkzeug' 的日志级别为 ERROR，忽略调试信息
# logging.getLogger('okx').setLevel(logging.ERROR)
# logging.getLogger('werkzeug').setLevel(logging.ERROR)
# logging.disable(logging.CRITICAL)


# 重写ConfigParser类，取消自动转换成小写的功能
class MyConfigParser(ConfigParser):

    def __init__(self, defaults=None):
        # 禁用插值功能，避免 % 字符引起的语法错误
        ConfigParser.__init__(self, defaults=defaults, interpolation=None)

    def optionxform(self, optionstr):
        return optionstr


# 文件操作类
class FileOpt:
    """
    最优化的文件操作类
    error_log 方法完全自动化，无需传参
    """

    def __init__(self, file_dir):
        self.file_dir = file_dir
        self._file_handle = None

    def _ensure_file_open(self):
        """确保文件处于打开状态"""
        if self._file_handle is None or self._file_handle.closed:
            self._file_handle = open(self.file_dir, "a", encoding="utf-8")

    # 从文件中读出匹配上的记录
    def read(self, pat):
        file = open(self.file_dir, "r")
        content = file.read()
        rst_list = re.compile(pat, re.M).findall(content)
        file.close()
        return rst_list

    # 把内容写入文件
    def write(self, content, model):
        if model == "a":
            self._ensure_file_open()
            self._file_handle.write(content + "\n")
            self._file_handle.flush()
        else:
            file = open(self.file_dir, model, encoding="utf-8")
            file.write(content + "\n")
            file.close()

    # 更新文件内容
    def replace(self, old_str, new_str):
        file_bak = "{0:s}.bak".format(self.file_dir)
        with open(self.file_dir, "r") as rf, open(file_bak, "w") as wf:
            for line in rf:
                wf.write(line.replace(old_str, new_str))
        os.remove(self.file_dir)
        os.rename(file_bak, self.file_dir)

    # 为日志打上时间标签
    def write_log(self, content, model="a", level="INFO"):
        tm = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))
        log_line = f"{tm} [{level}] {content}"
        self.write(log_line, model)

    def error_log(self, description: str = ""):
        """
        最优化的错误日志方法

        特点：
        1. 自动获取异常信息，无需传入traceback
        2. 自动捕获5个最重要的变量
        3. 不重复位置信息（Traceback已包含）
        4. 可选的错误描述

        Args:
            description: 可选的错误描述信息

        使用方式：
            # 最简单的用法
            sys_log.error_log()

            # 添加描述
            sys_log.error_log("更新聊天信息异常")
        """
        try:
            # 自动获取异常信息
            traceback_content = traceback.format_exc()

            # 获取调用者的重要变量
            important_vars = self._get_important_variables()

            # 构建增强信息
            enhanced_parts = []

            if description:
                enhanced_parts.append(f"描述: {description}")

            if important_vars:
                enhanced_parts.append(f"变量: {important_vars}")

            # 构建最终日志内容
            if enhanced_parts:
                enhanced_header = "\n".join(enhanced_parts)
                final_content = f"{enhanced_header}\n{traceback_content}"
            else:
                final_content = traceback_content

            # 写入日志
            self.write_log(final_content, "a", "ERROR")

        except Exception:
            # 增强功能失败时，回退到基本异常记录
            try:
                basic_traceback = traceback.format_exc()
                fallback_content = f"ERROR_LOG_ENHANCED_FAILED | {description}\n{basic_traceback}" if description else basic_traceback
                self.write_log(fallback_content, "a", "ERROR")
            except Exception:
                # 最后的保险
                self.write_log("CRITICAL_ERROR_LOG_FAILURE", "a", "ERROR")

    def _get_important_variables(self) -> str:
        """
        获取5个最重要的变量
        按重要性优先级排序
        """
        try:
            # 获取调用 error_log 的方法的栈帧
            frame = inspect.currentframe().f_back.f_back
            local_vars = frame.f_locals

            # 定义重要变量的优先级列表
            priority_variables = [
                # 第一优先级：ID类变量
                'user_id', 'chat_id', 'message_id', 'client_name', 'v_id',

                # 第二优先级：对象类变量
                'current_chat', 'message', 'callback', 'chatmember',

                # 第三优先级：业务变量
                'v_type', 'v_name', 'v_status', 'chat_type', 'user_type',

                # 第四优先级：其他重要变量
                'sql_str', 'params', 'rst', 'file_path', 'table_name'
            ]

            captured_vars = []

            # 按优先级查找变量
            for var_name in priority_variables:
                if var_name in local_vars:
                    var_value = local_vars[var_name]
                    formatted_value = self._format_variable_value(var_value)
                    if formatted_value:
                        captured_vars.append(f"{var_name}={formatted_value}")

                    # 达到5个变量就停止
                    if len(captured_vars) >= 5:
                        break

            # 如果优先级变量不足5个，查找其他有意义的变量
            if len(captured_vars) < 5:
                for var_name, var_value in local_vars.items():
                    # 跳过已捕获的变量
                    if any(var_name in captured for captured in captured_vars):
                        continue

                    # 跳过内置变量和无意义变量
                    if (var_name.startswith('__') or
                        var_name in ['self', 'cls', 'frame', 'traceback_content'] or
                        callable(var_value)):
                        continue

                    # 查找包含关键词的变量
                    if any(keyword in var_name.lower() for keyword in
                           ['id', 'name', 'type', 'status', 'data', 'info', 'result']):
                        formatted_value = self._format_variable_value(var_value)
                        if formatted_value:
                            captured_vars.append(f"{var_name}={formatted_value}")

                        if len(captured_vars) >= 5:
                            break

            return "\n".join(captured_vars)

        except Exception:
            return ""

    def _format_variable_value(self, value) -> str:
        """
        格式化变量值，确保输出简洁有用
        """
        try:
            if value is None:
                return "None"

            # 基本类型
            if isinstance(value, bool):
                return str(value)
            elif isinstance(value, (int, float)):
                return str(value)
            elif isinstance(value, str):
                # 字符串截断
                if len(value) > 30:
                    return f'"{value}"'

            # 容器类型
            elif isinstance(value, list):
                return value
            elif isinstance(value, tuple):
                return value
            elif isinstance(value, dict):
                return value
            elif isinstance(value, set):
                return value

            # 对象类型
            elif hasattr(value, 'id'):
                # Telegram对象或其他有id属性的对象
                obj_type = type(value).__name__
                return f"{obj_type}({value.id})"
            elif hasattr(value, '__dict__'):
                # 其他自定义对象
                obj_type = type(value).__name__
                return f"{obj_type}(obj)"
            else:
                # 其他类型
                return type(value).__name__

        except Exception:
            return "unknown"

    def debug_log(self, content):
        """调试日志的便捷方法"""
        self.write_log(content, "a", "DEBUG")

    def print_log(self, text: str):
        print(text)
        self.write_log(text)

    def clean_log(self, keep_num):
        with open(self.file_dir, "r", encoding="utf-8") as read_file:
            contents = read_file.readlines()

        if len(contents) > keep_num * 2:
            keep_contents = contents[-keep_num:]
            with open(self.file_dir, "w", encoding="utf-8") as write_file:
                write_file.writelines(keep_contents)


# 定义全局变量
client_list = os.environ.get("CLIENT_LIST", "").split(",") if os.environ.get("CLIENT_LIST") else []
sys_log = FileOpt(os.environ.get("SYS_LOG_FILE", "./log/log.txt"))


# 配置文件操作类
class ConfFile:

    def __init__(self, file_dir):
        self.file_dir = file_dir
        # 若配置文件不存在，则创建新
        pathlib.Path(self.file_dir).touch()
        self.config = MyConfigParser()
        self.config.read(self.file_dir, encoding="utf-8")

    # 写配置文件
    def write(self, section, option, value):
        try:
            self.config.set(section, str(option), str(value))
            self.config.write(open(self.file_dir, "w", encoding="utf-8"))
        except Exception as e:
            if re.match("^No section:.*", str(e)) is not None:
                self.config.add_section(section)
                self.write(section, option, value)
                return
            sys_log.error_log("配置文件写入异常")

    # 读配置文件
    def read(self, section, option, r_type="string"):
        try:
            if r_type == "string":
                return self.config.get(section, str(option))
            elif r_type == "int":
                return self.config.getint(section, str(option))
            elif r_type == "float":
                return self.config.getfloat(section, str(option))
            elif r_type == "boolean":
                return self.config.getboolean(section, str(option))
            else:
                return
        except Exception as e:
            if re.match("^No option .*? in section.*", str(e)) is not None:
                return
            sys_log.error_log("配置文件读取异常")
            return

    # 检查option是否存在
    def has_opt(self, section, option):
        try:
            return str(option) in self.config.options(section)
        except Exception:
            sys_log.error_log("检查配置选项异常")
            return False

    # 删除option操作
    def del_opt(self, section, option):
        try:
            self.config.remove_option(section, option)
            self.config.write(open(self.file_dir, "w"))
            return True
        except Exception:
            sys_log.error_log("删除配置选项异常")
            return False


# 改进型数据库操作类
class MySQLDB:
    # 危险SQL关键词模式
    DANGEROUS_PATTERNS = [
        r"\b(union\s+select)\b",
        r"\b(drop\s+table)\b",
        r"\b(delete\s+from)\b",
        r"\b(truncate\s+table)\b",
        r"\b(alter\s+table)\b",
        r"\b(create\s+table)\b",
        r"\b(insert\s+into)\b",
        r"\b(update\s+set)\b",
        r"(\-\-|\#|\/\*)",
        r"(\;.*\;)",
        r"(\'\s*or\s*\'\s*=\s*\')",
        r"(\'\s*or\s*1\s*=\s*1)",
        r"(\'\s*and\s*1\s*=\s*1)",
    ]

    def __init__(self, db_section):
        self.config = {
            "host": db_conf.read(db_section, "host"),
            "user": db_conf.read(db_section, "user"),
            "password": db_conf.read(db_section, "pwd"),
            "database": db_conf.read(db_section, "dbname"),
            "autocommit": False,
            "charset": "utf8mb4",
            "use_pure": True,
        }
        self.connection = ""
        self.cursor = ""
        self.last_conn = time.time()

    @classmethod
    def check_params(cls, params: Any) -> bool:
        # 将参数转换为字符串进行检查
        if isinstance(params, (list, tuple)):
            param_strings = [str(p) for p in params if p is not None]
        else:
            param_strings = [str(params)]

        for param_str in param_strings:
            param_lower = param_str.lower()
            for pattern in cls.DANGEROUS_PATTERNS:
                if re.search(pattern, param_lower, re.IGNORECASE):
                    return False
        return True

    # 连接数据库
    def open(self):
        try:
            self.connection = mysql.connector.connect(**self.config)
            self.cursor = self.connection.cursor()
            return True
        except Exception:
            sys_log.error_log("数据库连接异常")
            return False

    # 当60秒内第一次异常，尝试重连数据库
    def reopen(self):
        try:
            if time.time() - self.last_conn > 60:
                sys_log.write_log("尝试重连数据库...")
                self.last_conn = time.time()
                return self.open()
            else:
                return False
        except Exception:
            sys_log.error_log("数据库重连异常")
            return False

    # 连接数据库
    def close(self):
        try:
            if hasattr(self.cursor, 'close') and callable(self.cursor.close):
                self.cursor.close()
            if hasattr(self.connection, 'close') and callable(self.connection.close):
                self.connection.close()
            return True
        except Exception:
            sys_log.error_log("数据库关闭异常")
            return False

    # 开启事务
    def start_transaction(self):
        self.connection.start_transaction()

    # 提交
    def commit(self):
        self.connection.commit()

    # 回滚
    def rollback(self):
        self.connection.rollback()

    # 执行语句
    def run(self, sql_string, params_list=None):
        try:
            self.cursor.execute(sql_string, params_list)
            if sql_string.strip().upper().startswith("SELECT"):
                return self.cursor.fetchall()
            else:
                self.connection.commit()
                return self.cursor.rowcount
        except Exception:
            if self.reopen():
                return self.run(sql_string, params_list)
            else:
                sys_log.error_log(f"SQL执行异常: {sql_string}... 参数: {str(params_list)}...")
                if sql_string.strip().upper().startswith("SELECT"):
                    return ()
                else:
                    self.connection.rollback()
                    return -1

    # 执行不执行commit的非SELECT语句
    def exe_without_commit(self, sql_string, params_list=None):
        try:
            self.cursor.execute(sql_string, params_list)
            return self.cursor.rowcount
        except Exception:
            if self.reopen():
                return self.exe_without_commit(sql_string, params_list)
            else:
                sys_log.error_log(f"SQL执行异常(无提交): {sql_string[:100]}... 参数: {str(params_list)[:100]}...")
                return -1

    # 批量执行语句
    def run_many(self, sql_string, params_list):
        try:
            self.cursor.executemany(sql_string, params_list)
            return self.cursor.rowcount
        except Exception:
            if self.reopen():
                return self.run_many(sql_string, params_list)
            else:
                sys_log.error_log(f"SQL批量执行异常: {sql_string[:100]}... 参数数量: {len(params_list) if params_list else 0}")
                return -1

    # 执行存储过程
    def pro(self, proc_name, params_list):
        try:
            self.cursor.callproc(proc_name, params_list)
            # 返回查询结果
            return [
                result.fetchall()
                for result in list(self.cursor.stored_results())
            ]
        except Exception:
            if self.reopen():
                return self.pro(proc_name, params_list)
            else:
                sys_log.error_log(f"存储过程执行异常: {proc_name} 参数: {str(params_list)[:100]}...")
                return ()

    # 从txt文件中导入数据到数据表
    def import_from_txt(self, file_path, table):
        try:
            # 入库前记录数
            sql_string = "select count(1) from {0:s}".format(table)
            pre_cnt = self.run(sql_string)[0][0]
            with open(file_path, "r") as file:
                lines = file.readlines()
                params_list = [
                    tuple(line.strip().split(",")) for line in lines
                ]
                placeholders = ",".join(["%s"] * len(params_list[0]))
                sql_string = f"INSERT INTO {table} VALUES ({placeholders})"
                self.run_many(sql_string, params_list)
                file_cnt = sum(1 for line in file)
            # 入库后记录数
            aft_cnt = self.run(sql_string)[0][0]
            # 返回操作记录
            return file_cnt, pre_cnt, aft_cnt, aft_cnt - pre_cnt
        except Exception:
            if self.reopen():
                return self.import_from_txt(file_path, table)
            else:
                sys_log.error_log(f"文件导入数据异常: {file_path} -> {table}")
                return -1, -1, -1, -1

    # 从数据表中导出数据到txt文件
    def export_to_txt(self, table, file_path):
        try:
            sql_string = f"SELECT * FROM {table}"
            results = self.run(sql_string)
            with open(file_path, "w") as file:
                for row in results:
                    file.write(",".join(map(str, row)) + "\n")
        except Exception:
            if self.reopen():
                return self.export_to_txt(file_path, table)
            else:
                sys_log.error_log(f"数据导出文件异常: {table} -> {file_path}")


# 数据库相关变量
db_conf = ConfFile("./config/db_info.ini")
sys_conf = ConfFile("./config/sys_config.ini")
exe_connector = MySQLDB(os.environ.get("DB_WRITE_SECTION", ""))
exe_connector.open()
read_connector = MySQLDB(os.environ.get("DB_READ_SECTION", ""))
read_connector.open()


class Proxy:
    """
    代理管理类 - 统一管理所有SOCKS5代理操作
    
    基于新的ips表结构：
    - ip (varchar 20): IP地址
    - port (varchar 10): 端口，默认'1085'
    - name (varchar 20): 代理用户名，默认''
    - pwd (varchar 20): 代理密码，默认''
    - created_at (datetime): 建立时间
    - checked_at (datetime): 检查时间
    - check_status (char 1): 检查状态 0/1/2
    - used_at (datetime): 使用时间
    - use_cnt (int): 使用次数
    - use_status (char 1): 使用状态 0/1/2/3
    """
    
    def __init__(self):
        self.test_timeout: float = 5.0  # 测试超时时间
        self.max_workers: int = 10  # 最大并发数
        self.check_interval_hours: int = 24  # 检查间隔（小时）
        self.temp_use_hours: int = 24  # 临时使用时长（小时）
        self.test_targets: List[Tuple[str, int]] = [('www.google.com', 80)]  # 测试目标
    
    # ==================== 代理导入管理 ====================
    
    def load_from_file(self, file_path: str) -> Dict[str, int]:
        """
        从文件导入代理到数据库
        
        文件格式: ip,port,username,password (每行一个代理)
        如果没有用户名密码，可以只写 ip,port
        
        Args:
            file_path: 代理文件路径
            
        Returns:
            Dict: 导入统计 {'total': 总数, 'success': 成功数, 'failed': 失败数}
        """
        total = 0
        success = 0
        failed = 0
        
        try:
            sys_log.write_log("开始从文件导入代理: " + file_path)
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    total += 1
                    parts = line.split(',')
                    
                    if len(parts) < 2:
                        sys_log.error_log(f"第{line_num}行格式错误: {line}")
                        failed += 1
                        continue
                    
                    ip = parts[0].strip()
                    port = parts[1].strip() or '1085'
                    name = parts[2].strip() if len(parts) > 2 else ''
                    pwd = parts[3].strip() if len(parts) > 3 else ''

                    if self.add_proxy(ip, port, name, pwd):
                        success += 1
                    else:
                        failed += 1
            
            result = {'total': total, 'success': success, 'failed': failed}
            sys_log.write_log(f"文件导入完成: {result}")
            return result
            
        except Exception as e:
            sys_log.error_log(f"文件导入失败: {file_path} - {e}")
            raise
    
    def add_proxy(self, ip: str, port: str = '1085', name: str = '', pwd: str = '') -> bool:
        """
        添加单个代理

        Args:
            ip: IP地址
            port: 端口
            name: 代理用户名
            pwd: 代理密码

        Returns:
            bool: 是否添加成功
        """
        try:
            sql = """
            INSERT INTO ips (ip, port, name, pwd, created_at, checked_at, used_at)
            VALUES (%s, %s, %s, %s, NOW(), '2000-01-01 00:00:00', '2000-01-01 00:00:00')
            ON DUPLICATE KEY UPDATE
                port = VALUES(port),
                name = VALUES(name),
                pwd = VALUES(pwd)
            """
            affected = exe_connector.run(sql, [ip, port, name, pwd])
            
            if affected > 0:
                sys_log.write_log(f"代理添加成功: {ip}:{port}")
                return True
            return False
            
        except Exception as e:
            sys_log.error_log(f"代理添加失败: {ip}:{port} - {e}")
            return False
    
    # ==================== 代理测试管理 ====================
    
    def test_proxy(self, ip: str, port: str, name: str = '', pwd: str = '') -> bool:
        """
        测试单个SOCKS5代理是否可用

        Args:
            ip: IP地址
            port: 端口
            name: 代理用户名
            pwd: 代理密码

        Returns:
            bool: 代理是否可用
        """
        # 尝试每个测试目标
        for target_host, target_port in self.test_targets:
            try:
                if self._test_socks5(ip, int(port), target_host, target_port, name, pwd):
                    return True
            except Exception:
                continue
        
        # SOCKS5测试失败，回退到端口测试
        try:
            telnetlib.Telnet(ip, int(port), timeout=self.test_timeout)
            return True
        except Exception:
            return False
    
    def _test_socks5(self, ip: str, port: int, target_host: str, target_port: int,
                    name: str = '', pwd: str = '') -> bool:
        """测试SOCKS5代理连接"""
        try:
            sock = socks.socksocket()

            if name and pwd:
                sock.set_proxy(socks.SOCKS5, ip, port, username=name, password=pwd)
            else:
                sock.set_proxy(socks.SOCKS5, ip, port)
            
            sock.settimeout(self.test_timeout)
            sock.connect((target_host, target_port))
            sock.close()
            return True
        except Exception:
            return False
    
    def check_proxy(self, ip: str) -> str:
        """
        检查并更新单个代理状态
        
        Args:
            ip: IP地址
            
        Returns:
            str: 检查状态 ('0', '1', '2')
        """
        try:
            # 获取代理信息
            sql = "SELECT port, name, pwd FROM ips WHERE ip = %s"
            result = read_connector.run(sql, [ip])
            
            if not result:
                return '2'
            
            port, name, pwd = result[0]

            # 测试代理
            is_available = self.test_proxy(ip, port, name or '', pwd or '')
            status = '1' if is_available else '2'
            
            # 更新状态
            update_sql = "UPDATE ips SET check_status = %s, checked_at = NOW() WHERE ip = %s"
            exe_connector.run(update_sql, [status, ip])
            
            sys_log.write_log(f"代理检查完成: {ip}:{port} -> {status}")
            return status
            
        except Exception as e:
            sys_log.error_log(f"代理检查失败: {ip} - {e}")
            return '2'
    
    def batch_check(self, limit: int = 100) -> Dict[str, int]:
        """
        批量检查需要检查的代理
        
        Args:
            limit: 最大检查数量
            
        Returns:
            Dict: 检查结果统计
        """
        try:
            # 获取需要检查的代理
            sql = """
            SELECT ip, port, name, pwd
            FROM ips
            WHERE checked_at < DATE_SUB(NOW(), INTERVAL %s HOUR)
               OR check_status = '0'
            ORDER BY checked_at ASC, created_at DESC
            LIMIT %s
            """
            proxies = read_connector.run(sql, [self.check_interval_hours, limit])
            
            if not proxies:
                sys_log.write_log("没有需要检查的代理")
                return {'total': 0, 'available': 0, 'unavailable': 0}
            
            sys_log.write_log(f"开始批量检查 {len(proxies)} 个代理")
            
            # 并发测试
            available_count = 0
            unavailable_count = 0
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交测试任务
                future_to_proxy = {
                    executor.submit(self.test_proxy, row[0], row[1], row[2] or '', row[3] or ''): row[0]
                    for row in proxies
                }
                
                # 收集结果并更新数据库
                for future in concurrent.futures.as_completed(future_to_proxy):
                    ip = future_to_proxy[future]
                    try:
                        is_available = future.result()
                        status = '1' if is_available else '2'
                        
                        # 更新数据库
                        update_sql = "UPDATE ips SET check_status = %s, checked_at = NOW() WHERE ip = %s"
                        exe_connector.run(update_sql, [status, ip])
                        
                        if is_available:
                            available_count += 1
                        else:
                            unavailable_count += 1
                            
                    except Exception as e:
                        sys_log.error_log(f"代理测试异常: {ip} - {e}")
                        unavailable_count += 1
            
            result = {
                'total': len(proxies),
                'available': available_count,
                'unavailable': unavailable_count
            }
            
            sys_log.write_log(f"批量检查完成: {result}")
            return result
            
        except Exception as e:
            sys_log.error_log(f"批量检查失败: {e}")
            raise
    
    # ==================== 代理分配管理 ====================
    
    def get_proxy(self, use_type: str = 'temporary') -> Optional[Tuple[str, str, str, str]]:
        """
        获取一个可用代理
        
        Args:
            use_type: 使用类型 ('temporary', 'permanent', 'instant')
            
        Returns:
            Tuple: (ip, port, name, pwd) 或 None
        """
        # 映射使用类型
        use_status_map = {
            'temporary': '1',
            'permanent': '2',
            'instant': '3'
        }
        use_status = use_status_map.get(use_type, '1')
        
        try:
            # 获取候选代理
            sql = """
            SELECT ip, port, name, pwd
            FROM ips
            WHERE check_status IN ('0', '1')
              AND use_status = '0'
            ORDER BY use_cnt ASC, used_at ASC, checked_at DESC
            LIMIT 5
            """
            candidates = read_connector.run(sql)
            
            if not candidates:
                sys_log.write_log("没有可用的代理")
                return None
            
            # 逐个测试候选代理
            for row in candidates:
                ip, port, name, pwd = row

                # 测试代理
                if self.test_proxy(ip, port, name or '', pwd or ''):
                    # 分配代理
                    if self._allocate_proxy(ip, use_status):
                        sys_log.write_log(f"代理分配成功: {ip}:{port} ({use_type})")
                        return (ip, port, name or '', pwd or '')
                else:
                    # 标记为不可用
                    self._mark_unavailable(ip)
            
            sys_log.write_log("所有候选代理都不可用")
            return None
            
        except Exception as e:
            sys_log.error_log(f"获取代理失败: {e}")
            return None
    
    def release_proxy(self, ip: str) -> bool:
        """
        释放代理
        
        Args:
            ip: IP地址
            
        Returns:
            bool: 是否释放成功
        """
        try:
            sql = "UPDATE ips SET use_status = '0', used_at = NOW() WHERE ip = %s"
            affected = exe_connector.run(sql, [ip])
            
            if affected > 0:
                sys_log.write_log(f"代理释放成功: {ip}")
                return True
            return False
            
        except Exception as e:
            sys_log.error_log(f"代理释放失败: {ip} - {e}")
            return False
    
    def _allocate_proxy(self, ip: str, use_status: str) -> bool:
        """分配代理"""
        try:
            if use_status == '3':
                # 瞬间使用：增加计数但不改变状态
                sql = "UPDATE ips SET use_cnt = use_cnt + 1, used_at = NOW() WHERE ip = %s"
                affected = exe_connector.run(sql, [ip])
            else:
                # 其他类型：设置状态并增加计数
                sql = """
                UPDATE ips 
                SET use_status = %s, use_cnt = use_cnt + 1, used_at = NOW(), check_status = '1'
                WHERE ip = %s
                """
                affected = exe_connector.run(sql, [use_status, ip])
            
            return affected > 0
            
        except Exception as e:
            sys_log.error_log(f"代理分配失败: {ip} - {e}")
            return False
    
    def _mark_unavailable(self, ip: str):
        """标记代理为不可用"""
        try:
            sql = "UPDATE ips SET check_status = '2', checked_at = NOW() WHERE ip = %s"
            exe_connector.run(sql, [ip])
        except Exception as e:
            sys_log.error_log(f"标记代理不可用失败: {ip} - {e}")
    
    # ==================== 代理维护管理 ====================
    
    def cleanup(self) -> Dict[str, int]:
        """
        清理过期和无效状态
        
        Returns:
            Dict: 清理统计
        """
        try:
            # 清理过期的临时使用状态
            sql1 = """
            UPDATE ips 
            SET use_status = '0' 
            WHERE use_status = '1' 
              AND used_at < DATE_SUB(NOW(), INTERVAL %s HOUR)
            """
            temp_cleaned = exe_connector.run(sql1, [self.config.temp_use_hours])
            
            # 清理不在clients表中的长期使用状态
            sql2 = """
            UPDATE ips 
            SET use_status = '0' 
            WHERE use_status = '2' 
              AND ip NOT IN (SELECT DISTINCT proxy_ip FROM clients WHERE proxy_ip IS NOT NULL AND proxy_ip != '')
            """
            perm_cleaned = exe_connector.run(sql2)
            
            result = {
                'temp_cleaned': temp_cleaned,
                'perm_cleaned': perm_cleaned,
                'total_cleaned': temp_cleaned + perm_cleaned
            }
            
            if result['total_cleaned'] > 0:
                sys_log.write_log(f"状态清理完成: {result}")
            
            return result
            
        except Exception as e:
            sys_log.error_log(f"状态清理失败: {e}")
            return {'temp_cleaned': 0, 'perm_cleaned': 0, 'total_cleaned': 0}
    
    # ==================== 统计分析 ====================
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取代理池统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            stats = {}
            
            # 总体统计
            total_sql = "SELECT COUNT(*) FROM ips"
            stats['total'] = read_connector.run(total_sql)[0][0]
            
            # 按检查状态统计
            check_sql = "SELECT check_status, COUNT(*) FROM ips GROUP BY check_status"
            check_data = read_connector.run(check_sql)
            stats['by_check_status'] = {row[0]: row[1] for row in check_data}
            
            # 按使用状态统计
            use_sql = "SELECT use_status, COUNT(*) FROM ips GROUP BY use_status"
            use_data = read_connector.run(use_sql)
            stats['by_use_status'] = {row[0]: row[1] for row in use_data}
            
            # 需要检查的代理数量
            need_check_sql = """
            SELECT COUNT(*) FROM ips 
            WHERE checked_at < DATE_SUB(NOW(), INTERVAL %s HOUR) OR check_status = '0'
            """
            stats['need_check'] = read_connector.run(need_check_sql, [self.check_interval_hours])[0][0]
            
            # 可用代理数量
            available_sql = "SELECT COUNT(*) FROM ips WHERE check_status = '1' AND use_status = '0'"
            stats['available'] = read_connector.run(available_sql)[0][0]
            
            # 计算成功率
            checked_total = sum(stats['by_check_status'].values()) - stats['by_check_status'].get('0', 0)
            available_count = stats['by_check_status'].get('1', 0)
            stats['success_rate'] = (available_count / checked_total * 100) if checked_total > 0 else 0
            
            return stats
            
        except Exception as e:
            sys_log.error_log(f"获取统计信息失败: {e}")
            return {}


proxy_manager = Proxy()


# 字段转义
def explain(type, text, point="to"):
    try:
        to_dic = {}
        # 设定转义内容
        if type == "UserStatus":
            to_dic = {
                "1": UserStatus.ONLINE,
                "2": UserStatus.OFFLINE,
                "3": UserStatus.RECENTLY,
                "4": UserStatus.LAST_WEEK,
                "5": UserStatus.LAST_MONTH,
                "6": UserStatus.LONG_AGO,
            }
        elif type == "ChatType":
            to_dic = {
                "user": ChatType.PRIVATE,
                "bot": ChatType.BOT,
                "group": ChatType.GROUP,
                "supergroup": ChatType.SUPERGROUP,
                "channel": ChatType.CHANNEL,
                "forum": ChatType.FORUM,
                "direct": ChatType.DIRECT,
            }
        elif type == "ChatMemberStatus":
            to_dic = {
                0: ChatMemberStatus.OWNER,
                1: ChatMemberStatus.ADMINISTRATOR,
                2: ChatMemberStatus.MEMBER,
                3: ChatMemberStatus.LEFT,
                4: ChatMemberStatus.RESTRICTED,
                5: ChatMemberStatus.BANNED,
            }
        elif type == "bool":
            to_dic = {"0": False, "1": True}
        elif type == "MyclientStatus":
            to_dic = {
                "00": "待注册",
                "01": "待初始化",
                "02": "待养号",
                "10": "正常",
                "20": "限制",
                "30": "禁用",
                "40": "销号",
            }
        elif type == "JIAMI":
            to_dic = {
                "0": "O",
                "1": "I",
                "2": "Z",
                "3": "E",
                "4": "X",
                "5": "S",
                "6": "G",
                "7": "J",
                "8": "B",
                "9": "P",
            }
        # 确定转义方向
        if point == "to":
            return to_dic.get(text, None)
        elif point == "from":
            from_dict = {v: k for k, v in to_dic.items()}
            return from_dict.get(text, None)
        else:
            return None
    except Exception:
        sys_log.error_log("获取代理信息异常")
        return False


# 剔除特殊字符
def correct_char(sour_str):
    return "".join(re.findall( r"[\u4e00-\u9fa5a-zA-Z0-9 （）_()&%#@!$^【】.:~\-/,，。！]+", sour_str, re.S, ))


# 极简版参数操作类
class Params:
    """
    极简版参数管理类

    特点：
    1. 极简接口：params['config.timeout'] = 30, value = params['config.timeout']
    2. 智能类型推断：自动判断并转换类型
    3. 数据库结构简化：id, type, value, note 四个字段
    4. 开发者友好：note字段由DBA管理，代码不涉及

    数据库表结构：
    - id (varchar 50): 参数ID，如 config.timeout, config
    - type (varchar 50): 类型，string/int/float/bool/datetime/decimal
    - value (varchar 50): 字符串形式的值
    - note (varchar 255): 备注，由DBA管理

    使用示例：
    params['config.timeout'] = 30                    # 自动识别为int
    params['config.rate'] = 0.95                     # 自动识别为float
    params['config.price'] = "123.45D"               # 识别为decimal
    params['config.enabled'] = True                  # 自动识别为bool
    params['config.start_time'] = '2024-01-01 10:00:00'  # 识别为datetime

    timeout = params['config.timeout']               # 返回int类型的30
    """

    def __init__(self):
        """
        初始化参数管理器
        """
        self._cache = {}  # 简单缓存

        # SQL语句
        self._select_sql = "SELECT type, value FROM params WHERE id = %s"
        self._insert_sql = "INSERT INTO params (id, type, value) VALUES (%s, %s, %s)"
        self._update_sql = "UPDATE params SET type = %s, value = %s WHERE id = %s"

    def __getitem__(self, key: str):
        """
        获取参数值，返回对应类型的值

        Args:
            key: 参数ID，如 'config.timeout'

        Returns:
            对应类型的参数值，如果不存在返回None
        """
        # 检查缓存
        if key in self._cache:
            return self._cache[key]

        try:
            result = read_connector.run(self._select_sql, [key])
            if result:
                type_str, value_str = result[0]
                value = self._convert_from_string(type_str, value_str)

                # 缓存结果，如果缓存超过50个则清空
                if len(self._cache) >= 50:
                    self._cache.clear()
                self._cache[key] = value
                return value
            else:
                return None

        except Exception as e:
            sys_log.error_log(f"获取参数失败: {key} - {e}")
            return None

    def __setitem__(self, key: str, value):
        """
        设置参数值，自动判断类型

        Args:
            key: 参数ID，如 'config.timeout'
            value: 参数值，自动推断类型
        """
        try:
            # 自动推断类型
            type_str = self._detect_type(value)
            value_str = self._convert_to_string(value, type_str)

            # 检查参数是否已存在
            if self._exists(key):
                # 更新现有参数
                affected = exe_connector.run(self._update_sql, [type_str, value_str, key])
            else:
                # 插入新参数
                affected = exe_connector.run(self._insert_sql, [key, type_str, value_str])

            if affected > 0:
                # 更新缓存 - 存储转换后的真实值
                converted_value = self._convert_from_string(type_str, value_str)
                self._cache[key] = converted_value
                # sys_log.write_log(f"参数设置成功: {key} = {value} ({type_str})")
            else:
                sys_log.write_log(f"参数设置无效果: {key}")

        except Exception as e:
            sys_log.error_log(f"参数设置失败: {key} = {value} - {e}")

    def _detect_type(self, value):
        """
        自动检测值的类型

        Args:
            value: 要检测的值

        Returns:
            类型字符串: string/int/float/bool/datetime/decimal
        """
        # 处理字符串形式的decimal（以D结尾）
        if isinstance(value, str) and value.endswith('D'):
            try:
                # 验证是否为有效的decimal格式
                Decimal(value[:-1])
                return 'decimal'
            except:
                pass

        # 处理datetime格式的字符串
        if isinstance(value, str) and self._is_datetime_string(value):
            return 'datetime'

        # 处理基本类型
        if isinstance(value, bool):
            return 'bool'
        elif isinstance(value, int):
            return 'int'
        elif isinstance(value, float):
            return 'float'
        elif isinstance(value, datetime.datetime):
            return 'datetime'
        elif isinstance(value, Decimal):
            return 'decimal'
        else:
            return 'string'

    def _is_datetime_string(self, value: str) -> bool:
        """
        检查字符串是否为 %Y-%m-%d %H:%M:%S 格式的日期时间

        Args:
            value: 要检查的字符串

        Returns:
            是否为日期时间格式
        """
        try:
            datetime.datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
            return True
        except ValueError:
            return False

    def _convert_to_string(self, value, type_str: str) -> str:
        """
        将值转换为字符串存储

        Args:
            value: 要转换的值
            type_str: 类型字符串

        Returns:
            字符串形式的值
        """
        if value is None:
            return ''
        elif type_str == 'bool':
            return 'true' if value else 'false'
        elif type_str == 'datetime':
            if isinstance(value, datetime.datetime):
                return value.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return str(value)  # 已经是字符串格式
        elif type_str == 'decimal':
            if isinstance(value, str) and value.endswith('D'):
                return value[:-1]  # 移除D后缀
            else:
                return str(value)
        else:
            return str(value)

    def _convert_from_string(self, type_str: str, value_str: str):
        """
        从字符串转换为对应类型

        Args:
            type_str: 类型字符串
            value_str: 字符串值

        Returns:
            对应类型的值
        """
        if not value_str:
            return None

        try:
            if type_str == 'int':
                return int(value_str)
            elif type_str == 'float':
                return float(value_str)
            elif type_str == 'bool':
                return value_str.lower() in ('true', '1', 't', 'yes')
            elif type_str == 'datetime':
                return datetime.datetime.strptime(value_str, '%Y-%m-%d %H:%M:%S')
            elif type_str == 'decimal':
                return Decimal(value_str)
            else:
                return value_str
        except (ValueError, TypeError) as e:
            sys_log.error_log(f"类型转换失败: {type_str} '{value_str}' - {e}")
            return None

    # ==================== 实用方法 ====================

    def _exists(self, key: str) -> bool:
        """
        检查参数是否存在（内部方法）

        Args:
            key: 参数ID

        Returns:
            是否存在
        """
        try:
            result = read_connector.run(self._select_sql, [key])
            return len(result) > 0
        except Exception:
            return False

    def delete(self, key: str) -> bool:
        """
        删除参数

        Args:
            key: 参数ID

        Returns:
            是否删除成功
        """
        try:
            sql = "DELETE FROM params WHERE id = %s"
            affected = exe_connector.run(sql, [key])

            if affected > 0:
                # 清除缓存
                if key in self._cache:
                    del self._cache[key]

                sys_log.write_log(f"参数删除成功: {key}")
                return True

            return False

        except Exception as e:
            sys_log.error_log(f"参数删除失败: {key} - {e}")
            return False



    def refresh(self, key: str):
        """
        刷新参数值（从数据库重新加载）

        Args:
            key: 参数ID

        Returns:
            刷新后的参数值
        """
        # 清除缓存
        if key in self._cache:
            del self._cache[key]

        # 重新获取
        return self[key]

    def get_by_prefix(self, prefix: str) -> dict:
        """
        获取指定前缀的所有参数

        Args:
            prefix: 前缀，如 'config'（会自动添加'.'进行精确匹配）

        Returns:
            参数字典
        """
        try:
            # 精确匹配前缀，添加'.'确保不会匹配到configs等
            sql = "SELECT id, type, value FROM params WHERE id LIKE %s ORDER BY id"
            results = read_connector.run(sql, [f"{prefix}.%"])

            params = {}
            for row in results:
                key, type_str, value_str = row
                value = self._convert_from_string(type_str, value_str)
                params[key] = value

                # 更新缓存，如果缓存超过50个则清空
                if len(self._cache) >= 50:
                    self._cache.clear()
                self._cache[key] = value

            return params

        except Exception as e:
            sys_log.error_log(f"获取前缀参数失败: {prefix} - {e}")
            return {}


params = Params()


# 对应表chats
@dataclass
class TChats:
    """
    聊天信息数据类
    对应chats表结构
    """
    id: int
    type: str
    name: str
    outname: str
    title: str
    username: str
    first_name: str
    last_name: str
    status: str
    online_date: datetime.datetime

    def short_outname(self, length) -> str:
        return (self.outname if len(self.outname) <= length else
                self.outname[:length] + "..")

    def to_list(self) -> list:
        return [
            self.id,
            self.type,
            self.name,
            self.outname,
            self.title,
            self.username,
            self.first_name,
            self.last_name,
            self.status,
            self.online_date,
        ]


class MyChats:

    def __init__(self):
        self.__sql_id = "select id,type,name,outname,title,username,first_name,last_name,status,online_date from chats where id=%s"
        self.__sql_name = "select id,type,name,outname,title,username,first_name,last_name,status,online_date from chats where name=%s"
        self._cache = {}

    def __getitem__(self, id) -> Optional[TChats]:
        if id not in self._cache:
            rst = read_connector.run(self.__sql_id, [id])
            if len(rst) > 0:
                self._cache[id] = TChats(*rst[0])
        return self._cache.get(id, None)

    # 通过name来获取MyChat的信息
    def name(self, name) -> Optional[TChats]:
        if name not in self._cache:
            rst = read_connector.run(self.__sql_name, [name])
            if len(rst) > 0:
                self._cache[name] = TChats(*rst[0])
        return self._cache.get(name, None)

    # 更新chat信息
    def update_chat(self, current_chat):
        try:
            # 缓存管理：改为1000条限制（原来是2000）
            if len(self._cache) > 1000:
                self._cache.clear()
            v_id = current_chat.id
            # 收集User类型的信息
            if type(current_chat) == User:
                v_title = ""
                # v_type
                if current_chat.is_bot is not None and current_chat.is_bot:
                    v_type = "bot"
                else:
                    v_type = "user"
                # v_status
                if current_chat.status is not None:
                    v_status = explain("UserStatus", current_chat.status,
                                       "from")
                else:
                    v_status = "0"
                if current_chat.is_deleted is not None and current_chat.is_deleted:
                    v_status = "7"
                # v_online_date
                if current_chat.last_online_date is not None:
                    v_online_date = current_chat.last_online_date
                elif current_chat.next_offline_date is not None:
                    v_online_date = current_chat.next_offline_date
                else:
                    v_online_date = datetime.datetime.now()
            # 收集Chat类型的信息
            else:
                v_title = correct_char("" if current_chat.title is None else current_chat.title)
                v_type = explain("ChatType", current_chat.type, "from")
                if current_chat.is_deactivated is not None and current_chat.is_deactivated:
                    v_status = "9"
                else:
                    v_status = "0"
                v_online_date = datetime.datetime.strptime(
                    "2000-01-01 00:00:00", "%Y-%m-%d %H:%M:%S")
            # 收集共有的变量
            v_username = correct_char("" if current_chat.username is None else current_chat.username)
            v_first_name = correct_char("" if current_chat.first_name is None else current_chat.first_name)
            v_last_name = correct_char("" if current_chat.last_name is None else current_chat.last_name)
            v_outname = v_title + v_first_name + v_last_name
            v_outname = "_" if v_outname == "" else v_outname
            # v_status
            if current_chat.is_restricted is not None and current_chat.is_restricted:
                v_status = "8"
            # 检查内存中id是否已存在
            if v_id in self._cache:
                # 同步有更新的信息
                if (
                        v_type,
                        v_outname,
                        v_title,
                        v_username,
                        v_first_name,
                        v_last_name,
                        v_status,
                        v_online_date.strftime("%Y-%m-%d"),
                ) != (
                        self._cache[v_id].type,
                        self._cache[v_id].outname,
                        self._cache[v_id].title,
                        self._cache[v_id].username,
                        self._cache[v_id].first_name,
                        self._cache[v_id].last_name,
                        self._cache[v_id].status,
                        self._cache[v_id].online_date.strftime("%Y-%m-%d"),
                ):
                    # chat不更新user信息
                    if type(current_chat
                            ) == Chat and self._cache[v_id].type == "user":
                        return
                    sql_str = (
                        "update chats set type='{1:s}', outname='{2:s}', title='{3:s}', username='{4:s}', "
                        "first_name='{5:s}', last_name='{6:s}', status=if('{7:s}'<>'0','{7:s}',status), "
                        "online_date=if(online_date<'{8:%Y-%m-%d %X}', '{8:%Y-%m-%d %X}', online_date) where "
                        "id = {0:d}".format(
                            v_id,
                            v_type,
                            v_outname,
                            v_title,
                            v_username,
                            v_first_name,
                            v_last_name,
                            v_status,
                            v_online_date,
                        ))
                    exe_connector.run(sql_str)
                    self.__getitem__(v_id)
            else:
                sql_str = f"select id from chats where name = '{v_outname}'"
                rst = read_connector.run(sql_str)
                if len(rst) > 0 and rst[0][0] != v_id:
                    v_name = v_outname + "_" + str(v_id)
                else:
                    v_name = v_outname
                sql_str = (
                    "insert into chats(id,type,name,outname,title,username,first_name,last_name,status,"
                    "online_date) values({0:d},'{1:s}','{2:s}','{3:s}','{4:s}','{5:s}','{6:s}','{7:s}','{8:s}',"
                    "'{9:%Y-%m-%d %X}') on duplicate key update type='{1:s}',outname='{3:s}',title='{4:s}',"
                    "username='{5:s}',first_name='{6:s}',last_name='{7:s}',status=if('{8:s}'<>'0','{8:s}',"
                    "status),online_date=if(online_date<'{9:%Y-%m-%d %X}', '{9:%Y-%m-%d %X}', "
                    "online_date)".format(
                        v_id,
                        v_type,
                        v_name,
                        v_outname,
                        v_title,
                        v_username,
                        v_first_name,
                        v_last_name,
                        v_status,
                        v_online_date,
                    ))
                exe_connector.run(sql_str)
                self.__getitem__(v_id)
        except Exception:
            sys_log.error_log("更新聊天信息异常")

    # 更新message中的chat信息
    def update_message(self, message):
        try:
            # 为了更新user的状态，User类型需要优先获取
            if message.from_user is not None:
                self.update_chat(message.from_user)
            if message.new_chat_members is not None:
                for new_chat_member in message.new_chat_members:
                    self.update_chat(new_chat_member)
            if message.left_chat_member is not None:
                self.update_chat(message.left_chat_member)
            if message.sender_chat is not None:
                self.update_chat(message.sender_chat)
            if message.chat is not None:
                self.update_chat(message.chat)
            if message.via_bot is not None:
                self.update_chat(message.via_bot)
            if message.sender_business_bot is not None:
                self.update_chat(message.sender_business_bot)
        except Exception:
            sys_log.error_log("更新消息聊天信息异常")

    # 更新callback中的chat信息
    def update_callback(self, callback):
        try:
            # 为了更新user的状态，User类型需要优先获取
            if callback.from_user is not None:
                self.update_chat(callback.from_user)
            if callback.message is not None:
                self.update_message(callback.message)
        except Exception:
            sys_log.error_log("更新回调聊天信息异常")

    # 更新ChatMember中的chat信息
    def update_chatmember(self, chatmember):
        try:
            # 为了更新user的状态，User类型需要优先获取
            if chatmember.user is not None:
                self.update_chat(chatmember.user)
            if chatmember.chat is not None:
                self.update_chat(chatmember.chat)
            if chatmember.invited_by is not None:
                self.update_chat(chatmember.invited_by)
            if chatmember.promoted_by is not None:
                self.update_chat(chatmember.promoted_by)
            if chatmember.restricted_by is not None:
                self.update_chat(chatmember.restricted_by)
        except Exception:
            sys_log.error_log("更新群成员聊天信息异常")


myChats = MyChats()


@dataclass
class TClients:
    """
    Telegram客户端配置数据类
    基于最新的clients表结构，包含完整的代理配置
    """
    username: str
    is_bot: int
    proxy_ip: str
    proxy_port: str
    proxy_name: str
    proxy_pwd: str
    token: str
    api_id: int
    api_hash: str
    note: str

    @property
    def is_bot_account(self) -> bool:
        """判断是否为机器人账户"""
        return self.is_bot == 1

    @property
    def has_proxy(self) -> bool:
        """判断是否配置了代理"""
        return bool(self.proxy_ip.strip())


class Clients:

    def __init__(self, username_list):
        try:
            client_str = "','".join(username_list)
            sql_str = f"""
            SELECT username, is_bot, proxy_ip, proxy_port, proxy_name, proxy_pwd,
                   token, api_id, api_hash, note
            FROM clients
            WHERE username IN ('{client_str}')
            """
            rst = read_connector.run(sql_str)
            self._cache = {}  # username -> Client实例
            self._client_to_config = {}  # Client实例 -> TClients实例
            proxy = {}
            for row in rst:
                tclient = TClients(*row)
                proxy = {}

                # 处理代理配置
                if tclient.has_proxy:
                    proxy_port = tclient.proxy_port if tclient.proxy_port else '1085'

                    # 测试代理是否可用（支持认证）
                    if proxy_manager.test_proxy(tclient.proxy_ip, proxy_port,
                                              tclient.proxy_name, tclient.proxy_pwd):
                        # 使用现有代理
                        proxy = {
                            'scheme': 'socks5',
                            'hostname': tclient.proxy_ip,
                            'port': int(proxy_port)
                        }
                        if tclient.proxy_name and tclient.proxy_pwd:
                            proxy['username'] = tclient.proxy_name
                            proxy['password'] = tclient.proxy_pwd

                        sys_log.print_log(f"使用代理登录tg: {tclient.username}->{tclient.proxy_ip}:{proxy_port}")
                    else:
                        # 代理不可用，尝试获取新代理
                        new_proxy = proxy_manager.get_proxy('permanent')
                        if new_proxy:
                            ip, port, name, pwd = new_proxy
                            # 更新数据库中的代理配置
                            update_sql = """
                            UPDATE clients
                            SET proxy_ip=%s, proxy_port=%s, proxy_name=%s, proxy_pwd=%s
                            WHERE username=%s
                            """
                            exe_connector.run(update_sql, [ip, port, name, pwd, tclient.username])

                            proxy = {
                                'scheme': 'socks5',
                                'hostname': ip,
                                'port': int(port)
                            }
                            if name and pwd:
                                proxy['username'] = name
                                proxy['password'] = pwd

                            sys_log.print_log(f"使用新代理登录tg: {tclient.username}->{ip}:{port}")
                        else:
                            # 清除无效代理配置
                            clear_sql = """
                            UPDATE clients
                            SET proxy_ip='', proxy_port='', proxy_name='', proxy_pwd=''
                            WHERE username=%s
                            """
                            exe_connector.run(clear_sql, [tclient.username])
                            sys_log.print_log(f"无可用代理，直接使用服务器ip: {tclient.username}")
                else:
                    sys_log.print_log(f"没有设置代理，直接使用服务器ip: {tclient.username}")

                # 获取插件配置
                plugins_config = sys_conf.read("plugins", tclient.username)
                plugins = None
                if plugins_config:
                    parts = plugins_config.split("|")
                    if len(parts) == 1:
                        plugins = {
                            'root': 'plugins',
                            'include': parts[0].split(",")
                        }
                    else:
                        plugins = {
                            'root': 'plugins',
                            'include': parts[0].split(","),
                            'exclude': parts[1].split(",")
                        }

                # 构造Client参数（硬编码客户端信息）
                client_params = {
                    'name': f"sessions/{tclient.username}",  # 硬编码sessions目录
                    'api_id': tclient.api_id,
                    'api_hash': tclient.api_hash,
                    'app_version': 'KobeePro',    # 硬编码保护隐私
                    'device_model': 'PC',        # 硬编码保护隐私
                    'system_version': 'Linux',   # 硬编码保护隐私
                    'parse_mode': ParseMode.HTML
                    # 'parse_mode': ParseMode.HTML,
                    # 'link_preview_options': LinkPreviewOptions(is_disabled=True)
                }

                # 配置机器人令牌
                if tclient.is_bot_account:
                    client_params['bot_token'] = tclient.token

                # 添加代理配置
                if proxy:
                    client_params['proxy'] = proxy

                # 配置插件和更新策略：无论bot还是user，都根据plugins决定
                if plugins:
                    client_params['plugins'] = plugins
                else:
                    # 无插件时禁用更新
                    client_params['no_updates'] = True

                # 创建Client实例
                client_instance = Client(**client_params)
                self._cache[tclient.username] = client_instance
                self._client_to_config[client_instance] = tclient

        except Exception:
            sys_log.error_log("客户端初始化异常")

    def __getitem__(self, username) -> Client:
        """通过用户名获取Client实例"""
        return self._cache[username]

    def get_username(self, client: Client) -> str:
        return self._client_to_config[client].username

    def keys(self) -> list:
        return list(self._cache.keys())


clients = Clients(client_list)


class TChatUser:

    def __init__(self, values):
        (
            self.chat_id,
            self.chat_name,
            self.user_id,
            self.user_name,
            self.status,
            self.active_at,
            self.joined_at,
        ) = values


class ChatUser:

    def __init__(self):
        self.__sql_str = (
            "select chat_id,chat_name,user_id,user_name,status,active_at,joined_at from chat_user "
            "where chat_id=%s and user_id=%s")
        self._cache = {}

    # 获取chat_user的信息
    def __getitem__(self, args) -> TChatUser | None:
        (chat_id, user_id) = args
        key = str(chat_id) + "|" + str(user_id)
        if key not in self._cache:
            rst = read_connector.run(self.__sql_str, [chat_id, user_id])
            if len(rst) > 0:
                self._cache[key] = TChatUser(rst[0])
        return self._cache.get(key, None)

    # 把新的群成员关系入库
    def update_table(self, member: ChatMember, chat_id, record_user_status):
        try:
            myChats.update_chatmember(member)
            user_id = member.user.id
            status = explain("ChatMemberStatus", member.status, "from")

            if str(user_id) + "|" + str(status) not in record_user_status:
                joined_at = member.joined_date if member.joined_date is not None else datetime.datetime.now()

                sql_str = """
                INSERT INTO chat_user(chat_id, chat_name, user_id, user_name, status, joined_at)
                VALUES(%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                chat_name=%s, user_name=%s, status=%s, joined_at=LEAST(joined_at, %s)
                """
                chat_info = myChats[chat_id]
                user_info = myChats[user_id]

                exe_connector.run(sql_str, [
                    chat_id, chat_info.name, user_id, user_info.name, status, joined_at,
                    chat_info.name, user_info.name, status, joined_at
                ])
        except KeyError as e:
            sys_log.error_log(f"数据缺失异常 - chat_id: {chat_id}, user_id: {user_id}, 缺少键: {e}")
        except Exception as e:
            sys_log.error_log(f"更新群成员信息异常 - chat_id: {chat_id}, user_id: {user_id}, error: {e}")

    # 更新群成员信息
    async def update_chat_user(self, client_name, chat_id, no_force=True):
        try:
            chat_name = myChats[chat_id].name
            sys_log.write_log(f"检查是否需要更新群: {client_name}->{chat_name}")
            # 对于非管理员的Channel，不更新
            if myChats[chat_id].type == "channel":
                me = await clients[client_name].get_chat_member(chat_id, "me")
                await asyncio.sleep(3)
                if me.status not in (
                        ChatMemberStatus.OWNER,
                        ChatMemberStatus.ADMINISTRATOR,
                ):
                    sys_log.write_log(
                        f"非管理员的频道不更新: {client_name}->{chat_name}")
                    return
            # 判断是否到达更新时间
            sql_str = (
                f"select all_cnt,record_cnt, (case when time_to_sec(timediff(now(), updated_at))>("
                f"all_cnt/3000+2)*86400 then 1 else 0 end) from chat_user_info where chat_id = {chat_id}"
            )
            rst = read_connector.run(sql_str)
            if len(rst) != 0 and rst[0][2] == 0 and no_force:
                sys_log.write_log(f"更新周期内不更新: {client_name}->{chat_name}")
                return
            v_all_cnt = await clients[client_name].get_chat_members_count(
                chat_id)
            await asyncio.sleep(3)
            if v_all_cnt == 0:
                sys_log.write_log(f"目标群无成员不更新: {client_name}->{chat_name}")
                return
            sql_str = f"select count(1) from chat_user where chat_id = {chat_id}"
            v_record_cnt = read_connector.run(sql_str)[0][0]
            if (len(rst) != 0 and rst[0][1] == v_record_cnt
                    and rst[0][0] == v_all_cnt and no_force):
                sys_log.write_log(
                    f"成员数无变动不更新: {client_name}->{chat_name} 总人数：{v_all_cnt}")
                return
            sys_log.write_log(
                f"开始更新群: {client_name}->{chat_name} 官方总人数：{v_all_cnt}")
            # 提取已记录成员信息
            sql_str = f"select concat(user_id,'|',status) from chat_user where chat_id={chat_id}"
            rst = read_connector.run(sql_str)
            record_user_status = set(chain.from_iterable(rst))
            real_all_cnt = 0
            if len(record_user_status) > 9999 or (
                    myChats[chat_id].type == "channel"
                    and len(record_user_status) > 199):
                # 已经超限的群，只取最近在线的成员
                async for member in clients[client_name].get_chat_members(
                        chat_id, filter=ChatMembersFilter.RECENT):
                    self.update_table(member, chat_id, record_user_status)
                    real_all_cnt += 1
            else:
                # 未超限的群，取全部成员
                async for member in clients[client_name].get_chat_members(
                        chat_id):
                    self.update_table(member, chat_id, record_user_status)
                    real_all_cnt += 1
            # 更新chat_user_info信息
            record_cnt = chatUserInfo.update(chat_id, real_all_cnt, v_all_cnt)
            sys_log.write_log(
                f"群信息更新完毕: {client_name}->{chat_name} 数据表有效成员数：{record_cnt} 本轮轮询成员数："
                f"{real_all_cnt} 官方总人数：{v_all_cnt}")
        except Exception:
            sys_log.error_log("更新群信息异常")

    # 轮询各个client的群户信息
    async def poll_client(self, ck_client):
        try:
            for client_name in ck_client:
                sys_log.write_log("开始更新用户 {0:s} 的所有群信息".format(client_name))
                # bot 没有 get_dialogs 方法
                if "bot" in client_name:
                    rst = read_connector.run(
                        "select id from chats where username = %s",
                        [client_name])
                    rst = read_connector.run(
                        "select distinct chat_id from chat_user where user_id=%s",
                        [rst[0][0]],
                    )
                    for row in rst:
                        await self.update_chat_user(client_name, row[0])
                else:
                    async for dialog in clients[client_name].get_dialogs():
                        if dialog.chat.type in (
                                ChatType.GROUP,
                                ChatType.SUPERGROUP,
                                ChatType.CHANNEL,
                        ):
                            myChats.update_chat(dialog.chat)
                            await self.update_chat_user(
                                client_name, dialog.chat.id)
                sys_log.write_log("已更新用户 {0:s} 的所有群信息".format(client_name))
        except Exception:
            sys_log.error_log("轮询群信息异常")

    # 更新chat_user的活跃成员 更新频率为6小时
    # status: 0：creator；1：administrator；2：member；3：left；4：restricted；5：banned；8：unKnow；
    def active(self, chat_id, user_id):
        try:
            if len(self._cache) > 500:  # 从2000调整为500
                self._cache.clear()

            chat_user = self.__getitem__((chat_id, user_id))
            if (chat_user is None
                    or time.time() - chat_user.active_at.timestamp() > 21600):

                # 使用参数化查询
                sql_str = """
                INSERT INTO chat_user(chat_id, chat_name, user_id, user_name, active_at, joined_at)
                VALUES(%s, %s, %s, %s, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                chat_name=%s, user_name=%s, active_at=NOW()
                """
                chat_info = myChats[chat_id]
                user_info = myChats[user_id]

                exe_connector.run(sql_str, [
                    chat_id, chat_info.name, user_id, user_info.name,
                    chat_info.name, user_info.name
                ])

                # 更新缓存
                rst = read_connector.run(self.__sql_str, [chat_id, user_id])
                self._cache[str(chat_id) + "|" + str(user_id)] = TChatUser(rst[0])

        except KeyError as e:
            sys_log.error_log(f"数据缺失异常 - chat_id: {chat_id}, user_id: {user_id}, 缺少键: {e}")
        except Exception as e:
            sys_log.error_log(f"更新活跃成员异常 - chat_id: {chat_id}, user_id: {user_id}, error: {e}")

    # 加群动作
    def join(self, chat_id, user_id):
        try:
            sql_str = """
            INSERT INTO chat_user(chat_id, chat_name, user_id, user_name, status, active_at, joined_at)
            VALUES(%s, %s, %s, %s, 2, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            chat_name=%s, user_name=%s, status=2, active_at=NOW(), joined_at=NOW()
            """
            chat_info = myChats[chat_id]
            user_info = myChats[user_id]

            exe_connector.run(sql_str, [
                chat_id, chat_info.name, user_id, user_info.name,
                chat_info.name, user_info.name
            ])
        except KeyError as e:
            sys_log.error_log(f"数据缺失异常 - chat_id: {chat_id}, user_id: {user_id}, 缺少键: {e}")
        except Exception as e:
            sys_log.error_log(f"加群操作异常 - chat_id: {chat_id}, user_id: {user_id}, error: {e}")

    # 退群动作
    def leave(self, chat_id, user_id):
        try:
            sql_str = """
            INSERT INTO chat_user(chat_id, chat_name, user_id, user_name, status, active_at)
            VALUES(%s, %s, %s, %s, 3, NOW())
            ON DUPLICATE KEY UPDATE
            chat_name=%s, user_name=%s, status=3, active_at=NOW()
            """
            chat_info = myChats[chat_id]
            user_info = myChats[user_id]

            exe_connector.run(sql_str, [
                chat_id, chat_info.name, user_id, user_info.name,
                chat_info.name, user_info.name
            ])
        except KeyError as e:
            sys_log.error_log(f"数据缺失异常 - chat_id: {chat_id}, user_id: {user_id}, 缺少键: {e}")
        except Exception as e:
            sys_log.error_log(f"退群操作异常 - chat_id: {chat_id}, user_id: {user_id}, error: {e}")


chatUser = ChatUser()


class TChatUserInfo:

    def __init__(self, values):
        (
            self.chat_id,
            self.chat_name,
            self.record_cnt,
            self.real_all_cnt,
            self.all_cnt,
            self.updated_at,
        ) = values


class ChatUserInfo:

    def __init__(self):
        self.__sql_str = (
            "select chat_id,chat_name,record_cnt,real_all_cnt,all_cnt,updated_at from chat_user_info "
            "where chat_id=%s")

    def update(self, chat_id, real_all_cnt=0, all_cnt=0):
        try:
            sql_str = "SELECT COUNT(1) FROM chat_user WHERE chat_id=%s"
            rst = read_connector.run(sql_str, [chat_id])

            sql_str = """
            INSERT INTO chat_user_info(chat_id, chat_name, record_cnt, real_all_cnt, all_cnt, updated_at)
            VALUES(%s, %s, %s, %s, %s, NOW())
            ON DUPLICATE KEY UPDATE
            chat_name=%s, record_cnt=%s, real_all_cnt=%s, all_cnt=%s, updated_at=NOW()
            """
            chat_info = myChats[chat_id]
            record_cnt = rst[0][0]

            exe_connector.run(sql_str, [
                chat_id, chat_info.name, record_cnt, real_all_cnt, all_cnt,
                chat_info.name, record_cnt, real_all_cnt, all_cnt
            ])
            return record_cnt
        except KeyError as e:
            sys_log.error_log(f"数据缺失异常 - chat_id: {chat_id}, 缺少键: {e}")
        except Exception as e:
            sys_log.error_log(f"获取聊天用户信息异常 - chat_id: {chat_id}, error: {e}")


chatUserInfo = ChatUserInfo()

