# -*- coding: utf-8 -*-
"""
Unix Socket 消息代理
为 Robot Park 项目提供高性能进程间通信

Author: Robot Park Team
Date: 2024-01-01
"""

import socket
import os
import json
import asyncio
import threading
import time
from typing import Dict, List, Callable, Optional
from queue import Queue
from functools import wraps

# 导入项目日志系统
from shared.classes2 import sys_log


class UnixSocketBroker:
    """Unix Socket 消息代理核心类"""
    
    def __init__(self, socket_path: str = "./sessions/robot_park.sock"):
        self.socket_path = socket_path
        self.handlers: Dict[str, List[Callable]] = {}
        self.module_name: Optional[str] = None
        self.server_socket: Optional[socket.socket] = None
        self.client_connections: Dict[str, socket.socket] = {}
        self.is_server = False
        self.is_running = False
        self.connection_pool = Queue(maxsize=10)
        
    async def init_server(self):
        """初始化为服务器模式"""
        self.is_server = True
        
        # 删除已存在的socket文件
        if os.path.exists(self.socket_path):
            os.unlink(self.socket_path)
            
        # 创建Unix socket服务器
        self.server_socket = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        self.server_socket.bind(self.socket_path)
        self.server_socket.listen(10)
        
        # 设置文件权限（所有用户可读写）
        os.chmod(self.socket_path, 0o666)
        
        sys_log.write_log(f"Unix Socket服务器启动: {self.socket_path}")
        
    async def init_client(self, module_name: str):
        """初始化为客户端模式"""
        self.module_name = module_name
        self.is_server = False
        # sys_log.write_log(f"[{module_name}] Unix Socket客户端初始化")
        
    async def start_server(self):
        """启动服务器监听"""
        if not self.is_server:
            raise Exception("必须先调用 init_server()")
            
        self.is_running = True
        sys_log.write_log("开始监听客户端连接...")
        
        try:
            while self.is_running:
                try:
                    client_socket, _ = self.server_socket.accept()
                    sys_log.write_log("新客户端连接")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                    
                except Exception as e:
                    if self.is_running:
                        sys_log.error_log(f"接受连接失败: {e}")
                        
        except KeyboardInterrupt:
            sys_log.write_log("服务器停止")
        finally:
            self.cleanup_server()
            
    def _handle_client(self, client_socket: socket.socket):
        """处理客户端连接"""
        client_module = None
        try:
            while True:
                # 接收消息长度
                length_data = client_socket.recv(4)
                if not length_data:
                    break
                    
                message_length = int.from_bytes(length_data, 'big')
                
                # 接收完整消息
                message_data = b''
                while len(message_data) < message_length:
                    chunk = client_socket.recv(message_length - len(message_data))
                    if not chunk:
                        break
                    message_data += chunk
                
                if len(message_data) != message_length:
                    break
                    
                # 解析消息
                try:
                    message = json.loads(message_data.decode('utf-8'))
                    
                    # 处理注册消息
                    if message.get('type') == 'register':
                        client_module = message['module_name']
                        self.client_connections[client_module] = client_socket
                        sys_log.write_log(f"客户端注册: {client_module}")
                        continue
                        
                    # 路由普通消息
                    self._route_message(message)
                    
                except Exception as e:
                    sys_log.error_log(f"消息处理失败: {e}")
                    
        except Exception as e:
            sys_log.error_log(f"客户端处理错误: {e}")
        finally:
            if client_module and client_module in self.client_connections:
                del self.client_connections[client_module]
                sys_log.write_log(f"客户端断开: {client_module}")
            client_socket.close()
            
    def _route_message(self, message: dict):
        """路由消息到目标客户端"""
        receiver = message.get('receiver')
        sender = message.get('sender', 'unknown')
        msg_type = message.get('type', 'unknown')
        
        sys_log.write_log(f"路由消息: {sender} -> {receiver} ({msg_type})")
        
        if receiver in self.client_connections:
            try:
                target_socket = self.client_connections[receiver]
                message_data = json.dumps(message).encode('utf-8')
                
                # 发送消息长度和消息内容
                target_socket.send(len(message_data).to_bytes(4, 'big'))
                target_socket.send(message_data)
                
                sys_log.write_log(f"消息已投递到 {receiver}")
                
            except Exception as e:
                sys_log.error_log(f"消息投递失败: {e}")
                # 移除失效连接
                if receiver in self.client_connections:
                    del self.client_connections[receiver]
        else:
            sys_log.write_log(f"接收者 {receiver} 不在线")
            
    def _create_connection(self) -> socket.socket:
        """创建新的客户端连接"""
        sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5秒超时
        sock.connect(self.socket_path)
        return sock
        
    def _get_connection(self) -> socket.socket:
        """从连接池获取连接"""
        try:
            return self.connection_pool.get_nowait()
        except:
            return self._create_connection()
            
    def _return_connection(self, conn: socket.socket):
        """归还连接到连接池"""
        try:
            self.connection_pool.put_nowait(conn)
        except:
            conn.close()  # 连接池满了，直接关闭
            
    async def send_message(self, receiver: str, message_type: str, content: dict):
        """发送消息到指定接收者"""
        message = {
            "sender": self.module_name,
            "receiver": receiver,
            "type": message_type,
            "content": content,
            "timestamp": time.time()
        }
        
        try:
            # 使用连接池优化性能
            conn = self._get_connection()
            try:
                message_data = json.dumps(message).encode('utf-8')
                
                # 发送消息长度和消息内容
                conn.send(len(message_data).to_bytes(4, 'big'))
                conn.send(message_data)
                
                self._return_connection(conn)
                sys_log.write_log(f"[{self.module_name}] 发送消息到 {receiver}: {message_type}")
                
            except Exception as e:
                conn.close()
                raise e
                
        except Exception as e:
            sys_log.error_log(f"发送消息失败: {e}")
            
    async def start_listening(self):
        """开始监听消息"""
        try:
            # 连接到服务器（使用异步方式）
            reader, writer = await asyncio.open_unix_connection(self.socket_path)
            
            # 注册客户端
            register_msg = {
                "type": "register",
                "module_name": self.module_name
            }
            register_data = json.dumps(register_msg).encode('utf-8')
            writer.write(len(register_data).to_bytes(4, 'big'))
            writer.write(register_data)
            await writer.drain()
            
            # sys_log.write_log(f"[{self.module_name}] 开始监听消息...")
            
            # 持续监听消息
            while True:
                try:
                    # 接收消息长度（异步）
                    length_data = await reader.read(4)
                    if not length_data:
                        break

                    message_length = int.from_bytes(length_data, 'big')

                    # 接收完整消息（异步）
                    message_data = await reader.read(message_length)

                    if len(message_data) != message_length:
                        break

                    # 解析并处理消息
                    message = json.loads(message_data.decode('utf-8'))
                    await self._handle_message(message)

                except Exception as e:
                    sys_log.error_log(f"接收消息失败: {e}")
                    break

        except Exception as e:
            sys_log.error_log(f"监听失败: {e}")
        finally:
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
                
    async def _handle_message(self, message: dict):
        """处理接收到的消息"""
        message_type = message.get('type')
        sender = message.get('sender', 'unknown')
        
        sys_log.write_log(f"[{self.module_name}] 收到消息: {sender} -> {message_type}")
        
        if message_type in self.handlers:
            for handler in self.handlers[message_type]:
                try:
                    await handler(message)
                except Exception as e:
                    sys_log.error_log(f"处理器执行失败: {e}")
        else:
            sys_log.write_log(f"[{self.module_name}] 没有找到处理器: {message_type}")
            
    def register_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        if message_type not in self.handlers:
            self.handlers[message_type] = []
        self.handlers[message_type].append(handler)
        # sys_log.write_log(f"[{self.module_name}] 注册处理器: {message_type}")
        
    def cleanup_server(self):
        """清理服务器资源"""
        self.is_running = False
        if self.server_socket:
            self.server_socket.close()
        if os.path.exists(self.socket_path):
            os.unlink(self.socket_path)
        sys_log.write_log("服务器资源已清理")
        
    def check_connection(self) -> dict:
        """检查连接状态"""
        try:
            if not os.path.exists(self.socket_path):
                return {"status": "socket_not_exists", "healthy": False}
                
            # 尝试连接测试
            test_sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
            test_sock.settimeout(1)
            test_sock.connect(self.socket_path)
            test_sock.close()
            
            return {
                "status": "healthy",
                "healthy": True,
                "handlers_count": len(self.handlers),
                "socket_path": self.socket_path
            }
        except Exception as e:
            return {"status": "error", "healthy": False, "error": str(e)}


# 全局Unix Socket代理实例
unix_broker = UnixSocketBroker()


def unix_handler(message_type: str):
    """Unix Socket消息处理装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        
        unix_broker.register_handler(message_type, wrapper)
        return wrapper
    return decorator
