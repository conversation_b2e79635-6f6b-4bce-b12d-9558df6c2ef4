#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一国际化模块
使用方法：
    from shared.language import Language
    
    # 创建语言管理器实例
    language = Language()
    
    # 设置用户语言
    language[user_id] = "zh_CN"
    
    # 获取用户语言
    user_lang = language[user_id]
    
    # 获取翻译文本
    text = language.t(user_id, "通用.欢迎", bot_name="EdenBot")
    
    # 获取语言选择菜单
    menu = language.get_menu("zh_CN")  # 返回中文版本的菜单
"""

import json
import os
from typing import Dict, Any
from dataclasses import dataclass
from enum import Enum
from shared.classes2 import sys_log, read_connector, exe_connector

class SupportedLanguage(Enum):
    """支持的语言"""
    ZH_CN = "zh_CN"  # 简体中文
    EN_US = "en_US"  # 英文
    RU_RU = "ru_RU"  # 俄语
    KK_KZ = "kk_KZ"  # 哈萨克语

@dataclass
class LanguageInfo:
    """语言信息"""
    code: str
    name: str  # 语言显示名称
    flag: str  # 旗帜表情符号

class TLanguage:
    """数据库语言管理器"""
    
    def __init__(self):
        self.default_language = "zh_CN"
    
    def __setitem__(self, user_id: int, language_code: str):
        """设置用户语言"""
        sql = """
        INSERT INTO user_languages (user_id, language_code) 
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE 
            language_code = VALUES(language_code),
            updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            exe_connector.run(sql, (user_id, language_code))
        except Exception as e:
            sys_log.error_log(f"设置用户语言失败: {e}")
            raise
    
    def __getitem__(self, user_id: int) -> str:
        """获取用户语言"""
        sql = "SELECT language_code FROM user_languages WHERE user_id = %s"
        
        try:
            result = read_connector.run(sql, (user_id,))
            if result:
                return result[0]['language_code']
        except Exception as e:
            sys_log.error_log(f"获取用户语言失败: {e}")
        
        return self.default_language

class Language:
    """语言管理器"""
    
    def __init__(self):
        # 动态获取语言包目录
        plugin_name = os.environ.get('PLUGIN_NAME', 'default')
        self.locales_dir = f"language/{plugin_name}/"
        
        self._languages: Dict[str, Dict[str, Any]] = {}
        self._default_language = SupportedLanguage.ZH_CN.value
        self._cache: Dict[int, str] = {}  # 用户语言缓存
        
        # 语言信息映射
        self.language_info = {
            SupportedLanguage.ZH_CN.value: LanguageInfo("zh_CN", "简体中文", "🇨🇳"),
            SupportedLanguage.EN_US.value: LanguageInfo("en_US", "English", "🇺🇸"),
            SupportedLanguage.RU_RU.value: LanguageInfo("ru_RU", "Русский", "🇷🇺"),
            SupportedLanguage.KK_KZ.value: LanguageInfo("kk_KZ", "Қазақша", "🇰🇿")
        }
        
        # 初始化
        self.tLanguage = TLanguage()
        self._load_languages()
    
    def _load_languages(self):
        """加载所有语言包"""
        if not os.path.exists(self.locales_dir):
            os.makedirs(self.locales_dir, exist_ok=True)
            return
        
        for lang_code in SupportedLanguage:
            lang_file = os.path.join(self.locales_dir, f"{lang_code.value}.json")
            if os.path.exists(lang_file):
                try:
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        self._languages[lang_code.value] = json.load(f)
                except Exception as e:
                    sys_log.error_log(f"加载语言包失败 {lang_code.value}: {e}")
    
    def __setitem__(self, user_id: int, language_code: str):
        """设置用户语言"""
        if language_code not in self._languages:
            raise ValueError(f"不支持的语言代码: {language_code}")
        
        # 缓存控制：如果缓存中已有500个用户记录，清空缓存
        if len(self._cache) >= 500:
            self._cache.clear()
            # sys_log.write_log("用户语言缓存已清空（达到500条记录限制）")
        
        # 更新数据库
        self.tLanguage[user_id] = language_code
        # 更新缓存
        self._cache[user_id] = language_code
    
    def __getitem__(self, user_id: int) -> str:
        """获取用户语言（带缓存）"""
        # 先检查缓存
        if user_id in self._cache:
            return self._cache[user_id]
        
        # 从数据库获取
        language_code = self.tLanguage[user_id]
        
        # 缓存结果（不需要验证语言代码，因为入库前已检查过）
        self._cache[user_id] = language_code
        return language_code
    
    def t(self, user_id: int, key: str, **kwargs) -> str:
        """获取翻译文本"""
        language_code = self[user_id]
        return self._get_text_by_language(language_code, key, **kwargs)
    
    def _get_text_by_language(self, language_code: str, key: str, **kwargs) -> str:
        """
        根据语言代码获取文本
        
        支持多参数格式化，例如：
        text = "欢迎 {user_name}，您有 {count} 条消息"
        result = _get_text_by_language("zh_CN", "通用.欢迎", user_name="张三", count=5)
        """
        # 获取语言包
        lang_data = self._languages.get(language_code)
        if not lang_data:
            # 回退到默认语言
            lang_data = self._languages.get(self._default_language, {})
        
        # 解析嵌套键 (如 "通用.欢迎")
        keys = key.split('.')
        text = lang_data
        
        for k in keys:
            if isinstance(text, dict) and k in text:
                text = text[k]
            else:
                # 如果找不到，返回键名
                return key
        
        # 格式化文本（支持多个参数）
        if isinstance(text, str) and kwargs:
            try:
                return text.format(**kwargs)
            except (KeyError, ValueError) as e:
                sys_log.error_log(f"文本格式化失败: key={key}, kwargs={kwargs}, error={e}")
                return text
        
        return str(text)
    
    def get_menu(self, language_code: str = "zh_CN") -> Dict[str, str]:
        """
        获取语言选择菜单（根据指定语言返回对应版本）
        
        Args:
            language_code: 菜单显示的语言版本
        
        Returns:
            语言代码到本地化显示名称的映射
        """
        menu = {}
        
        # 根据指定语言获取菜单项的翻译
        for lang_code, info in self.language_info.items():
            if lang_code in self._languages:
                # 获取该语言在指定语言版本中的显示名称
                display_name = self._get_localized_language_name(lang_code, language_code)
                menu[lang_code] = f"{info.flag} {display_name}"
        
        return menu
    
    def _get_localized_language_name(self, target_lang: str, display_lang: str) -> str:
        """获取本地化的语言名称"""
        # 语言名称映射表
        language_names = {
            "zh_CN": {
                "zh_CN": "简体中文",
                "en_US": "英文", 
                "ru_RU": "俄语",
                "kk_KZ": "哈萨克语"
            },
            "en_US": {
                "zh_CN": "Chinese",
                "en_US": "English",
                "ru_RU": "Russian", 
                "kk_KZ": "Kazakh"
            },
            "ru_RU": {
                "zh_CN": "Китайский",
                "en_US": "Английский",
                "ru_RU": "Русский",
                "kk_KZ": "Казахский"
            },
            "kk_KZ": {
                "zh_CN": "Қытай тілі",
                "en_US": "Ағылшын тілі",
                "ru_RU": "Орыс тілі",
                "kk_KZ": "Қазақ тілі"
            }
        }
        
        return language_names.get(display_lang, {}).get(target_lang, self.language_info[target_lang].name)
    
    def get_supported_languages(self) -> list:
        """获取支持的语言列表"""
        return list(self._languages.keys())

# 支持的语言常量
SUPPORTED_LANGUAGES = {
    "zh_CN": "🇨🇳 简体中文",
    "en_US": "🇺🇸 English", 
    "ru_RU": "🇷🇺 Русский",
    "kk_KZ": "🇰🇿 Қазақша"
}
