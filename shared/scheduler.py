# -*- coding: utf-8 -*-
"""
APScheduler 调度框架 - 适配项目需求
1. 使用MySQL数据库存储任务（从环境变量获取，从环境变量获取，从环境变量获取，从环境变量获取，从环境变量获取）
2. 任务处理函数放在 ./plugins/handlers/项目名.py 中
3. 支持完整的触发器配置格式
4. 完全集成项目现有架构
"""

import asyncio
import json
import os
import importlib
import hashlib
import sys
import traceback
from apscheduler.jobstores.base import JobLookupError
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass, asdict
from apscheduler.jobstores.base import JobLookupError

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.calendarinterval import CalendarIntervalTrigger
from apscheduler.triggers.combining import AndTrigger, OrTrigger

from shared.classes import sys_log, db_conf, params, exe_connector


class PluginFilteredJobStore(SQLAlchemyJobStore):
    """
    插件过滤的 JobStore
    只加载和处理当前插件的任务，其他插件的任务完全不受影响
    """

    def __init__(self, plugin_name: str, **kwargs):
        """
        初始化插件过滤的 JobStore

        Args:
            plugin_name: 当前插件名称
            **kwargs: 传递给父类的参数
        """
        super().__init__(**kwargs)
        self.plugin_name = plugin_name
        self.prefix = f"{plugin_name}."

    def get_all_jobs(self):
        """
        只获取当前插件的任务（通过ID前缀过滤）
        重写父类方法，实现插件级过滤
        """
        try:
            # 获取所有任务
            all_jobs = super().get_all_jobs()

            # 过滤当前插件的任务（通过ID前缀）
            plugin_jobs = []

            for job in all_jobs:
                if job.id.startswith(self.prefix):
                    plugin_jobs.append(job)

            return plugin_jobs

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] JobStore 获取任务失败: {e}")
            return []

    def get_due_jobs(self, now):
        """
        只获取当前插件的到期任务（通过ID前缀过滤）
        重写父类方法，确保只处理当前插件的任务
        """
        try:
            # 获取所有到期任务
            all_due_jobs = super().get_due_jobs(now)

            # 过滤当前插件的任务（通过ID前缀）
            plugin_due_jobs = []

            for job in all_due_jobs:
                if job.id.startswith(self.prefix):
                    plugin_due_jobs.append(job)

            return plugin_due_jobs

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] JobStore 获取到期任务失败: {e}")
            return []

    def lookup_job(self, job_id):
        """
        查找特定任务（只支持前缀ID）
        只返回当前插件的任务
        """
        try:
            job = super().lookup_job(job_id)

            # 验证任务属于当前插件
            if job and job.id.startswith(self.prefix):
                return job

            return None

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] JobStore 查找任务失败: {job_id} - {e}")
            return None


# 独立函数已移动到APScheduler类中作为静态方法


@dataclass
class SchedulerTask:
    """调度任务配置 - 完整格式"""
    id: str                    # 任务ID
    name: str                  # 任务名称
    handler: str               # 处理函数名（直接对应函数名）
    
    # 触发器配置 - 只支持完整格式
    trigger_type: str          # 触发器类型: interval, cron, date, calendarinterval, combining
    trigger_config: Dict[str, Any]  # 触发器配置
    
    enabled: bool = True       # 是否启用
    max_retries: int = 3       # 最大重试次数
    max_instances: int = 1     # 最大并发实例
    
    # 业务参数
    params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.params is None:
            self.params = {}


class APScheduler:
    """APScheduler 调度器"""
    
    def __init__(self, plugin_name: str, config_file: str = None):
        self.plugin_name = plugin_name
        self.scheduler_id = f"{plugin_name}_{id(self)}"  # 唯一标识符

        # 配置文件路径
        if config_file is None:
            config_file = f"config/scheduler_{plugin_name}.json"
        self.config_file = config_file
        # sys_log.debug_log(f"[{self.plugin_name}] 调度器初始化: {self.config_file}")

        # 创建数据库URL
        db_url = self._create_database_url()

        # 创建调度器 - 使用插件过滤的 JobStore
        jobstores = {
            'default': PluginFilteredJobStore(
                plugin_name=plugin_name,
                url=db_url,
                tablename='scheduler_jobs'
            )
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': True,
            'max_instances': 1,
            'misfire_grace_time': 30
        }

        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )

        # 任务处理器注册表
        self.handlers: Dict[str, Callable] = {}

        # 任务配置
        self.tasks: Dict[str, SchedulerTask] = {}

        # 初始化时进行配置同步
        self._sync_config_on_startup()

        # sys_log.debug_log(f"[{self.plugin_name}] 调度器初始化完成")
    
    def _create_database_url(self) -> str:
        """创建数据库连接URL"""
        try:
            # 从环境变量获取数据库配置段
            db_section = os.environ['DB_WRITE_SECTION']

            host = db_conf.read(db_section, "host")
            port = db_conf.read(db_section, "port")
            dbname = db_conf.read(db_section, "dbname")
            user = db_conf.read(db_section, "user")
            pwd = db_conf.read(db_section, "pwd")
            
            # 构建MySQL连接URL
            db_url = f"mysql+pymysql://{user}:{pwd}@{host}:{port}/{dbname}?charset=utf8mb4"
            
            return db_url
            
        except Exception:
            sys_log.error_log(f"[{self.plugin_name}] 创建数据库URL失败")
            # 降级到内存存储
            return "sqlite:///:memory:"

    def _calculate_task_hash(self, task_data: dict) -> str:
        """计算单个任务的哈希值"""
        try:
            # 将任务数据转换为JSON字符串并计算哈希
            task_json = json.dumps(task_data, sort_keys=True, ensure_ascii=False)
            return hashlib.md5(task_json.encode('utf-8')).hexdigest()
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 计算任务哈希失败: {e}")
            return ""

    def _get_stored_task_hash(self, task_id: str) -> str:
        """获取存储的任务哈希值"""
        try:
            hash_key = f"scheduler_hash.{self.plugin_name}.{task_id}"
            stored_hash = params[hash_key]
            return stored_hash if stored_hash else ""
        except Exception:
            return ""

    def _store_task_hash(self, task_id: str, task_hash: str):
        """存储任务哈希值"""
        try:
            hash_key = f"scheduler_hash.{self.plugin_name}.{task_id}"
            params[hash_key] = task_hash
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 存储任务哈希失败: {task_id} - {e}")

    def _delete_task_hash(self, task_id: str):
        """删除任务哈希值"""
        try:
            hash_key = f"scheduler_hash.{self.plugin_name}.{task_id}"
            params.delete(hash_key)
            # sys_log.write_log(f"[{self.plugin_name}] 任务哈希已删除: {task_id}")
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 删除任务哈希失败: {task_id} - {e}")

    @staticmethod
    async def execute_scheduled_task(plugin_name: str, handler_name: str, **params):
        """静态任务执行器，避免序列化问题"""
        try:
            sys_log.write_log(f"[{plugin_name}] 开始执行任务: {handler_name}")

            # 动态导入处理器模块
            module_path = f"plugins.handlers.{plugin_name}"
            module = __import__(module_path, fromlist=[handler_name])
            handler = getattr(module, handler_name)

            # 执行任务，传递参数
            await handler(**params)

            sys_log.write_log(f"[{plugin_name}] 任务执行成功: {handler_name}")

        except Exception as e:
            sys_log.error_log(f"[{plugin_name}] 任务执行失败: {handler_name} - {e}")
            raise

    def auto_register_handlers(self):
        """自动注册启用任务的处理器"""
        try:
            # 获取所有启用任务需要的处理器名称
            enabled_handlers = set()
            for task in self.tasks.values():
                if task.enabled:
                    enabled_handlers.add(task.handler)

            if not enabled_handlers:
                sys_log.write_log(f"[{self.plugin_name}] 没有启用的任务，跳过处理器注册")
                return

            # 尝试导入插件处理器模块
            module_path = f"plugins.handlers.{self.plugin_name}"
            module = importlib.import_module(module_path)

            # 只注册启用任务需要的处理器
            registered_count = 0
            for attr_name in dir(module):
                if attr_name in enabled_handlers:  # 只注册需要的处理器
                    attr = getattr(module, attr_name)
                    if (callable(attr) and
                        not attr_name.startswith('_') and
                        hasattr(attr, '__module__') and
                        attr.__module__ == module_path):  # 只注册在当前模块中定义的函数
                        self.handlers[attr_name] = attr
                        registered_count += 1

            sys_log.write_log(f"[{self.plugin_name}] 自动注册完成，共 {registered_count} 个处理器（有效启用任务数）")

            # 提示未找到的处理器
            missing_handlers = enabled_handlers - set(self.handlers.keys())
            if missing_handlers:
                sys_log.error_log(f"[{self.plugin_name}] 未找到处理器: {', '.join(missing_handlers)}")

        except ImportError:
            sys_log.write_log(f"[{self.plugin_name}] 未找到处理器模块: plugins.handlers.{self.plugin_name}")
        except Exception:
            sys_log.error_log(f"[{self.plugin_name}] 自动注册处理器失败")
    
    def register_handler(self, name: str, handler: Callable):
        """手动注册处理器"""
        self.handlers[name] = handler
        sys_log.write_log(f"[{self.plugin_name}] 手动注册处理器: {name}")

    def _sync_config_on_startup(self):
        """启动时进行配置同步 - 以任务为维度"""
        try:
            # sys_log.write_log(f"[{self.plugin_name}] 开始任务级配置同步")

            # 获取配置文件中的任务
            config_tasks = self._load_config_file()
            if not config_tasks:
                sys_log.error_log(f"[{self.plugin_name}] 调度配置文件为空或不存在")
                return

            # 获取当前存储的任务哈希
            stored_task_hashes = self._get_all_stored_task_hashes()

            # 处理配置文件中的每个任务
            for task_data in config_tasks:
                task_id = task_data['id']
                current_hash = self._calculate_task_hash(task_data)
                stored_hash = stored_task_hashes.get(task_id, "")

                if current_hash != stored_hash:
                    # 任务有变化，重新加载
                    sys_log.write_log(f"[{self.plugin_name}] 任务有变化，重新加载: {task_id}")
                    self._remove_task_from_scheduler(task_id)
                    self._add_task_from_config(task_data)
                    self._store_task_hash(task_id, current_hash)
                else:
                    # 任务无变化，仅加载到内存
                    self._add_task_from_config(task_data, skip_scheduler=True)

            # 处理已删除的任务
            config_task_ids = {task['id'] for task in config_tasks}
            for stored_task_id in stored_task_hashes.keys():
                if stored_task_id not in config_task_ids:
                    # 配置中已删除的任务，从数据库中删除
                    self._remove_task_from_scheduler(stored_task_id)
                    self._delete_task_hash(stored_task_id)

            # sys_log.write_log(f"[{self.plugin_name}] 任务级配置同步完成")

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 配置同步失败: {e}")

    def _get_all_stored_task_hashes(self) -> dict:
        """获取所有存储的任务哈希值"""
        try:
            prefix = f"scheduler_hash.{self.plugin_name}"
            all_hashes = params.get_by_prefix(prefix)
            # sys_log.debug_log(f"[{self.plugin_name}] 获取所有存储的任务哈希成功: \n{all_hashes}")

            # 提取任务ID和哈希值
            task_hashes = {}
            for key, hash_value in all_hashes.items():
                # key格式: scheduler_hash.plugin_name.task_id
                parts = key.split('.')
                if len(parts) >= 3:
                    task_id = '.'.join(parts[2:])  # 支持task_id中包含点号
                    task_hashes[task_id] = hash_value

            return task_hashes
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 获取存储的任务哈希失败: {e}")
            return {}

    def _load_config_file(self) -> list:
        """加载配置文件，返回任务列表"""
        try:
            if not os.path.exists(self.config_file):
                return []

            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            return data.get('tasks', [])

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 加载配置文件失败: {e}")
            return []

    def _add_task_from_config(self, task_data: dict, skip_scheduler: bool = False):
        """从配置数据添加任务"""
        try:
            task = SchedulerTask(**task_data)
            self.tasks[task.id] = task

            # 如果任务启用且不跳过调度器，添加到调度器
            if task.enabled and not skip_scheduler:
                self._add_task_to_scheduler(task)

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 添加任务失败: {task_data.get('id', 'unknown')} - {e}")

    def _remove_task_from_scheduler(self, task_id: str):
        """从调度器中移除任务"""
        try:
            removed_from_scheduler = False
            prefixed_id = f"{self.plugin_name}.{task_id}"

            # 从调度器中删除
            if self.scheduler.get_job(prefixed_id):
                self.scheduler.remove_job(prefixed_id)
                removed_from_scheduler = True
                # sys_log.debug_log(f"[{self.plugin_name}] 从调度器删除任务: {task_id}")

            # 如果调度器未启动或删除失败，直接从数据库删除
            if not self.scheduler.running or not removed_from_scheduler:
                try:
                    delete_sql = "DELETE FROM scheduler_jobs WHERE id = %s"
                    exe_connector.run(delete_sql, (prefixed_id,))
                    # sys_log.debug_log(f"[{self.plugin_name}] 从数据库删除任务: {task_id}")
                except Exception as db_e:
                    sys_log.error_log(f"[{self.plugin_name}] 直接删除数据库任务失败: {task_id} - {db_e}")

            # 从内存中删除
            if task_id in self.tasks:
                del self.tasks[task_id]

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 删除任务失败: {task_id} - {e}")

    def _add_task_to_scheduler(self, task: SchedulerTask):
        """将任务添加到调度器"""
        try:
            # 创建触发器
            trigger = self._create_trigger(task)

            self.scheduler.add_job(
                func=APScheduler.execute_scheduled_task,
                trigger=trigger,
                kwargs={
                    'plugin_name': self.plugin_name,
                    'handler_name': task.handler,
                    **task.params
                },
                id=f"{self.plugin_name}.{task.id}",  # 数据库中使用前缀ID
                name=task.name,
                max_instances=task.max_instances,
                replace_existing=True,
                misfire_grace_time=30
            )

            # sys_log.debug_log(f"[{self.plugin_name}] 添加任务: {task.id}")

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 添加任务失败: {task.id} - {e}")
    
    def _create_trigger(self, task: SchedulerTask):
        """创建触发器"""
        trigger_type = task.trigger_type
        config = task.trigger_config
        
        if trigger_type == "interval":
            return IntervalTrigger(**config)
        elif trigger_type == "cron":
            return CronTrigger(**config)
        elif trigger_type == "date":
            if isinstance(config.get('run_date'), str):
                config['run_date'] = datetime.fromisoformat(config['run_date'])
            return DateTrigger(**config)
        elif trigger_type == "calendarinterval":
            return CalendarIntervalTrigger(**config)
        elif trigger_type == "combining":
            operator = config.get('operator', 'and')
            triggers = []
            
            for trigger_def in config.get('triggers', []):
                sub_task = SchedulerTask(
                    id="temp", name="temp", handler="temp",
                    trigger_type=trigger_def['type'],
                    trigger_config=trigger_def['config']
                )
                triggers.append(self._create_trigger(sub_task))
            
            if operator == 'and':
                return AndTrigger(triggers)
            else:
                return OrTrigger(triggers)
        else:
            raise ValueError(f"不支持的触发器类型: {trigger_type}")

    def start(self):
        """启动调度器"""
        self.scheduler.start()

        # 不再需要过滤，PluginFilteredJobStore 已经只加载当前插件的任务
        sys_log.write_log(f"[{self.plugin_name}] 调度器已启动")

    def stop(self):
        """停止调度器"""
        try:
            if self.scheduler.running:
                self.scheduler.shutdown()
                sys_log.write_log(f"[{self.plugin_name}] 调度器已停止")
            else:
                sys_log.write_log(f"[{self.plugin_name}] 调度器未运行，无需停止")
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 停止调度器异常: {e}")
    
    def pause_task(self, task_id: str):
        """暂停任务"""
        try:
            self.scheduler.pause_job(f"{self.plugin_name}.{task_id}")
            if task_id in self.tasks:
                self.tasks[task_id].enabled = False
            sys_log.write_log(f"[{self.plugin_name}] 暂停任务: {task_id}")
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 暂停任务失败: {task_id} - {e}")
    
    def resume_task(self, task_id: str):
        """恢复任务"""
        try:
            self.scheduler.resume_job(f"{self.plugin_name}.{task_id}")
            if task_id in self.tasks:
                self.tasks[task_id].enabled = True
            sys_log.write_log(f"[{self.plugin_name}] 恢复任务: {task_id}")
        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 恢复任务失败: {task_id} - {e}")

    def get_task_status(self, task_id: str) -> dict:
        """获取任务状态"""
        try:
            job = self.scheduler.get_job(f"{self.plugin_name}.{task_id}")

            if job:
                return {
                    'id': f"{self.plugin_name}.{task_id}",
                    'name': job.name,
                    'next_run_time': job.next_run_time,
                    'enabled': task_id in self.tasks and self.tasks[task_id].enabled,
                    'max_instances': job.max_instances,
                    'pending': job.pending
                }
            else:
                return {
                    'id': f"{self.plugin_name}.{task_id}",
                    'enabled': False,
                    'error': 'Task not found in scheduler'
                }

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 获取任务状态失败: {task_id} - {e}")
            return {
                'id': f"{self.plugin_name}.{task_id}",
                'enabled': False,
                'error': str(e)
            }

    def list_tasks(self) -> list:
        """列出所有任务"""
        try:
            tasks = []

            # 从调度器获取所有任务
            scheduler_jobs = self.scheduler.get_jobs()

            for job in scheduler_jobs:
                if job.id.startswith(self.prefix):
                    # 提取原始任务ID
                    original_id = job.id[len(self.prefix):]

                    task_info = {
                        'original_id': original_id,
                        'prefixed_id': job.id,
                        'name': job.name,
                        'next_run_time': job.next_run_time,
                        'enabled': original_id in self.tasks and self.tasks[original_id].enabled,
                        'handler': job.kwargs.get('handler_name', 'unknown')
                    }
                    tasks.append(task_info)

            return tasks

        except Exception as e:
            sys_log.error_log(f"[{self.plugin_name}] 列出任务失败: {e}")
            return []


# ==================== 便捷函数 ====================

def create_scheduler(plugin_name: str, config_file: str = None, auto_register: bool = True) -> APScheduler:
    """
    创建调度器

    Args:
        plugin_name: 插件名称
        config_file: 配置文件路径
        auto_register: 是否自动注册处理器

    Returns:
        APScheduler: 调度器实例
    """
    scheduler = APScheduler(plugin_name, config_file)

    # 自动注册处理器
    if auto_register:
        scheduler.auto_register_handlers()

    return scheduler
