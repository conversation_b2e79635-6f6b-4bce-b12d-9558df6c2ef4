# -*- coding: utf-8 -*-
"""
多语言频道女孩卡片展示系统 - 简化版
基于girl_classify表进行分类管理，整合所有功能到一个类中
"""
__author__ = 'Kobee.li'

from shared.classes2 import sys_log, params, ChatMessage, read_connector, exe_connector
from plugins.hazoo.common import clients, format_girl_channel, lang


class GirlChannelManager:
    """女孩频道管理器 - 简化版，整合所有功能"""
    
    def __init__(self):
        # 使用 common.py 中的统一语言实例
        self.channels = {}  # 存储频道配置
        self.load_channels()
    
    def load_channels(self):
        """加载频道配置"""
        try:
            channel_params = params.get_by_prefix("channel_config")
            for param_key, channel_id in channel_params.items():
                parts = param_key.split('.')
                if len(parts) == 3:
                    _, language, channel_type = parts
                    if language not in self.channels:
                        self.channels[language] = {}
                    self.channels[language][channel_type] = channel_id
        except Exception as e:
            sys_log.error_log(f"加载频道配置失败: {e}")
    
    def set_classify(self, girl_id=0, classify_type=0, opt='add'):
        """设置女孩分类"""
        try:
            if opt == 'add' and girl_id > 0 and classify_type > 0:
                sql_str = """
                    INSERT INTO girl_classify(girl_id, type)
                    VALUES (%s, %s)
                    ON DUPLICATE KEY UPDATE type = VALUES(type)
                """
                exe_connector.run(sql_str, [girl_id, classify_type])
                
            elif opt == 'delete':
                if girl_id > 0 and classify_type > 0:
                    sql_str = "DELETE FROM girl_classify WHERE girl_id = %s AND type = %s"
                    exe_connector.run(sql_str, [girl_id, classify_type])
                elif girl_id == 0 and classify_type > 0:
                    sql_str = "DELETE FROM girl_classify WHERE type = %s"
                    exe_connector.run(sql_str, [classify_type])
                elif girl_id > 0 and classify_type == 0:
                    sql_str = "DELETE FROM girl_classify WHERE girl_id = %s"
                    exe_connector.run(sql_str, [girl_id])
                    
            sys_log.write_log(f"女孩分类操作成功: girl_id={girl_id}, type={classify_type}, opt={opt}")
        except Exception as e:
            sys_log.error_log(f"设置女孩分类失败: {e}")
    
    def get_classify(self, girl_id=0, classify_type=0):
        """获取女孩分类情况"""
        try:
            if girl_id > 0 and classify_type == 0:
                sql_str = "SELECT girl_id, type FROM girl_classify WHERE girl_id = %s ORDER BY type"
                return read_connector.run(sql_str, [girl_id])
            elif girl_id == 0 and classify_type > 0:
                sql_str = "SELECT girl_id, type FROM girl_classify WHERE type = %s ORDER BY girl_id"
                return read_connector.run(sql_str, [classify_type])
            elif girl_id > 0 and classify_type > 0:
                sql_str = "SELECT girl_id, type FROM girl_classify WHERE girl_id = %s AND type = %s"
                return read_connector.run(sql_str, [girl_id, classify_type])
            else:
                return []
        except Exception as e:
            sys_log.error_log(f"获取女孩分类失败: {e}")
            return []
    
    def auto_classify(self, classify_list=[1, 2, 4, 5, 6]):
        """自动更新女孩分类"""
        try:
            # 1. 空闲频道
            if 1 in classify_list:
                self.set_classify(classify_type=1, opt='delete')
                sql_str = "INSERT INTO girl_classify(girl_id, type) SELECT id, 1 FROM girls WHERE status = 1"
                exe_connector.run(sql_str)

            # 2. 好评频道
            if 2 in classify_list:
                self.set_classify(classify_type=2, opt='delete')
                sql_str = "INSERT INTO girl_classify(girl_id, type) SELECT id, 2 FROM girls WHERE status = 1 AND grade >= 3"
                exe_connector.run(sql_str)

            # 4. 新女孩频道
            if 4 in classify_list:
                self.set_classify(classify_type=4, opt='delete')
                sql_str = "INSERT INTO girl_classify(girl_id, type) SELECT id, 4 FROM girls WHERE status = 1 ORDER BY id DESC LIMIT 40"
                exe_connector.run(sql_str)
            
            # 5. 丰满女孩频道
            if 5 in classify_list:
                self.set_classify(classify_type=5, opt='delete')
                sql_str = """
                    INSERT INTO girl_classify(girl_id, type)
                    SELECT id, 5 FROM girls
                    WHERE status = 1 AND (
                        boobs LIKE '%C' OR boobs LIKE '%D' OR boobs LIKE '%E' OR
                        boobs LIKE '%F' OR boobs LIKE '%G' OR boobs LIKE '%H'
                    )
                """
                exe_connector.run(sql_str)

            # 6. 年轻女孩频道
            if 6 in classify_list:
                self.set_classify(classify_type=6, opt='delete')
                sql_str = "INSERT INTO girl_classify(girl_id, type) SELECT id, 6 FROM girls WHERE status = 1 AND age < 20"
                exe_connector.run(sql_str)
            
            sys_log.write_log(f"自动女孩分类完成: {classify_list}")
        except Exception as e:
            sys_log.error_log(f"自动女孩分类失败: {e}")
    
    def should_send_to_channel(self, girl_id, channel_type):
        """判断女孩是否应该发送到指定频道"""
        if channel_type == "main":
            return True  # 主频道接受所有在线女孩
        elif channel_type == "assess":
            return False  # 点评频道不发送女孩卡片
        else:
            # 其他频道基于分类表判断
            type_mapping = {"avail": 1, "grade": 2, "new": 4, "boobs": 5, "young": 6}
            if channel_type in type_mapping:
                classify_type = type_mapping[channel_type]
                result = self.get_classify(girl_id=girl_id, classify_type=classify_type)
                return len(result) > 0
        return False
    
    def format_girl_card(self, girl, language):
        """根据语言格式化女孩卡片 - 使用新的统一格式化方法"""
        try:
            # 为频道创建虚拟用户ID
            virtual_user_id = self._get_virtual_user_id(language)
            # 设置虚拟用户的语言
            lang[virtual_user_id] = language
            # 使用统一的格式化方法
            return format_girl_channel(girl, virtual_user_id)
        except Exception as e:
            sys_log.error_log(f"格式化女孩卡片失败: {e}")
            # 兜底：使用中文格式
            virtual_user_id = self._get_virtual_user_id("zh_CN")
            lang[virtual_user_id] = "zh_CN"
            return format_girl_channel(girl, virtual_user_id)

    def _get_virtual_user_id(self, language):
        """为频道创建虚拟用户ID"""
        virtual_ids = {
            "zh_CN": -1001,
            "en_US": -1002,
            "ru_RU": -1003,
            "kk_KZ": -1004
        }
        return virtual_ids.get(language, -1001)
    
    # _format_english_card 已移至 common.py 中的 _format_girl_channel_english
    # _format_russian_card 已移至 common.py 中的 _format_girl_channel_russian
    # _format_kazakh_card 已移至 common.py 中的 _format_girl_channel_kazakh
    
    async def send_girl_to_channels(self, girl):
        """发送女孩卡片到匹配的频道"""
        results = {}
        try:
            for language, channels in self.channels.items():
                for channel_type, channel_id in channels.items():
                    if self.should_send_to_channel(girl.id, channel_type):
                        try:
                            card_text = self.format_girl_card(girl, language)
                            message = await clients['edenmanbot'].send_message(channel_id, card_text)
                            
                            # 保存消息到ChatMessage
                            chat_message = ChatMessage(channel_id, "girl_card")
                            await chat_message.add_msg([message], str(girl.id))
                            
                            if language not in results:
                                results[language] = []
                            results[language].append(message.id)
                            
                            sys_log.write_log(f"女孩卡片已发送: {language}_{channel_type} (女孩ID: {girl.id})")
                        except Exception as e:
                            sys_log.error_log(f"发送卡片失败: {language}_{channel_type}, 女孩ID: {girl.id}, 错误: {e}")
            return results
        except Exception as e:
            sys_log.error_log(f"发送女孩卡片失败: 女孩ID: {girl.id}, 错误: {e}")
            return {}


# 全局实例
girl_channel_manager = GirlChannelManager()



