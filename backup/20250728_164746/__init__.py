# -*- coding: utf-8 -*-
"""
Hazoo模块统一导入文件
解决导入语句过多的问题，兼顾职责清晰和导入便利
"""
__author__ = 'Kobee.li'

# 导入所有模块类
from .girls import Girls, TGirls
from .guests import Guests
from .girls_manage import *
# from .orders import Orders
# from .channels import Channels
# from .reviews import Reviews

# 统一导出，方便外部导入
__all__ = [
    'Girls',
    'TGirls',
    'Guests',
    # 'Orders',
    # 'Channels',
    # 'Reviews'
]
