# -*- coding: utf-8 -*-
"""
hazoo 项目共享模块
包含所有机器人共用的方法、变量和表情定义
"""
__author__ = 'Kobee.li'

import re
from shared.classes2 import *
from shared.language import Language


# 高级表情 - 如果有 Telegram Premium，可以使用自定义表情ID
CUSTOM_EMOJIS = {
    'premium_girl': '5789123456789',  # 这里应该是纯数字的自定义表情ID
    'premium_star': '5789123456790',  # 这里应该是纯数字的自定义表情ID
    'premium_heart': '5789123456791', # 这里应该是纯数字的自定义表情ID
    'premium_fire': '5789123456792',  # 这里应该是纯数字的自定义表情ID
}


# 表情管理器类
class EmojiManager:
    """表情管理器，支持 Telegram Premium 自定义表情"""

    def __init__(self, has_premium=False):
        self.has_premium = has_premium
        self.custom_ids = CUSTOM_EMOJIS if has_premium else {}

    def get_emoji(self, name, fallback):
        """获取表情，优先使用自定义表情"""
        if self.has_premium and name in self.custom_ids:
            custom_id = self.custom_ids[name]
            # 验证自定义表情ID是否为纯数字
            if custom_id.isdigit():
                return f"<emoji id=\"{custom_id}\">{fallback}</emoji>"
            else:
                # 如果不是纯数字，回退到普通表情
                return fallback
        return fallback

    def girl_emoji(self):
        """获取女孩表情"""
        return self.get_emoji('premium_girl', e_girl)

    def star_emoji(self):
        """获取星星表情"""
        return self.get_emoji('premium_star', e_star)

    def heart_emoji(self):
        """获取心形表情"""
        return self.get_emoji('premium_heart', e_heart_red)

    def fire_emoji(self):
        """获取火焰表情"""
        return self.get_emoji('premium_fire', e_fire)


# 图片现在是必填字段，按数据库字段顺序排列，特点也是必填项
REQUIRED_FIELDS = ['name', 'country', 'age', 'height', 'weight', 'boobs', 'baby', 'location', 'special', 'price_in', 'price_out', 'picture']


# 全局表情管理器实例
emoji_mgr = EmojiManager(has_premium=False)  # 启用自定义表情
# 语言管理实例 - 统一使用此实例
lang = Language()
# 共用的媒体处理实例
medias = Medias()
# 公共分割线
line = '-' * 25


def get_keyboard_common(keyboard_type: str, **kwargs) -> InlineKeyboardMarkup:
    """
    获取 Hazoo 项目的公共内联键盘

    Args:
        keyboard_type: 键盘类型
            - 'main_menu': 主菜单键盘（无参数）
            - 'girl_status_confirmation': 女孩状态确认键盘（需要 girl_id）
            - 'girl_management': 女孩管理键盘（需要 girl_id）
            - 'girl_details': 女孩详情键盘（需要 girl_id）
        **kwargs: 动态参数，根据键盘类型传入不同参数
            - girl_id: 女孩ID
            - order_id: 订单ID
            - user_id: 用户ID

    Returns:
        InlineKeyboardMarkup: 对应类型的内联键盘

    Raises:
        ValueError: 当必需参数缺失时抛出异常
    """

    # 主菜单键盘（从 edenmanbot.py 迁移）
    if keyboard_type == 'main_menu':
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton(f"{e_add} 新增女孩", callback_data="girl_add"),
                InlineKeyboardButton(f"{emoji_mgr.get_emoji('premium_girl', e_girl)} 女孩统计", callback_data="girl_stats"),
                InlineKeyboardButton(f"{e_search} 查看女孩", callback_data="girl_list")
            ],
            [
                InlineKeyboardButton(f"{e_home} 主菜单", callback_data="main_menu")
            ]
        ])

    # 女孩状态确认键盘
    elif keyboard_type == 'girl_status_confirmation':
        girl_id = kwargs.get('girl_id')
        if girl_id is None:
            raise ValueError("girl_status_confirmation 键盘需要 girl_id 参数")

        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🟢 1-在线", callback_data=f"girl_status_1_{girl_id}"),
                InlineKeyboardButton("🔵 2-私下推荐", callback_data=f"girl_status_2_{girl_id}")
            ],
            [
                InlineKeyboardButton("🔴 3-下架", callback_data=f"girl_status_3_{girl_id}"),
                InlineKeyboardButton("✏️ 继续编辑", callback_data=f"girl_edit_{girl_id}")
            ]
        ])

    # 女孩管理键盘
    elif keyboard_type == 'girl_management':
        girl_id = kwargs.get('girl_id')
        if girl_id is None:
            raise ValueError("girl_management 键盘需要 girl_id 参数")

        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🔧 修改", callback_data=f"girl_edit_{girl_id}"),
                InlineKeyboardButton("📊 统计", callback_data=f"girl_stats_{girl_id}")
            ],
            [
                InlineKeyboardButton("📤 发送给客户", callback_data=f"girl_send_{girl_id}"),
                InlineKeyboardButton("🏠 主页", callback_data="main_menu")
            ]
        ])

    # 女孩详情键盘
    elif keyboard_type == 'girl_details':
        girl_id = kwargs.get('girl_id')
        if girl_id is None:
            raise ValueError("girl_details 键盘需要 girl_id 参数")

        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("✏️ 编辑", callback_data=f"girl_edit_{girl_id}"),
                InlineKeyboardButton("🔄 状态", callback_data=f"girl_status_{girl_id}")
            ],
            [
                InlineKeyboardButton("📤 发送", callback_data=f"girl_send_{girl_id}"),
                InlineKeyboardButton("🔙 返回", callback_data="girls_list")
            ]
        ])

    # 默认键盘（兜底方案）
    else:
        return InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🏠 主页", callback_data="main_menu"),
                InlineKeyboardButton("❓ 帮助", callback_data="help")
            ]
        ])


def mean(type, text='', point='to'):
    """
    映射字典统一管理方法
    参考 robot_park 项目的 mean 方法实现

    Args:
        type: 映射类型
        text: 要转换的值
        point: 转换方向 ('to': 键->值, 'from': 值->键)

    Returns:
        转换后的值，如果找不到则返回 None
    """
    to_dic = {}

    # 字段名称映射
    if type == 'field_names':
        to_dic = {
            'name': '姓名', 'country': '国籍', 'age': '年龄', 'height': '身高', 'weight': '体重',
            'boobs': '胸围', 'baby': '孩子', 'location': '住址', 'special': '特点',
            'phone': '电话', 'price_in': '内部价格', 'price_out': '外部价格',
            'picture': '照片', 'note': '备注'
        }

    # 国籍选项映射
    elif type == 'country_options':
        to_dic = {
            0: '混血', 1: '哈萨克斯坦', 2: '俄罗斯', 3: '乌克兰', 4: '乌兹别克斯坦',
            5: '吉尔吉斯斯坦', 6: '土库曼斯坦', 7: '塔吉克斯坦', 8: '中国'
        }

    # 孩子选项映射
    elif type == 'baby_options':
        to_dic = {0: '没有', 1: '有', 2: '未知'}

    # 女孩状态映射
    elif type == 'girl_status':
        to_dic = {0: '暂缓', 1: '在线', 2: '私下推荐', 3: '下架'}

    # 英文女孩状态映射
    elif type == 'girl_status_en':
        to_dic = {0: "Paused", 1: "Online", 2: "Private", 3: "Offline"}

    # 俄语女孩状态映射
    elif type == 'girl_status_ru':
        to_dic = {0: "Приостановлено", 1: "В сети", 2: "Частное", 3: "Не в сети"}

    # 哈萨克语女孩状态映射
    elif type == 'girl_status_kz':
        to_dic = {0: "Тоқтатылған", 1: "Желіде", 2: "Жеке", 3: "Желіде жоқ"}

    # 女孩等级映射
    elif type == 'girl_grade':
        to_dic = {0: '无等级', 1: '不满意', 2: '普通', 3: '还不错', 4: '优质', 5: '极品'}

    # 国旗映射
    elif type == 'country_flags':
        to_dic = {
            0: "🇺🇳",  # 混血
            1: "🇰🇿",  # 哈萨克斯坦
            2: "🇷🇺",  # 俄罗斯
            3: "🇺🇦",  # 乌克兰
            4: "🇺🇿",  # 乌兹别克斯坦
            5: "🇰🇬",  # 吉尔吉斯斯坦
            6: "🇹🇲",  # 土库曼斯坦
            7: "🇹🇯",  # 塔吉克斯坦
            8: "🇨🇳"   # 中国
        }

    # 确定转换方向
    if point == 'to':
        return to_dic.get(text, None)
    elif point == 'from':
        from_dict = {v: k for k, v in to_dic.items()}
        return from_dict.get(text, None)
    else:
        return None


# 共用的输入验证函数
async def input_cert(str_input='', style='None', user_id=0, client=None):
    """统一输入验证函数"""
    try:
        if user_id and not MySQLDB.check_params([str_input]):
            client_username = clients.get_username(client)
            msg_text = f"机器人 {client_username} 收到用户 {myChats[user_id].outname}({user_id}) 输入的危险内容: \n{str_input}"
            sys_log.error_log(msg_text)
            if client:
                await client.send_message(params['carter'], msg_text)
            return [False, '请输入正确的内容']
        if style == '姓名':
            cleaned = str_input.strip().title()
            if len(cleaned) > 50:
                return [False, '姓名长度不能超过50个字符']
            elif len(cleaned) == 0:
                return [False, '姓名不能为空']
            else:
                return [True, cleaned]
        elif style == '年龄':
            if re.match(r'^[1-3][0-9]$', str_input.strip()):
                age = int(str_input.strip())
                if 18 <= age <= 39:
                    return [True, str(age)]
                else:
                    return [False, '年龄必须在18-39岁之间']
            else:
                return [False, '年龄格式不正确']
        elif style == '身高':
            if re.match(r'^[12][0-9]{2}$', str_input.strip()):
                height = int(str_input.strip())
                if 120 <= height <= 190:  # 调整最低身高为120cm
                    return [True, str(height)]
                else:
                    return [False, '身高必须在120-190cm之间']
            else:
                return [False, '身高格式不正确']
        elif style == '体重':
            if re.match(r'^[3-6][0-9]$', str_input.strip()):
                weight = int(str_input.strip())
                if 30 <= weight <= 65:  # 调整最低体重为30kg
                    return [True, str(weight)]
                else:
                    return [False, '体重必须在30-65kg之间']
            else:
                return [False, '体重格式不正确']
        elif style == '胸围':
            if re.match(r'^[2-4][0-9][a-fA-F]$', str_input.strip()):
                return [True, str_input.strip().upper()]
            else:
                return [False, '胸围格式不正确，如：32A, 34B, 36C']
        elif style == '住址':
            cleaned = str_input.strip().title()
            if len(cleaned) > 50:
                return [False, '住址长度不能超过50个字符']  # 更新错误信息
            else:
                return [True, cleaned]
        elif style == '特点':
            # cleaned = correct_char(str_input)
            cleaned = str_input.strip()
            if len(cleaned) > 255:
                return [False, '特点描述不能超过255个字符']  # 更新错误信息
            return [True, cleaned if cleaned else '-']
        elif style == '电话':
            cleaned_input = str_input.strip()
            if len(cleaned_input) > 50:
                return [False, '电话号码长度不能超过50个字符']
            # 检查是否只包含数字和'/'字符
            if cleaned_input and not re.match(r'^[0-9/]+$', cleaned_input):
                return [False, '电话号码只能包含数字和"/"字符']
            return [True, cleaned_input if cleaned_input else '-']
        elif style == '照片':
            # 图片字段验证 - 格式如 ",1,2,3,"
            if not str_input.strip():
                return [False, '照片是必填字段，不能为空']

            # 检查格式：必须以逗号开头和结尾
            if not str_input.startswith(',') or not str_input.endswith(','):
                return [False, '照片格式不正确，应为 ",1,2,3," 格式']

            # 检查中间部分是否为数字（用逗号分隔）
            middle_part = str_input[1:-1]  # 去掉首尾逗号
            if middle_part:  # 如果不为空
                parts = middle_part.split(',')
                for part in parts:
                    if not part.strip().isdigit():
                        return [False, '照片ID必须为数字，格式如 ",1,2,3,"']
            else:
                return [False, '照片不能为空，格式如 ",1,2,3,"']

            if len(str_input) > 500:
                return [False, '照片字段长度不能超过500个字符']

            return [True, str_input.strip()]
        elif style in ('内部价格', '外部价格'):
            # 允许4个数字或7个数字的格式
            if re.match(r'^[0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6}$', str_input.strip()):
                return [True, str_input.strip()]
            elif re.match(r'^[0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6} [0-9]{1,6}$', str_input.strip()):
                return [True, str_input.strip()]
            else:
                return [False, '价格格式不正确，如：1000 2000 3000 4000 或 1000 2000 3000 4000 5000 6000 7000']
        elif style == '备注':
            # cleaned = correct_char(str_input)
            cleaned = str_input.strip()
            if len(cleaned) > 255:
                return [False, '备注长度不能超过255个字符']
            return [True, cleaned if cleaned else '']
        return [False, '未知的验证类型']
    except Exception as e:
        sys_log.error_log(f"输入验证失败: {e}")
        return [False, '验证过程出错，请重试']


# ==================== 女孩卡片格式化方法（简洁版本） ====================

from plugins.hazoo.modules.girls import TGirls
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton


def format_girl_staff(girl: TGirls) -> str:
    """
    格式化女孩卡片 - 内部客服管理版本
    显示所有字段，用于内部管理和修改（纯中文）

    Args:
        girl: 女孩对象

    Returns:
        str: 格式化后的中文卡片文本
    """
    try:
        # 处理图片ID显示，去除两边的逗号
        picture_display = girl.picture.strip(',') if girl.picture else '无'

        lines = [
            "💋💋💋 <b>女孩详细信息</b> 💋💋💋",
            f"🆔 编号: {girl.id}",
            f"👤 姓名: {girl.name}",
            f"🌍 国籍: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
            f"🎂 年龄: {girl.age}岁",
            f"📏 身高: {girl.height}cm",
            f"⚖️ 体重: {girl.weight}kg",
            f"👙 胸围: {girl.boobs}",
            f"👶 孩子: {mean('baby_options', girl.baby)}",
            f"📍 地址: {girl.location}",
            f"🇨🇳 中文特点: {girl.special}",
            f"🇺🇸 英文特点: {girl.special_us}",
            f"🇷🇺 俄语特点: {girl.special_ru}",
            f"🇰🇿 哈语特点: {girl.special_kz}",
            f"📞 电话: {girl.phone}",
            f"💰 内部价格: {girl.price_in}",
            f"💵 外部价格: {girl.price_out}",
            f"📸 图片: {picture_display}",
            f"⭐ 评分: {girl.grade}",
            f"🧮 评价数: {girl.assess_cnt}",
            f"👀 浏览数: {girl.viewed}",
            f"{e_status} 状态: {mean('girl_status', girl.status)}",
            f"📝 备注: {girl.note}",
            line
        ]
        return "\n".join(lines)
    except Exception as e:
        sys_log.error_log(f"格式化内部管理卡片失败: {e}")
        return f"❌ 卡片格式化失败: {e}"


def format_girl_customer(girl: TGirls, user_id: int) -> str:
    """
    格式化女孩卡片 - 客户交流版本
    根据用户语言设置显示不同语言版本，包含grade等级文字和星星表情

    Args:
        girl: 女孩对象
        user_id: 用户ID（用于获取语言设置）

    Returns:
        str: 格式化后的卡片文本
    """
    try:
        language = lang[user_id]
        if language == "en_US":
            return _format_girl_customer_english(girl)
        elif language == "ru_RU":
            return _format_girl_customer_russian(girl)
        elif language == "kk_KZ":
            return _format_girl_customer_kazakh(girl)
        else:
            return _format_girl_customer_chinese(girl)
    except Exception as e:
        sys_log.error_log(f"格式化客户卡片失败: {e}")
        return f"❌ 卡片格式化失败: {e}"


def format_girl_channel(girl: TGirls, user_id: int) -> str:
    """
    格式化女孩卡片 - 频道展示版本
    根据用户语言设置显示不同语言版本，不包含grade字段

    Args:
        girl: 女孩对象
        user_id: 用户ID（用于获取语言设置，频道可使用虚拟用户ID）

    Returns:
        str: 格式化后的卡片文本
    """
    try:
        language = lang[user_id]
        if language == "en_US":
            return _format_girl_channel_english(girl)
        elif language == "ru_RU":
            return _format_girl_channel_russian(girl)
        elif language == "kk_KZ":
            return _format_girl_channel_kazakh(girl)
        else:
            return _format_girl_channel_chinese(girl)
    except Exception as e:
        sys_log.error_log(f"格式化频道卡片失败: {e}")
        return f"❌ 卡片格式化失败: {e}"


async def format_girl_customer_with_media(girl: TGirls, user_id: int, chat_id: int) -> bool:
    """
    发送客户版本卡片（通过copy_media_group发送，文本附在第一个图片中）

    Args:
        girl: 女孩对象
        user_id: 用户ID（用于获取语言设置）
        chat_id: 聊天ID

    Returns:
        bool: 发送是否成功
    """
    try:
        # 获取卡片文本
        card_text = format_girl_customer(girl, user_id)

        # 发送媒体组（图片已保证是media_group格式）
        try:
            # 解析图片ID列表，格式为 ",1,2,3,"
            picture_ids = [int(x) for x in girl.picture.strip(',').split(',') if x]

            # 使用copy_media_group发送，文本作为caption
            await clients['edenmanbot'].copy_media_group(
                chat_id=chat_id,
                from_chat_id=medias.chat_id,
                message_id=picture_ids,
                captions=card_text  # 文本附在第一个图片中
            )
        except Exception as e:
            sys_log.error_log(f"发送客户媒体失败: girl_id={girl.id}, picture={girl.picture}, 错误: {e}")
            # 如果媒体发送失败，则发送纯文本
            await clients['edenmanbot'].send_message(chat_id, card_text)

        return True
    except Exception as e:
        sys_log.error_log(f"发送客户卡片失败: {e}")
        return False


async def format_girl_channel_with_media(girl: TGirls, user_id: int, chat_id: int) -> bool:
    """
    发送频道版本卡片（通过copy_media_group发送，文本附在第一个图片中）

    Args:
        girl: 女孩对象
        user_id: 用户ID（用于获取语言设置，频道可使用虚拟用户ID）
        chat_id: 聊天ID

    Returns:
        bool: 发送是否成功
    """
    try:
        # 获取卡片文本
        card_text = format_girl_channel(girl, user_id)

        # 发送媒体组（图片已保证是media_group格式）
        try:
            # 解析图片ID列表，格式为 ",1,2,3,"
            picture_ids = [int(x) for x in girl.picture.strip(',').split(',') if x]

            # 使用copy_media_group发送，文本作为caption
            await clients['edenmanbot'].copy_media_group(
                chat_id=chat_id,
                from_chat_id=medias.chat_id,
                message_id=picture_ids,
                captions=card_text  # 文本附在第一个图片中
            )
        except Exception as e:
            sys_log.error_log(f"发送频道媒体失败: girl_id={girl.id}, picture={girl.picture}, 错误: {e}")
            # 如果媒体发送失败，则发送纯文本
            await clients['edenmanbot'].send_message(chat_id, card_text)

        return True
    except Exception as e:
        sys_log.error_log(f"发送频道卡片失败: {e}")
        return False


async def format_girl_staff_with_media(girl: TGirls, chat_id: int) -> bool:
    """
    发送内部管理版本卡片（先发送媒体，再发送带按钮的文本）

    Args:
        girl: 女孩对象
        chat_id: 聊天ID

    Returns:
        bool: 发送是否成功
    """
    try:
        # 1. 先发送媒体组（图片已保证是media_group格式）
        try:
            # 解析图片ID列表，格式为 ",1,2,3,"
            picture_ids = [int(x) for x in girl.picture.strip(',').split(',') if x]

            # 使用copy_media_group发送媒体
            await clients['edenmanbot'].copy_media_group(
                chat_id=chat_id,
                from_chat_id=medias.chat_id,
                message_id=picture_ids
            )
        except Exception as e:
            sys_log.error_log(f"发送内部管理媒体失败: girl_id={girl.id}, picture={girl.picture}, 错误: {e}")

        # 2. 发送带按钮的文本卡片
        card_text = format_girl_staff(girl)

        # 使用公共的keyboard（从get_keyboard函数获取）
        keyboard = get_keyboard_common('girl_management', girl.id)

        await clients['edenmanbot'].send_message(
            chat_id=chat_id,
            text=card_text,
            reply_markup=keyboard
        )

        return True
    except Exception as e:
        sys_log.error_log(f"发送内部管理卡片失败: {e}")
        return False


def _format_girl_customer_chinese(girl: TGirls) -> str:
    """格式化中文客户卡片"""
    lines = [
        "👩 女孩资料",
        f"🆔 编号: {girl.id}",
        f"🌍 国籍: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 年龄: {girl.age}岁",
        f"📏 身高: {girl.height}cm",
        f"⚖️ 体重: {girl.weight}kg",
        f"👙 胸围: {girl.boobs}",
        f"📍 地址: {girl.location}",
    ]

    # 添加特点字段
    if girl.special and girl.special.strip():
        lines.append(f"✨ 特点: {girl.special}")

    lines.append(f"💰 价格: {girl.price_out}")

    # 添加评分（如果有评价）
    if girl.assess_cnt > 0:
        grade_level = _get_grade_level_chinese(girl.grade)
        stars = "⭐" * _get_star_count(girl.grade)
        lines.append(f"⭐ 评级: {stars} {grade_level}")

    lines.extend([
        f"{e_status} 状态: {mean('girl_status', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_customer_english(girl: TGirls) -> str:
    """格式化英文客户卡片"""
    lines = [
        "👩 Girl Profile",
        f"🆔 ID: {girl.id}",
        f"🌍 Country: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 Age: {girl.age}",
        f"📏 Height: {girl.height}cm",
        f"⚖️ Weight: {girl.weight}kg",
        f"👙 Boobs: {girl.boobs}",
        f"📍 Location: {girl.location}",
    ]

    # 添加特点字段
    if girl.special_us and girl.special_us.strip():
        lines.append(f"✨ Features: {girl.special_us}")

    lines.append(f"💰 Price: {girl.price_out}")

    # 添加评分（如果有评价）
    if girl.assess_cnt > 0:
        grade_level = _get_grade_level_english(girl.grade)
        stars = "⭐" * _get_star_count(girl.grade)
        lines.append(f"⭐ Rating: {stars} {grade_level}")

    lines.extend([
        f"{e_status} Status: {mean('girl_status_en', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_customer_russian(girl: TGirls) -> str:
    """格式化俄语客户卡片"""
    lines = [
        "👩 Профиль девушки",
        f"🆔 ID: {girl.id}",
        f"🌍 Страна: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 Возраст: {girl.age}",
        f"📏 Рост: {girl.height}см",
        f"⚖️ Вес: {girl.weight}кг",
        f"👙 Грудь: {girl.boobs}",
        f"📍 Адрес: {girl.location}",
    ]

    # 添加特点字段
    if girl.special_ru and girl.special_ru.strip():
        lines.append(f"✨ Особенности: {girl.special_ru}")

    lines.append(f"💰 Цена: {girl.price_out}")

    # 添加评分（如果有评价）
    if girl.assess_cnt > 0:
        grade_level = _get_grade_level_russian(girl.grade)
        stars = "⭐" * _get_star_count(girl.grade)
        lines.append(f"⭐ Рейтинг: {stars} {grade_level}")

    lines.extend([
        f"{e_status} Статус: {mean('girl_status_ru', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_customer_kazakh(girl: TGirls) -> str:
    """格式化哈萨克语客户卡片"""
    lines = [
        "👩 Қыз профилі",
        f"🆔 ID: {girl.id}",
        f"🌍 Ел: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 Жасы: {girl.age}",
        f"📏 Бойы: {girl.height}см",
        f"⚖️ Салмағы: {girl.weight}кг",
        f"👙 Кеуде: {girl.boobs}",
        f"📍 Мекенжайы: {girl.location}",
    ]

    # 添加特点字段
    if girl.special_kz and girl.special_kz.strip():
        lines.append(f"✨ Ерекшеліктері: {girl.special_kz}")

    lines.append(f"💰 Бағасы: {girl.price_out}")

    # 添加评分（如果有评价）
    if girl.assess_cnt > 0:
        grade_level = _get_grade_level_kazakh(girl.grade)
        stars = "⭐" * _get_star_count(girl.grade)
        lines.append(f"⭐ Рейтинг: {stars} {grade_level}")

    lines.extend([
        f"{e_status} Күйі: {mean('girl_status_kz', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_channel_chinese(girl: TGirls) -> str:
    """格式化中文频道卡片"""
    lines = [
        "👩 女孩资料",
        f"🆔 编号: {girl.id}",
        f"🌍 国籍: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 年龄: {girl.age}岁",
        f"📏 身高: {girl.height}cm",
        f"⚖️ 体重: {girl.weight}kg",
        f"👙 胸围: {girl.boobs}",
        f"📍 地址: {girl.location}",
    ]

    # 添加特点字段
    if girl.special and girl.special.strip():
        lines.append(f"✨ 特点: {girl.special}")

    lines.extend([
        f"💰 价格: {girl.price_out}",
        f"{e_status} 状态: {mean('girl_status', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_channel_english(girl: TGirls) -> str:
    """格式化英文频道卡片"""
    lines = [
        "👩 Girl Profile",
        f"🆔 ID: {girl.id}",
        f"🌍 Country: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 Age: {girl.age}",
        f"📏 Height: {girl.height}cm",
        f"⚖️ Weight: {girl.weight}kg",
        f"👙 Bust: {girl.boobs}",
        f"📍 Location: {girl.location}",
    ]

    # 添加特点字段
    if girl.special_us and girl.special_us.strip():
        lines.append(f"✨ Features: {girl.special_us}")

    lines.extend([
        f"💰 Price: {girl.price_out}",
        f"{e_status} Status: {mean('girl_status_en', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_channel_russian(girl: TGirls) -> str:
    """格式化俄语频道卡片"""
    lines = [
        "👩 Профиль девушки",
        f"🆔 ID: {girl.id}",
        f"🌍 Страна: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 Возраст: {girl.age}",
        f"📏 Рост: {girl.height}см",
        f"⚖️ Вес: {girl.weight}кг",
        f"👙 Грудь: {girl.boobs}",
        f"📍 Адрес: {girl.location}",
    ]

    # 添加特点字段
    if girl.special_ru and girl.special_ru.strip():
        lines.append(f"✨ Особенности: {girl.special_ru}")

    lines.extend([
        f"💰 Цена: {girl.price_out}",
        f"{e_status} Статус: {mean('girl_status_ru', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _format_girl_channel_kazakh(girl: TGirls) -> str:
    """格式化哈萨克语频道卡片"""
    lines = [
        "👩 Қыз профилі",
        f"🆔 ID: {girl.id}",
        f"🌍 Ел: {mean('country_options', girl.country)} {mean('country_flags', girl.country)}",
        f"🎂 Жасы: {girl.age}",
        f"📏 Бойы: {girl.height}см",
        f"⚖️ Салмағы: {girl.weight}кг",
        f"👙 Кеуде: {girl.boobs}",
        f"📍 Мекенжайы: {girl.location}",
    ]

    # 添加特点字段
    if girl.special_kz and girl.special_kz.strip():
        lines.append(f"✨ Ерекшеліктері: {girl.special_kz}")

    lines.extend([
        f"💰 Бағасы: {girl.price_out}",
        f"{e_status} Күйі: {mean('girl_status_kz', girl.status)}",
        line
    ])

    return "\n".join(lines)


def _get_star_count(grade: float) -> int:
    """根据grade数值获取星星数量（修正版）"""
    if grade == 0:
        return 0
    elif 0 < grade < 1.1:
        return 1
    elif 1.1 <= grade < 2:
        return 2
    elif 2 <= grade < 3:
        return 3
    elif 3 <= grade < 4:
        return 4
    elif 4 <= grade <= 5:
        return 5
    else:
        return 0


def _get_grade_level_chinese(grade: float) -> str:
    """根据grade数值获取中文等级文字"""
    if grade == 0:
        return "无等级"
    elif 0 < grade < 1.1:
        return "不满意"
    elif 1.1 <= grade < 2:
        return "普通"
    elif 2 <= grade < 3:
        return "还不错"
    elif 3 <= grade < 4:
        return "优质"
    elif 4 <= grade <= 5:
        return "极品"
    else:
        return "未知"


def _get_grade_level_english(grade: float) -> str:
    """根据grade数值获取英文等级文字"""
    if grade == 0:
        return "No Rating"
    elif 0 < grade < 1.1:
        return "Unsatisfied"
    elif 1.1 <= grade < 2:
        return "Average"
    elif 2 <= grade < 3:
        return "Good"
    elif 3 <= grade < 4:
        return "Excellent"
    elif 4 <= grade <= 5:
        return "Premium"
    else:
        return "Unknown"


def _get_grade_level_russian(grade: float) -> str:
    """根据grade数值获取俄语等级文字"""
    if grade == 0:
        return "Без рейтинга"
    elif 0 < grade < 1.1:
        return "Неудовлетворительно"
    elif 1.1 <= grade < 2:
        return "Обычно"
    elif 2 <= grade < 3:
        return "Хорошо"
    elif 3 <= grade < 4:
        return "Отлично"
    elif 4 <= grade <= 5:
        return "Премиум"
    else:
        return "Неизвестно"


def _get_grade_level_kazakh(grade: float) -> str:
    """根据grade数值获取哈萨克语等级文字"""
    if grade == 0:
        return "Рейтингсіз"
    elif 0 < grade < 1.1:
        return "Қанағаттанарлықсыз"
    elif 1.1 <= grade < 2:
        return "Қалыпты"
    elif 2 <= grade < 3:
        return "Жақсы"
    elif 3 <= grade < 4:
        return "Өте жақсы"
    elif 4 <= grade <= 5:
        return "Ерекше"
    else:
        return "Белгісіз"


# 表情定义 - 统一管理，避免重复定义，合并到一个元组
(e_add, e_girl, e_photo, e_ok, e_no, e_warning, e_succ, e_home, e_write, e_baby,
 e_location, e_phone, e_money, e_note, e_search, e_heart_red, e_fire, e_star, e_flower,
 e_required, e_optional, e_edit, e_return, e_manage, e_tip,
 e_id, e_name, e_country, e_age, e_height, e_weight, e_boobs, e_baby_field,
 e_location_field, e_special, e_phone_field, e_price_in, e_price_out, e_picture,
 e_grade, e_viewed, e_status, e_note_field) = \
("➕", "👩", "📸", "✅", "❌", "⚠️", "🎉", "🏠", "✍️", "🤱",
 "📍", "📞", "💰", "📝", "🔎", "❤️", "🔥", "⭐", "🌹",
 "🔴", "⚪", "✏️", "🔙", "🛠️", "💡",
 "🆔", "👤", "🌍", "🎂", "📏", "⚖️", "👙", "👶",
 "📍", "✨", "📱", "💰", "💵", "📸",
 "⭐", "👀", "🚥", "📝")

